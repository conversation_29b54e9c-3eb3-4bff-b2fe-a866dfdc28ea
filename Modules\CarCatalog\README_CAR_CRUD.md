# Car CRUD Implementation - CarCatalog Module

## Overview
تم تنفيذ واجهة إضافة/تعديل السيارة باستخدام مكون Stepper المكون من 5 خطوات في لوحة تحكم الإدارة Dash.

## Features Implemented

### 1. CarController
- **Location**: `Modules/CarCatalog/Http/Controllers/Admin/CarController.php`
- **Methods**:
  - `index()` - عرض قائمة السيارات مع البحث والفلترة
  - `create()` - عرض صفحة إضافة سيارة جديدة
  - `store()` - حفظ السيارة الجديدة مع الصور والميزات
  - `show()` - عرض تفاصيل السيارة
  - `edit()` - عرض صفحة تعديل السيارة
  - `update()` - تحديث بيانات السيارة
  - `destroy()` - حذف السيارة (soft delete)
  - `getModelsByBrand()` - AJAX endpoint لجلب الموديلات حسب الماركة

### 2. Form Requests
- **StoreCarRequest**: التحقق من صحة البيانات عند إضافة سيارة جديدة
- **UpdateCarRequest**: التحقق من صحة البيانات عند تعديل السيارة

### 3. Stepper Interface (5 Steps)

#### Step 1: Basic Information (البيانات الأساسية)
- عنوان السيارة
- الماركة (مع تحميل ديناميكي للموديلات)
- الموديل
- سنة الصنع
- اللون الخارجي
- لون المقصورة الداخلية
- رقم الهيكل (VIN)
- رقم اللوحة
- وصف السيارة

#### Step 2: Technical Specifications (المواصفات الفنية)
- نوع الهيكل
- نوع ناقل الحركة
- نوع الوقود
- سعة المحرك
- عدد الأبواب
- عدد المقاعد
- المسافة المقطوعة
- حالة السيارة

#### Step 3: Features (الميزات)
- اختيار الميزات مجمعة حسب الفئة
- أزرار تحديد/إلغاء تحديد سريع لكل فئة

#### Step 4: Images & Video (الصور والفيديو)
- رفع صور متعددة (حتى 10 صور)
- اختيار الصورة الرئيسية
- معاينة الصور قبل الرفع
- رابط فيديو (اختياري)
- معاينة الفيديو

#### Step 5: Price & Status (السعر والحالة)
- السعر الأساسي والعملة
- سعر العرض (اختياري)
- تواريخ العرض
- حساب نسبة الخصم تلقائياً
- حالة السيارة (مميزة، مباعة، نشطة)
- ملخص بيانات السيارة

### 4. Views Structure
```
Modules/CarCatalog/Resources/views/admin/cars/
├── index.blade.php                    # قائمة السيارات
├── create.blade.php                   # إضافة سيارة جديدة
├── edit.blade.php                     # تعديل السيارة
├── show.blade.php                     # عرض تفاصيل السيارة
└── partials/
    ├── stepper_basic_info.blade.php   # الخطوة 1
    ├── stepper_tech_specs.blade.php   # الخطوة 2
    ├── stepper_features.blade.php     # الخطوة 3
    ├── stepper_images.blade.php       # الخطوة 4
    └── stepper_price_status.blade.php # الخطوة 5
```

### 5. Routes
- **Resource Routes**: `admin/cars/*`
- **AJAX Route**: `admin/brands/{brand}/models` - لجلب الموديلات

### 6. JavaScript Features
- **bs-stepper**: للتنقل بين الخطوات
- **AJAX Model Loading**: تحميل الموديلات عند اختيار الماركة
- **Image Preview**: معاينة الصور قبل الرفع
- **Video Preview**: معاينة الفيديو من YouTube/Vimeo
- **Price Calculation**: حساب نسبة الخصم تلقائياً
- **Summary Update**: تحديث ملخص البيانات في الخطوة الأخيرة

### 7. Media Library Integration
- **Collections**:
  - `car_images`: صور السيارة (متعددة)
  - `car_thumbnail`: الصورة الرئيسية (واحدة)
- **Conversions**:
  - `thumb`: 200x150px
  - `medium`: 800x600px

## Validation Rules

### Required Fields
- title (عنوان السيارة)
- brand_id (الماركة)
- car_model_id (الموديل)
- manufacturing_year_id (سنة الصنع)
- main_color_id (اللون الرئيسي)
- body_type_id (نوع الهيكل)
- transmission_type_id (نوع ناقل الحركة)
- fuel_type_id (نوع الوقود)
- condition (حالة السيارة)
- price (السعر)
- currency (العملة)

### Optional Fields
- description (الوصف)
- interior_color_id (لون المقصورة)
- vin (رقم الهيكل)
- plate_number (رقم اللوحة)
- engine_capacity (سعة المحرك)
- doors_count (عدد الأبواب)
- seats_count (عدد المقاعد)
- mileage (المسافة المقطوعة)
- features (الميزات)
- car_images (الصور)
- video_url (رابط الفيديو)
- offer_price (سعر العرض)
- offer_start_date/offer_end_date (تواريخ العرض)

## Permissions Required
- `manage_cars_admin`: للوصول إلى جميع عمليات إدارة السيارات

## Dependencies
- **spatie/laravel-medialibrary**: لإدارة الصور
- **bs-stepper**: للواجهة المتدرجة
- جميع نماذج البيانات الوصفية (Brand, CarModel, Color, etc.)

## Usage
1. انتقل إلى `/admin/cars` لعرض قائمة السيارات
2. اضغط "إضافة سيارة جديدة" لبدء عملية الإضافة
3. املأ البيانات في الخطوات الخمس
4. احفظ السيارة في الخطوة الأخيرة

## Notes
- يتم حفظ جميع البيانات في خطوة واحدة في النهاية
- يمكن التنقل بين الخطوات بحرية
- يتم التحقق من صحة البيانات عند الحفظ
- يدعم رفع صور متعددة مع اختيار الصورة الرئيسية
- يدعم الحذف الناعم للسيارات
