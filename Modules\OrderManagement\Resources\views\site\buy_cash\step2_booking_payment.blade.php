{{-- 
    الخطوة 2: تفاصيل الحجز والدفع - عملية شراء السيارة كاش
    
    يعرض هذا الـ view تفاصيل السيارة ومبلغ الحجز وخيارات الدفع
    بناءً على UIUX-FR.md (SITE-BUY-CASH-STEPX-001) و REQ-FR.md (MOD-ORDER-MGMT-FEAT-003)
--}}

@extends('site.layouts.site_layout')

@section('title', 'شراء السيارة - تفاصيل الحجز والدفع')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            
            {{-- عنوان الصفحة ومؤشر التقدم --}}
            <div class="text-center mb-4">
                <h2 class="mb-3">شراء السيارة كاش</h2>
                
                {{-- مؤشر التقدم (Stepper) --}}
                <div class="progress-stepper mb-4">
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">البيانات الشخصية</div>
                    </div>
                    <div class="step active">
                        <div class="step-number">2</div>
                        <div class="step-title">تفاصيل الحجز</div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">المستندات</div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-title">المراجعة والتأكيد</div>
                    </div>
                </div>
            </div>

            <div class="row">
                {{-- ملخص السيارة والأسعار --}}
                <div class="col-lg-5 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                تفاصيل السيارة والأسعار
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($car->getFirstMediaUrl('car_main_image') || $car->getFirstMediaUrl('car_images'))
                                <img src="{{ $car->getFirstMediaUrl('car_main_image') ?: $car->getFirstMediaUrl('car_images') }}" 
                                     alt="{{ $car->title }}" 
                                     class="img-fluid rounded mb-3">
                            @endif
                            
                            <h6 class="fw-bold">{{ $car->title }}</h6>
                            <p class="text-muted mb-2">
                                {{ $car->brand->name ?? '' }} - {{ $car->carModel->name ?? '' }}
                            </p>
                            <p class="text-muted mb-3">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $car->manufacturingYear->year ?? '' }}
                                <span class="mx-2">|</span>
                                <i class="fas fa-palette me-1"></i>
                                {{ $car->mainColor->name ?? '' }}
                            </p>
                            
                            {{-- تفاصيل الأسعار --}}
                            <div class="price-breakdown">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>سعر السيارة:</span>
                                    <span class="fw-bold">{{ number_format($car->price, 0) }} {{ $car->currency }}</span>
                                </div>
                                
                                <hr class="my-2">
                                
                                <div class="d-flex justify-content-between mb-2 text-success">
                                    <span>مبلغ الحجز المطلوب:</span>
                                    <span class="fw-bold">{{ number_format($reservationAmount, 0) }} {{ $car->currency }}</span>
                                </div>
                                
                                <div class="d-flex justify-content-between mb-3 text-muted">
                                    <span>المبلغ المتبقي (في المعرض):</span>
                                    <span>{{ number_format($remainingAmount, 0) }} {{ $car->currency }}</span>
                                </div>
                                
                                <div class="alert alert-info">
                                    <small>
                                        <i class="fas fa-info-circle me-1"></i>
                                        سيتم دفع المبلغ المتبقي عند استلام السيارة من المعرض
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- خيارات الدفع --}}
                <div class="col-lg-7">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                طريقة دفع مبلغ الحجز
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('site.order.cash.step3') }}" method="GET" id="paymentForm">
                                
                                {{-- خيارات طريقة الدفع --}}
                                <div class="mb-4">
                                    <label class="form-label fw-bold">اختر طريقة الدفع:</label>
                                    
                                    {{-- الدفع الإلكتروني --}}
                                    <div class="form-check payment-option mb-3">
                                        <input class="form-check-input" 
                                               type="radio" 
                                               name="payment_method" 
                                               id="online_payment" 
                                               value="online" 
                                               checked>
                                        <label class="form-check-label w-100" for="online_payment">
                                            <div class="payment-card">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-credit-card text-primary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">الدفع الإلكتروني</h6>
                                                        <small class="text-muted">
                                                            فيزا، ماستركارد، مدى، أبل باي
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <span class="badge bg-success">موصى به</span>
                                                    <span class="badge bg-info">فوري</span>
                                                </div>
                                            </div>
                                        </label>
                                    </div>

                                    {{-- الدفع في المعرض --}}
                                    <div class="form-check payment-option mb-3">
                                        <input class="form-check-input" 
                                               type="radio" 
                                               name="payment_method" 
                                               id="showroom_payment" 
                                               value="showroom">
                                        <label class="form-check-label w-100" for="showroom_payment">
                                            <div class="payment-card">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-building text-secondary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">الدفع في المعرض</h6>
                                                        <small class="text-muted">
                                                            نقداً أو بالبطاقة في المعرض
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <span class="badge bg-warning">يتطلب زيارة</span>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                {{-- معلومات إضافية --}}
                                <div class="alert alert-light">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-shield-alt text-success me-2"></i>
                                        ضمانات الأمان
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li>جميع المعاملات محمية بتشفير SSL</li>
                                        <li>لا نحتفظ ببيانات البطاقة الائتمانية</li>
                                        <li>يمكن إلغاء الحجز خلال 24 ساعة</li>
                                        <li>ضمان استرداد المبلغ في حالة عدم توفر السيارة</li>
                                    </ul>
                                </div>

                                {{-- شروط وأحكام --}}
                                <div class="form-check mb-4">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="terms_agreement" 
                                           name="terms_agreement" 
                                           required>
                                    <label class="form-check-label" for="terms_agreement">
                                        أوافق على 
                                        <a href="#" class="text-primary">شروط وأحكام الحجز</a>
                                        و
                                        <a href="#" class="text-primary">سياسة الاسترداد</a>
                                        <span class="text-danger">*</span>
                                    </label>
                                </div>

                                {{-- أزرار التنقل --}}
                                <div class="d-flex justify-content-between mt-4">
                                    <a href="{{ route('site.order.cash.step1', $car->id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        السابق
                                    </a>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        التالي: رفع المستندات
                                        <i class="fas fa-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress-stepper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.5;
}

.step.active, .step.completed {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.step.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.step.completed .step-number {
    background-color: #198754;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    text-align: center;
}

.payment-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.payment-option:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
}

.payment-option input[type="radio"]:checked + label .payment-card {
    background-color: #e7f3ff;
}

.payment-card {
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.price-breakdown {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}
</style>
@endpush
