<?php

namespace Modules\UserManagement\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Modules\Core\Http\Controllers\BaseController;

/**
 * متحكم طلب رابط إعادة تعيين كلمة المرور للمديرين/الموظفين
 *
 * يتعامل هذا المتحكم مع عرض نموذج طلب إعادة تعيين كلمة المرور ومعالجة الطلب
 */
class PasswordResetLinkController extends BaseController
{
    /**
     * عرض صفحة طلب إعادة تعيين كلمة المرور
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('usermanagement::admin.auth.forgot-password');
    }

    /**
     * معالجة طلب إرسال رابط إعادة تعيين كلمة المرور
     *
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $request->validate([
            'email' => ['required', 'email'],
        ]);

        // سنستخدم وسيط كلمات المرور الافتراضي هنا
        // يمكن تخصيص هذا لاستخدام وسيط مخصص إذا لزم الأمر
        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT
                    ? back()->with('status', __($status))
                    : back()->withErrors(['email' => __($status)]);
    }
}
