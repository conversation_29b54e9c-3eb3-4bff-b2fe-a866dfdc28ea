<?php

namespace Modules\OrderManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * طلب التحقق من صحة بيانات الخطوة الثانية لطلب التمويل
 * 
 * يتحقق من معلومات التمويل (الدخل، الالتزامات، المهنة، البنك)
 * بناءً على MOD-ORDER-MGMT-FEAT-004 في REQ-FR.md
 */
class FinanceOrderStep2Request extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return auth()->check() && 
               session()->has('finance_order_car_id') &&
               session()->has('finance_order_personal_data');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // معلومات التمويل الأساسية
            'down_payment_amount' => [
                'required',
                'numeric',
                'min:0',
                'max:' . (session('finance_order_car_price', 0) * 0.5) // حد أقصى 50% من سعر السيارة
            ],
            
            'monthly_income' => [
                'required',
                'numeric',
                'min:3000', // حد أدنى 3000 ريال
                'max:100000' // حد أقصى 100,000 ريال
            ],
            
            'monthly_obligations' => [
                'required',
                'numeric',
                'min:0',
                'max:' . (session('finance_order_monthly_income', 50000) * 0.8) // حد أقصى 80% من الدخل
            ],
            
            // معلومات العمل
            'employment_type' => [
                'required',
                'string',
                'in:government,private,self_employed,retired'
            ],
            
            'job_title' => [
                'required',
                'string',
                'min:2',
                'max:100'
            ],
            
            'employer_name' => [
                'required_unless:employment_type,self_employed',
                'string',
                'min:2',
                'max:100'
            ],
            
            'work_experience_years' => [
                'required',
                'integer',
                'min:0',
                'max:50'
            ],
            
            'current_job_years' => [
                'required',
                'integer',
                'min:0',
                'max:' . $this->input('work_experience_years', 50)
            ],
            
            // معلومات البنك
            'salary_bank' => [
                'required',
                'string',
                'min:2',
                'max:100'
            ],
            
            'bank_account_number' => [
                'required',
                'string',
                'regex:/^[0-9]{10,24}$/' // رقم حساب بنكي صالح
            ],
            
            'iban_number' => [
                'required',
                'string',
                'regex:/^SA[0-9]{22}$/' // رقم IBAN سعودي صالح
            ],
            
            // معلومات إضافية
            'has_other_loans' => [
                'required',
                'boolean'
            ],
            
            'other_loans_details' => [
                'required_if:has_other_loans,true',
                'nullable',
                'string',
                'max:500'
            ],
            
            'requested_financing_period' => [
                'required',
                'integer',
                'in:12,24,36,48,60' // فترات التمويل المتاحة بالأشهر
            ],
            
            // الموافقات
            'financing_terms_accepted' => [
                'required',
                'accepted'
            ],
            
            'credit_check_consent' => [
                'required',
                'accepted'
            ],
            
            'data_sharing_consent' => [
                'required',
                'accepted'
            ]
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'down_payment_amount.required' => 'مبلغ الدفعة الأولى مطلوب',
            'down_payment_amount.numeric' => 'مبلغ الدفعة الأولى يجب أن يكون رقماً',
            'down_payment_amount.min' => 'مبلغ الدفعة الأولى لا يمكن أن يكون سالباً',
            'down_payment_amount.max' => 'مبلغ الدفعة الأولى لا يمكن أن يزيد عن 50% من سعر السيارة',
            
            'monthly_income.required' => 'الدخل الشهري مطلوب',
            'monthly_income.numeric' => 'الدخل الشهري يجب أن يكون رقماً',
            'monthly_income.min' => 'الدخل الشهري يجب أن يكون 3000 ريال على الأقل',
            'monthly_income.max' => 'الدخل الشهري يجب ألا يزيد عن 100,000 ريال',
            
            'monthly_obligations.required' => 'الالتزامات الشهرية مطلوبة',
            'monthly_obligations.numeric' => 'الالتزامات الشهرية يجب أن تكون رقماً',
            'monthly_obligations.min' => 'الالتزامات الشهرية لا يمكن أن تكون سالبة',
            'monthly_obligations.max' => 'الالتزامات الشهرية لا يمكن أن تزيد عن 80% من الدخل',
            
            'employment_type.required' => 'نوع العمل مطلوب',
            'employment_type.in' => 'نوع العمل المختار غير صحيح',
            
            'job_title.required' => 'المسمى الوظيفي مطلوب',
            'job_title.min' => 'المسمى الوظيفي يجب أن يكون حرفين على الأقل',
            'job_title.max' => 'المسمى الوظيفي يجب ألا يزيد عن 100 حرف',
            
            'employer_name.required_unless' => 'اسم جهة العمل مطلوب',
            'employer_name.min' => 'اسم جهة العمل يجب أن يكون حرفين على الأقل',
            'employer_name.max' => 'اسم جهة العمل يجب ألا يزيد عن 100 حرف',
            
            'work_experience_years.required' => 'سنوات الخبرة مطلوبة',
            'work_experience_years.integer' => 'سنوات الخبرة يجب أن تكون رقماً صحيحاً',
            'work_experience_years.min' => 'سنوات الخبرة لا يمكن أن تكون سالبة',
            'work_experience_years.max' => 'سنوات الخبرة لا يمكن أن تزيد عن 50 سنة',
            
            'current_job_years.required' => 'سنوات العمل الحالي مطلوبة',
            'current_job_years.integer' => 'سنوات العمل الحالي يجب أن تكون رقماً صحيحاً',
            'current_job_years.min' => 'سنوات العمل الحالي لا يمكن أن تكون سالبة',
            'current_job_years.max' => 'سنوات العمل الحالي لا يمكن أن تزيد عن إجمالي سنوات الخبرة',
            
            'salary_bank.required' => 'البنك المحول عليه الراتب مطلوب',
            'salary_bank.min' => 'اسم البنك يجب أن يكون حرفين على الأقل',
            'salary_bank.max' => 'اسم البنك يجب ألا يزيد عن 100 حرف',
            
            'bank_account_number.required' => 'رقم الحساب البنكي مطلوب',
            'bank_account_number.regex' => 'رقم الحساب البنكي غير صحيح',
            
            'iban_number.required' => 'رقم الآيبان مطلوب',
            'iban_number.regex' => 'رقم الآيبان غير صحيح',
            
            'has_other_loans.required' => 'يجب تحديد ما إذا كان لديك قروض أخرى',
            
            'other_loans_details.required_if' => 'تفاصيل القروض الأخرى مطلوبة',
            'other_loans_details.max' => 'تفاصيل القروض الأخرى يجب ألا تزيد عن 500 حرف',
            
            'requested_financing_period.required' => 'فترة التمويل المطلوبة مطلوبة',
            'requested_financing_period.in' => 'فترة التمويل المختارة غير صحيحة',
            
            'financing_terms_accepted.required' => 'يجب الموافقة على شروط التمويل',
            'financing_terms_accepted.accepted' => 'يجب الموافقة على شروط التمويل',
            
            'credit_check_consent.required' => 'يجب الموافقة على فحص السجل الائتماني',
            'credit_check_consent.accepted' => 'يجب الموافقة على فحص السجل الائتماني',
            
            'data_sharing_consent.required' => 'يجب الموافقة على مشاركة البيانات',
            'data_sharing_consent.accepted' => 'يجب الموافقة على مشاركة البيانات'
        ];
    }

    /**
     * أسماء الحقول المخصصة للعرض في رسائل الخطأ
     */
    public function attributes(): array
    {
        return [
            'down_payment_amount' => 'مبلغ الدفعة الأولى',
            'monthly_income' => 'الدخل الشهري',
            'monthly_obligations' => 'الالتزامات الشهرية',
            'employment_type' => 'نوع العمل',
            'job_title' => 'المسمى الوظيفي',
            'employer_name' => 'اسم جهة العمل',
            'work_experience_years' => 'سنوات الخبرة',
            'current_job_years' => 'سنوات العمل الحالي',
            'salary_bank' => 'البنك المحول عليه الراتب',
            'bank_account_number' => 'رقم الحساب البنكي',
            'iban_number' => 'رقم الآيبان',
            'has_other_loans' => 'وجود قروض أخرى',
            'other_loans_details' => 'تفاصيل القروض الأخرى',
            'requested_financing_period' => 'فترة التمويل المطلوبة',
            'financing_terms_accepted' => 'الموافقة على شروط التمويل',
            'credit_check_consent' => 'الموافقة على فحص السجل الائتماني',
            'data_sharing_consent' => 'الموافقة على مشاركة البيانات'
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'job_title' => trim($this->job_title ?? ''),
            'employer_name' => trim($this->employer_name ?? ''),
            'salary_bank' => trim($this->salary_bank ?? ''),
            'bank_account_number' => trim($this->bank_account_number ?? ''),
            'iban_number' => strtoupper(trim($this->iban_number ?? '')),
            'other_loans_details' => trim($this->other_loans_details ?? ''),
        ]);
    }
}
