<?php

namespace Modules\UserManagement\Http\Requests\Admin;

use Illuminate\Auth\Events\Lockout;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تسجيل الدخول للمديرين/الموظفين
 *
 * يتعامل هذا الطلب مع التحقق من صحة بيانات تسجيل الدخول ومصادقة المستخدم
 * مع دعم تسجيل الدخول باستخدام البريد الإلكتروني أو رقم الجوال
 */
class LoginRequest extends BaseRequest
{
    /**
     * تحديد قواعد التحقق من صحة البيانات المطبقة على الطلب
     *
     * @return array
     */
    public function rules()
    {
        return [
            'identifier' => ['required', 'string'],
            'password' => ['required', 'string'],
        ];
    }

    /**
     * مصادقة المستخدم
     *
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authenticate(): void
    {
        $this->ensureIsNotRateLimited();

        // محاولة المصادقة باستخدام البريد الإلكتروني
        if (! Auth::attempt(['email' => $this->input('identifier'), 'password' => $this->input('password')], $this->boolean('remember'))) {
            // إذا فشلت المصادقة بالبريد، حاول باستخدام رقم الجوال
            if (! Auth::attempt(['phone_number' => $this->input('identifier'), 'password' => $this->input('password')], $this->boolean('remember'))) {
                RateLimiter::hit($this->throttleKey());

                throw ValidationException::withMessages([
                    'identifier' => __('auth.failed'),
                ]);
            }
        }

        // التحقق من أن المستخدم المصادق عليه لديه أحد الأدوار المطلوبة
        if (! Auth::user()->hasAnyRole(['Super Admin', 'Employee'])) {
            Auth::logout(); // تسجيل الخروج إذا لم يكن مصرحًا له
            RateLimiter::hit($this->throttleKey());

            throw ValidationException::withMessages([
                'identifier' => __('auth.failed'), // أو رسالة أكثر تحديدًا مثل 'غير مصرح بالوصول'
            ]);
        }

        RateLimiter::clear($this->throttleKey());
    }

    /**
     * التأكد من أن المستخدم لم يتجاوز الحد الأقصى لمحاولات تسجيل الدخول
     *
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'identifier' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * الحصول على مفتاح الحد من المحاولات للمستخدم
     */
    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->input('identifier')).'|'.$this->ip());
    }
}
