<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('car_models', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('brand_id')->comment('معرّف الماركة');
            $table->string('name', 100)->comment('اسم الموديل');
            $table->boolean('status')->default(true)->comment('حالة الموديل (نشط/غير نشط)');
            $table->timestamps();
            $table->softDeletes();

            // إنشاء الفهارس
            $table->index('brand_id');
            $table->index('name');
            $table->index('status');

            // إنشاء المفتاح الخارجي
            $table->foreign('brand_id')
                ->references('id')
                ->on('brands')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            // إنشاء فهرس مركب للفرادة
            $table->unique(['brand_id', 'name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('car_models');
    }
};
