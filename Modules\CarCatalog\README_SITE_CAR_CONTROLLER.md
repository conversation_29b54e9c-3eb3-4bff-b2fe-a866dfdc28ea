# Site Car Controller Implementation - CarCatalog Module

## Overview
تم تنفيذ Controller لعرض قائمة السيارات في الموقع العام مع دعم كامل للفلترة والترتيب والترقيم.

## Features Implemented

### 1. SiteCarController
- **Location**: `Modules/CarCatalog/Http/Controllers/Site/SiteCarController.php`
- **Method**: `index(Request $request): View`
- **Purpose**: عرض قائمة السيارات المتاحة مع الفلترة والترتيب

### 2. Filtering Capabilities
- **البحث النصي**: في العنوان، الوصف، اسم الماركة، واسم الموديل
- **فلتر الماركات**: اختيار متعدد للماركات (brand_ids[])
- **فلتر الموديلات**: اختيار متعدد للموديلات (model_ids[])
- **فلتر سنوات الصنع**: اختيار متعدد أو نطاق (year_ids[] أو min_year/max_year)
- **فلتر نطاق السعر**: من وإلى (min_price/max_price)
- **فلتر الألوان**: اختيار متعدد للألوان (color_ids[])
- **فلتر أنواع ناقل الحركة**: اختيار متعدد (transmission_type_ids[])
- **فلتر أنواع الوقود**: اختيار متعدد (fuel_type_ids[])
- **فلتر أنواع الهيكل**: اختيار متعدد (body_type_ids[])

### 3. Sorting Options
- **latest**: الأحدث أولاً (افتراضي)
- **price_asc**: السعر من الأقل للأعلى
- **price_desc**: السعر من الأعلى للأقل
- **oldest**: الأقدم أولاً
- **featured**: المميزة أولاً ثم الأحدث

### 4. Pagination
- دعم الترقيم مع خيارات: 12, 15, 24, 48 عنصر لكل صفحة
- الافتراضي: 15 عنصر لكل صفحة
- الحفاظ على query parameters في روابط الترقيم

### 5. Filter Options Data
- جلب الماركات النشطة مع عدد السيارات المتاحة
- جلب الألوان مع عدد السيارات المتاحة
- جلب سنوات الصنع مع عدد السيارات المتاحة
- جلب أنواع ناقل الحركة مع عدد السيارات المتاحة
- جلب أنواع الوقود مع عدد السيارات المتاحة
- جلب أنواع الهيكل مع عدد السيارات المتاحة
- حساب نطاق الأسعار (أقل وأعلى سعر)

### 6. Car Display Conditions
- عرض السيارات المتاحة فقط: `is_sold = false` و `is_active = true`
- تحميل العلاقات الضرورية مع eager loading لتجنب N+1 queries

### 7. Updated Routes
- **Route**: `GET /cars` → `SiteCarController@index`
- **Name**: `site.cars.index`
- **AJAX Route**: `GET /brands/{brand}/models` → `SiteCarController@getModelsByBrand`
- **AJAX Name**: `site.brands.models.get`
- **Updated in**: `routes/web.php`

### 8. Enhanced View
- **Location**: `resources/views/site/cars/index.blade.php`
- **Features**:
  - عرض إحصائيات سريعة (عدد السيارات المتاحة، عدد الفلاتر المطبقة)
  - نموذج فلترة تفاعلي
  - عرض الفلاتر المطبقة حالياً
  - بطاقات السيارات مع الصور والمعلومات الأساسية
  - دعم الصور من spatie/laravel-medialibrary
  - عرض السيارات المميزة
  - ترقيم النتائج
  - رسائل عدم وجود نتائج

## Technical Implementation Details

### Eager Loading
```php
$query = Car::with([
    'media',
    'brand',
    'carModel',
    'manufacturingYear',
    'mainColor',
    'transmissionType',
    'fuelType',
    'bodyType'
]);
```

### Filter Logic
- استخدام `whereIn()` للفلاتر متعددة الاختيار
- استخدام `where()` مع المقارنات للنطاقات
- استخدام `whereHas()` للبحث في العلاقات
- استخدام `LIKE` للبحث النصي

### Performance Optimizations
- Eager loading لتجنب N+1 queries
- استخدام `withCount()` لحساب عدد السيارات لكل فلتر
- استخدام `having()` لإخفاء الخيارات التي لا تحتوي على سيارات

### Security Considerations
- تطهير جميع المدخلات
- استخدام `filled()` للتحقق من وجود القيم
- التحقق من نوع البيانات للمصفوفات
- قيم محددة مسبقاً لخيارات الترقيم

## Usage
1. انتقل إلى `/cars` لعرض قائمة السيارات
2. استخدم نموذج الفلترة لتطبيق المعايير المطلوبة
3. اختر ترتيب النتائج حسب الحاجة
4. تصفح النتائج باستخدام الترقيم

## Dependencies
- **spatie/laravel-medialibrary**: لعرض صور السيارات
- جميع نماذج البيانات الوصفية (Brand, CarModel, Color, etc.)
- Bootstrap 5: للواجهة
- Font Awesome: للأيقونات

## Additional Routes Added
تم إضافة routes مؤقتة لجميع الصفحات المفقودة في header و footer:

**صفحات الموقع العام:**
- `site.contact` - اتصل بنا
- `site.branches` - فروعنا
- `site.services.index` - خدماتنا
- `site.corporate.index` - مبيعات الشركات
- `site.request-car.step1` - اطلب سيارتك
- `site.about` - من نحن
- `site.favorites` - المفضلة
- `site.careers` - الوظائف
- `site.faq` - الأسئلة الشائعة
- `site.privacy` - سياسة الخصوصية
- `site.terms` - الشروط والأحكام
- `site.support` - الدعم الفني
- `site.promotions.index` - عروض السيارات

**routes المصادقة:**
- `login` - تسجيل الدخول (redirect إلى UserManagement module)
- `register` - إنشاء حساب (redirect إلى UserManagement module)
- `logout` - تسجيل الخروج

**routes العملاء:**
- `customer.dashboard` - لوحة تحكم العميل
- `customer.profile` - الملف الشخصي
- `customer.orders` - طلباتي

جميع هذه الصفحات تستخدم `resources/views/site/placeholder.blade.php` كـ view مؤقت.

## Notes
- يتم عرض السيارات المتاحة فقط (غير مباعة ونشطة)
- دعم كامل للترجمة في النماذج
- تحسين الأداء مع eager loading
- واجهة مستخدم متجاوبة
- دعم الصور مع fallback للسيارات بدون صور
- تم حل جميع أخطاء routes المفقودة في header و footer
