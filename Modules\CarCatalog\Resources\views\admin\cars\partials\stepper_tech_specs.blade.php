<div id="tech-specs-part" class="content" role="tabpanel" aria-labelledby="tech-specs-part-trigger">
    <h6 class="mb-4 text-primary">
        <i class="fas fa-cogs me-2"></i>
        المواصفات الفنية للسيارة
    </h6>

    <div class="row g-3">
        <!-- نوع الهيكل -->
        <div class="col-md-4">
            <label for="body_type_id" class="form-label">نوع الهيكل <span class="text-danger">*</span></label>
            <select class="form-select @error('body_type_id') is-invalid @enderror"
                    id="body_type_id" name="body_type_id" required>
                <option value="">اختر نوع الهيكل</option>
                @foreach($bodyTypes as $bodyType)
                    <option value="{{ $bodyType->id }}"
                            {{ old('body_type_id', isset($car) ? $car->body_type_id : '') == $bodyType->id ? 'selected' : '' }}>
                        {{ $bodyType->name }}
                    </option>
                @endforeach
            </select>
            @error('body_type_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- نوع ناقل الحركة -->
        <div class="col-md-4">
            <label for="transmission_type_id" class="form-label">نوع ناقل الحركة <span class="text-danger">*</span></label>
            <select class="form-select @error('transmission_type_id') is-invalid @enderror"
                    id="transmission_type_id" name="transmission_type_id" required>
                <option value="">اختر نوع ناقل الحركة</option>
                @foreach($transmissionTypes as $transmissionType)
                    <option value="{{ $transmissionType->id }}"
                            {{ old('transmission_type_id', isset($car) ? $car->transmission_type_id : '') == $transmissionType->id ? 'selected' : '' }}>
                        {{ $transmissionType->name }}
                    </option>
                @endforeach
            </select>
            @error('transmission_type_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- نوع الوقود -->
        <div class="col-md-4">
            <label for="fuel_type_id" class="form-label">نوع الوقود <span class="text-danger">*</span></label>
            <select class="form-select @error('fuel_type_id') is-invalid @enderror"
                    id="fuel_type_id" name="fuel_type_id" required>
                <option value="">اختر نوع الوقود</option>
                @foreach($fuelTypes as $fuelType)
                    <option value="{{ $fuelType->id }}"
                            {{ old('fuel_type_id', isset($car) ? $car->fuel_type_id : '') == $fuelType->id ? 'selected' : '' }}>
                        {{ $fuelType->name }}
                    </option>
                @endforeach
            </select>
            @error('fuel_type_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- سعة المحرك -->
        <div class="col-md-4">
            <label for="engine_capacity" class="form-label">سعة المحرك (سي سي)</label>
            <input type="number" class="form-control @error('engine_capacity') is-invalid @enderror"
                   id="engine_capacity" name="engine_capacity"
                   value="{{ old('engine_capacity', isset($car) ? $car->engine_capacity : '') }}"
                   placeholder="مثال: 2500"
                   min="100" max="8000" step="1">
            <div class="form-text">سعة المحرك بالسي سي (CC)</div>
            @error('engine_capacity')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- عدد الأبواب -->
        <div class="col-md-4">
            <label for="doors_count" class="form-label">عدد الأبواب</label>
            <select class="form-select @error('doors_count') is-invalid @enderror"
                    id="doors_count" name="doors_count">
                <option value="">اختر عدد الأبواب</option>
                <option value="2" {{ old('doors_count', isset($car) ? $car->doors_count : '') == '2' ? 'selected' : '' }}>2 أبواب</option>
                <option value="3" {{ old('doors_count', isset($car) ? $car->doors_count : '') == '3' ? 'selected' : '' }}>3 أبواب</option>
                <option value="4" {{ old('doors_count', isset($car) ? $car->doors_count : '') == '4' ? 'selected' : '' }}>4 أبواب</option>
                <option value="5" {{ old('doors_count', isset($car) ? $car->doors_count : '') == '5' ? 'selected' : '' }}>5 أبواب</option>
                <option value="6" {{ old('doors_count', isset($car) ? $car->doors_count : '') == '6' ? 'selected' : '' }}>6 أبواب</option>
            </select>
            @error('doors_count')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- عدد المقاعد -->
        <div class="col-md-4">
            <label for="seats_count" class="form-label">عدد المقاعد</label>
            <select class="form-select @error('seats_count') is-invalid @enderror"
                    id="seats_count" name="seats_count">
                <option value="">اختر عدد المقاعد</option>
                <option value="2" {{ old('seats_count', isset($car) ? $car->seats_count : '') == '2' ? 'selected' : '' }}>2 مقاعد</option>
                <option value="4" {{ old('seats_count', isset($car) ? $car->seats_count : '') == '4' ? 'selected' : '' }}>4 مقاعد</option>
                <option value="5" {{ old('seats_count', isset($car) ? $car->seats_count : '') == '5' ? 'selected' : '' }}>5 مقاعد</option>
                <option value="7" {{ old('seats_count', isset($car) ? $car->seats_count : '') == '7' ? 'selected' : '' }}>7 مقاعد</option>
                <option value="8" {{ old('seats_count', isset($car) ? $car->seats_count : '') == '8' ? 'selected' : '' }}>8 مقاعد</option>
                <option value="9" {{ old('seats_count', isset($car) ? $car->seats_count : '') == '9' ? 'selected' : '' }}>9 مقاعد</option>
                <option value="12" {{ old('seats_count', isset($car) ? $car->seats_count : '') == '12' ? 'selected' : '' }}>12 مقعد</option>
            </select>
            @error('seats_count')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- المسافة المقطوعة -->
        <div class="col-md-6">
            <label for="mileage" class="form-label">المسافة المقطوعة</label>
            <div class="input-group">
                <input type="number" class="form-control @error('mileage') is-invalid @enderror"
                       id="mileage" name="mileage"
                       value="{{ old('mileage', isset($car) ? $car->mileage : '') }}"
                       placeholder="مثال: 50000"
                       min="0">
                <select class="form-select @error('mileage_unit') is-invalid @enderror"
                        id="mileage_unit" name="mileage_unit" style="max-width: 120px;">
                    <option value="km" {{ old('mileage_unit', isset($car) ? $car->mileage_unit : 'km') == 'km' ? 'selected' : '' }}>كيلومتر</option>
                    <option value="miles" {{ old('mileage_unit', isset($car) ? $car->mileage_unit : '') == 'miles' ? 'selected' : '' }}>ميل</option>
                </select>
            </div>
            <div class="form-text">المسافة التي قطعتها السيارة</div>
            @error('mileage')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            @error('mileage_unit')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- حالة السيارة -->
        <div class="col-md-6">
            <label for="condition" class="form-label">حالة السيارة <span class="text-danger">*</span></label>
            <select class="form-select @error('condition') is-invalid @enderror"
                    id="condition" name="condition" required>
                <option value="">اختر حالة السيارة</option>
                <option value="new" {{ old('condition', isset($car) ? $car->condition : '') == 'new' ? 'selected' : '' }}>جديدة</option>
                <option value="used" {{ old('condition', isset($car) ? $car->condition : '') == 'used' ? 'selected' : '' }}>مستعملة</option>
                <option value="certified_pre_owned" {{ old('condition', isset($car) ? $car->condition : '') == 'certified_pre_owned' ? 'selected' : '' }}>مستعملة معتمدة</option>
            </select>
            <div class="form-text">حدد حالة السيارة الحالية</div>
            @error('condition')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <div class="mt-4 d-flex justify-content-between">
        <button type="button" class="btn btn-secondary" onclick="stepper.previous()">
            <i class="fas fa-arrow-right me-1"></i>
            السابق: البيانات الأساسية
        </button>
        <button type="button" class="btn btn-primary" onclick="stepper.next()">
            التالي: الميزات
            <i class="fas fa-arrow-left ms-1"></i>
        </button>
    </div>
</div>
