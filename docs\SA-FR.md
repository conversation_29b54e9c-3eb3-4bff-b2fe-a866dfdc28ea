**START OF `SA-FR.md`**
---

## SA-FR.md - ملخص الهيكل المعماري للنظام المتكامل (نسخة نهائية معتمدة ذاتيًا)

**تاريخ الإنشاء:** (تاريخ اليوم)
**إصدار المستند:** 1.0 (نهائي معتمد ذاتيًا)

### مقدمة

**معرف القسم:** `SA-INTRO-001`

الغرض من هذا المستند هو تقديم ملخص للهيكل المعماري المقترح لمنصة معرض السيارات الإلكترونية المتكاملة. يستند هذا الهيكل إلى التحليل الدقيق للمتطلبات والمواصفات المحددة في وثائق المرحلة 01 المعتمدة، ويهدف إلى توجيه عملية التطوير لضمان بناء نظام قوي، قابل للصيانة، وقابل للتوسع. يتم التركيز بشكل خاص على التكامل الفعال للوحة تحكم Dash المخصصة ضمن بيئة Laravel 10، ودعم جميع المخرجات النهائية المتوقعة للمشروع.

**المخرجات النهائية المتوقعة للمشروع بالكامل (المرجع الأساسي والنهائي):**
1.  **موقع إلكتروني فعال:** مرفوع على خادم عبر الإنترنت، جاهز لاستقبال الزوار بدون مشاكل.
2.  **لوحة تحكم احترافية (مخصصة من الصفر باستخدام أصول Dash HTML/CSS/JS):** (للإدارة و/أو العملاء) جاهزة للاستخدام، توفر تجربة مستخدم ممتازة، وتدعم اللغة العربية (RTL) بشكل كامل.
3.  **واجهة موقع (Frontend) جذابة وفعالة:** (باستخدام Blade إذا كانت مختلفة عن لوحة التحكم) جاهزة لاستقبال الزوار وتوفير تجربة مستخدم سلسة.
4.  **تطبيق موبايل (Flutter) كامل (إذا كان مطلوبًا):** جاهز للرفع على متاجر التطبيقات ويعمل بشكل صحيح ومتكامل.

### القرارات المعمارية الرئيسية المقترحة (Key Architectural Decisions)

**(معرف القسم: `SA-ADR-001`)**

*   **معرف القرار:** `ADR-001`
    *   **القرار:** **اعتماد بنية خلفية أحادية (Monolithic Backend) مع Laravel 10 منظمة كوحدات نمطية (Modular Monolith).**
    *   **الوصف:** سيتم بناء النظام الخلفي باستخدام الإصدار 10 من إطار عمل Laravel. سيتم تنظيم منطق الأعمال والميزات في وحدات نمطية منفصلة باستخدام حزمة `nwidart/laravel-modules` كما هو مفصل في `STRU-FR.md`. هذا النهج يجمع بين بساطة تطوير وصيانة النظام الأحادي مع فوائد التنظيم والفصل التي توفرها الوحدات النمطية، مما يخدم بناء وصيانة جميع **[المخرجات النهائية الأربعة]**.
    *   **التأثير على المخرجات النهائية:** يضمن أساسًا خلفيًا قويًا وموحدًا لـ [موقع إلكتروني فعال]، [لوحة تحكم احترافية Dash]، [واجهة موقع Blade]، و [API تطبيق Flutter].

*   **معرف القرار:** `ADR-002`
    *   **القرار:** **بناء لوحة التحكم المخصصة (Dash) كواجهات Laravel Blade ديناميكية باستخدام أصول HTML/CSS/JS الثابتة المتوفرة.**
    *   **الوصف:** سيتم تكييف ملفات `Dash/index.html`, `Dash/style.css`, و `Dash/script.js` (الموجودة في مجلد `Dash/` في جذر المشروع كما هو محدد في `STRU-DASH-ASSETS-ROOT-001`) لإنشاء واجهات Blade ديناميكية ضمن Laravel (موجودة في `resources/views/admin/` كما هو محدد في `STRU-LARAVEL-VIEWS-ADMIN-001`). سيتم استخدام Laravel Vite (أو Mix) لتجميع ومعالجة أصول CSS و JavaScript الخاصة بـ Dash. سيتم إنشاء ملفات مخصصة مثل `dash_custom.css` (لتجاوزات الأنماط عند الضرورة، ويُحمّل بعد `Dash/style.css`) و `dash_app.js` (لمعالجة البيانات الديناميكية للرسوم البيانية، وتكييف تفاعلات JavaScript الأصلية من `Dash/script.js`، ويُحمّل بعد `Dash/script.js` أو يستورده إذا أمكن). ستتفاعل وحدات التحكم (Controllers) في Laravel مع هذه الواجهات لتمرير البيانات وعرضها بشكل ديناميكي، مع إعطاء الأولوية لدعم اللغة العربية (RTL) بشكل كامل منذ البداية كجزء من عملية التكييف. الهدف هو *توسيع* و *تكييف* وظائف Dash الأصلية بدلاً من إعادة كتابتها بالكامل ما لم يكن ذلك ضروريًا وحتميًا.
    *   **التأثير على المخرجات النهائية:** يخدم بشكل مباشر بناء **[لوحة تحكم احترافية]** مع الحفاظ على الشكل والمظهر الأساسي لأصول Dash ولكن مع مرونة وقوة Laravel ودعم RTL.

*   **معرف القرار:** `ADR-003`
    *   **القرار:** **استخدام Laravel Blade لواجهة الموقع العامة (Public Frontend).**
    *   **الوصف:** سيتم تطوير واجهة الموقع العامة التي يتفاعل معها الزوار والعملاء باستخدام نظام قوالب Laravel Blade. هذا يضمن تكاملاً سلسًا مع الـ Backend المبني بـ Laravel.
    *   **التأثير على المخرجات النهائية:** يخدم بناء **[موقع إلكتروني فعال]** و **[واجهة موقع جذابة وفعالة]**.

*   **معرف القرار:** `ADR-004`
    *   **القرار:** **تطوير تطبيق موبايل للعملاء باستخدام Flutter (إذا كان ضمن النطاق النهائي).**
    *   **الوصف:** سيتم بناء تطبيق الموبايل للعملاء باستخدام إطار عمل Flutter، مما يسمح بتطوير تطبيق يعمل على منصتي iOS و Android من قاعدة كود واحدة. سيتفاعل التطبيق مع الـ Backend عبر واجهة برمجة تطبيقات RESTful API.
    *   **التأثير على المخرجات النهائية:** يخدم بناء **[تطبيق موبايل Flutter كامل]**.

*   **معرف القرار:** `ADR-005`
    *   **القرار:** **توفير واجهة برمجة تطبيقات (RESTful API) مخصصة لتطبيق Flutter والعملاء الخارجيين الآخرين المحتملين.**
    *   **الوصف:** سيتم بناء مجموعة من نقاط نهاية API (محددة في `TS-FR.md`، قسم 2) باستخدام Laravel، مع موديول `Api` (`MOD-API`) مخصص لتجميع هذه النقاط أو توفير نقاط نهاية عامة. سيتم استخدام Laravel Sanctum للمصادقة المستندة إلى الـ Token.
    *   **التأثير على المخرجات النهائية:** ضروري لعمل **[تطبيق موبايل Flutter كامل]** بشكل صحيح ومتكامل مع النظام الأساسي.

*   **معرف القرار:** `ADR-006`
    *   **القرار:** **إدارة الحالة وقاعدة البيانات (Database State Management and Migrations).**
    *   **الوصف:** سيتم استخدام Laravel Migrations لإدارة تطور مخطط قاعدة البيانات بشكل تدريجي ومتسق عبر جميع بيئات النشر. سيتم استخدام Seeders لملء البيانات الأساسية والوهمية. مخطط قاعدة البيانات المفصل في `TS-FR.md` هو المرجع.
    *   **التأثير على المخرجات النهائية:** يضمن سلامة البيانات واتساقها، وهو أمر حيوي لجميع **[المخرجات النهائية]**.

*   **معرف القرار:** `ADR-007`
    *   **القرار:** **استخدام بنية CSS و JavaScript قابلة للصيانة وقابلة للتوسع للوحة تحكم Dash المخصصة وواجهة الموقع.**
    *   **الوصف:** لواجهة لوحة تحكم Dash، سيتم تكييف `Dash/style.css` و `Dash/script.js` مع إنشاء ملفات مخصصة (`dash_custom.css` و `dash_app.js` كما هو مقترح في `UIUX-DASH-CUSTOMIZATION-001` و `STRU-FR.md`) لتجاوز الأنماط عند الحاجة، وتطبيق الهوية البصرية المطلوبة، وإضافة التفاعلات الديناميكية المرتبطة بـ Laravel. سيتم استخدام Laravel Vite (أو Mix) لبناء وتجميع هذه الأصول. لواجهة الموقع العامة، سيتم استخدام نهج مشابه مع CSS و JS مخصصين.
    *   **التأثير على المخرجات النهائية:** يضمن أن **[لوحة التحكم الاحترافية]** و **[واجهة الموقع الجذابة]** تكونان قابلتين للتطوير والتخصيص البصري والوظيفي على المدى الطويل.

*   **معرف القرار:** `ADR-008`
    *   **القرار:** **اعتماد استراتيجيات أمان شاملة على مستوى المعمارية.**
    *   **الوصف:** سيتم تطبيق مبادئ الأمان المحددة في `TS-FR.md` (القسم 5) و `REQ-FR.md` (المتطلبات غير الوظيفية للأمان) في جميع طبقات النظام، بما في ذلك تأمين الواجهات (Blade/Dash، وتفاعلات JavaScript المرتبطة بها، و API)، حماية البيانات، وإدارة الصلاحيات.
    *   **التأثير على المخرجات النهائية:** يضمن موثوقية وأمان جميع **[المخرجات النهائية]**.

### اعتبارات أداء وقابلية التوسع للوحة تحكم Dash المخصصة

**(معرف القسم: `SA-DASH-PERF-SCAL-001`)**

نظراً لطبيعة بناء لوحة التحكم من أصول ثابتة، يجب مراعاة النقاط التالية لضمان الأداء وقابلية التوسع:
*   **معالجة الأصول (`Asset Processing`):** التأكيد على أهمية معالجة وتصغير (`minification`) أصول `Dash/style.css` و `Dash/script.js` (بالإضافة إلى `dash_custom.css` و `dash_app.js`) باستخدام Laravel Vite/Mix لتقليل حجم الملفات وتسريع التحميل.
*   **التحميل الكسول (`Lazy Loading`):** النظر في إمكانية التحميل الكسول لمكونات JavaScript أو CSS غير الضرورية في التحميل الأولي للصفحة في لوحة التحكم، خاصة إذا كانت بعض الأقسام أو المكتبات (مثل مكتبات الرسوم البيانية المعقدة) لا تُستخدم إلا في صفحات معينة.
*   **استعلامات قاعدة البيانات (`Database Queries`):** تجنب استدعاء استعلامات قاعدة بيانات كثيفة أو معقدة مباشرة داخل Blade views الخاصة بـ Dash. يجب أن تتم معالجة البيانات وتجهيزها مسبقًا في Controllers أو Services قبل تمريرها إلى الواجهة.
*   **التخزين المؤقت (`Caching`):** تطبيق استراتيجيات تخزين مؤقت مناسبة (مثل Laravel Cache) للبيانات التي تُعرض بشكل متكرر في لوحة التحكم Dash والتي لا تتغير بوتيرة سريعة (مثل بعض الإحصائيات أو قوائم الخيارات).
*   **الطوابير (`Queues`):** استخدام نظام الطوابير في Laravel (Laravel Queues) لمعالجة المهام الطويلة أو التي لا تتطلب استجابة فورية (مثل توليد تقارير معقدة أو إرسال إشعارات جماعية) لتجنب إبطاء استجابة واجهة المستخدم في لوحة التحكم.

### مخطط تدفق البيانات العام (وصف نصي)

**(معرف القسم: `SA-DATAFLOW-001`)**

1.  **تفاعل المستخدم مع الواجهات الأمامية:**
    *   **لوحة تحكم Dash (إدارة/عميل):** يتفاعل المستخدم (مدير/عميل) مع واجهات Blade الديناميكية (المبنية من أصول Dash). يتم إرسال الطلبات (HTTP GET/POST) إلى وحدات التحكم (Controllers) في Laravel. قد تتضمن تفاعلات JavaScript داخل لوحة تحكم Dash (مثل تحديث الرسوم البيانية أو بعض الجداول) استدعاءات AJAX إلى نقاط نهاية API داخلية مخصصة ومحمية، والتي بدورها تتفاعل مع وحدات التحكم والخدمات.
    *   **واجهة الموقع العامة (Blade):** يتفاعل المستخدم (زائر/عميل) مع صفحات Blade. يتم إرسال الطلبات إلى وحدات التحكم في Laravel.
    *   **تطبيق Flutter:** يتفاعل المستخدم (عميل) مع شاشات Flutter. يتم إرسال طلبات HTTP (GET, POST, PUT, DELETE) إلى نقاط نهاية API المحددة في `MOD-API`.

2.  **معالجة الطلبات في Laravel Backend:**
    *   **التوجيه (Routing):** يستقبل Laravel الطلب ويوجهه إلى وحدة التحكم المناسبة بناءً على ملفات المسارات (`web.php`, `admin.php` للـ Dash, `api.php`).
    *   **الوسائط (Middleware):** يتم تطبيق الوسائط (مثل المصادقة، التفويض، CSRF) على الطلبات الواردة.
    *   **وحدات التحكم (Controllers):** تعالج وحدات التحكم الطلب. قد تستدعي Form Requests للتحقق من صحة المدخلات.
    *   **منطق الأعمال (Business Logic):** قد تستدعي وحدات التحكم Services أو Repositories (الموجودة داخل الموديولات) لتنفيذ منطق الأعمال.
    *   **التفاعل مع قاعدة البيانات:** تتفاعل النماذج (Models) أو الـ Repositories مع قاعدة البيانات (MySQL) باستخدام Eloquent ORM لجلب البيانات أو تخزينها.
    *   **الإشعارات والمهام في الخلفية:** قد يتم إرسال إشعارات (عبر `MOD-NOTIFICATION`) أو جدولة مهام (Queued Jobs) للمعالجة في الخلفية (مثل إرسال البريد).

3.  **الاستجابة للمستخدم:**
    *   **لوحة تحكم Dash وواجهة الموقع العامة:** تقوم وحدات التحكم بإعداد البيانات وتمريرها إلى Blade views. يقوم Blade بتصيير HTML الديناميكي وإرساله كاستجابة للمتصفح.
    *   **تطبيق Flutter:** تقوم وحدات التحكم في `MOD-API` بإرجاع استجابات JSON (مُنسقة باستخدام API Resources) إلى تطبيق Flutter.

4.  **التفاعل مع الخدمات الخارجية:**
    *   **بوابات الدفع:** يتفاعل النظام مع بوابات الدفع (من `MOD-ORDER-MGMT`) لإتمام عمليات الدفع عبر الإنترنت.
    *   **بوابات SMS:** يتفاعل النظام مع بوابات SMS (من `MOD-NOTIFICATION`) لإرسال رسائل نصية.

### مبررات القرارات المعمارية

**(معرف القسم: `SA-JUSTIFICATIONS-001`)**

*   **اختيار Laravel 10 و `nwidart/laravel-modules` (`ADR-001`):** يوفر Laravel بيئة تطوير قوية ومتكاملة مع نظام بيئي واسع. استخدام `nwidart/laravel-modules` يعزز تنظيم الكود ويجعله أكثر قابلية للصيانة والتوسع، وهو أمر حيوي لمشروع بهذا الحجم والتعقيد، ويدعم بناء جميع **[المخرجات النهائية]** بكفاءة.
*   **بناء لوحة تحكم Dash كواجهات Blade ديناميكية (`ADR-002`):** هذا القرار ينبع مباشرة من متطلبات المشروع الأساسية. دمج أصول Dash الثابتة ضمن Blade يسمح بالاستفادة من قوة Laravel في التوجيه، المصادقة، إدارة البيانات، وتوليد المحتوى الديناميكي، مع الحفاظ على المظهر والوظائف الأساسية التي توفرها أصول Dash. هذا النهج يخدم بشكل مباشر بناء **[لوحة تحكم احترافية]** قابلة للتخصيص والتوسع.
*   **استخدام Blade لواجهة الموقع العامة (`ADR-003`):** يوفر Blade تكاملاً سلسًا مع الـ Backend ويسمح بإعادة استخدام المكونات، مما يسرع عملية التطوير ويحافظ على الاتساق، ويدعم بناء **[موقع إلكتروني فعال]** و **[واجهة موقع جذابة]**.
*   **تطوير تطبيق Flutter (`ADR-004`):** يسمح Flutter ببناء **[تطبيق موبايل كامل]** لكلا المنصتين (iOS و Android) من قاعدة كود واحدة، مما يوفر الوقت والتكلفة مع تقديم تجربة مستخدم أصلية.
*   **API مخصص لتطبيق Flutter (`ADR-005`):** ضروري لفصل اهتمامات الواجهة الخلفية عن تطبيق الموبايل، مما يسمح بتطوير واختبار كل جزء بشكل مستقل ويضمن أمان البيانات وتدفقها بشكل منظم إلى **[تطبيق الموبايل]**.
*   **إدارة أصول Dash بشكل مركزي ولكن معالج (`ADR-007`):** الاحتفاظ بأصول `Dash/` في الجذر مع معالجتها وتجميعها بواسطة Vite/Mix يوفر وضوحًا لمصدر الأصول الثابتة مع الاستفادة من أدوات Laravel لتحسين الأداء وإدارة الإصدارات، وهو مهم لـ **[لوحة التحكم الاحترافية]**.

---
**END OF `SA-FR.md`**
---
