<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تحديث ماركة موجودة.
 *
 * يتحقق هذا الطلب من صحة بيانات تعديل ماركة موجودة
 */
class UpdateBrandRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:100',
                Rule::unique('brands')->ignore($this->brand->id),
            ],
            'logo'        => 'nullable|image|mimes:jpg,jpeg,png,svg,webp|max:1024',
            'description' => 'nullable|string|max:500',
            'status'      => 'required|boolean',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة للقواعد المحددة.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required'      => 'اسم الماركة مطلوب',
            'name.string'        => 'اسم الماركة يجب أن يكون نصًا',
            'name.max'           => 'اسم الماركة يجب ألا يتجاوز 100 حرف',
            'name.unique'        => 'اسم الماركة موجود بالفعل',
            'logo.image'         => 'الشعار يجب أن يكون صورة',
            'logo.mimes'         => 'الشعار يجب أن يكون بأحد الصيغ التالية: jpg, jpeg, png, svg, webp',
            'logo.max'           => 'حجم الشعار يجب ألا يتجاوز 1 ميجابايت',
            'description.string' => 'الوصف يجب أن يكون نصًا',
            'description.max'    => 'الوصف يجب ألا يتجاوز 500 حرف',
            'status.required'    => 'حالة الماركة مطلوبة',
            'status.boolean'     => 'حالة الماركة يجب أن تكون صحيحة أو خاطئة',
        ];
    }
}
