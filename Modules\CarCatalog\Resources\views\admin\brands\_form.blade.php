{{-- حقل اسم الماركة --}}
<div class="mb-3">
    <label for="name" class="form-label">اسم الماركة <span class="text-danger">*</span></label>
    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $brand->name ?? '') }}" required>
    @error('name')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>

{{-- حقل وصف الماركة --}}
<div class="mb-3">
    <label for="description" class="form-label">الوصف</label>
    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description', $brand->description ?? '') }}</textarea>
    @error('description')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>

{{-- حقل حالة الماركة --}}
<div class="mb-3">
    <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
        <option value="1" {{ (old('status', isset($brand) ? $brand->status : 1) == 1) ? 'selected' : '' }}>نشط</option>
        <option value="0" {{ (old('status', isset($brand) ? $brand->status : '') == '0') ? 'selected' : '' }}>غير نشط</option>
    </select>
    @error('status')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>

{{-- حقل شعار الماركة --}}
<div class="mb-3">
    <label for="logo" class="form-label">شعار الماركة</label>
    <input type="file" class="form-control @error('logo') is-invalid @enderror" id="logo" name="logo" accept="image/*">
    @error('logo')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    
    {{-- عرض الشعار الحالي إذا كان موجودًا --}}
    @if(isset($brand) && $brand->hasMedia('brand_logos'))
        <div class="mt-2">
            <img src="{{ $brand->getFirstMediaUrl('brand_logos', 'thumb') }}" alt="{{ $brand->name }}" width="100" class="img-thumbnail rounded">
            <small>الشعار الحالي</small>
        </div>
    @endif
</div>
