<?php

namespace Modules\Notification\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Modules\UserManagement\Models\User;

/**
 * بريد الترحيب بالمستخدمين الجدد
 *
 * يتم إرسال هذا البريد عند تسجيل مستخدم جديد في النظام
 */
class WelcomeEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * المستخدم الذي سيتم إرسال البريد له
     *
     * @var \Modules\UserManagement\Models\User
     */
    public $user;

    /**
     * إنشاء نسخة جديدة من الرسالة
     *
     * @param \Modules\UserManagement\Models\User $user
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * بناء الرسالة
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('مرحباً بك في ' . config('app.name') . '!')
                    ->markdown('notification::emails.welcome');
    }
}
