<?php

namespace Modules\CarCatalog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * TransmissionType Model.
 *
 * يمثل هذا النموذج جدول أنواع ناقل الحركة في النظام
 *
 * @property int $id
 * @property string $name اسم نوع ناقل الحركة
 * @property string|null $description وصف نوع ناقل الحركة
 * @property bool $status حالة نوع ناقل الحركة (نشط/غير نشط)
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\CarCatalog\Models\Car[] $cars علاقة السيارات
 */
class TransmissionType extends Model
{
    use HasFactory;

    /**
     * تعطيل الطوابع الزمنية (created_at, updated_at)
     * حيث أن بيانات أنواع ناقل الحركة عادة ما تكون ثابتة.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * الخصائص المحمية من التعديل الجماعي.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'status',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * علاقة السيارات المرتبطة بهذا النوع من ناقل الحركة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cars()
    {
        return $this->hasMany(Car::class);
    }

    /**
     * مصنع النموذج.
     *
     * @return \Modules\CarCatalog\Database\factories\TransmissionTypeFactory
     */
    protected static function newFactory()
    {
        return \Modules\CarCatalog\Database\factories\TransmissionTypeFactory::new();
    }
}
