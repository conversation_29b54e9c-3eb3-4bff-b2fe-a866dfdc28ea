@extends('dashboard::layouts.admin_layout')

@section('title', 'إضافة نوع وقود جديد')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إضافة نوع وقود جديد',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'أنواع الوقود', 'url' => route('admin.fuel-types.index')],
            ['name' => 'إضافة جديد', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.fuel-types.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>'
    ])

    {{-- نموذج إضافة نوع وقود جديد --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">بيانات نوع الوقود</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.fuel-types.store') }}" method="POST">
                @csrf

                @include('carcatalog::admin.fuel-types._form')

                <div class="mt-4 d-flex justify-content-end">
                    <button type="submit" class="btn btn-success me-2">حفظ نوع الوقود</button>
                    <a href="{{ route('admin.fuel-types.index') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
