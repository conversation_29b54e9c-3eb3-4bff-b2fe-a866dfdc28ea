@extends('dashboard::layouts.admin_layout')

@section('title', 'إدارة الألوان')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h4 class="fw-bold mb-0" style="color: var(--primary-color);">إدارة الألوان</h4>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb brand-breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="brand-link">لوحة التحكم</a>
                    </li>
                    <li class="breadcrumb-item">إدارة السيارات</li>
                    <li class="breadcrumb-item active" aria-current="page">الألوان</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.colors.create') }}" class="btn btn-brand-primary">
                <i class="fas fa-plus me-1"></i> إضافة لون جديد
            </a>
        </div>
    </div>

    {{-- عرض رسائل النجاح والخطأ --}}
    @if(session('success'))
        <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {{-- نموذج البحث والفلترة --}}
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.colors.index') }}" class="mb-0">
                <div class="row gx-2 gy-2 align-items-center">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="بحث بالاسم..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">كل الحالات</option>
                            <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>نشط</option>
                            <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-brand-info w-100">بحث</button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ route('admin.colors.index') }}" class="btn btn-brand-secondary w-100">إعادة تعيين</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- جدول الألوان --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">قائمة الألوان</h5>
        </div>
        <div class="card-body table-responsive p-0">
            <table class="table table-hover recent-activity-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم اللون</th>
                        <th>كود Hex</th>
                        <th>عينة اللون</th>
                        <th>الحالة</th>
                        <th>عدد السيارات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($colors as $color)
                        <tr>
                            <td>{{ $color->id }}</td>
                            <td>{{ $color->name }}</td>
                            <td>
                                @if($color->hex_code)
                                    <code>{{ $color->hex_code }}</code>
                                @else
                                    <span class="text-muted">غير محدد</span>
                                @endif
                            </td>
                            <td>
                                @if($color->hex_code)
                                    <div style="width: 30px; height: 30px; background-color: {{ $color->hex_code }}; border: 1px solid #ddd; border-radius: 4px;" title="{{ $color->hex_code }}"></div>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge rounded-pill bg-{{ $color->status ? 'success' : 'danger' }}">
                                    {{ $color->status ? 'نشط' : 'غير نشط' }}
                                </span>
                            </td>
                            <td>{{ $color->cars_count }}</td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('admin.colors.edit', $color->id) }}" class="btn btn-sm btn-warning me-1 action-btn edit" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.colors.destroy', $color->id) }}" method="POST" class="d-inline"
                                          onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا اللون؟');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger action-btn delete" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center">لا توجد ألوان لعرضها حاليًا.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    {{-- ترقيم الصفحات --}}
    <div class="mt-3">
        {{ $colors->appends(request()->query())->links('pagination::bootstrap-5') }}
    </div>
</div>
@endsection
