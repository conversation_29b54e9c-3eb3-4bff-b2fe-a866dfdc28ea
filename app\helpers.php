<?php

/**
 * ملف الدوال المساعدة العامة
 * يحتوي على دوال مساعدة مستخدمة عبر التطبيق
 */

if (!function_exists('format_currency')) {
    /**
     * تنسيق العملة بالريال السعودي
     *
     * @param float $amount
     * @return string
     */
    function format_currency($amount)
    {
        return number_format($amount, 2) . ' ر.س';
    }
}

if (!function_exists('format_number')) {
    /**
     * تنسيق الأرقام بالفواصل
     *
     * @param int|float $number
     * @return string
     */
    function format_number($number)
    {
        return number_format($number);
    }
}

if (!function_exists('setting')) {
    /**
     * الحصول على إعداد من قاعدة البيانات أو القيمة الافتراضية
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        // يمكن تطوير هذا لاحقاً للحصول على الإعدادات من قاعدة البيانات
        $settings = [
            'main_phone' => '+966501234567',
            'whatsapp_number' => '+966501234567',
            'company_name' => 'موتور لاين',
            'company_email' => '<EMAIL>',
            'company_address' => 'الرياض، المملكة العربية السعودية',
        ];

        return $settings[$key] ?? $default;
    }
}

if (!function_exists('get_car_status_badge')) {
    /**
     * الحصول على شارة حالة السيارة
     *
     * @param string $status
     * @return string
     */
    function get_car_status_badge($status)
    {
        $badges = [
            'available' => '<span class="brand-status-badge brand-status-success">متوفرة</span>',
            'sold' => '<span class="brand-status-badge brand-status-danger">مباعة</span>',
            'reserved' => '<span class="brand-status-badge brand-status-warning">محجوزة</span>',
            'maintenance' => '<span class="brand-status-badge brand-status-info">صيانة</span>',
        ];

        return $badges[$status] ?? '<span class="brand-status-badge brand-status-primary">' . $status . '</span>';
    }
}

if (!function_exists('get_user_avatar')) {
    /**
     * الحصول على صورة المستخدم أو الصورة الافتراضية
     *
     * @param \App\Models\User|null $user
     * @return string
     */
    function get_user_avatar($user = null)
    {
        if (!$user) {
            return asset('images/default-avatar.png');
        }

        return $user->profile_image_url ?? asset('images/default-avatar.png');
    }
}

if (!function_exists('truncate_text')) {
    /**
     * اقتطاع النص مع إضافة نقاط
     *
     * @param string $text
     * @param int $length
     * @param string $suffix
     * @return string
     */
    function truncate_text($text, $length = 100, $suffix = '...')
    {
        if (mb_strlen($text) <= $length) {
            return $text;
        }

        return mb_substr($text, 0, $length) . $suffix;
    }
}

if (!function_exists('get_file_size_human')) {
    /**
     * تحويل حجم الملف إلى تنسيق قابل للقراءة
     *
     * @param int $bytes
     * @return string
     */
    function get_file_size_human($bytes)
    {
        $units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.2f", $bytes / pow(1024, $factor)) . ' ' . $units[$factor];
    }
}

if (!function_exists('is_rtl_language')) {
    /**
     * التحقق من أن اللغة الحالية تستخدم الكتابة من اليمين لليسار
     *
     * @return bool
     */
    function is_rtl_language()
    {
        $rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        return in_array(app()->getLocale(), $rtlLanguages);
    }
}

if (!function_exists('get_breadcrumb_separator')) {
    /**
     * الحصول على فاصل مسار التنقل حسب اتجاه اللغة
     *
     * @return string
     */
    function get_breadcrumb_separator()
    {
        return is_rtl_language() ? '←' : '→';
    }
}

if (!function_exists('format_date_arabic')) {
    /**
     * تنسيق التاريخ بالعربية
     *
     * @param \Carbon\Carbon|string $date
     * @param string $format
     * @return string
     */
    function format_date_arabic($date, $format = 'Y/m/d')
    {
        if (is_string($date)) {
            $date = \Carbon\Carbon::parse($date);
        }

        return $date->format($format);
    }
}

if (!function_exists('get_time_ago_arabic')) {
    /**
     * الحصول على الوقت المنقضي بالعربية
     *
     * @param \Carbon\Carbon|string $date
     * @return string
     */
    function get_time_ago_arabic($date)
    {
        if (is_string($date)) {
            $date = \Carbon\Carbon::parse($date);
        }

        $diff = $date->diffInMinutes(now());

        if ($diff < 1) {
            return 'الآن';
        } elseif ($diff < 60) {
            return $diff . ' دقيقة';
        } elseif ($diff < 1440) {
            $hours = floor($diff / 60);
            return $hours . ' ساعة';
        } else {
            $days = floor($diff / 1440);
            return $days . ' يوم';
        }
    }
}
