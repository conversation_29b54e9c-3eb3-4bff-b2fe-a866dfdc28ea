<?php

namespace Modules\CarCatalog\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\CarCatalog\Models\TransmissionType;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\CarCatalog\Models\TransmissionType>
 */
class TransmissionTypeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = TransmissionType::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $transmissionTypes = [
            'أوتوماتيك',
            'يدوي',
            'CVT',
            'نصف أوتوماتيك',
            'DSG',
            'تيبترونيك',
        ];

        return [
            'name'        => $this->faker->unique()->randomElement($transmissionTypes),
            'description' => $this->faker->optional()->sentence(),
            'status'      => $this->faker->boolean(80), // 80% احتمال أن يكون نشط
        ];
    }

    /**
     * Indicate that the transmission type is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => true,
        ]);
    }

    /**
     * Indicate that the transmission type is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }
}
