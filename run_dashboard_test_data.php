<?php

/**
 * ملف سريع لتشغيل البيانات الوهمية المحسنة للوحة البيانات
 *
 * هذا الملف يمكن تشغيله لإنشاء بيانات وهمية شاملة وواقعية لاختبار لوحة البيانات
 *
 * تشغيل الملف:
 * php run_dashboard_test_data.php
 */

echo "🚀 بدء إنشاء البيانات الوهمية المحسنة للوحة البيانات...\n\n";

// تشغيل جميع الـ Seeders المحسنة
echo "📊 إنشاء البيانات الأساسية والمستخدمين...\n";
$output = shell_exec('php artisan db:seed 2>&1');
echo $output . "\n";

// تشغيل seeder الطلبات الوهمية
echo "📋 إنشاء طلبات وهمية لاختبار الإحصائيات...\n";
$output = shell_exec('php artisan db:seed --class="Modules\CarCatalog\Database\Seeders\TestOrdersSeeder" 2>&1');
echo $output . "\n";

echo "✅ تم إنشاء البيانات الوهمية المحسنة بنجاح!\n\n";
echo "📋 ملخص البيانات المُنشأة:\n";
echo "   ✨ الماركات والموديلات:\n";
echo "      - 20 ماركة عالمية مع أوصاف تفصيلية\n";
echo "      - أكثر من 150 موديل متنوع لكل ماركة\n";
echo "   🚗 السيارات:\n";
echo "      - 50 سيارة وهمية بمواصفات واقعية\n";
echo "      - أسعار متدرجة حسب الماركة والسنة\n";
echo "      - ميزات متنوعة حسب فئة السيارة\n";
echo "   👥 المستخدمين:\n";
echo "      - 5 موظفين بصلاحيات مختلفة\n";
echo "      - 45 عميل مع تواريخ إنشاء متنوعة\n";
echo "   📊 البيانات الوصفية:\n";
echo "      - سنوات صنع من 2015 إلى 2025\n";
echo "      - 18 لون مختلف مع أكواد الألوان\n";
echo "      - 7 أنواع ناقل حركة متقدمة\n";
echo "      - 7 أنواع وقود شاملة\n";
echo "      - 10 أنواع هياكل مختلفة\n";
echo "      - 6 فئات ميزات مع أكثر من 60 ميزة\n";
echo "   📋 الطلبات الوهمية:\n";
echo "      - طلبات متنوعة لاختبار الإحصائيات\n";
echo "      - حالات مختلفة (معلقة، مؤكدة، مكتملة)\n\n";

echo "🌐 يمكنك الآن زيارة لوحة البيانات لرؤية البيانات الديناميكية:\n";
echo "   http://localhost:8000/admin/dashboard\n\n";

echo "🔑 بيانات تسجيل الدخول:\n";
echo "   المدير العام: <EMAIL> / password\n";
echo "   موظف: <EMAIL> / password\n";
echo "   عميل: <EMAIL> / password\n\n";

echo "📝 ملاحظات:\n";
echo "   - جميع البيانات واقعية ومتنوعة لاختبار شامل\n";
echo "   - الأسعار والمواصفات منطقية حسب الماركة والسنة\n";
echo "   - الميزات مرتبطة بفئة السيارة (فاخرة/عادية)\n";
echo "   - التواريخ متنوعة لاختبار الإحصائيات الزمنية\n";
echo "   - يمكن تشغيل الملف مرة أخرى لإضافة المزيد من البيانات\n\n";

echo "🎉 انتهى! استمتع باختبار النظام مع البيانات الواقعية!\n";
