<?php

namespace Tests\Feature;

use Tests\TestCase;
use Modules\CarCatalog\Models\TransmissionType;
use Modules\UserManagement\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TransmissionTypeCrudTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // إنشاء صلاحية إدارة بيانات السيارات
        Permission::create(['name' => 'manage_car_metadata']);

        // إنشاء دور الإدارة
        $role = Role::create(['name' => 'Super Admin']);
        $role->givePermissionTo('manage_car_metadata');

        // إنشاء مستخدم وإعطاؤه الصلاحيات
        $this->user = User::create([
            'first_name' => 'أحمد',
            'last_name' => 'محمد',
            'email' => '<EMAIL>',
            'phone_number' => '0501234567',
            'email_verified_at' => now(),
            'password' => bcrypt('password'),
            'status' => 'active',
        ]);
        $this->user->assignRole('Super Admin');
    }

    /** @test */
    public function it_can_display_transmission_types_index()
    {
        // إنشاء بعض أنواع ناقل الحركة للاختبار
        TransmissionType::factory()->count(3)->create();

        $response = $this->actingAs($this->user)
            ->get(route('admin.transmission-types.index'));

        $response->assertStatus(200);
        $response->assertViewIs('carcatalog::admin.transmission-types.index');
    }

    /** @test */
    public function it_can_create_transmission_type()
    {
        $transmissionTypeData = [
            'name' => 'أوتوماتيك',
            'description' => 'ناقل حركة أوتوماتيكي',
            'status' => true,
        ];

        $response = $this->actingAs($this->user)
            ->post(route('admin.transmission-types.store'), $transmissionTypeData);

        $response->assertRedirect(route('admin.transmission-types.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('transmission_types', [
            'name' => 'أوتوماتيك',
            'description' => 'ناقل حركة أوتوماتيكي',
            'status' => true,
        ]);
    }

    /** @test */
    public function it_can_update_transmission_type()
    {
        $transmissionType = TransmissionType::factory()->create([
            'name' => 'يدوي',
            'status' => true,
        ]);

        $updateData = [
            'name' => 'يدوي محدث',
            'description' => 'ناقل حركة يدوي محدث',
            'status' => false,
        ];

        $response = $this->actingAs($this->user)
            ->put(route('admin.transmission-types.update', $transmissionType), $updateData);

        $response->assertRedirect(route('admin.transmission-types.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('transmission_types', [
            'id' => $transmissionType->id,
            'name' => 'يدوي محدث',
            'description' => 'ناقل حركة يدوي محدث',
            'status' => false,
        ]);
    }

    /** @test */
    public function it_can_delete_transmission_type_without_cars()
    {
        $transmissionType = TransmissionType::factory()->create();

        $response = $this->actingAs($this->user)
            ->delete(route('admin.transmission-types.destroy', $transmissionType));

        $response->assertRedirect(route('admin.transmission-types.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseMissing('transmission_types', [
            'id' => $transmissionType->id,
        ]);
    }

    /** @test */
    public function it_validates_required_fields()
    {
        $response = $this->actingAs($this->user)
            ->post(route('admin.transmission-types.store'), []);

        $response->assertSessionHasErrors(['name', 'status']);
    }

    /** @test */
    public function it_validates_unique_name()
    {
        TransmissionType::factory()->create(['name' => 'أوتوماتيك']);

        $response = $this->actingAs($this->user)
            ->post(route('admin.transmission-types.store'), [
                'name' => 'أوتوماتيك',
                'status' => true,
            ]);

        $response->assertSessionHasErrors(['name']);
    }
}
