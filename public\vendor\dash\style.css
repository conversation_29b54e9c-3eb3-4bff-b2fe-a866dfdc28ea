/* Global Styles */
:root {
    --primary-color: #0f172a;
    --secondary-color: #1e3a8a;
    --accent-color: #f97316;
    --light-bg: #f8fafc;
    --dark-bg: #020617;
    --sidebar-bg-start: #122a89;
    --sidebar-bg-end: #071a62;
    --sidebar-item-hover: rgba(255, 255, 255, 0.08);
    --sidebar-item-active: rgba(255, 255, 255, 0.15);
    --sidebar-border: rgba(255, 255, 255, 0.12);
    --text-color: #334155;
    --text-light: #f8fafc;
    --text-muted: #94a3b8;
    --sidebar-width: 280px;
    --sidebar-mini-width: 80px;
    --border-radius: 10px;
    --transition-speed: 0.3s;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

body {
    font-family: 'IBM Plex Sans Arabic', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-bg);
    color: var(--text-color);
    overflow-x: hidden;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: var(--sidebar-width);
    background: linear-gradient(145deg, var(--sidebar-bg-start) 0%, var(--sidebar-bg-end) 100%);
    color: var(--text-light);
    z-index: 1030;
    transition: all var(--transition-speed);
    box-shadow: -2px 0 15px rgba(0, 0, 0, 0.2);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
    border-left: 1px solid rgba(255, 255, 255, 0.05);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
}

/* كود جديد لحالة القائمة المصغرة */
.sidebar.mini-mode {
    width: var(--sidebar-mini-width);
    overflow-x: hidden;
}

.sidebar.mini-mode .sidebar-header h4,
.sidebar.mini-mode .sidebar-header p,
.sidebar.mini-mode .nav-section-title,
.sidebar.mini-mode .nav-link span,
.sidebar.mini-mode .chevron,
.sidebar.mini-mode .submenu {
    display: none;
}

.sidebar.mini-mode .nav-link {
    justify-content: center;
    padding: 0.8rem;
}

.sidebar.mini-mode .nav-link i {
    margin: 0;
    font-size: 1.3rem;
}

.sidebar.mini-mode.hover-expand {
    width: var(--sidebar-width);
    z-index: 1050;
}

.sidebar.mini-mode.hover-expand .sidebar-header h4,
.sidebar.mini-mode.hover-expand .sidebar-header p,
.sidebar.mini-mode.hover-expand .nav-section-title,
.sidebar.mini-mode.hover-expand .nav-link span,
.sidebar.mini-mode.hover-expand .chevron {
    display: block;
}

.sidebar.mini-mode.hover-expand .nav-link {
    justify-content: flex-start;
    padding: 0.8rem 1rem;
}

.sidebar.mini-mode.hover-expand .nav-link i {
    margin-left: 10px;
    font-size: 1.1rem;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--sidebar-border);
    background-color: rgba(0, 0, 0, 0.2);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

/* تعديل جديد للعنوان في القائمة المصغرة */
.sidebar.mini-mode .sidebar-header {
    padding: 0.8rem;
    height: 60px;
    justify-content: center;
}

.sidebar.mini-mode .sidebar-header .sidebar-logo-mini {
    display: block;
    font-size: 1.5rem;
    color: var(--accent-color);
}

.sidebar-logo-mini {
    display: none;
}

.sidebar-header h4 {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
    margin-bottom: 0;
}

.sidebar-header p {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    margin-bottom: 0;
}

.nav-section-title {
    padding: 0.7rem 1.5rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.5);
    margin-bottom: 0.5rem;
    margin-top: 1.2rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    position: relative;
}

.nav-section-title:after {
    content: '';
    position: absolute;
    right: 1.5rem;
    bottom: 0.4rem;
    width: 2.5rem;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), transparent);
}

.nav-item {
    margin-bottom: 2px;
    position: relative;
}

.nav-link {
    color: rgba(255, 255, 255, 0.75);
    border-radius: 8px;
    margin: 0 0.75rem;
    padding: 0.8rem 1rem;
    font-weight: 500;
    transition: all var(--transition-speed);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.nav-link:before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 0;
    background-color: var(--accent-color);
    transition: all var(--transition-speed);
}

.nav-link:hover:before,
.nav-link.active:before {
    width: 4px;
}

.nav-link:hover,
.nav-link.active {
    color: white;
    transform: translateX(-5px);
    padding-right: calc(1rem + 5px);
}

.sidebar.mini-mode .nav-link:hover,
.sidebar.mini-mode .nav-link.active {
    transform: none;
    padding-right: 0.8rem;
}

.sidebar.mini-mode.hover-expand .nav-link:hover,
.sidebar.mini-mode.hover-expand .nav-link.active {
    transform: translateX(-5px);
    padding-right: calc(1rem + 5px);
}

.nav-link.active {
    background-color: rgba(0, 0, 0, 0.35);
    font-weight: 600;
}

.nav-link i {
    width: 24px;
    text-align: center;
    margin-left: 10px;
    font-size: 1.1rem;
    transition: all var(--transition-speed);
    color: rgba(255, 255, 255, 0.7);
}

.nav-link:hover i,
.nav-link.active i {
    color: var(--accent-color);
    transform: scale(1.1);
    text-shadow: 0 0 10px rgba(249, 115, 22, 0.5);
}

.nav-link .chevron {
    margin-right: auto;
    margin-left: 0;
    transition: transform var(--transition-speed);
}

.nav-link[aria-expanded="true"] .chevron,
.chevron.rotate {
    transform: rotate(180deg);
}

.submenu {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    margin-right: 1.2rem;
    margin-left: 1.2rem;
    margin-bottom: 0.8rem;
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.submenu .nav-link {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius);
    margin: 0.2rem 0;
}

.submenu .nav-link:before {
    display: none;
}

.submenu .nav-link i {
    font-size: 0.9rem;
    opacity: 0.8;
}

.submenu .nav-link:hover,
.submenu .nav-link.active {
    background-color: rgba(0, 0, 0, 0.2);
    color: white;
    transform: none;
    padding-right: 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.submenu .nav-link.active {
    border-right: 2px solid var(--accent-color);
}

.submenu .badge {
    font-size: 0.65rem;
    font-weight: 700;
    padding: 0.25rem 0.45rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Divider */
.sidebar-divider {
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.12) 50%,
        transparent 100%);
    margin: 1.2rem 1.5rem;
}

/* Main Content Styles */
.main-content {
    margin-right: var(--sidebar-width);
    transition: all var(--transition-speed);
    padding: 20px;
}

.main-content.mini-sidebar {
    margin-right: var(--sidebar-mini-width);
}

/* Navbar Styles */
.top-navbar {
    background-color: white;
    border-bottom: 1px solid #eee;
    height: 70px;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
}
.top-navbar .nav-link, .top-navbar .nav-link:hover, .top-navbar .nav-link.active {
    color: var(--bs-heading-color);
}
.top-navbar .nav-link:before {

    background-color: transparent;
    transition: none;
}
.navbar-toggler {
    border: none;
    padding: 0;
    font-size: 1.5rem;
    color: var(--primary-color);
    background: transparent;
}
.navbar-toggler:focus {
    box-shadow: none;
}

.search-bar {
    max-width: 300px;
}

.search-bar .input-group {
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.search-bar .form-control {
    border-radius: 50px 0 0 50px;
    border: none;
    padding-right: 20px;
}

.search-bar .btn {
    border-radius: 0 50px 50px 0;
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.notification-bell {
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    color: var(--primary-color);
    transition: all var(--transition-speed);
}

.notification-bell:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--accent-color);
}

.notification-badge {
    position: absolute;
    top: -5px;
    left: -5px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.user-dropdown img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
    transition: all var(--transition-speed);
}

.user-dropdown:hover img {
    border-color: var(--accent-color);
}

/* Cards Styles */
.dashboard-card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: transform 0.2s, box-shadow 0.2s;
    background-color: white;
    border: none;
    height: 100%;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-title {
    color: var(--primary-color);
    font-weight: 600;
}

.stat-card {
    border-right: 4px solid var(--secondary-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    font-size: 1.8rem;
    background: linear-gradient(145deg, #ffffff, #f0f0f0);
    box-shadow: 5px 5px 10px #d9d9d9, -5px -5px 10px #ffffff;
}

.text-indigo {
    color: #6366f1 !important;
}

.bg-indigo-subtle {
    background-color: rgba(99, 102, 241, 0.1) !important;
}

.text-purple {
    color: #8b5cf6 !important;
}

.bg-purple-subtle {
    background-color: rgba(139, 92, 246, 0.1) !important;
}

.chart-container {
    height: 300px;
    width: 100%;
}

.activity-item {
    border-right: 3px solid #eee;
    padding: 10px 20px 10px 10px;
    position: relative;
    margin-bottom: 15px;
}

.activity-item::before {
    content: '';
    position: absolute;
    right: -8px;
    top: 15px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background-color: var(--secondary-color);
}

.activity-item.new::before {
    background-color: var(--accent-color);
}

.alert-card {
    border-right: 4px solid var(--accent-color);
}

.car-color-display {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
}

.car-color-circle {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    border-radius: 50%;
    margin: auto 0;
    margin-left: 8px;
    flex-shrink: 0;
}

.car-color-name {
    font-size: 0.95rem;
    color: #333;
    text-align: right;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
}

.car-color-display:hover .car-color-circle {
    transform: scale(1.1);
    box-shadow: 0 0 5px rgba(0,0,0,0.2);
}

/* Recent Activity Table */
.recent-activity-table {
    border-collapse: separate;
    border-spacing: 0;
}

.recent-activity-table th,
.recent-activity-table td {
    padding: 12px 15px;
    vertical-align: middle;
}

.recent-activity-table th {
    background-color: rgba(0, 0, 0, 0.02);
    font-weight: 600;
    color: var(--primary-color);
}

.recent-activity-table tr {
    transition: all var(--transition-speed);
}

.recent-activity-table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.status-badge {
    padding: 5px 12px;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-block;
}

.status-pending {
    background-color: #fff8e1;
    color: #ff9800;
}

.status-processing {
    background-color: #e3f2fd;
    color: #2196f3;
}

.status-completed {
    background-color: #e8f5e9;
    color: #4caf50;
}

.status-cancelled {
    background-color: #fce4ec;
    color: #e91e63;
}

/* Action Buttons */
.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    background-color: transparent;
    border: none;
    transition: all var(--transition-speed);
    font-size: 0.9rem;
    text-decoration: none;
}

.action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--primary-color);
    text-decoration: none;
}

.action-btn.view:hover {
    color: var(--secondary-color);
}

.action-btn.edit:hover {
    color: #2196f3;
}

.action-btn.delete:hover {
    color: var(--accent-color);
}

/* تحسين مظهر الروابط في أزرار الإجراءات */
a.action-btn {
    color: var(--text-color);
}

a.action-btn:hover {
    color: var(--primary-color);
    text-decoration: none;
}

a.action-btn.view:hover {
    color: var(--secondary-color);
}

a.action-btn.edit:hover {
    color: #2196f3;
}

a.action-btn.delete:hover {
    color: var(--accent-color);
}

/* BS-Stepper Customization */
.bs-stepper .step-trigger {
    color: var(--text-muted);
}

.bs-stepper .step-trigger:hover {
    color: var(--primary-color-darker);
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.bs-stepper .step-trigger.active,
.bs-stepper .step-trigger.active:hover {
    color: var(--primary-color);
}

.bs-stepper .bs-stepper-header .line {
    background-color: var(--border-color);
}

.bs-stepper .bs-stepper-circle {
    background-color: var(--light-gray);
    color: var(--text-muted);
    border: 1px solid var(--border-color);
}

.bs-stepper .step-trigger.active .bs-stepper-circle,
.bs-stepper .step-trigger.completed .bs-stepper-circle {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.bs-stepper .step-trigger.completed .bs-stepper-circle {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.bs-stepper .step-trigger.active .bs-stepper-label {
    color: var(--primary-color);
    font-weight: 600;
}

.bs-stepper .step-trigger.completed .bs-stepper-label {
    color: var(--success-color);
}

.bs-stepper-content {
    padding-top: 20px;
}

/* Footer */
.footer {
    margin-right: var(--sidebar-width);
    transition: all var(--transition-speed);
}

.footer.mini-sidebar {
    margin-right: var(--sidebar-mini-width);
}

/* Animation for Submenu */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.submenu {
    animation: slideDown 0.3s ease forwards;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 50px;
    background-color: rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.progress-bar {
    border-radius: 50px;
}

/* تعديلات جديدة لدعم الموبايل */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1020;
}

.sidebar-overlay.show {
    display: block;
}

/* Styling for bs-stepper */
.bs-stepper .step-trigger {
    color: var(--text-color);
}

.bs-stepper .step-trigger:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
}

.bs-stepper .bs-stepper-circle {
    background-color: var(--secondary-color);
    color: #fff;
}

.bs-stepper .step-trigger.active .bs-stepper-circle,
.bs-stepper .step-trigger:focus .bs-stepper-circle {
    background-color: var(--primary-color);
}

.bs-stepper .line {
    background-color: var(--border-color);
}

.bs-stepper-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.bs-stepper-content .content {
    padding: 1rem 0;
}

.card-header.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Responsive adjustments */
/* تعديلات الموبايل */
@media (max-width: 992px) {
    :root {
        --sidebar-mini-width: 0px;
    }

    .sidebar {
        right: calc(-1 * var(--sidebar-width));
        box-shadow: none;
    }

    .sidebar.mobile-show {
        right: 0;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
    }

    .main-content,
    .main-content.mini-sidebar,
    .footer,
    .footer.mini-sidebar {
        margin-right: 0;
    }

    .sidebar.mini-mode {
        right: calc(-1 * var(--sidebar-width));
    }

    .sidebar.mini-mode.mobile-show {
        right: 0;
        width: var(--sidebar-width);
    }

    .sidebar.mini-mode.mobile-show .sidebar-header h4,
    .sidebar.mini-mode.mobile-show .sidebar-header p,
    .sidebar.mini-mode.mobile-show .nav-section-title,
    .sidebar.mini-mode.mobile-show .nav-link span,
    .sidebar.mini-mode.mobile-show .chevron {
        display: block;
    }

    .sidebar.mini-mode.mobile-show .sidebar-logo-mini {
        display: none;
    }

    .sidebar.mini-mode.mobile-show .nav-link {
        justify-content: flex-start;
        padding: 0.8rem 1rem;
    }

    .sidebar.mini-mode.mobile-show .nav-link i {
        margin-left: 10px;
    }

    .sidebar.mini-mode.mobile-show .submenu {
        display: block;
    }

    .sidebar.mini-mode.mobile-show .sidebar-header {
        padding: 1.5rem;
        height: auto;
    }

    .mobile-sidebar-close {
        position: absolute;
        top: 12px;
        left: 12px;
        background: rgba(0, 0, 0, 0.2);
        color: white;
        border: none;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
        z-index: 10;
    }

    .mobile-sidebar-close:hover {
        background: rgba(0, 0, 0, 0.3);
        transform: scale(1.1);
    }
}
