# Site Frontend Layout Documentation

## نظرة عامة

تم إنشاء هيكل التخطيط الرئيسي للموقع العام (Site Frontend) كجزء من المهمة **PH03-TASK-001**. يوفر هذا الهيكل أساساً متيناً وقابلاً للتوسع لجميع صفحات الموقع العام.

## الملفات المنشأة

### 1. ملف التخطيط الرئيسي
- **المسار**: `resources/views/site/layouts/site_layout.blade.php`
- **الوصف**: الملف الأساسي الذي يحتوي على هيكل HTML الكامل
- **المميزات**:
  - دعم كامل للغة العربية (RTL)
  - تكامل مع Laravel Vite
  - Meta tags للـ SEO
  - Open Graph و Twitter Cards
  - دعم الخطوط العربية (IBM Plex Sans Arabic)
  - نظام التنبيهات المدمج
  - زر العودة للأعلى
  - شاشة التحميل

### 2. ملف الرأس (Header)
- **المسار**: `resources/views/site/layouts/partials/_header.blade.php`
- **المميزات**:
  - شريط علوي اختياري مع معلومات الاتصال
  - قائمة تنقل رئيسية مع الروابط
  - أيقونات التفاعل (البحث، المفضلة)
  - نظام المصادقة (تسجيل الدخول/الخروج)
  - مودال البحث المتقدم
  - تصميم متجاوب

### 3. ملف التذييل (Footer)
- **المسار**: `resources/views/site/layouts/partials/_footer.blade.php`
- **المميزات**:
  - أقسام منظمة للروابط
  - معلومات الاتصال
  - أيقونات وسائل التواصل الاجتماعي
  - شريط حقوق النشر
  - تصميم متجاوب

### 4. ملف الأنماط المخصص
- **المسار**: `resources/css/site_app.css`
- **المميزات**:
  - متغيرات CSS مخصصة
  - أنماط بطاقات السيارات
  - أنماط النماذج والأزرار
  - تحسينات للاستجابة
  - دعم الطباعة
  - تحسينات الوصولية

### 5. ملف JavaScript المخصص
- **المسار**: `resources/js/site_app.js`
- **المميزات**:
  - كائن SiteApp للإدارة العامة
  - نظام CSRF Token
  - إدارة شاشة التحميل
  - نظام Toast للتنبيهات
  - وظائف المفضلة
  - البحث الديناميكي
  - دوال مساعدة للتنسيق

## الاستخدام

### إنشاء صفحة جديدة

```blade
@extends('site.layouts.site_layout')

@section('title', 'عنوان الصفحة')
@section('meta_description', 'وصف الصفحة للـ SEO')

@section('content')
<div class="container">
    <h1>محتوى الصفحة</h1>
    <!-- محتوى الصفحة هنا -->
</div>
@endsection

@push('styles')
<style>
    /* أنماط مخصصة للصفحة */
</style>
@endpush

@push('scripts')
<script>
    // جافا سكريبت مخصص للصفحة
</script>
@endpush
```

### استخدام بطاقات السيارات

```blade
<div class="car-card">
    <div class="car-card-image">
        <img src="{{ $car->main_image_url }}" alt="{{ $car->name }}">
        <div class="car-card-badge">{{ $car->status_label }}</div>
    </div>
    <div class="car-card-body">
        <h5 class="car-card-title">{{ $car->name }}</h5>
        <div class="car-card-price">{{ $car->formatted_price }}</div>
        <div class="car-card-specs">
            <div class="car-spec">
                <i class="fas fa-calendar"></i>
                {{ $car->manufacturing_year }}
            </div>
            <!-- المزيد من المواصفات -->
        </div>
        <div class="car-card-actions">
            <a href="{{ route('site.cars.show', $car) }}" class="btn btn-brand-primary flex-fill">
                عرض التفاصيل
            </a>
            <button class="favorite-btn" data-car-id="{{ $car->id }}">
                <i class="far fa-heart"></i>
            </button>
        </div>
    </div>
</div>
```

### استخدام JavaScript

```javascript
// إظهار شاشة التحميل
SiteApp.showLoading();

// إخفاء شاشة التحميل
SiteApp.hideLoading();

// إظهار تنبيه
SiteApp.showToast('رسالة النجاح', 'success');
SiteApp.showToast('رسالة الخطأ', 'error');

// تنسيق الأرقام
const formattedNumber = SiteApp.utils.formatNumber(123456);
const formattedCurrency = SiteApp.utils.formatCurrency(50000);
```

## الهوية البصرية

يستخدم التخطيط الهوية البصرية المحددة في `public/css/brand-identity.css`:

- **الألوان الأساسية**: متدرج من الأزرق إلى البنفسجي
- **الخط**: IBM Plex Sans Arabic للنصوص العربية
- **الأيقونات**: Font Awesome 6
- **الإطار**: Bootstrap 5 RTL

## المسارات المطلوبة

التخطيط يتوقع وجود المسارات التالية (يجب إنشاؤها في المراحل القادمة):

```php
// المسارات الأساسية
Route::name('site.')->group(function () {
    Route::get('/', 'HomeController@index')->name('home');
    Route::get('/cars', 'CarController@index')->name('cars.index');
    Route::get('/cars/{car}', 'CarController@show')->name('cars.show');
    Route::get('/services', 'ServiceController@index')->name('services.index');
    Route::get('/corporate', 'CorporateController@index')->name('corporate.index');
    Route::get('/promotions', 'PromotionController@index')->name('promotions.index');
    Route::get('/request-car/step1', 'RequestCarController@step1')->name('request-car.step1');
    Route::get('/about', 'PageController@about')->name('about');
    Route::get('/contact', 'PageController@contact')->name('contact');
    Route::get('/branches', 'PageController@branches')->name('branches');
    Route::get('/faq', 'PageController@faq')->name('faq');
    Route::get('/privacy', 'PageController@privacy')->name('privacy');
    Route::get('/terms', 'PageController@terms')->name('terms');
    Route::get('/support', 'PageController@support')->name('support');
    Route::get('/careers', 'PageController@careers')->name('careers');
    Route::get('/favorites', 'FavoriteController@index')->name('favorites');
});

// مسارات العملاء
Route::middleware(['auth', 'role:Customer'])->name('customer.')->group(function () {
    Route::get('/dashboard', 'CustomerController@dashboard')->name('dashboard');
    Route::get('/profile', 'CustomerController@profile')->name('profile');
    Route::get('/orders', 'CustomerController@orders')->name('orders');
});

// مسارات API
Route::prefix('api')->group(function () {
    Route::get('/brands', 'Api\BrandController@index');
    Route::get('/brands/{brand}/models', 'Api\BrandController@models');
    Route::post('/favorites/toggle', 'Api\FavoriteController@toggle');
    Route::get('/favorites/count', 'Api\FavoriteController@count');
});
```

## اختبار التخطيط

تم إنشاء صفحة اختبار في `resources/views/site/test_layout.blade.php` يمكن الوصول إليها عبر:

```
http://your-domain.com/test-layout
```

هذه الصفحة تحتوي على:
- اختبار جميع عناصر التخطيط
- بطاقات السيارات التجريبية
- نماذج البحث
- الأزرار والتنبيهات
- اختبار JavaScript

## المتطلبات التقنية

- Laravel 10+
- Bootstrap 5 RTL
- Font Awesome 6
- Laravel Vite
- PHP 8.1+

## الخطوات التالية

1. **إنشاء Controllers للموقع العام**
2. **إنشاء المسارات المطلوبة**
3. **تطوير صفحات المحتوى**
4. **تكامل قاعدة البيانات**
5. **اختبار شامل للوظائف**

## الدعم والصيانة

- جميع الملفات موثقة بالتفصيل
- الكود يتبع معايير Laravel
- دعم كامل للغة العربية
- تصميم متجاوب ومتوافق مع جميع الأجهزة
