## 00-FR.md - التحليل الأولي للمشروع ومقترح الموديولات (نسخة نهائية معتمدة ذاتيًا)

**تاريخ الإنشاء:** 2024-07-26
**إصدار المستند:** 1.0 (نهائي)

---
## أسئلة حاسمة تتطلب موافقة المستخدم قبل الانتقال للمرحلة التالية

1.  **تفاصيل "خدمات ما بعد البيع المجانية" (بقيمة 500 ريال):** ما هي الخدمات المحددة التي تشملها هذه القيمة؟ هل هي ثابتة لجميع السيارات أم متغيرة؟ (لتصميم العرض بشكل دقيق في صفحة تفاصيل السيارة).
2.  **آلية تفعيل ميزة "رشح عميل" لبعض العملاء:** هل هناك معايير محددة لاختيار العملاء الذين ستتاح لهم هذه الميزة في لوحة التحكم الخاصة بهم؟ أم ستكون متاحة للجميع أو يتم تفعيلها يدويًا من الإدارة؟ (لتصميم منطق الصلاحيات).
3.  **الحد الأقصى لحجم الملفات المرفوعة:** هل هناك حد أقصى محدد لحجم صور السيارات أو المستندات المرفوعة من العملاء؟ (لتكوين إعدادات `spatie/laravel-medialibrary` وخوادم الويب).
4.  **بيانات الرسوم البيانية في لوحة التحكم (`dashboard.html`):** ملف `script.js` المرفق مع `dashboard.html` يقوم بتهيئة رسوم بيانية متعددة ببيانات ثابتة. هل هناك تصور محدد لمصادر البيانات الديناميكية لهذه الرسوم من الـ Backend (مثل أي تقارير يجب أن تعكسها، الفترات الزمنية، إلخ)؟ أم أن الهدف في المرحلة الأولى هو مجرد عرض هيكل اللوحة مع بيانات وهمية للرسوم؟ (لتوجيه تطوير موديول `Dashboard` و `Api` بشكل صحيح).
---

### 1. تأكيد مراجعة المدخلات وفهمها

تمت مراجعة وفهم جميع مستندات الإدخال المقدمة، بما في ذلك `initial_input_summary_final_revised.md`، `project-details.md`، وملفات لوحة التحكم `dashboard.html`، `style.css`، و `script.js` (التي ستكون موجودة في مجلد `Dash` في جذر المشروع). يهدف هذا المستند إلى تلخيص المتطلبات الأولية وتقديم اقتراح هيكلي للمشروع.

### 2. النظرة العامة للمشروع

*   **الغرض الرئيسي (MOD-CORE-001):** تطوير منصة إلكترونية متكاملة لمعرض سيارات تتيح للمستخدمين تصفح وشراء السيارات الجديدة فقط، مع تقديم خدمات إضافية وإدارة فعالة للعمليات.
*   **الأهداف الأساسية:**
    *   توفير واجهة مستخدم سهلة وجذابة لعرض السيارات الجديدة وتفاصيلها (FEAT-CORE-001).
    *   تمكين عمليات الشراء أونلاين (كاش أو جمع بيانات للتمويل) (FEAT-ORDER-001).
    *   تقديم وإدارة خدمات إضافية متعلقة بالسيارات (FEAT-SERVICE-001).
    *   إدارة طلبات شراء السيارات بكميات كبيرة للشركات (FEAT-CORP-001).
    *   عرض العروض الترويجية بطريقة فعالة (FEAT-PROMO-001).
    *   توفير لوحات تحكم مخصصة (إدارة وعميل) باستخدام أصول Dash المتوفرة (HTML/CSS/JS) ودمجها مع Laravel Blade (FEAT-DASH-001).
    *   تطوير تطبيق موبايل Flutter (FEAT-FLUTTER-001).
*   **القيمة المقترحة:** تبسيط عملية شراء السيارات الجديدة، توفير تجربة رقمية متكاملة للعملاء، وزيادة كفاءة عمليات المعرض.

### 3. أنواع المستخدمين والجمهور المستهدف

1.  **المستخدمون العاديون (العملاء الأفراد) (USER-TYPE-001):**
    *   الوصف: أفراد يرغبون بشراء سيارات جديدة.
    *   الواجهات الرئيسية: واجهة الموقع العامة (Blade)، تطبيق Flutter، لوحة تحكم العميل (Dash Custom).
2.  **الشركات (USER-TYPE-002):**
    *   الوصف: شركات ومؤسسات ترغب بشراء سيارات بكميات كبيرة.
    *   الواجهات الرئيسية: قسم مبيعات الشركات في واجهة الموقع العامة (Blade).
3.  **مديرو النظام (USER-TYPE-003):**
    *   الوصف: فريق الإدارة المسؤول عن إدارة المنصة بشكل كامل.
    *   الواجهات الرئيسية: لوحة تحكم الإدارة (Dash Custom).
4.  **الموظفون (USER-TYPE-004):**
    *   الوصف: فريق العمل في المعرض بمعالجة الطلبات والتواصل.
    *   الواجهات الرئيسية: لوحة تحكم الإدارة (Dash Custom) بصلاحيات محددة.

### 4. الميزات والوظائف الأولية

| المعرف الفريد للميزة | اسم الميزة/الوظيفة                                  | النوع (وظيفي/غير وظيفي/قيد) | المكون المسؤول الأساسي (Backend API, Dash Custom Panel, Flutter, Public Blade) |
| :------------------ | :-------------------------------------------------- | :-------------------------- | :------------------------------------------------------------------------------- |
| **السيارات**        |                                                     |                             |                                                                                  |
| FEAT-CAR-001        | عرض قائمة السيارات الجديدة (مع صور ومواصفات وأسعار)  | وظيفي                       | Backend API, Public Blade, Flutter App, Dash Custom Panel (للعرض والإدارة)       |
| FEAT-CAR-002        | نظام فلترة متقدم للسيارات                            | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-CAR-003        | صفحة تفاصيل السيارة (صور، مواصفات، أزرار إجراءات)   | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-CAR-004        | تحميل مواصفات السيارة (PDF)                         | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-CAR-005        | إضافة السيارة للمفضلة                               | وظيفي                       | Backend API, Public Blade, Flutter App, لوحة تحكم العميل (Dash Custom)          |
| FEAT-CAR-006        | مقارنة السيارات                                     | وظيفي                       | Backend API, Public Blade, Flutter App, لوحة تحكم العميل (Dash Custom)          |
| FEAT-CAR-007        | مشاركة إعلان السيارة                                 | وظيفي                       | Public Blade, Flutter App                                                        |
| FEAT-CAR-008        | إدارة ماركات السيارات (إضافة/تعديل/حذف)             | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-CAR-009        | إدارة موديلات السيارات (إضافة/تعديل/حذف)            | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-CAR-010        | إدارة ميزات السيارات (إضافة/تعديل/حذف)             | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-CAR-011        | إدارة ألوان السيارات (إضافة/تعديل/حذف)              | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-CAR-012        | إدارة سنوات الصنع (إضافة/تعديل/حذف)                | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-CAR-013        | إدارة أنواع ناقل الحركة (إضافة/تعديل/حذف)          | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-CAR-014        | إدارة أنواع الوقود (إضافة/تعديل/حذف)               | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-CAR-015        | استيراد/تصدير بيانات السيارات (Excel/CSV)            | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-CAR-016        | إدارة صور السيارات (رفع، تحديد رئيسية، ترتيب)       | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-CAR-017        | تحديد سيارة مميزة                                  | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| **الطلبات والشراء** |                                                     |                             |                                                                                  |
| FEAT-ORDER-001      | عملية شراء السيارة (كاش أو تمويل)                   | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-ORDER-002      | صفحة "كيف أشتريها؟" (كاش/تمويل)                     | وظيفي                       | Public Blade, Flutter App                                                        |
| FEAT-ORDER-003      | نموذج طلب السيارة كاش (5 مراحل)                      | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-ORDER-004      | نموذج طلب التمويل (3 مراحل)                         | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-ORDER-005      | دفع مبلغ الحجز أونلاين أو في المعرض                   | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-ORDER-006      | رفع المستندات المطلوبة للشراء/التمويل                | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-ORDER-007      | إدارة طلبات الشراء (عرض، تحديث حالة)                | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-ORDER-008      | إدارة طلبات التمويل (عرض، تحديث حالة)               | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-ORDER-009      | إضافة فاتورة للدفع المتبقي في المعرض (موظف)         | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| **الخدمات**        |                                                     |                             |                                                                                  |
| FEAT-SERVICE-001    | عرض قائمة الخدمات المتاحة (مع أسعار وتفاصيل)         | وظيفي                       | Backend API, Public Blade                                                        |
| FEAT-SERVICE-002    | نموذج طلب شراء خدمة (بدون تسجيل دخول)                | وظيفي                       | Backend API, Public Blade                                                        |
| FEAT-SERVICE-003    | إدارة فئات الخدمات (إضافة/تعديل/حذف)               | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-SERVICE-004    | إدارة الخدمات (إضافة/تعديل/حذف، أسعار)             | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-SERVICE-005    | إدارة طلبات الخدمات                                 | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| **مبيعات الشركات**  |                                                     |                             |                                                                                  |
| FEAT-CORP-001       | صفحة مخصصة لطلبات أساطيل الشركات                    | وظيفي                       | Public Blade                                                                     |
| FEAT-CORP-002       | نموذج طلب للشركات (بدون تسجيل دخول)                 | وظيفي                       | Backend API, Public Blade                                                        |
| FEAT-CORP-003       | إدارة طلبات الشركات                                 | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| **عروض السيارات**   |                                                     |                             |                                                                                  |
| FEAT-PROMO-001      | صفحة عرض بنرات العروض الترويجية                       | وظيفي                       | Backend API, Public Blade                                                        |
| FEAT-PROMO-002      | صفحة تفاصيل العرض (مع السيارات المشمولة)             | وظيفي                       | Backend API, Public Blade                                                        |
| FEAT-PROMO-003      | إدارة العروض (إضافة/تعديل/حذف، ربط بالسيارات)        | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| **اطلب سيارتك**     |                                                     |                             |                                                                                  |
| FEAT-REQCAR-001     | عملية طلب سيارة مخصصة (متعددة الخطوات)               | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| **المستخدمون والحسابات** |                                               |                             |                                                                                  |
| FEAT-USER-001       | تسجيل حساب جديد للعملاء                             | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-USER-002       | تسجيل الدخول للعملاء                                | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-USER-003       | استعادة كلمة المرور للعملاء                          | وظيفي                       | Backend API, Public Blade, Flutter App                                           |
| FEAT-USER-004       | لوحة تحكم العميل (Dash Custom)                      | وظيفي                       | Backend API, لوحة تحكم العميل (Dash Custom)                                     |
| FEAT-USER-005       | إدارة الملف الشخصي للعميل                           | وظيفي                       | Backend API, لوحة تحكم العميل (Dash Custom)                                     |
| FEAT-USER-006       | عرض طلبات العميل وحالتها                             | وظيفي                       | Backend API, لوحة تحكم العميل (Dash Custom)                                     |
| FEAT-USER-007       | عرض المعاملات المالية والفواتير للعميل              | وظيفي                       | Backend API, لوحة تحكم العميل (Dash Custom)                                     |
| FEAT-USER-008       | قسم "رشح عميل" في لوحة تحكم العميل                  | وظيفي                       | Backend API, لوحة تحكم العميل (Dash Custom)                                     |
| FEAT-USER-009       | إدارة العملاء (قائمة، إضافة/تعديل) (مدير)         | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-USER-010       | إدارة الموظفين (قائمة، إضافة/تعديل، أدوار وصلاحيات) | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| **لوحة تحكم الإدارة (Dash Custom)** |                                  |                             |                                                                                  |
| FEAT-DASH-001       | توفير لوحات تحكم مخصصة (إدارة وعميل) باستخدام أصول Dash المتوفرة (HTML/CSS/JS) ودمجها مع Laravel Blade | وظيفي                       | Backend API, Dash Custom Panel (Laravel Blade integration)                       |
| FEAT-ADMIN-001      | لوحة بيانات رئيسية (إحصائيات، رسوم بيانية، آخر نشاطات) | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-ADMIN-002      | إدارة محتوى الصفحات الثابتة (من نحن، سياسة، شروط)   | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-ADMIN-003      | إعدادات النظام (معلومات المعرض، SEO، دفع، إشعارات) | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-ADMIN-004      | النسخ الاحتياطي واستعادة النظام                     | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| FEAT-ADMIN-005      | تقارير النظام (مبيعات، عملاء، سيارات)               | وظيفي                       | Backend API, Dash Custom Panel                                                   |
| **عام**            |                                                     |                             |                                                                                  |
| FEAT-COMM-001       | التواصل المباشر (واتساب، اتصال)                     | وظيفي                       | Public Blade, Flutter App                                                        |
| FEAT-NOTIF-001      | نظام الإشعارات (داخل النظام، بريد، SMS)             | وظيفي                       | Backend API, Dash Custom Panel, لوحة تحكم العميل (Dash Custom)                    |
| FEAT-SEO-001        | تحسين محركات البحث (SEO) طبيعي للسيارات            | غير وظيفي                   | Backend API, Public Blade                                                        |
| FEAT-LANG-001       | دعم اللغة العربية كلغة أساسية (RTL)                | قيد                         | Public Blade, Flutter App, Dash Custom Panel, لوحة تحكم العميل (Dash Custom)    |
| FEAT-LANG-002       | تصميم يراعي إضافة لغة إنجليزية مستقبلاً            | غير وظيفي                   | Public Blade, Flutter App, Dash Custom Panel, لوحة تحكم العميل (Dash Custom)    |
| FEAT-RESP-001       | تصميم متجاوب مع جميع الشاشات                      | غير وظيفي                   | Public Blade, Flutter App, Dash Custom Panel, لوحة تحكم العميل (Dash Custom)    |
| FEAT-UXUI-001       | واجهة مستخدم سهلة وجذابة وتجربة مستخدم سلسة        | غير وظيفي                   | Public Blade, Flutter App, Dash Custom Panel, لوحة تحكم العميل (Dash Custom)    |
| FEAT-SEC-001        | أمان البيانات والمعاملات                           | غير وظيفي                   | Backend API                                                                      |
| FEAT-PERF-001       | أداء عالي وسرعة استجابة للنظام                     | غير وظيفي                   | Backend API, Public Blade, Flutter App                                           |

#### 4.1. الصفحات/الشاشات الرئيسية المتوقعة

##### 4.1.1. لوحة تحكم الإدارة (Dash Custom - بناءً على `dashboard.html` وملحقاته)

*   **الرئيسية (لوحة البيانات):** (إحصائيات عامة، رسوم بيانية، آخر النشاطات، تنبيهات).
*   **إدارة السيارات:**
    *   عرض جميع السيارات (مع فلاتر وبحث).
    *   إضافة/تعديل سيارة (سيتم تكييف الـ Stepper الموجود في `dashboard.html` ليتناسب مع تبويبات المعلومات الأساسية، التسعير والوصف، المواصفات الفنية، الميزات، والصور كما هو مفصل في `project-details.md` و `initial_input_summary_final_revised.md`).
    *   إدارة الماركات.
    *   إدارة الموديلات.
    *   إدارة ميزات السيارات.
    *   إدارة المواصفات الفنية الإضافية.
    *   إدارة الألوان.
    *   إدارة سنوات الصنع.
    *   إدارة أنواع ناقل الحركة.
    *   إدارة أنواع الوقود.
    *   إدارة صور السيارات.
*   **إدارة المبيعات:**
    *   إدارة الطلبات (جديدة، قيد المعالجة، مكتملة، ملغاة).
    *   إدارة طلبات التمويل.
*   **إدارة الخدمات:**
    *   إدارة فئات الخدمات.
    *   إدارة الخدمات.
    *   إدارة طلبات الخدمات.
*   **إدارة المستخدمين:**
    *   إدارة العملاء.
    *   إدارة الموظفين (مع الأدوار والصلاحيات).
    *   إدارة طلبات الشركات.
*   **إدارة المحتوى:**
    *   إدارة الصفحات الثابتة (من نحن، سياسة الخصوصية، الشروط والأحكام، الأسئلة الشائعة).
    *   إدارة محتوى الصفحة الرئيسية (بنرات، سيارات مميزة).
    *   إدارة العروض الترويجية.
*   **إعدادات النظام:**
    *   الإعدادات العامة (معلومات المعرض، SEO).
    *   إعدادات المدفوعات (بوابات الدفع).
    *   إعدادات الإشعارات (قوالب البريد/SMS).
    *   النسخ الاحتياطي والاستعادة.
*   **التقارير:** (تقارير المبيعات، العملاء، السيارات).

##### 4.1.2. لوحة تحكم العميل (Dash Custom - تصميم مشابه للوحة الإدارة ولكن بميزات العميل)

*   **الرئيسية (ملخص الحساب):** (معلومات ترحيبية، عدد الطلبات، المفضلة).
*   **طلباتي:** (عرض جميع الطلبات، تفاصيل الطلب، حالة الطلب، المستندات المطلوبة).
*   **طلبات التمويل:** (عرض طلبات التمويل الحالية وحالتها).
*   **رشح عميل:** (نموذج لإرسال توصية).
*   **المفضلة:** (عرض السيارات المحفوظة، إمكانية المقارنة).
*   **المعاملات المالية:** (سجل المعاملات، الفواتير والإيصالات).
*   **الإشعارات:** (مركز الإشعارات).
*   **الملف الشخصي:** (تعديل المعلومات الشخصية، العناوين).

##### 4.1.3. واجهة الموقع العامة (Public Blade Frontend)

*   الصفحة الرئيسية.
*   قسم السيارات الجديدة (قائمة، فلاتر).
*   صفحة تفاصيل السيارة.
*   قسم خدماتنا (قائمة الخدمات، نموذج طلب خدمة).
*   قسم مبيعات الشركات (معلومات، نموذج طلب).
*   قسم عروض السيارات (بنرات العروض).
*   صفحة تفاصيل العرض.
*   قسم اطلب سيارتك (خطوات الطلب).
*   صفحات شراء السيارة كاش وتمويل.
*   صفحات تسجيل الدخول وإنشاء حساب.
*   الصفحات الثابتة (من نحن، سياسة الخصوصية، شروط وأحكام، أسئلة شائعة).
*   صفحة التواصل.

##### 4.1.4. تطبيق Flutter

*   شاشة البداية (Splash Screen).
*   شاشات المصادقة (تسجيل دخول، إنشاء حساب، نسيت كلمة المرور).
*   الشاشة الرئيسية (عرض بنرات، سيارات مميزة، روابط سريعة).
*   قائمة السيارات (مع فلاتر وبحث).
*   شاشة تفاصيل السيارة.
*   شاشات عملية "اطلب سيارتك".
*   شاشات عملية الشراء (كاش وتمويل).
*   لوحة تحكم العميل المصغرة:
    *   طلباتي.
    *   المفضلة.
    *   الملف الشخصي.
    *   الإشعارات.
*   شاشة عرض الخدمات.
*   شاشة عرض العروض.
*   شاشة التواصل (معلومات الاتصال).

### 5. اقتراح هيكل موديولات رئيسي للمشروع (باستخدام `nwidart/laravel-modules`)

سيتم تقسيم المشروع إلى الموديولات التالية لتنظيم الكود وفصل الاهتمامات:

1.  **`Core` (MOD-CORE):**
    *   الوصف: يحتوي على الوظائف الأساسية المشتركة، مثل Traits، Helpers، Base Classes (Controllers, Models, Requests)، إعدادات عامة للنظام يتم استخدامها عبر الموديولات الأخرى.
    *   التفاعل: يخدم جميع الموديولات والواجهات.
2.  **`UserManagement` (MOD-USER-MGMT):**
    *   الوصف: مسؤول عن إدارة المستخدمين (عملاء، موظفين، مديرين)، المصادقة، الأدوار والصلاحيات، الملفات الشخصية.
    *   الوظائف الرئيسية: FEAT-USER-001 إلى FEAT-USER-010.
    *   التفاعل: يوفر API لتطبيق Flutter وواجهات Blade، ويدعم لوحات التحكم Dash (للعميل والإدارة).
3.  **`CarCatalog` (MOD-CAR-CATALOG):**
    *   الوصف: مسؤول عن إدارة كل ما يتعلق بالسيارات: الماركات، الموديلات، السنوات، الألوان، الميزات، المواصفات، الصور، وإدارة السيارات نفسها.
    *   الوظائف الرئيسية: FEAT-CAR-001 إلى FEAT-CAR-017.
    *   التفاعل: يوفر API لتطبيق Flutter وواجهات Blade لعرض السيارات، ويدعم لوحة تحكم الإدارة Dash لإدارة بيانات السيارات.
4.  **`OrderManagement` (MOD-ORDER-MGMT):**
    *   الوصف: مسؤول عن إدارة عمليات الشراء (كاش وتمويل)، طلبات الحجز، تتبع حالة الطلبات، إدارة المستندات المتعلقة بالطلبات.
    *   الوظائف الرئيسية: FEAT-ORDER-001 إلى FEAT-ORDER-009، FEAT-REQCAR-001.
    *   التفاعل: يوفر API لتطبيق Flutter وواجهات Blade لعمليات الشراء، ويدعم لوحات التحكم Dash (للعميل والإدارة) لتتبع وإدارة الطلبات.
5.  **`ServiceManagement` (MOD-SERVICE-MGMT):**
    *   الوصف: مسؤول عن إدارة الخدمات الإضافية، فئاتها، أسعارها، ومعالجة طلبات الخدمات.
    *   الوظائف الرئيسية: FEAT-SERVICE-001 إلى FEAT-SERVICE-005.
    *   التفاعل: يوفر واجهات Blade لعرض وطلب الخدمات، ويدعم لوحة تحكم الإدارة Dash لإدارة الخدمات وطلباتها.
6.  **`CorporateSales` (MOD-CORP-SALES):**
    *   الوصف: مسؤول عن إدارة طلبات السيارات بكميات كبيرة من الشركات.
    *   الوظائف الرئيسية: FEAT-CORP-001 إلى FEAT-CORP-003.
    *   التفاعل: يوفر واجهات Blade لتقديم طلبات الشركات، ويدعم لوحة تحكم الإدارة Dash لإدارة هذه الطلبات.
7.  **`PromotionManagement` (MOD-PROMO-MGMT):**
    *   الوصف: مسؤول عن إدارة العروض الترويجية للسيارات وربطها بالسيارات والبنوك (إذا لزم الأمر).
    *   الوظائف الرئيسية: FEAT-PROMO-001 إلى FEAT-PROMO-003.
    *   التفاعل: يوفر واجهات Blade لعرض العروض، ويدعم لوحة تحكم الإدارة Dash لإدارة العروض.
8.  **`Cms` (MOD-CMS):**
    *   الوصف: نظام إدارة المحتوى للصفحات الثابتة (من نحن، سياسة الخصوصية، الشروط والأحكام، الأسئلة الشائعة) ومحتوى الصفحة الرئيسية.
    *   الوظائف الرئيسية: FEAT-ADMIN-002.
    *   التفاعل: يوفر واجهات Blade لعرض المحتوى، ويدعم لوحة تحكم الإدارة Dash لتعديل المحتوى.
9.  **`Notification` (MOD-NOTIFICATION):**
    *   الوصف: مسؤول عن إرسال الإشعارات عبر القنوات المختلفة (بريد إلكتروني، SMS، إشعارات داخلية).
    *   الوظائف الرئيسية: FEAT-NOTIF-001.
    *   التفاعل: يخدم جميع الموديولات الأخرى التي تحتاج لإرسال إشعارات.
10. **`Dashboard` (MOD-DASHBOARD):**
    *   الوصف: هذا الموديول سيحتوي على المنطق الخاص بالـ Backend **وتكامل الواجهة الأمامية (Frontend Integration)** للوحات التحكم (الإدارة والعميل) المبنية بأصول Dash (HTML/CSS/JS). سيتضمن **طرق عرض Blade (Views)** التي تدمج أصول Dash، بالإضافة إلى Controllers، Requests، Resources، وربما Services التي تخدم واجهات Dash بشكل خاص، وتتفاعل مع الموديولات الأخرى لجلب البيانات وتنفيذ الإجراءات. **سيكون هذا الموديول مسؤولاً عن تحويل ملفات Dash الثابتة إلى واجهات ديناميكية متكاملة مع Laravel.**
    *   الوظائف الرئيسية: FEAT-DASH-001، FEAT-ADMIN-001، FEAT-ADMIN-003، FEAT-ADMIN-004، FEAT-ADMIN-005.
    *   التفاعل: يخدم لوحات التحكم Dash المخصصة (المبنية من أصول Dash وملفات Blade) بشكل مباشر ويتفاعل مع باقي الموديولات (UserManagement, CarCatalog, OrderManagement إلخ) لجلب البيانات ومعالجتها.
11. **`Api` (MOD-API):**
    *   الوصف: هذا الموديول سيكون مسؤولاً عن توفير نقاط نهاية API (Endpoints) لتطبيق Flutter وأي واجهات أمامية أخرى قد تحتاج إلى استهلاك البيانات بشكل منفصل عن Blade views التقليدية.
    *   الوظائف الرئيسية: FEAT-FLUTTER-001 وجزء من وظائف الموديولات الأخرى التي تحتاج عرضها عبر API.
    *   التفاعل: يخدم تطبيق Flutter بشكل أساسي، ويتفاعل مع الموديولات الأخرى (UserManagement, CarCatalog, OrderManagement إلخ) لجلب البيانات.

### 6. تأكيد استخدام التقنيات الأساسية

*   **إطار عمل Backend:** Laravel 10.
*   **لوحات التحكم (إدارة وعميل):** سيتم بناؤها كـ Custom Admin Panel عن طريق **دمج ملفات `index.html`, `style.css`, `script.js` المتوفرة** (والتي ستكون في مجلد `Dash` في جذر المشروع) **داخل بنية Laravel Blade views**. سيتم تحليل وتقسيم ملفات Dash HTML إلى مكونات Blade قابلة لإعادة الاستخدام لإنشاء واجهات ديناميكية متكاملة.
*   **تنظيم الموديولات:** سيتم استخدام حزمة `nwidart/laravel-modules`.
*   **تطبيق الموبايل:** Flutter.
*   **واجهة الموقع العامة:** Laravel Blade.

### 7. اقتراح حزم Laravel متوافقة

| الحزمة                                  | التبرير                                                                                                                                  | دعم التكامل مع الواجهات                                                                                                |
| :-------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------- |
| `nwidart/laravel-modules`              | لتنظيم الكود إلى موديولات منفصلة كما هو مطلوب.                                                                                             | أساسي لهيكلة الـ Backend لدعم جميع الواجهات.                                                                             |
| `spatie/laravel-permission`             | لإدارة الأدوار والصلاحيات للموظفين ومديري النظام بشكل فعال.                                                                                | يضمن أن لوحة تحكم Dash تعرض الوظائف المناسبة بناءً على صلاحيات المستخدم المسجل دخوله.                                    |
| `spatie/laravel-medialibrary`           | لإدارة رفع وتخزين صور السيارات والمستندات المرفقة بكفاءة وسهولة.                                                                           | يسهل التعامل مع الصور في الـ Backend لعرضها في جميع الواجهات (Dash, Blade, Flutter).                                      |
| `laravel/sanctum`                       | لتوفير نظام مصادقة بسيط وآمن لواجهات SPA (إذا تم اعتماد هذا النهج لأجزاء من لوحة التحكم Dash) ولتطبيق Flutter. **مصادقة الويب التقليدية (Session-based) ستكون الأساس للوحة التحكم Dash المبنية بـ Blade، ويمكن استخدام Sanctum إذا كانت هناك تفاعلات AJAX كثيفة تتطلب حماية API أو لتأمين API تطبيق Flutter.** | أساسي لتأمين الـ APIs التي يستهلكها تطبيق Flutter، وربما أجزاء من لوحة التحكم Dash إذا اعتمدت على تفاعلات API مكثفة.     |
| `barryvdh/laravel-dompdf` (أو `spatie/laravel-pdf`) | لإنشاء ملفات PDF لمواصفات السيارات.                                                                                                 | يمكن للـ Backend توليد PDF وعرضه أو إتاحته للتحميل عبر Dash, Blade, أو Flutter.                                          |
| `laravel/telescope`                     | لتتبع واكتشاف الأخطاء (Debugging) أثناء عملية التطوير بشكل فعال.                                                                           | أداة تطوير للـ Backend، لا تؤثر مباشرة على الواجهات ولكن تحسن جودة المنتج النهائي.                                        |
| `owen-it/laravel-auditing`              | (اختياري، للنظر فيه) لتسجيل التغييرات الهامة على البيانات (مثل تغيير أسعار السيارات، تحديث الطلبات) لزيادة الأمان والمتابعة.                | يؤثر على الـ Backend، ويمكن عرض سجلات التدقيق في لوحة تحكم Dash إذا لزم الأمر.                                          |
| `maatwebsite/excel`                     | (اختياري، للنظر فيه إذا كان حجم الاستيراد/التصدير كبيرًا ومعقدًا) لاستيراد وتصدير بيانات السيارات من/إلى ملفات Excel/CSV.                 | يخدم وظيفة استيراد/تصدير البيانات في لوحة تحكم Dash.                                                                     |
| `intervention/image`                    | لمعالجة الصور (تغيير الحجم، القص، إضافة علامات مائية) عند الرفع.                                                                         | يضمن أن الصور المعروضة في جميع الواجهات محسّنة وذات جودة مناسبة.                                                        |

### 8. اقتراح مكتبات Flutter (إذا انطبق)

*   `http` أو `dio`: لإجراء طلبات الشبكة إلى Laravel API.
*   `provider` أو `flutter_bloc` أو `riverpod`: لإدارة الحالة (State Management).
*   `shared_preferences`: لتخزين البيانات البسيطة محليًا (مثل token المصادقة، تفضيلات المستخدم).
*   `image_picker`: لاختيار الصور من الجهاز لرفعها (مثلاً لصورة الملف الشخصي أو مستندات).
*   `file_picker`: لاختيار الملفات (PDFs, Docs) لرفعها.
*   `url_launcher`: لفتح الروابط الخارجية (مثل روابط واتساب أو الاتصال).
*   `flutter_html`: (إذا كان سيتم عرض محتوى HTML معقد من CMS).
*   `permission_handler`: لطلب وإدارة الأذونات (مثل الوصول للكاميرا، التخزين).

### 9. النظر في "الأفكار الإضافية خارج الصندوق"

*   **أتمتة التوليد:** يمكن استخدام Artisan commands مخصصة لتوليد بعض الأكواد المتكررة داخل الموديولات.
*   **نظام إشعارات متقدم:** موديول `Notification` المقترح سيغطي هذا، مع إمكانية تخصيص قوالب الإشعارات من لوحة التحكم Dash.
*   **تحليل سلوك المستخدم:** يمكن دمج أدوات تحليل مثل Google Analytics، أو بناء نظام بسيط لتتبع مشاهدات السيارات والصفحات الأكثر زيارة (يمكن عرضها كتقارير في لوحة Dash).
*   **تدقيق التغييرات:** حزمة `owen-it/laravel-auditing` المقترحة تخدم هذا الغرض.
*   **بحث شامل:** يمكن استخدام Laravel Scout مع Algolia أو MeiliSearch لتوفير بحث متقدم وسريع في السيارات والمحتوى، خاصة إذا كان حجم البيانات كبيرًا.

### 10. القيود والاعتبارات الخاصة

1.  **نطاق المنتجات:** السيارات الجديدة فقط حاليًا، مع تصميم يراعي التوسع للمستعملة.
2.  **متطلبات اللغة:** العربية أساسية (RTL)، مع تصميم يراعي الإنجليزية مستقبلاً.
3.  **متطلبات التمويل:** جمع بيانات فقط، المعالجة يدوية.
4.  **إدارة المستندات:** نظام آمن لرفع وإدارة المستندات.
5.  **تكامل الاتصالات:** دعم واتساب والاتصال المباشر.
6.  **سهولة الاستخدام:** واجهات بديهية للعملاء والإدارة (خاصة في لوحة Dash المخصصة).
7.  **المدفوعات:** دعم مبلغ الحجز أونلاين، والباقي في المعرض.
8.  **لا يوجد مخزون للسيارات:** لا حاجة لنظام إدارة مخزون.
9.  **بناء لوحة التحكم Dash:** الاعتماد على الأصول `dashboard.html`, `style.css`, `script.js` **(التي ستكون في مجلد `Dash` في جذر المشروع)** يتطلب تحليلًا دقيقًا لهذه الملفات لضمان توافقها مع Laravel Blade وإمكانية تقسيمها إلى مكونات Blade قابلة لإعادة الاستخدام. **يجب مراعاة أن `script.js` قد يحتاج إلى تعديلات ليعمل بشكل صحيح ضمن بيئة Laravel ومع البيانات الديناميكية (مثل تهيئة الرسوم البيانية ببيانات من الـ Backend).**

### 11. الافتراضات الأولية

1.  **مصادر السيارات:** الإضافة يدوية من الإدارة. (مبرر: مذكور في `initial_input_summary`)
2.  **تكامل التمويل:** جمع بيانات يدوي، لا ربط آلي. (مبرر: مذكور في `initial_input_summary`)
3.  **إتمام المعاملات:** الحجز أونلاين، الإتمام في المعرض. (مبرر: مذكور في `initial_input_summary`)
4.  **الدفع المرحلي:** مبلغ حجز أولي، الباقي في المعرض. (مبرر: مذكور في `initial_input_summary`)
5.  **خدمات ما بعد البيع:** المعرض يقدمها كما هو مذكور. (مبرر: مذكور في `initial_input_summary`)
6.  **بوابة الدفع:** سيتم اختيار بوابة دفع سعودية سهلة التكامل. (مبرر: مذكور في `initial_input_summary`)
7.  **بوابة الرسائل SMS:** سيتم اختيار بوابة رسائل سعودية سهلة التكامل. (مبرر: مذكور في `initial_input_summary`)
8.  **هوية المعرض البصرية:** سيتوفر شعار وألوان العلامة التجارية لتوجيه تصميم الواجهات (خاصة Public Blade و Flutter). (افتراض ضروري للتصميم)
9.  **محتوى الصفحات الثابتة:** سيتم توفير المحتوى الأولي لهذه الصفحات من قبل العميل. (افتراض لتعبئة CMS)

### 12. المخاطر الأولية المحتملة (بالإضافة للمخاطر والفجوات المذكورة في سجل التعديلات)

1.  **RISK-DASH-INTEG-001:** تعقيد دمج أصول لوحة التحكم Dash (HTML/CSS/JS) مع Laravel Blade وتحويلها إلى مكونات ديناميكية قابلة لإعادة الاستخدام قد يستغرق وقتًا أطول من المتوقع.
2.  **RISK-DATA-SEC-001:** ضمان أمان بيانات العملاء المالية والشخصية يتطلب تطبيق معايير أمان صارمة وتشفير مناسب.
3.  **RISK-UX-MULTI-STEP-001:** عمليات الشراء والتمويل متعددة الخطوات قد تكون مربكة للمستخدم إذا لم يتم تصميمها بعناية فائقة لضمان تجربة مستخدم سلسة.
4.  **RISK-FLUTTER-SYNC-001:** ضمان التزامن الكامل للبيانات والوظائف بين تطبيق Flutter والموقع الإلكتروني يتطلب تخطيطًا دقيقًا للـ API.
5.  **RISK-THIRD-PARTY-001:** الاعتماد على بوابات دفع ورسائل SMS خارجية قد يؤدي إلى مشاكل إذا كانت هناك صعوبات في التكامل أو انقطاع في خدماتها.
6.  **RISK-DASH-JS-COMPLEXITY-001:** ملف `script.js` المرفق مع `dashboard.html` قد يحتوي على منطق JavaScript معقد. تكييفه ليعمل بسلاسة مع مكونات Blade الديناميكية والبيانات من Laravel قد يكون معقدًا.
7.  **RISK-DASH-STYLING-OVERRIDE-001:** ملف `style.css` قد يحتوي على أنماط عامة قد تتعارض مع أنماط Laravel أو حزم أخرى.
8.  **GAP-DASH-RESPONSIVENESS-TESTING-001 (فجوة):** الحاجة لاختبارات تجاوب مكثفة للوحة التحكم Dash المدمجة.
9.  **RISK-FLUTTER-FEATURE-PARITY-001:** تحدي ضمان التكافؤ في الميزات وتجربة المستخدم بين Flutter والموقع.
10. **GAP-USER-ROLES-DETAIL-001 (فجوة):** عدم تحديد تفاصيل الأدوار والصلاحيات للموظفين بشكل كامل.

--- نهاية المستند 00-FR.md ---