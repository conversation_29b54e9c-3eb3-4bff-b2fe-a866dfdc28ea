@extends('dashboard::layouts.admin_layout')

@section('title', 'إضافة سيارة جديدة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bs-stepper/dist/css/bs-stepper.min.css">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إضافة سيارة جديدة',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'السيارات', 'url' => route('admin.cars.index')],
            ['name' => 'إضافة جديدة', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.cars.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>'
    ])

    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        إضافة سيارة جديدة
                    </h5>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>يرجى تصحيح الأخطاء التالية:</h6>
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.cars.store') }}" method="POST" enctype="multipart/form-data" id="carForm">
                        @csrf

                        <!-- Stepper -->
                        <div class="bs-stepper">
                            <div class="bs-stepper-header" role="tablist">
                                <div class="step" data-target="#basic-info-part">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="basic-info-part" id="basic-info-part-trigger">
                                        <span class="bs-stepper-circle"><i class="fas fa-info-circle"></i></span>
                                        <span class="bs-stepper-label">البيانات الأساسية</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#tech-specs-part">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="tech-specs-part" id="tech-specs-part-trigger">
                                        <span class="bs-stepper-circle"><i class="fas fa-cogs"></i></span>
                                        <span class="bs-stepper-label">المواصفات الفنية</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#features-part">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="features-part" id="features-part-trigger">
                                        <span class="bs-stepper-circle"><i class="fas fa-list-ul"></i></span>
                                        <span class="bs-stepper-label">الميزات</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#images-part">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="images-part" id="images-part-trigger">
                                        <span class="bs-stepper-circle"><i class="fas fa-images"></i></span>
                                        <span class="bs-stepper-label">الصور والفيديو</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#price-status-part">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="price-status-part" id="price-status-part-trigger">
                                        <span class="bs-stepper-circle"><i class="fas fa-tag"></i></span>
                                        <span class="bs-stepper-label">السعر والحالة</span>
                                    </button>
                                </div>
                            </div>

                            <div class="bs-stepper-content">
                                <!-- Basic Info Part -->
                                @include('car_catalog::admin.cars.partials.stepper_basic_info')

                                <!-- Technical Specs Part -->
                                @include('car_catalog::admin.cars.partials.stepper_tech_specs')

                                <!-- Features Part -->
                                @include('car_catalog::admin.cars.partials.stepper_features')

                                <!-- Images Part -->
                                @include('car_catalog::admin.cars.partials.stepper_images')

                                <!-- Price and Status Part -->
                                @include('car_catalog::admin.cars.partials.stepper_price_status')
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bs-stepper/dist/css/bs-stepper.min.css">
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/bs-stepper/dist/js/bs-stepper.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    // تهيئة Stepper
    var stepperNode = document.querySelector('.bs-stepper');
    if (stepperNode) {
        window.stepper = new Stepper(stepperNode, {
            linear: false,
            animation: true
        });
    }

    // إضافة مراقبة إرسال الـ form للتشخيص
    const carForm = document.getElementById('carForm');
    if (carForm) {
        carForm.addEventListener('submit', function(e) {
            console.log('Form submission started...');
            console.log('Form action:', this.action);
            console.log('Form method:', this.method);
            console.log('Form enctype:', this.enctype);

            // التحقق من وجود CSRF token
            const csrfToken = document.querySelector('input[name="_token"]');
            console.log('CSRF token found:', csrfToken ? 'Yes' : 'No');
            if (csrfToken) {
                console.log('CSRF token value:', csrfToken.value);
            }

            // التحقق من الحقول المطلوبة
            const requiredFields = [
                'title', 'brand_id', 'car_model_id', 'manufacturing_year_id',
                'main_color_id', 'body_type_id', 'transmission_type_id',
                'fuel_type_id', 'condition', 'price', 'currency'
            ];
            let missingFields = [];

            requiredFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (!field || !field.value.trim()) {
                    missingFields.push(fieldName);
                }
            });

            if (missingFields.length > 0) {
                console.error('Missing required fields:', missingFields);
                alert('يرجى ملء جميع الحقول المطلوبة: ' + missingFields.join(', '));
                e.preventDefault();
                return false;
            }

            console.log('Form validation passed, submitting...');

            // طباعة جميع بيانات الـ form للتشخيص
            const formData = new FormData(this);
            console.log('Form data:');
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
            }

            // إضافة loading state للزر
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';

                // إعادة تفعيل الزر بعد 10 ثوان في حالة عدم الاستجابة
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ السيارة';
                }, 10000);
            }

            // إضافة مراقبة للتأكد من أن الـ form يتم إرساله فعلاً
            console.log('Form is about to be submitted...');

            // مراقبة ما بعد الإرسال
            setTimeout(() => {
                console.log('Form submission timeout - checking if still on same page...');
                console.log('Current URL:', window.location.href);
            }, 2000);
        });
    }

    // تحميل الموديلات عند اختيار الماركة
    const brandSelect = document.getElementById('brand_id');
    const modelSelect = document.getElementById('car_model_id');

    if (brandSelect && modelSelect) {
        // تعطيل قائمة الموديلات في البداية
        modelSelect.disabled = true;

        brandSelect.addEventListener('change', function() {
            const brandId = this.value;

            // إعادة تعيين قائمة الموديلات
            modelSelect.innerHTML = '<option value="">اختر الموديل</option>';
            modelSelect.disabled = true;

            if (brandId) {
                const url = `{{ url('admin/brands') }}/${brandId}/models`;
                console.log('Fetching models from URL:', url);

                // جلب الموديلات عبر AJAX
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    credentials: 'same-origin'
                })
                    .then(response => {
                        console.log('Response status:', response.status);
                        console.log('Response headers:', response.headers);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Response data:', data);
                        if (data.success && Array.isArray(data.models)) {
                            modelSelect.disabled = false;
                            data.models.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.id;
                                option.textContent = model.name;
                                modelSelect.appendChild(option);
                            });
                            console.log(`تم تحميل ${data.models.length} موديل بنجاح`);
                        } else {
                            console.error('Invalid response format:', data);
                            modelSelect.innerHTML = '<option value="">خطأ في تحميل الموديلات</option>';
                            modelSelect.disabled = false;
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching models:', error);
                        alert('حدث خطأ أثناء جلب الموديلات: ' + error.message);
                    });
            }
        });
    }

    // معالجة رفع الصور
    const imageInput = document.getElementById('car_images');
    const imagePreview = document.getElementById('image-preview');

    if (imageInput && imagePreview) {
        imageInput.addEventListener('change', function() {
            imagePreview.innerHTML = '';

            Array.from(this.files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const div = document.createElement('div');
                        div.className = 'col-md-3 mb-3';
                        div.innerHTML = `
                            <div class="card">
                                <img src="${e.target.result}" class="card-img-top" style="height: 150px; object-fit: cover;">
                                <div class="card-body p-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="main_image_index" value="${index}" ${index === 0 ? 'checked' : ''}>
                                        <label class="form-check-label small">صورة رئيسية</label>
                                    </div>
                                </div>
                            </div>
                        `;
                        imagePreview.appendChild(div);
                    };
                    reader.readAsDataURL(file);
                }
            });
        });
    }
});
</script>
@endpush
