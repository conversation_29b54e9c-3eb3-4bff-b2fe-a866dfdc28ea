@extends('site.layouts.site_layout')

@section('title', $page_title . ' - موتور لاين')
@section('meta_description', $page_title)

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
            <div class="placeholder-content">
                <div class="placeholder-icon mb-4">
                    <i class="fas fa-tools fa-5x text-muted"></i>
                </div>
                
                <h1 class="display-4 mb-3">{{ $page_title }}</h1>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    هذه الصفحة قيد التطوير حالياً. سيتم إطلاقها قريباً مع المزيد من الميزات الرائعة.
                </div>
                
                <p class="lead text-muted mb-4">
                    نعمل بجد لتوفير أفضل تجربة لك. تابعنا للحصول على آخر التحديثات.
                </p>
                
                <div class="placeholder-actions">
                    <a href="{{ route('site.home') }}" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    
                    <a href="{{ route('site.cars.index') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-car me-2"></i>
                        تصفح السيارات
                    </a>
                </div>
                
                <div class="mt-5">
                    <h5 class="mb-3">في الوقت الحالي يمكنك:</h5>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="feature-card p-3 border rounded">
                                <i class="fas fa-search fa-2x text-primary mb-2"></i>
                                <h6>تصفح السيارات</h6>
                                <p class="small text-muted">اكتشف مجموعتنا الواسعة من السيارات</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="feature-card p-3 border rounded">
                                <i class="fas fa-filter fa-2x text-success mb-2"></i>
                                <h6>فلترة النتائج</h6>
                                <p class="small text-muted">استخدم الفلاتر للعثور على السيارة المثالية</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="feature-card p-3 border rounded">
                                <i class="fas fa-heart fa-2x text-danger mb-2"></i>
                                <h6>حفظ المفضلة</h6>
                                <p class="small text-muted">احفظ السيارات التي تعجبك</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="contact-info mt-5 p-4 bg-light rounded">
                    <h6 class="mb-3">تحتاج مساعدة؟</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1">
                                <i class="fas fa-phone text-primary me-2"></i>
                                <strong>الهاتف:</strong> +966 11 123 4567
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <strong>البريد:</strong> <EMAIL>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.placeholder-content {
    padding: 2rem 0;
}

.placeholder-icon {
    opacity: 0.6;
}

.feature-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.contact-info {
    border: 1px solid #e9ecef;
}

@media (max-width: 768px) {
    .placeholder-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .placeholder-actions .btn:last-child {
        margin-bottom: 0;
    }
}
</style>
@endsection
