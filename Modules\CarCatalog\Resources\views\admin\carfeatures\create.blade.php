@extends('dashboard::layouts.admin_layout')

@section('title', 'إضافة ميزة سيارة جديدة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إضافة ميزة سيارة جديدة',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'ميزات السيارات', 'url' => route('admin.car-features.index')],
            ['name' => 'إضافة جديد', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.car-features.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>'
    ])

    {{-- نموذج إضافة ميزة سيارة --}}
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus text-primary"></i>
                        إضافة ميزة سيارة جديدة
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.car-features.store') }}" method="POST">
                        @csrf

                        @include('carcatalog::admin.carfeatures._form')

                        {{-- أزرار الإجراءات --}}
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.car-features.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الميزة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
