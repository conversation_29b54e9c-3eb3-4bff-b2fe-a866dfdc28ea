@extends('dashboard::layouts.admin_layout')

@section('title', 'تفاصيل نوع الوقود')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid">
    {{-- عنوان الصفحة --}}
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">تفاصيل نوع الوقود: {{ $fueltype->name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.fuel-types.index') }}">أنواع الوقود</a></li>
                        <li class="breadcrumb-item active">التفاصيل</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    {{-- تفاصيل نوع الوقود --}}
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">معلومات نوع الوقود</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الاسم:</label>
                                <p class="text-muted">{{ $fueltype->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p>
                                    <span class="badge rounded-pill bg-{{ $fueltype->status ? 'success' : 'danger' }}">
                                        {{ $fueltype->status ? 'نشط' : 'غير نشط' }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    @if($fueltype->description)
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">الوصف:</label>
                                    <p class="text-muted">{{ $fueltype->description }}</p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            {{-- إحصائيات --}}
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">الإحصائيات</h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="fas fa-car fa-3x text-primary mb-2"></i>
                            <h4 class="mb-1">{{ $fueltype->cars_count }}</h4>
                            <p class="text-muted mb-0">سيارة مرتبطة</p>
                        </div>
                    </div>
                </div>
            </div>
            
            {{-- الإجراءات --}}
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">الإجراءات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.fuel-types.edit', $fueltype) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="{{ route('admin.fuel-types.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        @if($fueltype->cars_count == 0)
                            <form action="{{ route('admin.fuel-types.destroy', $fueltype) }}" 
                                  method="POST" 
                                  onsubmit="return confirm('هل أنت متأكد من حذف نوع الوقود هذا؟')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </form>
                        @else
                            <button type="button" class="btn btn-danger" disabled title="لا يمكن الحذف - مرتبط بسيارات">
                                <i class="fas fa-trash"></i> حذف (غير متاح)
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
