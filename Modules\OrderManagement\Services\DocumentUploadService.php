<?php

namespace Modules\OrderManagement\Services;

use Modules\OrderManagement\Models\Order;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Exception;

/**
 * خدمة رفع المستندات
 *
 * تتولى معالجة وتخزين المستندات المرفوعة للطلبات
 * باستخدام spatie/laravel-medialibrary
 * بناءً على MOD-ORDER-MGMT-FEAT-006 في REQ-FR.md
 */
class DocumentUploadService
{
    /**
     * أنواع المستندات المدعومة
     */
    private const SUPPORTED_DOCUMENT_TYPES = [
        'national_id_front' => 'صورة الهوية الوطنية (الوجه الأمامي)',
        'national_id_back' => 'صورة الهوية الوطنية (الوجه الخلفي)',
        'driving_license' => 'رخصة القيادة',
        'salary_certificate' => 'شهادة الراتب',
        'bank_statement' => 'كشف حساب بنكي',
        'additional_documents' => 'مستندات إضافية'
    ];

    /**
     * الحد الأقصى لحجم الملف (بالبايت)
     */
    private const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB

    /**
     * أنواع الملفات المدعومة
     */
    private const ALLOWED_MIME_TYPES = [
        'image/jpeg',
        'image/png',
        'image/jpg',
        'image/webp',
        'application/pdf'
    ];

    /**
     * رفع مستند واحد للطلب
     */
    public function uploadDocument(Order $order, UploadedFile $file, string $documentType, string $description = null): bool
    {
        try {
            // التحقق من صحة نوع المستند
            if (!$this->isValidDocumentType($documentType)) {
                throw new Exception("نوع المستند غير مدعوم: {$documentType}");
            }

            // التحقق من صحة الملف
            $this->validateFile($file);

            // رفع الملف باستخدام spatie/laravel-medialibrary
            $mediaItem = $order->addMedia($file)
                              ->usingFileName($this->generateSecureFileName($file))
                              ->usingName($description ?: $this->getDocumentTypeName($documentType))
                              ->toMediaCollection($documentType);

            Log::info('تم رفع مستند بنجاح', [
                'order_id' => $order->id,
                'document_type' => $documentType,
                'file_name' => $mediaItem->file_name,
                'file_size' => $mediaItem->size
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('خطأ في رفع المستند', [
                'order_id' => $order->id,
                'document_type' => $documentType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * رفع مستندات متعددة للطلب
     */
    public function uploadMultipleDocuments(Order $order, array $files, array $documentTypes, array $descriptions = []): array
    {
        $results = [];
        $errors = [];

        foreach ($files as $index => $file) {
            if (!$file instanceof UploadedFile) {
                continue;
            }

            $documentType = $documentTypes[$index] ?? 'additional_documents';
            $description = $descriptions[$index] ?? null;

            try {
                $this->uploadDocument($order, $file, $documentType, $description);
                $results[] = [
                    'success' => true,
                    'document_type' => $documentType,
                    'file_name' => $file->getClientOriginalName()
                ];
            } catch (Exception $e) {
                $errors[] = [
                    'success' => false,
                    'document_type' => $documentType,
                    'file_name' => $file->getClientOriginalName(),
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'successful_uploads' => $results,
            'failed_uploads' => $errors,
            'total_processed' => count($files),
            'successful_count' => count($results),
            'failed_count' => count($errors)
        ];
    }

    /**
     * رفع مستندات من بيانات الجلسة
     */
    public function uploadDocumentsFromSession(Order $order, array $sessionData): bool
    {
        try {
            $uploadedCount = 0;

            // رفع المستندات الأساسية
            $requiredDocuments = ['national_id_front', 'national_id_back', 'driving_license'];

            foreach ($requiredDocuments as $docType) {
                if (isset($sessionData[$docType]) && $sessionData[$docType] instanceof UploadedFile) {
                    $this->uploadDocument($order, $sessionData[$docType], $docType);
                    $uploadedCount++;
                }
            }

            // رفع المستندات الإضافية
            if (isset($sessionData['additional_documents']) && is_array($sessionData['additional_documents'])) {
                $descriptions = $sessionData['additional_documents_descriptions'] ?? [];

                foreach ($sessionData['additional_documents'] as $index => $file) {
                    if ($file instanceof UploadedFile) {
                        $description = $descriptions[$index] ?? "مستند إضافي " . ($index + 1);
                        $this->uploadDocument($order, $file, 'additional_documents', $description);
                        $uploadedCount++;
                    }
                }
            }

            Log::info('تم رفع مستندات الطلب من الجلسة', [
                'order_id' => $order->id,
                'uploaded_count' => $uploadedCount
            ]);

            return $uploadedCount > 0;

        } catch (Exception $e) {
            Log::error('خطأ في رفع مستندات الطلب من الجلسة', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * التحقق من صحة الملف
     */
    private function validateFile(UploadedFile $file): void
    {
        // التحقق من صحة الملف
        if (!$file->isValid()) {
            throw new Exception('الملف المرفوع تالف أو غير صالح');
        }

        // التحقق من حجم الملف
        if ($file->getSize() > self::MAX_FILE_SIZE) {
            throw new Exception('حجم الملف يجب ألا يزيد عن 2 ميجابايت');
        }

        // التحقق من نوع الملف
        if (!in_array($file->getMimeType(), self::ALLOWED_MIME_TYPES)) {
            throw new Exception('نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, JPG, WEBP, PDF');
        }
    }

    /**
     * التحقق من صحة نوع المستند
     */
    private function isValidDocumentType(string $documentType): bool
    {
        return array_key_exists($documentType, self::SUPPORTED_DOCUMENT_TYPES);
    }

    /**
     * الحصول على اسم نوع المستند
     */
    private function getDocumentTypeName(string $documentType): string
    {
        return self::SUPPORTED_DOCUMENT_TYPES[$documentType] ?? $documentType;
    }

    /**
     * توليد اسم ملف آمن
     */
    private function generateSecureFileName(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('Y-m-d_H-i-s');
        $randomString = \Str::random(8);

        return "doc_{$timestamp}_{$randomString}.{$extension}";
    }

    /**
     * حذف مستند من الطلب
     */
    public function deleteDocument(Order $order, string $documentType, int $mediaId = null): bool
    {
        try {
            if ($mediaId) {
                $mediaItem = $order->getMedia($documentType)->find($mediaId);
                if ($mediaItem) {
                    $mediaItem->delete();
                    return true;
                }
            } else {
                // حذف جميع مستندات النوع المحدد
                $order->clearMediaCollection($documentType);
                return true;
            }

            return false;

        } catch (Exception $e) {
            Log::error('خطأ في حذف المستند', [
                'order_id' => $order->id,
                'document_type' => $documentType,
                'media_id' => $mediaId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * الحصول على قائمة مستندات الطلب
     */
    public function getOrderDocuments(Order $order): array
    {
        $documents = [];

        foreach (self::SUPPORTED_DOCUMENT_TYPES as $type => $name) {
            $mediaItems = $order->getMedia($type);

            if ($mediaItems->isNotEmpty()) {
                $documents[$type] = [
                    'name' => $name,
                    'files' => $mediaItems->map(function ($item) {
                        return [
                            'id' => $item->id,
                            'name' => $item->name,
                            'file_name' => $item->file_name,
                            'size' => $item->human_readable_size,
                            'mime_type' => $item->mime_type,
                            'url' => $item->getUrl(),
                            'created_at' => $item->created_at
                        ];
                    })->toArray()
                ];
            }
        }

        return $documents;
    }

    /**
     * رفع مستند واحد من Request مباشرة
     */
    public function uploadSingleDocumentFromRequest(Order $order, string $fileInputName, string $documentType, string $description = null): bool
    {
        try {
            $file = request()->file($fileInputName);

            if (!$file) {
                throw new Exception('لم يتم العثور على الملف في الطلب');
            }

            return $this->uploadDocument($order, $file, $documentType, $description);

        } catch (Exception $e) {
            Log::error('خطأ في رفع المستند من Request', [
                'order_id' => $order->id,
                'file_input_name' => $fileInputName,
                'document_type' => $documentType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * التحقق من وجود مستند معين للطلب
     */
    public function hasDocument(Order $order, string $documentType): bool
    {
        return $order->getMedia($documentType)->isNotEmpty();
    }

    /**
     * الحصول على عدد المستندات لنوع معين
     */
    public function getDocumentCount(Order $order, string $documentType): int
    {
        return $order->getMedia($documentType)->count();
    }

    /**
     * الحصول على جميع أنواع المستندات المدعومة
     */
    public function getSupportedDocumentTypes(): array
    {
        return self::SUPPORTED_DOCUMENT_TYPES;
    }

    /**
     * الحصول على الحد الأقصى لحجم الملف
     */
    public function getMaxFileSize(): int
    {
        return self::MAX_FILE_SIZE;
    }

    /**
     * الحصول على أنواع الملفات المدعومة
     */
    public function getAllowedMimeTypes(): array
    {
        return self::ALLOWED_MIME_TYPES;
    }

    /**
     * حذف مستند بواسطة معرف الوسائط فقط
     */
    public function deleteDocumentById(Order $order, int $mediaId): bool
    {
        try {
            // البحث عن المستند في جميع المجموعات
            $mediaItem = $order->getMedia()->where('id', $mediaId)->first();

            if (!$mediaItem) {
                throw new Exception('المستند غير موجود');
            }

            // حذف المستند
            $documentType = $mediaItem->collection_name;
            $fileName = $mediaItem->file_name;

            $mediaItem->delete();

            Log::info('تم حذف مستند بنجاح بواسطة المعرف', [
                'order_id' => $order->id,
                'media_id' => $mediaId,
                'document_type' => $documentType,
                'file_name' => $fileName
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('خطأ في حذف المستند بواسطة المعرف', [
                'order_id' => $order->id,
                'media_id' => $mediaId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * التحقق من صحة نوع المستند وإرجاع رسالة خطأ مفصلة
     */
    public function validateDocumentTypeWithMessage(string $documentType): array
    {
        if (!$this->isValidDocumentType($documentType)) {
            return [
                'valid' => false,
                'message' => "نوع المستند '{$documentType}' غير مدعوم. الأنواع المدعومة: " . implode(', ', array_keys(self::SUPPORTED_DOCUMENT_TYPES))
            ];
        }

        return [
            'valid' => true,
            'message' => 'نوع المستند صحيح'
        ];
    }

    /**
     * التحقق من صحة الملف وإرجاع رسالة خطأ مفصلة
     */
    public function validateFileWithMessage(UploadedFile $file): array
    {
        try {
            $this->validateFile($file);
            return [
                'valid' => true,
                'message' => 'الملف صحيح'
            ];
        } catch (Exception $e) {
            return [
                'valid' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
