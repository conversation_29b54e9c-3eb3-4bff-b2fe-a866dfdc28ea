@extends('dashboard::layouts.admin_layout')

@section('title', 'إضافة نوع هيكل سيارة جديد')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إضافة نوع هيكل سيارة جديد',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'أنواع هياكل السيارات', 'url' => route('admin.body-types.index')],
            ['name' => 'إضافة جديد', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.body-types.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>'
    ])

    {{-- نموذج إضافة نوع هيكل سيارة جديد --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">بيانات نوع هيكل السيارة</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.body-types.store') }}" method="POST">
                @csrf

                @include('carcatalog::admin.bodytypes._form')

                <div class="mt-4 d-flex justify-content-end">
                    <button type="submit" class="btn btn-success me-2">حفظ نوع هيكل السيارة</button>
                    <a href="{{ route('admin.body-types.index') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
