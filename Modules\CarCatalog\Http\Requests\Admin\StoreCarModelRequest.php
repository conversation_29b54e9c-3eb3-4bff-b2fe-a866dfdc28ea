<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تخزين موديل سيارة جديد.
 *
 * يتحقق هذا الطلب من صحة بيانات إضافة موديل سيارة جديد
 */
class StoreCarModelRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'brand_id' => 'required|exists:brands,id',
            'name'     => [
                'required',
                'string',
                'max:100',
                'unique:car_models,name,NULL,id,brand_id,' . $this->input('brand_id'),
            ],
            'status' => 'required|boolean',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة للقواعد المحددة.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'brand_id.required' => 'الماركة مطلوبة',
            'brand_id.exists'   => 'الماركة المحددة غير موجودة',
            'name.required'     => 'اسم الموديل مطلوب',
            'name.string'       => 'اسم الموديل يجب أن يكون نصًا',
            'name.max'          => 'اسم الموديل يجب ألا يتجاوز 100 حرف',
            'name.unique'       => 'اسم الموديل موجود بالفعل لهذه الماركة',
            'status.required'   => 'حالة الموديل مطلوبة',
            'status.boolean'    => 'حالة الموديل يجب أن تكون صحيحة أو خاطئة',
        ];
    }
}
