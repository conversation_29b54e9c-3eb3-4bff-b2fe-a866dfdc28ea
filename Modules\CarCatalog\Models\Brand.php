<?php

namespace Modules\CarCatalog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Core\Models\BaseModel;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

/**
 * Brand Model.
 *
 * يمثل هذا النموذج جدول ماركات السيارات في النظام
 *
 * @property int $id
 * @property string $name اسم الماركة
 * @property int|null $logo_id معرّف شعار الماركة
 * @property string|null $description وصف مختصر للماركة
 * @property bool $status حالة الماركة (نشطة/غير نشطة)
 * @property \Illuminate\Support\Carbon|null $created_at تاريخ الإنشاء
 * @property \Illuminate\Support\Carbon|null $updated_at تاريخ التحديث
 * @property \Illuminate\Support\Carbon|null $deleted_at تاريخ الحذف الناعم
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\CarCatalog\Models\CarModel[] $carModels علاقة موديلات السيارات
 * @property \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection|\Spatie\MediaLibrary\MediaCollections\Models\Media[] $media مجموعة الوسائط
 */
class Brand extends BaseModel implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use HasTranslations;
    use \App\Traits\HasStandardMediaConversions;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'status',
    ];

    /**
     * الخصائص التي يمكن ترجمتها.
     *
     * @var array
     */
    public $translatable = [
        'name',
        'description',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة.
     *
     * @var array
     */
    protected $casts = [
        'status'     => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * علاقة موديلات السيارات المرتبطة بهذه الماركة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function carModels()
    {
        return $this->hasMany(CarModel::class);
    }

    /**
     * علاقة السيارات المرتبطة بهذه الماركة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cars()
    {
        return $this->hasMany(Car::class);
    }

    /**
     * تسجيل مجموعات الوسائط.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('brand_logos')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp']);
    }

    /**
     * تسجيل تحويلات الوسائط.
     *
     * @param mixed|null $media
     */
    public function registerMediaConversions($media = null): void
    {
        // استخدام التحويلات المعيارية لشعارات الماركات
        $this->registerBrandLogoConversions($media);
    }

    /**
     * مصنع النموذج.
     *
     * @return \Modules\CarCatalog\Database\factories\BrandFactory
     */
    protected static function newFactory()
    {
        return \Modules\CarCatalog\Database\factories\BrandFactory::new();
    }
}
