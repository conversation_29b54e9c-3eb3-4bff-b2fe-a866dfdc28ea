@extends('site.layouts.site_layout')

@section('title', $page->title)

@if($page->meta_description)
@section('meta_description', $page->meta_description)
@endif

@if($page->meta_keywords)
@section('meta_keywords', $page->meta_keywords)
@endif

@push('styles')
<style>
    .page-content {
        font-size: 1.1rem;
        line-height: 1.8;
    }
    
    .page-content h1, .page-content h2, .page-content h3, 
    .page-content h4, .page-content h5, .page-content h6 {
        color: var(--primary-color);
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    
    .page-content ul, .page-content ol {
        margin-bottom: 1.5rem;
    }
    
    .page-content li {
        margin-bottom: 0.5rem;
    }
    
    .page-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 4rem 0;
        margin-bottom: 3rem;
    }
    
    .page-hero h1 {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-nav {
        background: #f8f9fa;
        padding: 1rem 0;
        margin-bottom: 2rem;
    }
    
    .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
    }
    
    .contact-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .contact-item i {
        margin-top: 0.25rem;
        font-size: 1.2rem;
    }
    
    .value-item {
        padding: 1.5rem;
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .value-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        border-color: var(--primary-color);
    }
    
    .accordion-button {
        background: #f8f9fa;
        border: none;
        font-weight: 600;
        color: var(--primary-color);
    }
    
    .accordion-button:not(.collapsed) {
        background: var(--primary-color);
        color: white;
    }
    
    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
    }
    
    .branch-card {
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .branch-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-color: var(--primary-color) !important;
    }
    
    .source-indicator {
        position: absolute;
        top: 1rem;
        left: 1rem;
        background: rgba(var(--primary-color-rgb), 0.1);
        color: var(--primary-color);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    @media (max-width: 768px) {
        .page-hero {
            padding: 2rem 0;
        }
        
        .page-hero h1 {
            font-size: 2rem;
        }
        
        .contact-item {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .contact-item i {
            margin-top: 0;
        }
    }
</style>
@endpush

@section('content')
<!-- Page Hero Section -->
<section class="page-hero">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1>{{ $page->title }}</h1>
                @if($page->source === 'cms')
                    <p class="lead mb-0">محتوى محدث من نظام إدارة المحتوى</p>
                @else
                    <p class="lead mb-0">معلومات مهمة حول خدماتنا</p>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Breadcrumb Navigation -->
<section class="breadcrumb-nav">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ route('site.home') }}" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>الرئيسية
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">{{ $page->title }}</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Page Content -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="position-relative">
                    <!-- Source Indicator (for development/admin purposes) -->
                    @if(config('app.debug') || auth()->check())
                        <div class="source-indicator">
                            {{ $page->source === 'cms' ? 'من CMS' : 'محتوى افتراضي' }}
                        </div>
                    @endif
                    
                    <!-- Page Content -->
                    <div class="page-content">
                        {!! $page->content !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h3 class="h4 text-primary mb-3">هل تحتاج لمساعدة إضافية؟</h3>
                <p class="text-muted mb-4">فريقنا جاهز لمساعدتك في أي استفسار</p>
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="tel:920000000" class="btn btn-primary">
                        <i class="fas fa-phone me-2"></i>اتصل بنا
                    </a>
                    <a href="https://wa.me/966500000000" target="_blank" class="btn btn-success">
                        <i class="fab fa-whatsapp me-2"></i>واتساب
                    </a>
                    @if($page->slug !== 'contact-us')
                        <a href="{{ route('site.pages.show', 'contact-us') }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>اتصل بنا
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Pages Section -->
@if($page->slug !== 'about-us')
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="h5 text-center mb-4">صفحات ذات صلة</h3>
                <div class="row justify-content-center">
                    @php
                        $relatedPages = [
                            'about-us' => 'من نحن',
                            'privacy-policy' => 'سياسة الخصوصية',
                            'terms-conditions' => 'الشروط والأحكام',
                            'faq' => 'الأسئلة الشائعة'
                        ];
                        
                        // Remove current page from related pages
                        unset($relatedPages[$page->slug]);
                        
                        // Show only 3 related pages
                        $relatedPages = array_slice($relatedPages, 0, 3, true);
                    @endphp
                    
                    @foreach($relatedPages as $slug => $title)
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-primary">{{ $title }}</h6>
                                    <a href="{{ route('site.pages.show', $slug) }}" class="btn btn-outline-primary btn-sm">
                                        اقرأ المزيد
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>
@endif
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips if any
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Smooth scrolling for anchor links within the page
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add animation to value items on scroll (if present)
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe value items
    document.querySelectorAll('.value-item').forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(item);
    });
    
    // Handle contact form submission (if present)
    const contactForm = document.querySelector('.contact-form form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show success message (placeholder - replace with actual form handling)
            alert('شكراً لتواصلكم معنا. سنقوم بالرد عليكم في أقرب وقت ممكن.');
            
            // Reset form
            this.reset();
        });
    }
});
</script>
@endpush
