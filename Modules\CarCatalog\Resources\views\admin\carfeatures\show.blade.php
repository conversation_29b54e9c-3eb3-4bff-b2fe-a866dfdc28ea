@extends('dashboard::layouts.admin_layout')

@section('title', 'تفاصيل ميزة السيارة: ' . $carfeature->name)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid">
    {{-- عنوان الصفحة --}}
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">تفاصيل ميزة السيارة: {{ $carfeature->name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item">إدارة السيارات</li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.car-features.index') }}">ميزات السيارات</a></li>
                        <li class="breadcrumb-item active">التفاصيل</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    {{-- تفاصيل ميزة السيارة --}}
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle text-info"></i>
                        تفاصيل ميزة السيارة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {{-- المعلومات الأساسية --}}
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">المعلومات الأساسية</h6>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">اسم الميزة:</label>
                                <p class="mb-0">{{ $carfeature->name }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">فئة الميزة:</label>
                                <p class="mb-0">
                                    @if($carfeature->category)
                                        <span class="badge bg-info">{{ $carfeature->category->name }}</span>
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </p>
                            </div>

                            @if($carfeature->description)
                                <div class="mb-3">
                                    <label class="form-label fw-bold">الوصف:</label>
                                    <p class="mb-0">{{ $carfeature->description }}</p>
                                </div>
                            @endif

                            <div class="mb-3">
                                <label class="form-label fw-bold">ترتيب العرض:</label>
                                <p class="mb-0">
                                    <span class="badge bg-secondary">{{ $carfeature->display_order }}</span>
                                </p>
                            </div>
                        </div>

                        {{-- الخصائص والحالة --}}
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">الخصائص والحالة</h6>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p class="mb-0">
                                    <span class="badge rounded-pill bg-{{ $carfeature->status ? 'success' : 'danger' }}">
                                        {{ $carfeature->status ? 'نشط' : 'غير نشط' }}
                                    </span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">قابلة للتصفية:</label>
                                <p class="mb-0">
                                    <span class="badge rounded-pill bg-{{ $carfeature->is_filterable ? 'primary' : 'secondary' }}">
                                        {{ $carfeature->is_filterable ? 'نعم' : 'لا' }}
                                    </span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">ميزة مميزة:</label>
                                <p class="mb-0">
                                    <span class="badge rounded-pill bg-{{ $carfeature->is_highlighted ? 'warning' : 'secondary' }}">
                                        {{ $carfeature->is_highlighted ? 'نعم' : 'لا' }}
                                    </span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">عدد السيارات المرتبطة:</label>
                                <p class="mb-0">
                                    <span class="badge bg-info">{{ $carfeature->cars_count }}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    {{-- معلومات التواريخ --}}
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                                <p class="mb-0 text-muted">{{ $carfeature->created_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ آخر تحديث:</label>
                                <p class="mb-0 text-muted">{{ $carfeature->updated_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                        </div>
                    </div>

                    {{-- أزرار الإجراءات --}}
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.car-features.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        <div>
                            <a href="{{ route('admin.car-features.edit', $carfeature) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            @if($carfeature->cars_count == 0)
                                <form action="{{ route('admin.car-features.destroy', $carfeature) }}" 
                                      method="POST" 
                                      style="display: inline;"
                                      onsubmit="return confirm('هل أنت متأكد من حذف ميزة السيارة هذه؟')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
