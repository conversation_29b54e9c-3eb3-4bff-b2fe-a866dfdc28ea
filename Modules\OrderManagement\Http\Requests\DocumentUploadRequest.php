<?php

namespace Modules\OrderManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب التحقق من صحة رفع المستندات
 * 
 * يتحقق من صحة البيانات المرسلة لرفع مستند واحد للطلب
 * بناءً على MOD-ORDER-MGMT-FEAT-006 في REQ-FR.md
 */
class DocumentUploadRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // معرف الطلب
            'order_id' => [
                'required',
                'integer',
                'exists:orders,id',
                // التحقق من أن الطلب ينتمي للمستخدم الحالي
                Rule::exists('orders', 'id')->where(function ($query) {
                    return $query->where('user_id', auth()->id());
                })
            ],
            
            // نوع المستند
            'document_type' => [
                'required',
                'string',
                'in:national_id_front,national_id_back,driving_license,salary_certificate,bank_statement,additional_documents'
            ],
            
            // الملف المرفوع
            'file' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB
            ],
            
            // وصف المستند (اختياري)
            'description' => [
                'nullable',
                'string',
                'max:255'
            ]
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            // رسائل معرف الطلب
            'order_id.required' => 'معرف الطلب مطلوب',
            'order_id.integer' => 'معرف الطلب يجب أن يكون رقماً صحيحاً',
            'order_id.exists' => 'الطلب غير موجود أو غير مصرح لك بالوصول إليه',
            
            // رسائل نوع المستند
            'document_type.required' => 'نوع المستند مطلوب',
            'document_type.string' => 'نوع المستند يجب أن يكون نصاً',
            'document_type.in' => 'نوع المستند غير مدعوم',
            
            // رسائل الملف
            'file.required' => 'يجب اختيار ملف للرفع',
            'file.file' => 'يجب أن يكون المرفق ملفاً صالحاً',
            'file.mimes' => 'نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, JPG, WEBP, PDF',
            'file.max' => 'حجم الملف يجب ألا يزيد عن 2 ميجابايت',
            
            // رسائل الوصف
            'description.string' => 'وصف المستند يجب أن يكون نصاً',
            'description.max' => 'وصف المستند يجب ألا يزيد عن 255 حرف'
        ];
    }

    /**
     * التحقق من صحة الملف المرفوع
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من أن الملف المرفوع ليس تالفاً
            if ($this->hasFile('file')) {
                $file = $this->file('file');
                if (!$file->isValid()) {
                    $validator->errors()->add('file', 'الملف المرفوع تالف أو غير صالح');
                }
            }
        });
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف نوع المستند
        if ($this->has('document_type')) {
            $this->merge([
                'document_type' => trim(strtolower($this->document_type))
            ]);
        }

        // تنظيف الوصف
        if ($this->has('description')) {
            $this->merge([
                'description' => trim($this->description)
            ]);
        }
    }

    /**
     * الحصول على البيانات المحققة مع معلومات إضافية
     */
    public function getValidatedDataWithExtras(): array
    {
        $validated = $this->validated();
        
        // إضافة معلومات إضافية عن الملف
        if ($this->hasFile('file')) {
            $file = $this->file('file');
            $validated['file_info'] = [
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'extension' => $file->getClientOriginalExtension()
            ];
        }

        return $validated;
    }

    /**
     * الحصول على أسماء الخصائص المخصصة
     */
    public function attributes(): array
    {
        return [
            'order_id' => 'معرف الطلب',
            'document_type' => 'نوع المستند',
            'file' => 'الملف',
            'description' => 'وصف المستند'
        ];
    }
}
