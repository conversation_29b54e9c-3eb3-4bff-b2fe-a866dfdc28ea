## STRAT-FR.md - استراتيجيات النشر والاختبار للمشروع (نسخة نهائية معتمدة ذاتيًا)

**تاريخ الإنشاء:** (تاريخ اليوم)
**إصدار المستند:** 1.0 (نهائي معتمد ذاتيًا)

---

### مقدمة

**معرف القسم:** `STRAT-INTRO-001`

الغرض من هذا المستند هو تقديم الاستراتيجيات الشاملة لنشر واختبار منصة معرض السيارات الإلكترونية المتكاملة. يُعتبر هذا المستند خارطة الطريق الاستراتيجية التي تهدف إلى ضمان تحقيق أعلى مستويات الجودة، التشغيل الفعال، والموثوقية لجميع مكونات المشروع، وبشكل خاص **[المخرجات النهائية الأربعة المتوقعة للمشروع بالكامل]**:
1.  **موقع إلكتروني فعال:** مرفوع على خادم عبر الإنترنت، جاهز لاستقبال الزوار بدون مشاكل.
2.  **لوحة تحكم احترافية (مخصصة من الصفر باستخدام أصول Dash HTML/CSS/JS):** (للإدارة و/أو العملاء) جاهزة للاستخدام، توفر تجربة مستخدم ممتازة، وتدعم اللغة العربية (RTL) بشكل كامل.
3.  **واجهة موقع (Frontend) جذابة وفعالة:** (باستخدام Blade إذا كانت مختلفة عن لوحة التحكم) جاهزة لاستقبال الزوار وتوفير تجربة مستخدم سلسة.
4.  **تطبيق موبايل (Flutter) كامل (إذا كان مطلوبًا):** جاهز للرفع على متاجر التطبيقات ويعمل بشكل صحيح ومتكامل.

تستند هذه الاستراتيجيات بشكل مباشر إلى التحليل الدقيق والفهم العميق لجميع وثائق المرحلة 01 المعتمدة ذاتيًا، والتي تتضمن: `00-FR.md` (التحليل الأولي ومقترح الموديولات)، `PO-FR.md` (نظرة عامة على المشروع)، `REQ-FR.md` (المتطلبات التفصيلية)، `TS-FR.md` (المواصفات التقنية)، `STRU-FR.md` (هيكل المشروع)، و `UIUX-FR.md` (تصميم واجهات المستخدم وتجربة المستخدم). جميع هذه المستندات تشير إلى استخدام إطار عمل Laravel 10، وبناء لوحة تحكم مخصصة (للإدارة والعملاء) من الصفر باستخدام أصول HTML/CSS/JS المتوفرة (الموجودة في مجلد `Dash/` في جذر المشروع) ودمجها كواجهات Laravel Blade ديناميكية، بالإضافة إلى تطوير تطبيق Flutter للعملاء (إذا كان ضمن النطاق النهائي) وواجهة موقع عامة باستخدام Blade.

يهدف هذا المستند إلى أن يكون دليلاً استراتيجيًا واضحًا ومفصلاً، قابل للاستهلاك الآلي من قبل أنظمة الذكاء الاصطناعي الأخرى (LLMs) في المراحل اللاحقة، مع التركيز على "ماذا" و "لماذا" و "كيف" بشكل استراتيجي لضمان نجاح المشروع وتحقيق أهدافه. هذا المستند وصفي بالكامل ولا يتضمن أي كود برمجي أو أوامر تنفيذ فعلية.

**تأكيد مراجعة وفهم المدخلات:**
تمت مراجعة وفهم جميع مستندات المرحلة 01 المذكورة أعلاه بشكل كامل، مع استيعاب خصوصية بناء لوحة التحكم المخصصة من أصول Dash ضمن بيئة Laravel 10. الاستراتيجيات التالية موجهة لضمان التنفيذ والاختبار الناجح لهذه البنية.

---

### 1. استراتيجية النشر (Deployment Strategy)

**(معرف القسم: `STRAT-DEPLOY-001`)**

تهدف استراتيجية النشر هذه إلى تحديد العمليات والأدوات والبيئات اللازمة لنشر جميع مكونات منصة معرض السيارات بشكل موثوق وفعال وآمن، مما يضمن توفر واستدامة **[المخرجات النهائية الأربعة المتوقعة للمشروع بالكامل]** للمستخدمين النهائيين.

#### 1.1. بيئات النشر (Deployment Environments)

**(معرف الاستراتيجية: `STRAT-DEPLOY-ENVS-001`)**

سيتم استخدام مجموعة من بيئات النشر المتدرجة لضمان اختبار شامل وتقليل المخاطر قبل الوصول إلى بيئة الإنتاج الحية. كل بيئة لها غرض محدد وتخدم بشكل مباشر جودة وفعالية **[المخرجات النهائية]**:

*   **1.1.1. بيئة التطوير (Development Environment):**
    *   **الغرض:** بيئة محلية يستخدمها المطورون لبناء وتجربة الميزات الجديدة وإصلاح الأخطاء.
    *   **الخدمة للمخرجات النهائية:** تمكين البناء الأولي لجميع المكونات ([موقع إلكتروني]، [لوحة تحكم Dash]، [واجهة موقع Blade]، [تطبيق Flutter]) واختبارها بشكل فردي.
    *   **التكوين النموذجي:** خوادم ويب محلية (مثل Laravel Sail, XAMPP, Valet)، قواعد بيانات محلية، أدوات تطوير مدمجة (IDEs).

*   **1.1.2. بيئة الاختبار الداخلي/التجميع (Internal Testing/Staging Environment):**
    *   **الغرض:** بيئة مخصصة لتجميع عمل المطورين المختلفين، إجراء اختبارات التكامل، واختبارات الميزات الكاملة من قبل فريق الاختبار الداخلي (QA) أو فريق المشروع.
    *   **الخدمة للمخرجات النهائية:** ضمان تكامل جميع أجزاء النظام ([موقع إلكتروني] مع [لوحة تحكم Dash] مع [API تطبيق Flutter]) وعملها معًا بشكل صحيح. اختبار وظائف لوحة تحكم Dash المخصصة بعد دمجها مع Laravel Backend.
    *   **التكوين النموذجي:** خادم مشابه لبيئة الإنتاج (ولكن بموارد أقل)، قاعدة بيانات منفصلة تحتوي على بيانات اختبار واقعية، يتم تحديثها باستمرار من خلال CI/CD.

*   **1.1.3. بيئة ما قبل الإنتاج (Pre-production/UAT Environment - إذا لزم الأمر ومناسب للميزانية):**
    *   **الغرض:** بيئة مطابقة لبيئة الإنتاج قدر الإمكان، مخصصة لاختبارات القبول من المستخدم (UAT) من قبل العميل أو أصحاب المصلحة.
    *   **الخدمة للمخرجات النهائية:** التأكد من أن **[المخرجات النهائية الأربعة]** تلبي توقعات المستخدمين النهائيين ومتطلبات العمل قبل الإطلاق الرسمي.
    *   **التكوين النموذجي:** خادم مطابق للإنتاج، نسخة من قاعدة بيانات الإنتاج (مع إخفاء/تعديل البيانات الحساسة)، نفس إعدادات الأمان والتكوين.

*   **1.1.4. بيئة الإنتاج (Production Environment):**
    *   **الغرض:** البيئة الحية التي يستخدمها المستخدمون النهائيون (عملاء المعرض، الإدارة).
    *   **الخدمة للمخرجات النهائية:** توفير **[المخرجات النهائية الأربعة]** بشكل موثوق وآمن وعالي الأداء للمستخدمين الفعليين.
    *   **التكوين النموذجي:** خوادم عالية الأداء والموثوقية، قواعد بيانات مُحسَّنة، إجراءات أمان مشددة، نسخ احتياطي منتظم، مراقبة مستمرة.

#### 1.2. استراتيجية النشر للـ Laravel Backend (لـ [موقع إلكتروني فعال] و [لوحة تحكم احترافية Dash المخصصة])

**(معرف الاستراتيجية: `STRAT-DEPLOY-BACKEND-DASH-001`)**

تعتمد هذه الاستراتيجية على الأتمتة لضمان نشر سريع وموثوق ومتسق للواجهة الخلفية المبنية بـ Laravel (بما في ذلك لوحة تحكم Dash المدمجة كواجهات Blade).

*   **1.2.1. آلية النشر (Deployment Mechanism - CI/CD):**
    *   **الوصف:** سيتم اعتماد خط أنابيب للتكامل المستمر والنشر المستمر (CI/CD) لأتمتة عملية البناء، الاختبار، والنشر.
    *   **الأدوات المقترحة:** GitHub Actions (إذا كان الكود مستضافًا على GitHub) أو GitLab CI (إذا كان على GitLab). يمكن النظر في Deployer PHP كأداة مساعدة لتنفيذ مهام النشر على الخادم.
    *   **خطوات CI/CD النموذجية:**
        1.  **Push to Repository:** المطور يدفع الكود إلى فرع محدد (e.g., `develop` لـ Staging، `main` لـ Production).
        2.  **Build & Test (CI):**
            *   سحب أحدث نسخة من الكود.
            *   تثبيت الاعتماديات (Composer install, npm install).
            *   تشغيل تحليل الكود الثابت (Static Code Analysis - e.g., PHPStan, ESLint).
            *   تشغيل اختبارات الوحدة واختبارات الميزات/التكامل (PHPUnit).
            *   بناء الأصول (Compiling Assets - Vite/Mix لتجميع CSS/JS، بما في ذلك أصول `Dash/` المخصصة إلى مجلد `public/build/` أو ما يعادله).
        3.  **Deploy (CD - للبيئة المستهدفة):**
            *   إذا نجحت جميع خطوات CI، يتم النشر إلى البيئة المناسبة (Staging, Production).
            *   وضع التطبيق في وضع الصيانة (Maintenance Mode) (إذا لم يتم استخدام Zero-Downtime).
            *   مزامنة الملفات الجديدة/المعدلة مع الخادم.
            *   تشغيل Migrations لقاعدة البيانات (`php artisan migrate --force`).
            *   (اختياري) تشغيل Seeders للبيانات الأساسية (إذا لزم الأمر).
            *   مسح وتحديث الـ Caches (`php artisan optimize:clear`, `php artisan config:cache`, `php artisan route:cache`, `php artisan view:cache`).
            *   إعادة تشغيل أي خدمات متعلقة (مثل `php-fpm`, `supervisor` للـ queues).
            *   إخراج التطبيق من وضع الصيانة.
    *   **الخدمة للمخرجات النهائية:** ضمان نشر تحديثات **[الموقع الإلكتروني الفعال]** و **[لوحة التحكم الاحترافية Dash]** بسرعة وموثوقية، مع تقليل الأخطاء البشرية.

*   **1.2.2. إدارة التكوين (Configuration Management):**
    *   **الوصف:** سيتم استخدام ملفات `.env` لإدارة تكوينات البيئة المختلفة (قاعدة البيانات، مفاتيح API، إلخ).
    *   **التفاصيل:**
        *   ملف `.env.example` سيتم تضمينه في مستودع الكود كقالب.
        *   سيتم إنشاء ملف `.env` خاص لكل بيئة على الخادم المعني، ولن يتم تضمينه في مستودع الكود.
        *   سيتم تخزين البيانات الحساسة (مثل مفاتيح API، كلمات مرور قاعدة البيانات) بشكل آمن في متغيرات البيئة على الخادم أو باستخدام خدمات إدارة الأسرار (Secrets Management) إذا توفرت (مثل HashiCorp Vault، AWS Secrets Manager - يعتمد على البنية التحتية للاستضافة).
    *   **الخدمة للمخرجات النهائية:** ضمان عمل **[الموقع الإلكتروني]** و **[لوحة التحكم]** بشكل صحيح في كل بيئة دون الحاجة لتعديل الكود، وحماية البيانات الحساسة.

*   **1.2.3. إدارة قاعدة البيانات (Database Management during Deployment):**
    *   **الوصف:** سيتم إدارة تغييرات مخطط قاعدة البيانات باستخدام Laravel Migrations.
    *   **التفاصيل:**
        *   سيتم تشغيل `php artisan migrate --force` كجزء من عملية النشر (CD) لتطبيق أي migrations جديدة.
        *   سيتم استخدام Seeders لملء الجداول بالبيانات الأساسية (مثل الأدوار والصلاحيات الأولية، فئات الخدمات، أنواع السيارات المبدئية) أو بيانات وهمية لبيئات التطوير والاختبار.
        *   سيتم وضع استراتيجية للنسخ الاحتياطي المنتظم لقاعدة بيانات الإنتاج واستعادتها (يتم تحديدها بناءً على مزود الاستضافة).
    *   **الخدمة للمخرجات النهائية:** ضمان اتساق مخطط قاعدة البيانات وتوفر البيانات اللازمة لعمل **[الموقع الإلكتروني]** و **[لوحة التحكم]** و **[تطبيق Flutter]** (عبر API).

*   **1.2.4. نشر الأصول المجمعة (Compiled Assets - CSS/JS من أصول Dash المخصصة):**
    *   **الوصف:** سيتم استخدام Laravel Vite (أو Mix) لتجميع ومعالجة جميع أصول الواجهة الأمامية (CSS و JavaScript). هذه العملية ستشمل:
        1.  **أصول Dash الأساسية:** أصول `Dash/style.css` و `Dash/script.js` سيتم نسخها ومعالجتها (مثل التصغير) ودمجها في عملية البناء. يجب التأكد من أن أي مسارات نسبية داخل هذه الملفات (مثل الإشارة إلى صور أو خطوط ضمن مجلد `Dash/`) يتم التعامل معها بشكل صحيح أثناء عملية البناء والنشر.
        2.  **ملفات CSS/JS المخصصة للوحة التحكم:** ملفات مثل `resources/css/dash_custom.css` (لتجاوز أنماط Dash إذا لزم الأمر) و `resources/js/dash_app.js` (لتكامل Laravel مع وظائف Dash JS وتمرير البيانات الديناميكية) سيتم تجميعها ومعالجتها.
        3.  **إدارة التبعيات:** أي مكتبات JavaScript إضافية مطلوبة للوحة التحكم (مثل Chart.js إذا لم تكن مضمنة بشكل كامل أو إذا تم تحديثها، أو أي مكتبات أخرى مستخدمة في `Dash/script.js` وتحتاج لإدارة عبر npm/yarn) سيتم إدارتها عبر `package.json` وتضمينها في عملية البناء.
        4.  **Versioning و Cache Busting:** سيتم استخدام Laravel helpers (مثل `Vite::asset()`) في Blade views للإشارة إلى الأصول المجمعة. سيقوم Vite تلقائيًا بتضمين hash للملف في اسمه لضمان cache busting فعال.
    *   سيتم تشغيل أمر بناء الأصول (e.g., `npm run build`) كجزء من خط أنابيب CI. الملفات المجمعة والنهائية (عادةً في `public/build/assets/` أو مسار مشابه يعتمد على Vite) هي التي سيتم نشرها إلى الخادم.
    *   **الخدمة للمخرجات النهائية:** ضمان تحميل **[لوحة التحكم الاحترافية Dash]** و **[واجهة الموقع الجذابة]** بسرعة وكفاءة، وعرضها بشكل صحيح.

*   **1.2.5. استراتيجية عدم التوقف (Zero-Downtime Deployment - إذا كانت مطلوبة ومناسبة للميزانية):**
    *   **الوصف:** إذا كانت متطلبات العمل تقتضي عدم توقف الخدمة أثناء النشر، يمكن اعتماد استراتيجيات مثل Blue/Green deployment أو استخدام أدوات مثل Envoyer أو Deployer PHP التي تدعم atomic deployments (عبر symlinks).
    *   **التفاصيل:** يتطلب هذا تخطيطًا إضافيًا للبنية التحتية وتكوين أدوات النشر. للمرحلة الأولية، قد يكون النشر مع نافذة صيانة قصيرة مقبولًا ما لم يُحدد خلاف ذلك.
    *   **الخدمة للمخرجات النهائية:** ضمان استمرارية توفر **[الموقع الإلكتروني]** و **[لوحة التحكم]** للمستخدمين.

*   **1.2.6. استراتيجية التراجع (Rollback Strategy):**
    *   **الوصف:** يجب أن تكون هناك آلية واضحة للتراجع السريع إلى الإصدار السابق المستقر في حالة حدوث مشاكل حرجة بعد النشر.
    *   **التفاصيل:**
        *   الاحتفاظ بنسخ من الإصدارات السابقة للكود والأصول على الخادم.
        *   أدوات النشر (مثل Deployer PHP) غالبًا ما توفر أوامر تراجع مدمجة.
        *   يجب أن تتضمن عملية التراجع أيضًا التراجع عن أي migrations لقاعدة البيانات إذا كان ذلك آمنًا وممكنًا (باستخدام `php artisan migrate:rollback --step=X`). في حالة عدم إمكانية التراجع الآمن للـ migrations (خاصة تلك التي تتضمن فقدان بيانات)، يجب الاعتماد على استعادة قاعدة البيانات من أحدث نسخة احتياطية تم أخذها قبل النشر. **يجب التأكد أيضًا من أن آلية التراجع للكود تشمل التراجع إلى الإصدار المتوافق من الأصول المجمعة (compiled assets)، خاصة ملفات CSS/JS للوحة تحكم Dash، لتجنب مشاكل عدم تطابق إصدارات الكود والأصول.**
        *   يجب توثيق خطوات التراجع واختبارها.
    *   **الخدمة للمخرجات النهائية:** تقليل تأثير المشاكل الناتجة عن النشر على **[المخرجات النهائية]**.

*   **1.2.7. المراقبة والتنبيه بعد النشر (Post-Deployment Monitoring and Alerting):**
    *   **الوصف:** بعد كل عملية نشر، يجب مراقبة النظام عن كثب للتأكد من عمله بشكل صحيح واكتشاف أي مشاكل مبكرًا.
    *   **الأدوات المقترحة:**
        *   خدمات تتبع الأخطاء (Error Tracking) مثل Sentry.io أو Bugsnag لتلقي تنبيهات فورية بالأخطاء والاستثناءات في بيئة الإنتاج.
        *   خدمات مراقبة الأداء (APM - Application Performance Monitoring) مثل New Relic أو Datadog (إذا كانت الميزانية تسمح) لمراقبة أداء التطبيق واستعلامات قاعدة البيانات.
        *   مراقبة سجلات الخادم (Server Logs) وسجلات التطبيق (Application Logs).
    *   **الخدمة للمخرجات النهائية:** ضمان استقرار وموثوقية **[الموقع الإلكتروني]** و **[لوحة التحكم]** و **[API تطبيق Flutter]** بعد التحديثات.

#### 1.3. استراتيجية النشر لتطبيق Flutter (لـ [تطبيق موبايل كامل] - إذا كان ضمن النطاق)

**(معرف الاستراتيجية: `STRAT-DEPLOY-FLUTTER-001`)**

إذا كان تطبيق Flutter جزءًا من المخرجات النهائية، فستركز استراتيجية نشره على الأتمتة والتوزيع المنظم للاختبار والنشر على المتاجر.

*   **1.3.1. عملية البناء (Build Process - Automated and Secure):**
    *   **الوصف:** أتمتة عملية بناء إصدارات Release من تطبيق Flutter لكل من Android (APK/AAB) و iOS (IPA).
    *   **الأدوات المقترحة:** خدمات CI/CD مخصصة للموبايل مثل Codemagic، Bitrise، أو استخدام GitHub Actions مع fastlane.
    *   **التفاصيل:**
        *   تكوين بيئات بناء نظيفة.
        *   إدارة مفاتيح التوقيع (Signing Keys) والشهادات (Certificates) بشكل آمن (باستخدام متغيرات البيئة المشفرة في خدمة CI/CD أو أدوات إدارة الأسرار).
        *   تكوين متغيرات بيئة خاصة بالتطبيق (مثل عنوان API للإنتاج).
    *   **الخدمة للمخرجات النهائية:** إنتاج نسخ موثوقة من **[تطبيق الموبايل الكامل]** جاهزة للتوزيع.

*   **1.3.2. التوزيع للاختبار (Distribution for Testing):**
    *   **الوصف:** توزيع نسخ الاختبار (Beta builds) على فريق الاختبار الداخلي والعميل (لـ UAT).
    *   **الأدوات المقترحة:**
        *   **iOS:** TestFlight.
        *   **Android:** Google Play Console Internal Testing أو Closed Testing tracks.
        *   **Cross-platform:** Firebase App Distribution.
    *   **الخدمة للمخرجات النهائية:** تسهيل عملية اختبار **[تطبيق الموبايل الكامل]** على أجهزة حقيقية من قبل المختبرين وأصحاب المصلحة.

*   **1.3.3. النشر على المتاجر (Store Deployment):**
    *   **الوصف:** عملية رفع الإصدارات النهائية من التطبيق إلى Google Play Store و Apple App Store.
    *   **التفاصيل:**
        *   إعداد قوائم التطبيقات (Store Listings) بشكل كامل (الوصف، الصور، الكلمات المفتاحية، سياسة الخصوصية).
        *   اتباع إرشادات كل متجر لعملية المراجعة والنشر.
        *   يمكن أتمتة جزء من عملية الرفع باستخدام أدوات مثل fastlane أو خدمات CI/CD التي تدعم ذلك.
    *   **الخدمة للمخرجات النهائية:** إتاحة **[تطبيق الموبايل الكامل]** للمستخدمين النهائيين.

*   **1.3.4. آلية التحديثات المستقبلية (Future Updates Mechanism):**
    *   **الوصف:** تحديد كيفية إدارة ونشر التحديثات والإصدارات الجديدة للتطبيق.
    *   **التفاصيل:**
        *   استخدام نظام ترقيم إصدارات دلالي (Semantic Versioning - MAJOR.MINOR.PATCH).
        *   تخطيط دورات الإصدار (Release Cycles).
        *   إبلاغ المستخدمين بالتحديثات الجديدة داخل التطبيق أو عبر إشعارات المتجر.
        *   (اختياري) استخدام ميزات مثل Firebase Remote Config للتحكم في بعض سلوكيات التطبيق دون الحاجة لتحديث كامل.
    *   **الخدمة للمخرجات النهائية:** ضمان استمرارية تحسين وتطوير **[تطبيق الموبايل الكامل]** ومعالجة أي مشاكل تظهر بعد الإطلاق.

---

### 2. استراتيجية الاختبار (Testing Strategy)

**(معرف القسم: `STRAT-TEST-001`)**

تهدف استراتيجية الاختبار هذه إلى تحديد أنواع ومستويات الاختبارات المختلفة التي سيتم إجراؤها لضمان جودة وفعالية وموثوقية وأمان **[المخرجات النهائية الأربعة المتوقعة للمشروع بالكامل]**، مع التركيز بشكل خاص على اختبار وظائف وتجربة مستخدم لوحة تحكم Dash المخصصة.

#### 2.1. نهج الاختبار العام (Overall Testing Approach)

**(معرف الاستراتيجية: `STRAT-TEST-APPROACH-001`)**

*   **هرم الاختبار (Test Pyramid):** سيتم التركيز على بناء قاعدة قوية من اختبارات الوحدة (Unit Tests)، تليها طبقة أصغر من اختبارات التكامل/الميزات (Integration/Feature Tests)، ثم طبقة أصغر من اختبارات الواجهة الرسومية (UI Tests) والاختبارات الشاملة (E2E Tests). هذا يضمن اكتشاف الأخطاء مبكرًا وبتكلفة أقل.
*   **الاختبار المستمر (Continuous Testing):** سيتم دمج الاختبارات في خط أنابيب CI/CD ليتم تشغيلها تلقائيًا مع كل تغيير في الكود، مما يوفر تغذية راجعة سريعة للمطورين.
*   **الاختبار القائم على المخاطر (Risk-Based Testing):** سيتم إعطاء الأولوية لاختبار الميزات والوظائف الأكثر أهمية أو الأكثر عرضة للمخاطر (مثل عمليات الدفع، إدارة المستخدمين، الوظائف الأساسية في لوحة تحكم Dash).
*   **الاختبار الاستكشافي (Exploratory Testing):** بالإضافة إلى الاختبارات المكتوبة، سيقوم فريق الاختبار (أو المطورون إذا لم يكن هناك فريق اختبار مخصص في البداية) بإجراء اختبارات استكشافية لمحاولة كسر النظام والعثور على حالات حافة غير متوقعة.

#### 2.2. اختبارات الواجهة الخلفية (Laravel Backend Testing)

**(معرف الاستراتيجية: `STRAT-TEST-BACKEND-001`)**

سيتم استخدام PHPUnit كإطار عمل رئيسي لاختبار الواجهة الخلفية لـ Laravel.

*   **2.2.1. اختبارات الوحدة (Unit Tests):**
    *   **التركيز:** اختبار أصغر وحدات الكود بشكل منفصل (مثل Models, Services, Repositories, Helper functions, Custom Validation Rules).
    *   **الأهداف:** التحقق من صحة المنطق الداخلي لكل وحدة، تغطية حالات النجاح والفشل المتوقعة.
    *   **مثال:** اختبار `CarModel` للتأكد من حساب السعر الإجمالي بشكل صحيح، اختبار Service للتحقق من تطبيقه لمنطق أعمال معين.
*   **2.2.2. اختبارات الميزات/التكامل (Feature/Integration Tests):**
    *   **التركيز:** اختبار تفاعل عدة مكونات معًا، أو اختبار نقاط نهاية (endpoints) كاملة من خلال طلبات HTTP.
    *   **الأهداف:** التحقق من أن الميزات تعمل كما هو متوقع من البداية إلى النهاية (دون الاعتماد على واجهة المستخدم الفعلية)، اختبار التفاعل مع قاعدة البيانات.
    *   **مثال:** اختبار عملية تسجيل مستخدم جديد (طلب POST إلى endpoint التسجيل، التحقق من إنشاء المستخدم في قاعدة البيانات، التحقق من إرسال OTP). اختبار إضافة سيارة من خلال استدعاء الـ Controller method المعني والتحقق من البيانات في DB.
*   **2.2.3. اختبارات واجهة برمجة التطبيقات (API Tests):**
    *   **التركيز:** اختبار نقاط نهاية API الموجهة لتطبيق Flutter أو أي عملاء خارجيين آخرين.
    *   **الأهداف:** التحقق من صحة الطلبات، الاستجابات (هيكل JSON، رموز الحالة HTTP)، المصادقة، التفويض، معالجة الأخطاء.
    *   **مثال:** اختبار `API-EP-CAR-001` (جلب قائمة السيارات) للتحقق من أن الفلاتر تعمل بشكل صحيح، وأن الترقيم صحيح، وأن الاستجابة تحتوي على البيانات المتوقعة.

#### 2.3. اختبارات واجهات لوحة تحكم Dash (Dash UI Testing - المخصصة)

**(معرف الاستراتيجية: `STRAT-TEST-DASH-UI-001`)**

نظرًا لأن لوحة تحكم Dash مبنية كواجهات Blade مخصصة تتفاعل مع أصول Dash (HTML/CSS/JS)، فإن اختبارها يتطلب تركيزًا خاصًا على تفاعلات واجهة المستخدم وسلوك JavaScript.

*   **2.3.1. اختبارات المتصفح (Browser Tests):**
    *   **الأدوات المقترحة:** Laravel Dusk (للاختبارات التي تتطلب تفاعلاً عميقًا مع بيئة Laravel) أو Playwright/Cypress (إذا كانت هناك حاجة لتفاعلات JavaScript أكثر تعقيدًا أو اختبارات عبر متصفحات متعددة بشكل أسهل). سيتم تقييم الأداة الأنسب بناءً على تعقيد التفاعلات.
    *   **التركيز:**
        *   التحقق من أن صفحات لوحة التحكم Dash المختلفة تُعرض بشكل صحيح مع البيانات الديناميكية من Laravel.
        *   اختبار وظائف النماذج (Forms) في لوحة التحكم (إدخال البيانات، التقديم، التحقق من الصحة، رسائل الخطأ).
        *   اختبار تفاعلات واجهة المستخدم الرئيسية (النقر على الأزرار، التنقل في القوائم، فتح المودالات، عمل Stepper لإضافة سيارة).
        *   التحقق من أن JavaScript المخصص (في `dash_app.js` أو التعديلات الموثقة على `Dash/script.js` الأصلي) يعمل كما هو متوقع. يشمل ذلك:
            *   **تهيئة الرسوم البيانية:** التحقق من أن جميع الرسوم البيانية المحددة في `UIUX-FR.md` (ضمن `DASH-ADMIN-HOME-001`) يتم تهيئتها بشكل صحيح بالبيانات الديناميكية المرسلة من Laravel (من `MOD-DASHBOARD-FEAT-002`). اختبار حالات مختلفة للبيانات (فارغة، نقاط قليلة، نقاط كثيرة، بيانات غير متوقعة).
            *   **تفاعلات القائمة الجانبية (`_sidebar.blade.php`):** اختبار فتح وإغلاق القوائم الفرعية، عمل وضع `mini-mode`، وسلوك `hover-expand` كما هو موصوف في `UIUX-FR.md` (بناءً على `Dash/script.js` وتكييفه). التحقق من أن الروابط الديناميكية (من `route()`) تعمل.
            *   **وظائف Stepper (`bs-stepper`):** اختبار التنقل بين خطوات نموذج إضافة/تعديل السيارة (`DASH-CAR-CRUD-001`)، والتحقق من أي منطق JS مرتبط بالـ Stepper.
            *   **تفاعلات الشريط العلوي (`_topbar.blade.php`):** اختبار عمل قائمة المستخدم المنسدلة، أيقونة الإشعارات، وزر تبديل القائمة.
            *   **أي تحديثات AJAX داخلية:** إذا تم تصميم أي تحديثات لمكونات لوحة التحكم Dash عبر AJAX (مثل تحديث جزئي لجدول أو بطاقة)، يجب اختبار هذه التفاعلات بشكل كامل.
        *   اختبار وظائف الجداول (الفرز، البحث، الترقيم) إذا كانت منفذة مع تفاعلات client-side أو server-side.
    *   **مثال:** اختبار عملية إضافة سيارة جديدة عبر واجهة Stepper في لوحة التحكم، والتحقق من أن السيارة تظهر في قائمة السيارات بعد الحفظ. اختبار عرض الرسوم البيانية في لوحة البيانات والتأكد من أنها تعكس البيانات المرسلة من الـ Backend.

*   **2.3.2. اختبارات التجاوب (Responsive Design Testing):**
    *   **التركيز:** التأكد من أن تصميم لوحة تحكم Dash (المبني على أصول Dash) يتكيف بشكل صحيح مع مختلف أحجام الشاشات (Desktops, Tablets, Mobiles) كما هو محدد في `UIUX-FR.md`.
    *   **الأساليب:** استخدام أدوات مطوري المتصفح (Browser Developer Tools) لمحاكاة أحجام الشاشات المختلفة، اختبار يدوي على أجهزة حقيقية، (اختياري) استخدام خدمات اختبار التجاوب السحابية (مثل BrowserStack, Sauce Labs).

*   **2.3.3. اختبارات قابلية الاستخدام (Usability Testing) لواجهات لوحة تحكم Dash المخصصة:**
    *   **التركيز:** تقييم مدى سهولة استخدام لوحة التحكم من قبل المستخدمين المستهدفين (مديرو النظام، الموظفين، العملاء للوحتهم الخاصة).
    *   **الأساليب:**
        *   **اختبار تفكير بصوت عال (Think-aloud testing):** ملاحظة مستخدمين حقيقيين (أو ممثلين لهم) أثناء محاولتهم إكمال مهام محددة في لوحة التحكم، وتشجيعهم على التعبير عن أفكارهم.
        *   **اختبارات المهام (Task-based testing):** إعطاء المستخدمين مهامًا لإكمالها (مثل إضافة سيارة، تحديث حالة طلب) وقياس الوقت المستغرق، معدل النجاح، ومستوى الرضا.
        *   **الاستبيانات والمقابلات:** جمع ملاحظات المستخدمين حول تجربتهم.
    *   **الهدف:** تحديد أي نقاط صعوبة أو إرباك في التصميم وتحسينها.

*   **2.3.4. اختبارات الوصولية (Accessibility Testing):**
    *   **التركيز:** التأكد من أن لوحة تحكم Dash تلبي معايير WCAG 2.1 المستوى AA (كما هو محدد في `NFR-USAB-007` ضمن `UIUX-FR.md`).
    *   **الأدوات والأساليب:** استخدام أدوات فحص الوصولية الآلية (مثل Axe, Lighthouse)، اختبار يدوي للتنقل باستخدام لوحة المفاتيح، التحقق من تباين الألوان، التأكد من وجود نصوص بديلة للصور، واستخدام HTML دلالي.

*   **2.3.5. اختبار تكامل البيانات مع واجهة Dash (Dash Data Integration Testing):**
    *   **التركيز:** التحقق من أن البيانات المرسلة من Laravel Controllers يتم عرضها بشكل صحيح ودقيق في مكونات واجهة Dash المختلفة.
    *   **الأهداف:** ضمان صحة البيانات المعروضة في الجداول، النماذج، البطاقات الإحصائية، والرسوم البيانية. التأكد من أن الفلاتر وخيارات الترتيب في الجداول تسترجع وتعرض البيانات الصحيحة.
    *   **الأساليب:** يمكن دمج هذا النوع من الاختبار ضمن اختبارات المتصفح (Browser Tests) من خلال التأكيد على وجود محتوى معين في الصفحة (e.g., `->assertSee('اسم العميل X')` في جدول العملاء)، أو التحقق من أن القيم المعروضة في البطاقات الإحصائية تتطابق مع القيم المحسوبة/المتوقعة من قاعدة البيانات. بالنسبة للرسوم البيانية، يمكن التحقق من أن البيانات الأساسية التي يتم تمريرها إلى JavaScript لتهيئة الرسم البياني صحيحة.
    *   **مثال:** عند عرض قائمة السيارات، التأكد من أن عدد الصفوف في الجدول يطابق عدد السيارات في قاعدة البيانات التي تطابق الفلاتر المطبقة، وأن سعر كل سيارة معروض بشكل صحيح.

#### 2.4. اختبارات واجهات الموقع العام (Public Site Blade UI Testing - إذا كانت مختلفة)

**(معرف الاستراتيجية: `STRAT-TEST-PUBLIC-UI-001`)**

إذا كانت واجهة الموقع العامة (المبنية بـ Blade) لها تصميم وسلوكيات مختلفة بشكل كبير عن لوحة تحكم Dash، فسيتم تطبيق نفس مبادئ اختبارات المتصفح، التجاوب، قابلية الاستخدام، والوصولية المذكورة في القسم 2.3، ولكن مع التركيز على رحلات المستخدم الخاصة بالموقع العام (تصفح السيارات، تقديم الطلبات، إلخ).

#### 2.5. اختبارات تطبيق Flutter (لـ [تطبيق موبايل كامل] - إذا كان مطلوبًا)

**(معرف الاستراتيجية: `STRAT-TEST-FLUTTER-001`)**

سيتم اعتماد نهج متعدد المستويات لاختبار تطبيق Flutter.

*   **2.5.1. اختبارات الوحدة (Unit Tests):**
    *   **التركيز:** اختبار منطق الأعمال في الـ Providers/Blocs/Cubits/Controllers، الدوال المساعدة، ونماذج البيانات.
*   **2.5.2. اختبارات الويدجت (Widget Tests):**
    *   **التركيز:** اختبار واجهات المستخدم المصغرة (Widgets) بشكل منفصل للتأكد من أنها تُعرض بشكل صحيح وتتفاعل كما هو متوقع مع مدخلات المستخدم والحالات المختلفة.
*   **2.5.3. اختبارات التكامل (Integration Tests) / اختبارات الميزات:**
    *   **التركيز:** اختبار تدفقات المستخدم الكاملة داخل التطبيق التي قد تشمل عدة شاشات وتفاعلات مع الـ Backend API (باستخدام mock API أو API حقيقي لبيئة الاختبار).
    *   **مثال:** اختبار عملية تسجيل الدخول الكاملة، تصفح السيارات وتطبيق الفلاتر، تقديم طلب شراء.
*   **2.5.4. الاختبار على أجهزة وأنظمة تشغيل مختلفة (Device and OS Testing):**
    *   **التركيز:** التأكد من عمل التطبيق بشكل صحيح على مجموعة متنوعة من أجهزة Android و iOS الفعلية والمحاكيات، مع التركيز على أحجام الشاشات وإصدارات أنظمة التشغيل الشائعة.
    *   **الأدوات:** Firebase Test Lab, AWS Device Farm (إذا كانت الميزانية تسمح) أو اختبار يدوي على مجموعة من الأجهزة.
*   **2.5.5. اختبار التعامل مع حالات الاتصال بالشبكة (Network Condition Testing):**
    *   **التركيز:** اختبار سلوك التطبيق في حالات عدم الاتصال بالإنترنت أو ضعف الاتصال، وكيفية تعامله مع تخزين البيانات المؤقت (caching)، عرض رسائل مناسبة للمستخدم، والمزامنة عند عودة الاتصال.
    *   **الأساليب:** استخدام أدوات محاكاة الشبكة في المحاكيات أو أدوات مطوري النظام، واختبار يدوي في ظروف شبكة متغيرة.

#### 2.6. اختبارات شاملة (End-to-End - E2E - Testing)

**(معرف الاستراتيجية: `STRAT-TEST-E2E-001`)**

*   **التركيز:** اختبار رحلات المستخدم الكاملة عبر جميع مكونات النظام المتكاملة (واجهة الموقع/التطبيق -> API -> Backend -> قاعدة البيانات -> واجهة لوحة التحكم Dash).
*   **الأهداف:** التحقق من أن النظام يعمل كوحدة واحدة متكاملة، وأن البيانات تتدفق بشكل صحيح بين المكونات.
*   **الأساليب:** يمكن أن تكون هذه الاختبارات يدوية في البداية، مع إمكانية أتمتة بعض السيناريوهات الحرجة باستخدام أدوات مثل Playwright أو Cypress التي يمكنها التفاعل مع واجهات الويب المختلفة.
*   **مثال:** سيناريو تسجيل عميل جديد عبر الموقع، تقديم طلب شراء سيارة، ثم تسجيل دخول مدير النظام إلى لوحة تحكم Dash ورؤية الطلب الجديد وتحديث حالته، ثم التحقق من أن العميل يرى الحالة المحدثة في لوحة تحكمه أو عبر إشعار.

#### 2.7. اختبارات القبول من المستخدم (User Acceptance Testing - UAT)

**(معرف الاستراتيجية: `STRAT-TEST-UAT-001`)**

*   **التركيز:** يتم إجراؤها من قبل العميل أو أصحاب المصلحة (أو ممثلين عنهم) على بيئة ما قبل الإنتاج (UAT Environment) للتحقق من أن النظام يلبي متطلبات العمل وتوقعات المستخدمين.
*   **الأهداف:** الحصول على موافقة نهائية قبل الإطلاق.
*   **العملية:**
    1.  إعداد بيئة UAT مع بيانات اختبار واقعية.
    2.  توفير سيناريوهات اختبار (Test Cases) أو قائمة بالوظائف الرئيسية للعميل ليقوم باختبارها.
    3.  جمع ملاحظات العميل وأي مشاكل يتم اكتشافها.
    4.  معالجة المشاكل وإعادة الاختبار حتى يتم الحصول على الموافقة.

#### 2.8. اختبارات غير وظيفية (Non-Functional Testing)

**(معرف الاستراتيجية: `STRAT-TEST-NONFUNC-001`)**

سيتم التركيز على المتطلبات غير الوظيفية الهامة المحددة في `REQ-FR.md` و `UIUX-FR.md`.

*   **2.8.1. اختبارات الأداء (Performance Testing):**
    *   **التركيز:** قياس سرعة استجابة النظام، زمن تحميل الصفحات، وقدرته على التعامل مع عدد معين من المستخدمين المتزامنين.
    *   **الأدوات المقترحة:** Apache JMeter, k6, Locust (لاختبار الحمل على API والـ Backend). Google PageSpeed Insights, Lighthouse, WebPageTest (لاختبار أداء الواجهة الأمامية للموقع ولوحة التحكم Dash).
    *   **السيناريوهات:** محاكاة تصفح المستخدمين، تقديم طلبات، عمليات بحث وفلترة، استخدام لوحة التحكم Dash.
    *   **الأهداف:** التأكد من تحقيق متطلبات الأداء المحددة في `NFR-PERF-*`.
    *   **2.8.1.1. اختبار أداء واجهات لوحة تحكم Dash المخصصة:**
        *   **التركيز:** قياس أداء تحميل وعرض الصفحات الرئيسية والمكونات المعقدة في لوحة تحكم Dash (مثل لوحة البيانات الرئيسية `DASH-ADMIN-HOME-001` مع رسومها البيانية، صفحة إضافة/تعديل السيارة `DASH-CAR-CRUD-001` مع Stepper، وصفحات القوائم التي تحتوي على جداول بيانات كبيرة).
        *   **الأهداف:** التحقق من تحقيق متطلبات الأداء المحددة في `REQ-FR.md` (مثل `NFR-PERF-002` لزمن تحميل لوحة التحكم، و `NFR-PERF-005` لحجم ملفات JS/CSS).
        *   **المقاييس:** Largest Contentful Paint (LCP), First Input Delay (FID), Cumulative Layout Shift (CLS), حجم الصفحة الإجمالي، عدد طلبات HTTP.
        *   **الأدوات:** Lighthouse, Google PageSpeed Insights, WebPageTest، وأدوات مطوري المتصفح لتحليل أداء تحميل الأصول وتصيير الصفحة.
*   **2.8.2. اختبارات الأمان (Security Testing):**
    *   **التركيز:** تحديد ومعالجة الثغرات الأمنية المحتملة في النظام.
    *   **الأساليب:**
        *   مراجعة الكود (Security Code Review) للأجزاء الحساسة.
        *   فحص الثغرات الآلي (Automated Vulnerability Scanning) باستخدام أدوات مثل OWASP ZAP, Nessus (إذا توفرت).
        *   (اختياري، حسب الميزانية) اختبار الاختراق (Penetration Testing) من قبل متخصصين خارجيين قبل الإطلاق.
        *   التحقق من تطبيق جميع متطلبات الأمان المحددة في `NFR-SEC-*` و `TS-FR.md` (القسم 5).
*   **2.8.3. اختبارات قابلية الاستخدام (Usability Testing):** (تم تغطيتها جزئيًا في 2.3.3 و 2.4، ولكن يمكن توسيعها لتشمل الموقع العام وتطبيق Flutter بشكل رسمي).
*   **2.8.4. اختبارات التوافق (Compatibility Testing):** التأكد من عمل الموقع ولوحة التحكم Dash على أحدث إصدارين من المتصفحات الشائعة (Chrome, Firefox, Safari, Edge) وعلى أنظمة التشغيل الرئيسية (Windows, macOS). لتطبيق Flutter، اختبار التوافق مع إصدارات iOS و Android المستهدفة.

#### 2.9. إدارة بيانات الاختبار (Test Data Management)

**(معرف الاستراتيجية: `STRAT-TEST-DATA-001`)**

*   **الوصف:** سيتم إنشاء مجموعة متنوعة من بيانات الاختبار لتمثيل سيناريوهات مختلفة وحالات حافة.
*   **التفاصيل:**
    *   استخدام Laravel Seeders و Factories لإنشاء بيانات وهمية منظمة لبيئات التطوير والاختبار الداخلي. يجب التأكيد على أهمية إنشاء سيناريوهات بيانات اختبار متنوعة تغطي حالات واجهات Dash المختلفة (حالات فارغة، بيانات كثيفة، نصوص طويلة، قيم رقمية متنوعة للرسوم البيانية، مجموعات صلاحيات مستخدمين معقدة).
    *   لبيئة UAT، يمكن استخدام نسخة من بيانات الإنتاج مع إخفاء/تعديل البيانات الشخصية الحساسة (Data Masking/Anonymization) للامتثال لسياسات الخصوصية.
    *   يجب أن تكون بيانات الاختبار قابلة لإعادة التعيين (Reset) بسهولة.

#### 2.10. تتبع وإدارة العيوب (Bug Tracking and Management)

**(معرف الاستراتيجية: `STRAT-TEST-BUGS-001`)**

*   **الوصف:** سيتم استخدام نظام لتتبع وإدارة العيوب (Bugs) التي يتم اكتشافها خلال جميع مراحل الاختبار.
*   **الأدوات المقترحة:** GitHub Issues (إذا كان المستودع على GitHub)، Trello (للبساطة)، أو Jira (إذا كانت هناك حاجة لإدارة مشاريع أكثر تعقيدًا).
*   **العملية النموذجية لتتبع العيب:**
    1.  **الإبلاغ عن العيب:** وصف واضح للعيب، خطوات إعادة إنتاجه، البيئة التي ظهر فيها، لقطات شاشة/فيديو (إن أمكن)، الخطورة المتوقعة.
    2.  **التصنيف وتحديد الأولويات:** يتم مراجعة العيب، تحديد خطورته وأولويته، وتعيينه لمطور.
    3.  **الإصلاح:** المطور يقوم بإصلاح العيب.
    4.  **التحقق (Verification):** فريق الاختبار (أو المبلغ) يتحقق من أن العيب قد تم إصلاحه في بيئة الاختبار.
    5.  **الإغلاق:** يتم إغلاق العيب إذا تم التحقق من الإصلاح بنجاح.

---

### 3. ربط الاستراتيجيات بالمخرجات النهائية (Linking Strategies to Final Deliverables)

**(معرف القسم: `STRAT-LINK-DELIV-001`)**

تم تصميم استراتيجيات النشر والاختبار الموضحة أعلاه لضمان تحقيق **[المخرجات النهائية الأربعة المتوقعة للمشروع بالكامل]** بأعلى معايير الجودة والفعالية:

1.  **[موقع إلكتروني فعال]:**
    *   **النشر:** استراتيجية نشر الـ Laravel Backend (CI/CD، إدارة التكوين، نشر الأصول) تضمن توفر الموقع واستقراره وتحديثه بشكل موثوق.
    *   **الاختبار:** اختبارات الواجهة الخلفية، اختبارات واجهات الموقع العام (المتصفح، التجاوب، قابلية الاستخدام، الوصولية)، اختبارات الأداء والأمان، و UAT تضمن أن الموقع يعمل بشكل صحيح، سريع، آمن، وسهل الاستخدام.

2.  **[لوحة تحكم احترافية (مخصصة من الصفر باستخدام أصول Dash HTML/CSS/JS)]:**
    *   **النشر:** نفس استراتيجية نشر الـ Laravel Backend، مع التركيز على النشر الصحيح للأصول المجمعة من Dash، تضمن عمل لوحة التحكم بشكل فعال. استراتيجية إدارة نطاق CSS وتعارضاته (`GAP-STRAT-DASH-CSS-CONFLICT-MGMT-001` من السجل) يجب أن تُدمج هنا لضمان مظهر متناسق.
    *   **الاختبار:** اختبارات واجهات لوحة تحكم Dash المخصصة (المتصفح مع التركيز على تفاعلات Blade/JS، التجاوب، قابلية الاستخدام، الوصولية، تكامل البيانات، واختبارات أداء محددة لواجهات Dash) هي حاسمة لضمان أن لوحة التحكم المبنية على أصول Dash والمدمجة مع Laravel تعمل كما هو متوقع، وتوفر تجربة مستخدم ممتازة، وتدعم اللغة العربية (RTL) بشكل كامل. اختبارات الـ Backend تضمن صحة البيانات والوظائف التي تخدمها لوحة التحكم.

3.  **[واجهة موقع (Frontend) جذابة وفعالة (باستخدام Blade إذا كانت مختلفة عن لوحة التحكم)]:**
    *   **النشر:** تخدمها استراتيجية نشر الـ Laravel Backend والأصول.
    *   **الاختبار:** تخدمها استراتيجيات اختبار واجهات الموقع العام المذكورة، مع التركيز على الجاذبية البصرية وسلاسة تجربة المستخدم كما هو محدد في `UIUX-FR.md`.

4.  **[تطبيق موبايل (Flutter) كامل (إذا كان مطلوبًا)]:**
    *   **النشر:** استراتيجية نشر تطبيق Flutter (البناء الآلي، التوزيع للاختبار، النشر على المتاجر) تضمن وصول التطبيق للمستخدمين بشكل سلس.
    *   **الاختبار:** اختبارات تطبيق Flutter (وحدة، ويدجت، تكامل، اختبار على أجهزة مختلفة، واختبار التعامل مع حالات الاتصال بالشبكة) تضمن أن التطبيق يعمل بشكل صحيح وموثوق ومتكامل مع الـ Backend API.

تضمن استراتيجيات الاختبار الشاملة (E2E) و UAT أن جميع هذه المخرجات تعمل معًا بشكل متناغم لتحقيق أهداف المشروع الكلية.

---
*(نهاية مستند `STRAT-FR.md` - النسخة النهائية المعتمدة ذاتيًا)*