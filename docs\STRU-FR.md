## STRU-FR.md - هيكل المشروع (نسخة نهائية معتمدة ذاتيًا)

**تاريخ الإنشاء:** (تاريخ اليوم)
**إصدار المستند:** 1.0 (نهائي)

---

### مقدمة

الغرض من هذا المستند هو تحديد الهيكل المقترح للمجلدات والملفات لمشروع منصة معرض السيارات الإلكترونية المتكاملة. هذا الهيكل هو الخريطة التنظيمية الأساسية التي سيُبنى عليها المشروع، ويهدف إلى ضمان تنظيم منطقي وفعال وقابل للصيانة والتوسع.

يستند هذا الهيكل بشكل مباشر إلى المتطلبات والمواصفات المعتمدة في المستندات التالية:
*   `00-FR.md` (التحليل الأولي ومقترح الموديولات - النهائي المعتمد)
*   `PO-FR.md` (نظرة عامة على المشروع - النهائي المعتمد)
*   `REQ-FR.md` (المتطلبات التفصيلية - النهائي المعتمد)
*   `TS-FR.md` (المواصفات التقنية - النهائي المعتمد)

سيتم بناء الـ Backend باستخدام إطار عمل Laravel 10، مع تنظيم الموديولات الوظيفية باستخدام حزمة `nwidart/laravel-modules`. سيتم بناء لوحة التحكم المخصصة (للإدارة والعملاء) عن طريق دمج وتكييف أصول HTML/CSS/JS المتوفرة في مجلد `Dash/` (الموجود في جذر المشروع) وتحويلها إلى واجهات Laravel Blade ديناميكية. كما سيتم تطوير تطبيق موبايل للعملاء باستخدام Flutter.

يهدف هذا الهيكل إلى خدمة المخرجات النهائية المتوقعة للمشروع بكفاءة وجودة عالية:
1.  موقع إلكتروني فعال.
2.  لوحة تحكم احترافية (مخصصة من أصول Dash).
3.  واجهة موقع (Frontend) جذابة وفعالة (Blade).
4.  تطبيق موبايل (Flutter) كامل.

تمت مراجعة وفهم جميع المستندات المذكورة أعلاه بشكل كامل، وهذا الهيكل يعكس هذا الفهم.

---

### 1. هيكل مشروع Laravel Backend (متضمنًا `nwidart/laravel-modules` ولوحة تحكم Dash المخصصة)

**(معرف القسم: `STRU-LARAVEL-BACKEND-001`)**

يصف هذا القسم هيكل المجلدات والملفات المقترح للـ Backend المبني باستخدام Laravel 10. يتم تنظيم الكود بشكل أساسي باستخدام موديولات منفصلة (`nwidart/laravel-modules`)، مع دمج وتخصيص لوحة تحكم Dash المخصصة (باستخدام أصول HTML/CSS/JS من مجلد `Dash/` في جذر المشروع) كواجهات Blade.

#### 1.1. الهيكل العام (Top-Level Structure)

**(معرف المكون: `STRU-LARAVEL-TOPLEVEL-001`)**

سيحتوي جذر مشروع Laravel على المجلدات والملفات القياسية، بالإضافة إلى مجلد `Modules` الذي تديره حزمة `nwidart/laravel-modules` ومجلد `Dash` الذي يحتوي على أصول لوحة التحكم الثابتة.

*   `app/`
    *   `Console/Commands/`: أوامر Artisan المخصصة على مستوى التطبيق.
    *   `Exceptions/`: معالج الاستثناءات العام.
    *   `Http/Controllers/`: وحدات التحكم الأساسية للتطبيق العام (غير الموديولات).
        *   `Site/`: (مقترح) وحدات تحكم لواجهة الموقع العامة إذا كانت هناك صفحات لا تنتمي لموديول معين.
        *   `Admin/`: (مقترح) وحدات تحكم مركزية للوحة تحكم الإدارة Dash المخصصة.
            *   `DashboardController.php`: (مثال) وحدة تحكم لعرض لوحة البيانات الرئيسية للإدارة (تجمع بيانات من موديولات مختلفة).
    *   `Http/Middleware/`: الوسائط (Middleware) المخصصة على مستوى التطبيق.
    *   `Models/`: (ملاحظة: سيتم نقل نموذج `User.php` وأي نماذج أخرى خاصة بوظائف محددة إلى الموديولات المعنية، مثل `UserManagement`. هذا المجلد قد يبقى فارغًا أو يحتوي فقط على نماذج مجردة جدًا إذا لزم الأمر على مستوى التطبيق.)
    *   `Providers/`: مزودو الخدمات (Service Providers) على مستوى التطبيق.
    *   `Services/`: (مقترح) للخدمات العامة على مستوى التطبيق.
*   `bootstrap/`
*   `config/`: ملفات التهيئة للتطبيق والموديولات.
*   `database/`
    *   `factories/`: Factories لإنشاء بيانات وهمية (على مستوى التطبيق والموديولات).
    *   `migrations/`: Migrations لإنشاء جداول قاعدة البيانات (على مستوى التطبيق والموديولات).
    *   `seeders/`: Seeders لملء قاعدة البيانات ببيانات أولية (على مستوى التطبيق والموديولات).
*   `Modules/`: **(معرف المكون: `STRU-LARAVEL-MODULESDIR-001`)** (يتم إنشاؤه وإدارته بواسطة `nwidart/laravel-modules`) - سيتم تفصيله لاحقًا.
*   `public/`
    *   `index.php`
    *   `.htaccess`
    *   `assets/`: مجلد مقترح للأصول العامة للموقع (CSS, JS, Images) إذا لم تكن ضمن `resources/` وتُعالج بـ Vite/Mix.
        *   `site/`: أصول واجهة الموقع العامة.
        *   `dash/`: (مقترح، ويُفضل) سيتم استخدام Vite (أو Mix) لتجميع ومعالجة أصول Dash من مجلد `Dash/` في الجذر، وسيتم نشر الأصول المُعالجة (CSS/JS المجمعة والمصغرة، الصور، الخطوط) إلى هذا المجلد (`public/assets/dash/`) أو مسار مشابه ضمن `public/build/assets/` إذا تم استخدام نظام بناء Laravel القياسي مع versioning. هذا يضمن إدارة أفضل للأصول وتحسين الأداء.
*   `resources/`
    *   `css/`: (إذا تم استخدام Vite/Mix لتجميع CSS).
    *   `js/`: (إذا تم استخدام Vite/Mix لتجميع JS).
        *   `dash_app.js`: (مقترح) ملف JavaScript رئيسي للوحة تحكم Dash يتم تجميعه بواسطة Vite/Mix. سيقوم هذا الملف بـ:
            1.  استيراد `Dash/script.js` الأصلي (إذا كان ذلك ممكنًا ومناسبًا، أو سيتم تضمين `Dash/script.js` مباشرة في Blade layout ويقوم `dash_app.js` بتوسيع وظائفه).
            2.  تهيئة أي مكتبات JavaScript إضافية مستخدمة في لوحة التحكم Dash (مثل Chart.js إذا لم يتم تهيئتها بالكامل في `Dash/script.js` الأصلي).
            3.  احتواء الكود المخصص لربط بيانات Laravel بالرسوم البيانية والمكونات الديناميكية الأخرى في لوحة التحكم.
            4.  التعامل مع أي تفاعلات AJAX مخصصة داخل لوحة التحكم Dash.
            5.  أي تعديلات أو تجاوزات لسلوكيات `Dash/script.js` الأصلية المطلوبة لتكامل Laravel.
    *   `lang/`: ملفات اللغة للترجمة.
    *   `views/`:
        *   `admin/`: **(معرف المكون: `STRU-LARAVEL-VIEWS-ADMIN-001`)** (أو اسم مشابه مثل `dash_panel/`) لتجميع واجهات Blade الخاصة بلوحة تحكم الإدارة Dash المخصصة.
            *   `layouts/`:
                *   `admin_layout.blade.php`: التخطيط الرئيسي للوحة تحكم الإدارة Dash، يدمج هيكل `Dash/index.html` ويستخدم أصول CSS/JS من مجلد `Dash/` (أو الأصول المجمعة في `public/assets/dash/`).
                *   `customer_layout.blade.php`: (إذا كانت لوحة تحكم العميل تستخدم نفس الأصول ولكن بتخطيط مختلف قليلاً) أو يمكن أن تكون ضمن موديول `UserManagement`.
            *   `partials/` (أو `components/`):
                *   `_sidebar.blade.php`: مكون Blade للقائمة الجانبية للوحة تحكم الإدارة Dash (مستخلص ومكيف من `Dash/index.html`).
                *   `_topbar.blade.php`: مكون Blade للشريط العلوي للوحة تحكم الإدارة Dash (مستخلص ومكيف من `Dash/index.html`).
                *   `_footer.blade.php`: مكون Blade للتذييل.
                *   `_stepper_car_form.blade.php`: (مثال) مكون Blade لـ Stepper المستخدم في إضافة/تعديل سيارة، يعتمد على بنية Stepper في `Dash/index.html`.
                    *   `stepper_car_form_parts/`: (مقترح) مجلد فرعي لمكونات Stepper.
                        *   `basic_info.blade.php`
                        *   `tech_specs.blade.php`
                        *   `features.blade.php`
                        *   `images.blade.php`
                        *   `price_status.blade.php`
            *   `dashboard.blade.php`: الصفحة الرئيسية للوحة تحكم الإدارة (تعرض الإحصائيات والرسوم البيانية).
            *   (مجلدات وصفحات أخرى سيتم إنشاؤها هنا لخدمة وظائف لوحة التحكم المركزية أو كقوالب أساسية تستخدمها الموديولات، مثل `settings/index.blade.php`).
        *   `site/`: **(معرف المكون: `STRU-LARAVEL-VIEWS-SITE-001`)** واجهات Blade للموقع العام (الصفحة الرئيسية، صفحة تفاصيل السيارة، إلخ).
            *   `layouts/site_layout.blade.php`: التخطيط الرئيسي للموقع العام.
            *   `partials/`: مكونات مشتركة للموقع العام.
            *   `home.blade.php`
            *   `cars/index.blade.php`
            *   `cars/show.blade.php`
            *   (صفحات أخرى حسب الحاجة).
        *   `auth/`: واجهات Blade لعمليات المصادقة (تسجيل، تسجيل دخول، إلخ) إذا لم تكن ضمن موديول.
        *   `vendor/`: واجهات Blade التي تنشرها الحزم (مثل الإشعارات، البريد).
*   `routes/`
    *   `web.php`: المسارات الرئيسية للموقع العام. قد يقوم بتضمين ملفات مسارات الواجهة العامة من الموديولات.
    *   `admin.php`: **(معرف المكون: `STRU-LARAVEL-ROUTES-ADMIN-001`)** (أو `dash.php`) ملف مسارات مركزي للوحة تحكم الإدارة Dash. سيتعامل مع المسارات التي تخدم واجهات Blade المستندة إلى أصول Dash. سيتم تجميعها غالبًا بـ prefix (e.g., `/admin`) ومجموعة middleware (e.g., `auth`, `role:admin`). قد يقوم بتضمين ملفات المسارات الإدارية من الموديولات.
    *   `api.php`: المسارات الرئيسية للـ API الخارجية (خاصة لـ Flutter). قد يقوم بتضمين ملفات مسارات API من الموديولات.
    *   `channels.php`
    *   `console.php`
*   `storage/`
*   `tests/`
*   `vendor/`
*   `.env`
*   `artisan`
*   `composer.json`
*   `package.json`
*   `vite.config.js` (أو `webpack.mix.js`)
*   `Dash/`: **(معرف المكون: `STRU-DASH-ASSETS-ROOT-001`)** (في جذر المشروع)
    *   `index.html`: ملف HTML المرجعي للوحة التحكم.
    *   `style.css`: ملف CSS الرئيسي للوحة التحكم.
    *   `script.js`: ملف JavaScript الرئيسي للوحة التحكم.
    *   (أي أصول أخرى مرتبطة مثل مجلدات `img/`, `fonts/` إذا كانت جزءًا من أصول Dash الأصلية).

#### 1.2. هيكل المجلدات داخل كل موديول (تحت `Modules/`)

**(معرف القسم: `STRU-LARAVEL-MODULES-INTERNAL-001`)**

سيتم إنشاء الموديولات التالية (المستمدة من `00-FR.md`) باستخدام `php artisan module:make [ModuleName]`. كل موديول سيتبع هيكلًا قياسيًا توفره حزمة `nwidart/laravel-modules`، مع بعض الإضافات المقترحة.

*   **قائمة الموديولات الرئيسية:**
    *   `Core` **(معرف الموديول: `STRU-MOD-CORE-001`)**
    *   `UserManagement` **(معرف الموديول: `STRU-MOD-USERMANAGEMENT-001`)**
    *   `CarCatalog` **(معرف الموديول: `STRU-MOD-CARCATALOG-001`)**
    *   `OrderManagement` **(معرف الموديول: `STRU-MOD-ORDERMANAGEMENT-001`)**
    *   `ServiceManagement` **(معرف الموديول: `STRU-MOD-SERVICEMANAGEMENT-001`)**
    *   `CorporateSales` **(معرف الموديول: `STRU-MOD-CORPORATESALES-001`)**
    *   `PromotionManagement` **(معرف الموديول: `STRU-MOD-PROMOTIONMANAGEMENT-001`)**
    *   `Cms` **(معرف الموديول: `STRU-MOD-CMS-001`)**
    *   `Notification` **(معرف الموديول: `STRU-MOD-NOTIFICATION-001`)**
    *   `Dashboard` **(معرف الموديول: `STRU-MOD-DASHBOARD-001`)**
    *   `Api` **(معرف الموديول: `STRU-MOD-API-001`)**

*   **هيكل المجلدات النموذجي داخل كل موديول (مثال: `Modules/CarCatalog/`)**:

    *   `config/`: ملفات التهيئة الخاصة بالموجهات (مثل `config.php`).
    *   `Console/`: أوامر Artisan الخاصة بالموجهات.
    *   `DataTransferObjects/`: (مقترح، DTOs) لتمرير البيانات المهيكلة بين الطبقات.
    *   `Database/`
        *   `Migrations/`: Migrations لإنشاء جداول قاعدة البيانات الخاصة بالموجهات.
        *   `Seeders/`: Seeders لملء جداول الموجهات ببيانات أولية.
        *   `factories/`: (إذا لزم الأمر) Factories خاصة بالموجهات.
    *   `Models/`: (تم تغيير الاسم من `Entities` الافتراضي في `nwidart/laravel-modules` إلى `Models` للاتساق مع اصطلاحات Laravel القياسية. سيتم تكوين الحزمة لاستخدام هذا الاسم.) نماذج Eloquent الخاصة بالموجهات.
        *   (مثال: `Car.php`, `Brand.php`, `CarModel.php` في موديول `CarCatalog`).
    *   `Http/`
        *   `Controllers/`:
            *   `Admin/`: **(معرف المكون: `STRU-MOD-CTRL-ADMIN-001`)** وحدات التحكم التي تخدم أقسام لوحة تحكم الإدارة Dash المتعلقة بهذا الموديول.
                *   (مثال: `Modules/CarCatalog/Http/Controllers/Admin/CarController.php`, `BrandController.php`).
            *   `Api/`: **(معرف المكون: `STRU-MOD-CTRL-API-001`)** وحدات التحكم التي تخدم نقاط نهاية API الخارجية (لـ Flutter) المتعلقة بهذا الموديول.
                *   (مثال: `Modules/CarCatalog/Http/Controllers/Api/CarApiController.php`).
            *   `Site/`: (إذا كان الموديول يوفر صفحات للموقع العام) وحدات التحكم التي تخدم واجهة الموقع العامة.
                *   (مثال: `Modules/CarCatalog/Http/Controllers/Site/CarPublicController.php`).
        *   `Middleware/`: الوسائط الخاصة بالموجهات.
        *   `Requests/`: Form Requests للتحقق من صحة مدخلات وحدات التحكم (Admin, Api, Site).
            *   `Admin/`: (مثال: `Modules/CarCatalog/Http/Requests/Admin/StoreCarRequest.php`).
            *   `Api/`: (مثال: `Modules/CarCatalog/Http/Requests/Api/FilterCarsRequest.php`).
    *   `Listeners/`: مستمعو الأحداث (Event Listeners) الخاصة بالموجهات.
    *   `Mail/`: Mailables الخاصة بالموجهات.
    *   `Notifications/`: فئات الإشعارات الخاصة بالموجهات.
    *   `Providers/`:
        *   `[ModuleName]ServiceProvider.php`: مزود الخدمة الرئيسي للموديول (لتسجيل المسارات، الواجهات، إلخ).
        *   `RouteServiceProvider.php`: (عادة يتم تضمينه في المزود الرئيسي) لتحديد مجموعات المسارات وملفات المسارات.
    *   `Repositories/`: (مقترح، إذا تم اعتماد نمط Repository) لتجريد منطق الوصول إلى البيانات.
    *   `Resources/`
        *   `assets/`: (إذا كانت هناك أصول خاصة بالموجهات لا تتم معالجتها مركزيًا).
        *   `lang/`: ملفات الترجمة الخاصة بالموجهات.
        *   `views/`:
            *   `admin/`: **(معرف المكون: `STRU-MOD-VIEWS-ADMIN-001`)** واجهات Blade التي يستخدمها الموديول لعرض المحتوى ضمن لوحة تحكم الإدارة Dash.
                *   (مثال: `Modules/CarCatalog/Resources/views/admin/cars/index.blade.php`, `create.blade.php`, `edit.blade.php`, `_form.blade.php`).
                *   هذه الواجهات ستمتد من التخطيط الرئيسي للوحة التحكم (`admin.layouts.admin_layout`) أو ستُستخدم كمكونات جزئية.
            *   `site/`: (إذا كان الموديول يوفر صفحات للموقع العام) واجهات Blade للموقع العام.
            *   `mail/`: قوالب Blade للبريد الإلكتروني.
    *   `Routes/`:
        *   `admin.php`: (أو اسم مشابه مثل `dash.php`) مسارات الويب المحمية التي تخدم أقسام لوحة تحكم الإدارة Dash المتعلقة بهذا الموديول. يتم تسجيلها عادةً بـ prefix (e.g., `/admin/cars`).
        *   `api.php`: مسارات API الخارجية (لـ Flutter) المتعلقة بهذا الموديول. يتم تسجيلها عادةً بـ prefix (e.g., `/api/v1/flutter/cars`).
        *   `web.php`: (إذا كان الموديول يوفر صفحات للموقع العام) مسارات الويب العامة.
    *   `Services/`: (مقترح) للخدمات ومنطق الأعمال الخاص بالموجهات.
    *   `Tests/`: اختبارات الوحدة والميزات الخاصة بالموجهات.
    *   `composer.json`: (خاص بالموجهات، لإدارة أي تبعيات خاصة بالموجهات).
    *   `module.json`: ملف تعريف الموديول.

*   **تخصيص موديول `Dashboard` (`STRU-MOD-DASHBOARD-001`):**
    *   **الغرض الرئيسي:** هذا الموديول سيكون مسؤولاً عن:
        1.  توفير وحدة التحكم الرئيسية (`DashboardController.php`) لعرض لوحة البيانات الرئيسية للإدارة، والتي تجمع وتعرض بيانات من مختلف الموديولات الأخرى.
        2.  توفير واجهات Blade الرئيسية للوحة البيانات (`dashboard_home.blade.php`) وأي مكونات Blade مشتركة خاصة باللوحة الرئيسية لا تنتمي لموديول وظيفي آخر (مثل هياكل البطاقات الإحصائية العامة، قوالب الرسوم البيانية التي يتم ملؤها ديناميكيًا).
        3.  (مهم) قد يحتوي على وحدات تحكم وواجهات لإدارة جوانب من "إعدادات النظام" العامة (`FEAT-ADMIN-003`) التي لا ترتبط مباشرة بموديول `Core` ولكنها تُعرض وتُدار من لوحة التحكم (مثل واجهة تعديل إعدادات الموقع، SEO، تكاملات خارجية).
        4.  (مهم) قد يحتوي على وحدات تحكم وواجهات لوظائف إدارية عامة أخرى مثل "التقارير" (`FEAT-ADMIN-005`) إذا لم تكن هذه التقارير جزءًا من موديول وظيفي محدد، و "النسخ الاحتياطي والاستعادة" (`FEAT-ADMIN-004`) إذا كانت تتطلب واجهة مستخدم.
    *   **الهيكل المقترح:**
        *   `Http/Controllers/Admin/`:
            *   `DashboardController.php`: لعرض لوحة البيانات الرئيسية.
            *   `SystemSettingsController.php`: (مثال) لإدارة واجهة إعدادات النظام.
            *   `ReportController.php`: (مثال) لعرض التقارير العامة.
        *   `Resources/views/admin/`:
            *   `dashboard_home.blade.php`: الواجهة الرئيسية للوحة البيانات.
            *   `settings/index.blade.php`: (مثال) واجهة إعدادات النظام.
            *   `reports/sales_report.blade.php`: (مثال) واجهة تقرير المبيعات.
            *   `partials/` (أو `components/`): مكونات Blade مشتركة للوحة التحكم.
        *   `Services/`:
            *   `DashboardDataService.php`: لجمع بيانات لوحة البيانات من موديولات أخرى.
            *   `SystemSettingsService.php`: (مثال) للتعامل مع منطق حفظ واسترجاع إعدادات النظام.
        *   `Routes/admin.php`: مسارات خاصة بوحدات التحكم في هذا الموديول (لوحة البيانات، الإعدادات، التقارير).

*   **تخصيص موديول `Api` (`STRU-MOD-API-001`):**
    *   هذا الموديول سيركز على توفير بنية أساسية للـ API.
    *   `Http/Controllers/Api/V1/`: وحدات تحكم لتجميع أو تنسيق الاستجابات من موديولات أخرى إذا لزم الأمر، أو لنقاط نهاية عامة لا تنتمي لموديول محدد.
    *   `Http/Resources/`: API Resources لتنسيق استجابات JSON.
    *   قد لا يحتوي على `views` أو `Database` migrations إذا كان يعتمد كليًا على موديولات أخرى للبيانات.

---

### 2. هيكل مشروع Flutter

**(معرف القسم: `STRU-FLUTTER-APP-001`)**

سيتبع مشروع Flutter هيكلًا منظمًا لتسهيل التطوير والصيانة. الاقتراح هو اتباع نمط معماري مثل Feature-First أو Clean Architecture، مع تجميع الملفات المتعلقة بكل ميزة/موديول.

*   **الهيكل العام داخل مجلد `lib/` (بعد `flutter create project_name`):**
    *   `main.dart`: نقطة الدخول الرئيسية للتطبيق.
    *   `app.dart`: (مقترح) الـ Widget الرئيسي للتطبيق (MaterialApp/CupertinoApp، إعدادات السمات، توجيه المسارات).
    *   `core/`: **(معرف المكون: `STRU-FLUTTER-CORE-001`)**
        *   `constants/`: الثوابت العامة (أسماء المسارات، مفاتيح التخزين، ألوان السمات).
        *   `errors/`: معالجة الأخطاء (exceptions, failures).
        *   `network/`: إعدادات الاتصال بالشبكة (Dio instance, interceptors).
        *   `theme/`: إعدادات السمات.
        *   `usecases/`: (إذا تم استخدام Clean Architecture).
        *   `utils/`: دوال مساعدة عامة.
        *   `widgets/`: Widgets مشتركة وقابلة لإعادة الاستخدام عبر التطبيق.
    *   `config/`:
        *   `router/`: إعدادات توجيه المسارات (e.g., باستخدام GoRouter أو AutoRoute).
        *   `dio_client.dart`: إعدادات العميل HTTP (Dio).
    *   `data/`: **(معرف المكون: `STRU-FLUTTER-DATA-001`)**
        *   `datasources/`:
            *   `remote/`: مصادر البيانات من API (e.g., `auth_remote_datasource.dart`, `car_remote_datasource.dart`).
            *   `local/`: مصادر البيانات المحلية (e.g., `shared_preferences_service.dart`).
        *   `models/`: نماذج البيانات المستلمة من API (e.g., `user_model.dart`, `car_model.dart`).
        *   `repositories/`: تطبيقات واجهات المستودعات (Repositories) (e.g., `auth_repository_impl.dart`).
    *   `domain/`: **(معرف المكون: `STRU-FLUTTER-DOMAIN-001`)** (إذا تم استخدام Clean Architecture)
        *   `entities/`: الكيانات الأساسية.
        *   `repositories/`: واجهات المستودعات.
        *   `usecases/`: حالات الاستخدام الخاصة بكل ميزة.
    *   `presentation/`: **(معرف المكون: `STRU-FLUTTER-PRESENTATION-001`)**
        *   `providers/` (أو `blocs/` أو `cubits/` أو `controllers/`): لإدارة الحالة (State Management) (e.g., باستخدام Riverpod, BLoC, Provider).
        *   `screens/` (أو `pages/`): شاشات التطبيق، يمكن تنظيمها فرعيًا حسب الميزات.
        *   `widgets/`: Widgets خاصة بكل شاشة أو ميزة.
    *   `features/` (أو `modules/`): **(معرف المكون: `STRU-FLUTTER-FEATURESDIR-001`)** (لنمط Feature-First، يعكس موديولات الـ Backend)
        *   `auth/` (يعكس `UserManagement` جزئيًا)
            *   `data/` (datasources, models, repositories خاصة بالمصادقة)
            *   `domain/` (entities, repositories, usecases خاصة بالمصادقة)
            *   `presentation/` (providers, screens, widgets خاصة بالمصادقة مثل `login_screen.dart`, `register_screen.dart`)
        *   `car_catalog/` (يعكس `CarCatalog`)
            *   `data/`
            *   `domain/`
            *   `presentation/` (e.g., `car_list_screen.dart`, `car_detail_screen.dart`, `car_filter_provider.dart`)
        *   `orders/` (يعكس `OrderManagement`)
            *   `data/`
            *   `domain/`
            *   `presentation/` (e.g., `order_form_screen.dart`, `my_orders_screen.dart`)
        *   `profile/` (يعكس جزء إدارة الملف الشخصي من `UserManagement`)
            *   `data/`
            *   `domain/`
            *   `presentation/` (e.g., `profile_screen.dart`, `edit_profile_screen.dart`)
        *   `services/` (يعكس `ServiceManagement`)
        *   `promotions/` (يعكس `PromotionManagement`)
        *   `notifications/` (يعكس `Notification`)
        *   `cms_pages/` (يعكس `Cms` لعرض الصفحات الثابتة)
        *   (وغيرها من الميزات حسب الحاجة)
    *   `generated/`: (للملفات التي يتم إنشاؤها تلقائيًا مثل `l10n.dart` للترجمة، أو ملفات `freezed`/`json_serializable`).
*   `assets/`:
    *   `images/`
    *   `fonts/`
    *   `translations/` (لملفات JSON للترجمة إذا لم يتم استخدام ARB).
*   `test/`: للاختبارات.

---

### 3. مبررات موجزة للقرارات الهيكلية

**(معرف القسم: `STRU-JUSTIFICATIONS-001`)**

1.  **مجلد `Dash/` في جذر المشروع (`STRU-DASH-ASSETS-ROOT-001`):**
    *   **التبرير:** تم وضع أصول Dash (HTML, CSS, JS) في مجلد مخصص في جذر المشروع بناءً على طلب محدد لتسهيل الوصول إليها مباشرة وفصلها عن أصول Laravel القياسية. سيتم استخدام Vite/Mix لتجميع ومعالجة هذه الأصول ونشرها إلى `public/assets/dash/` أو ما يعادله، مما يضمن إدارة أفضل للأصول وتحسين الأداء.
    *   **الاستفادة لـ LLM لاحق:** يعرف LLM أن مصدر الحقيقة لأصول Dash هو `Dash/`، وأن أي تخصيص سيتم إما عبر تكميلها/تجاوزها بملفات CSS/JS إضافية (مثل `resources/js/dash_app.js` و `resources/css/dash_custom.css` التي يتم تجميعها بواسطة Vite/Mix).

2.  **تنظيم واجهات Blade للوحة تحكم Dash (`STRU-LARAVEL-VIEWS-ADMIN-001`):**
    *   **التبرير:** وضع جميع واجهات Blade المتعلقة بلوحة تحكم الإدارة Dash تحت `resources/views/admin/` يوفر مركزية ووضوحًا. التخطيط الرئيسي (`admin_layout.blade.php`) سيكون مسؤولاً عن دمج هيكل `Dash/index.html` مع Blade. المكونات الفرعية (`_sidebar.blade.php`, `_topbar.blade.php`) سيتم استخلاصها وتكييفها من `Dash/index.html` لتعمل بشكل ديناميكي مع بيانات Laravel. تقسيم مكون Stepper إلى أجزاء Blade فرعية تحت `resources/views/admin/partials/stepper_car_form_parts/` يعزز التنظيم.
    *   **الاستفادة لـ LLM لاحق:** يعرف LLM أين يجد وينشئ واجهات Blade للوحة التحكم، وكيف يتم ربطها بالتخطيط الرئيسي الذي يستخدم أصول Dash، وكيفية تنظيم مكونات Stepper.

3.  **وحدات تحكم لوحة تحكم Dash (مزيج من المركزي والخاص بالموجهات):**
    *   **التبرير:** وحدة تحكم مركزية مثل `app/Http/Controllers/Admin/DashboardController.php` مناسبة للصفحة الرئيسية للوحة التحكم التي قد تجمع بيانات من عدة موديولات. أما وحدات التحكم الخاصة بإدارة الكيانات (مثل السيارات، المستخدمين، الطلبات) فمن الأفضل وضعها داخل الموديول المعني تحت `Modules/[ModuleName]/Http/Controllers/Admin/` (مثل `STRU-MOD-CTRL-ADMIN-001`). موديول `Dashboard` (`STRU-MOD-DASHBOARD-001`) سيتولى أيضًا مسؤولية واجهات ووحدات تحكم لوظائف إدارية عامة مثل إعدادات النظام والتقارير. هذا يحافظ على مبدأ الفصل الواضح للمسؤوليات (SoC).
    *   **الاستفادة لـ LLM لاحق:** يعرف LLM مكان البحث عن وحدات التحكم أو إنشائها بناءً على طبيعة الوظيفة (عامة للوحة التحكم، إدارية عامة ضمن موديول `Dashboard`، أو خاصة بموديول وظيفي).

4.  **مسارات لوحة تحكم Dash (`STRU-LARAVEL-ROUTES-ADMIN-001`):**
    *   **التبرير:** ملف `routes/admin.php` مركزي يوفر نقطة دخول واحدة لمسارات لوحة التحكم. يمكن لهذا الملف بعد ذلك تضمين ملفات المسارات الإدارية من كل موديول (e.g., `Modules/[ModuleName]/Routes/admin.php`) باستخدام `Route::group` مع prefix واسم مناسب. هذا يحافظ على تنظيم المسارات ويسمح للموديولات بتعريف مساراتها الإدارية الخاصة.
    *   **الاستفادة لـ LLM لاحق:** يعرف LLM كيفية تعريف وتسجيل المسارات المتعلقة بلوحة التحكم، سواء كانت مركزية أو خاصة بالموجهات.

5.  **ملف `dash_app.js` المقترح (`resources/js/dash_app.js`):**
    *   **التبرير:** ملف `dash_app.js` سيكون نقطة الدخول الرئيسية لـ JavaScript الخاص بلوحة التحكم Dash الذي يتم تجميعه بواسطة Vite/Mix. سيقوم باستيراد أو تضمين منطق `Dash/script.js` الأصلي، وتهيئة مكتبات JS الإضافية، واحتواء الكود المخصص لربط بيانات Laravel بالرسوم البيانية والمكونات الديناميكية، ومعالجة تفاعلات AJAX، وتجاوز سلوكيات `Dash/script.js` الأصلية عند الحاجة.
    *   **الاستفادة لـ LLM لاحق:** يعرف LLM أن منطق JS الإضافي أو التخصيصات للوحة التحكم يجب أن توضع وتُدار من خلال `dash_app.js` وعملية البناء.

6.  **هيكل Flutter (`STRU-FLUTTER-FEATURESDIR-001`):**
    *   **التبرير:** استخدام هيكل قائم على الميزات/الموديولات (Feature-First) في Flutter، حيث يعكس كل مجلد ميزة رئيسية (مثل `auth`, `car_catalog`) موديولات الـ Backend، يسهل التنظيم وفهم الكود، خاصة في التطبيقات الكبيرة. كل مجلد ميزة يحتوي على طبقاته الخاصة (data, domain, presentation).
    *   **الاستفادة لـ LLM لاحق:** يعرف LLM كيفية تنظيم كود Flutter بشكل يتوافق مع هيكل ميزات النظام، مما يسهل تحديد أماكن الملفات عند توليد كود Flutter.

7.  **استخدام `Models/` بدلاً من `Entities/` داخل الموديولات:**
    *   **التبرير:** لتوحيد المصطلحات مع Laravel القياسي، مما يسهل على المطورين فهم الهيكل.
    *   **الاستفادة لـ LLM لاحق:** اتساق في التسمية عبر المشروع.

8.  **إضافة مجلد `DataTransferObjects/` داخل الموديولات:**
    *   **التبرير:** لتحسين هيكلة تمرير البيانات بين طبقات التطبيق، مما يزيد من وضوح الكود وقابليته للصيانة.
    *   **الاستفادة لـ LLM لاحق:** مكان محدد لإنشاء واستخدام DTOs.

---
*(نهاية مستند `STRU-FR.md`)*