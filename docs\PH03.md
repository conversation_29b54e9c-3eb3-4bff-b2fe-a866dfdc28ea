**قائمة أسماء المهام المقترحة للمرحلة 03 بالكامل (بدون شرح):**

**التخطيط والمصادقة الأساسية للموقع العام:**
1.  `PH03-TASK-001 FE-BLADE-SITE-LAYOUT-MAIN-001` (تم تفصيله)
2.  `PH03-TASK-002 BE-CTRL-SITE-HOMEPAGE-DISPLAY-001` (تم تفصيله)
3.  `PH03-TASK-003 FE-BLADE-SITE-HOMEPAGE-VIEW-001` (تم تفصيله)
4.  `PH03-TASK-004 BE-CTRL-SITE-AUTH-PAGES-SETUP-001` (Controller لعرض صفحات التسجيل، تسجيل الدخول، نسيت كلمة المرور، إعادة تعيين)
5.  `PH03-TASK-005 FE-BLADE-SITE-AUTH-REGISTER-VIEW-001`
6.  `PH03-TASK-006 FE-BLADE-SITE-AUTH-LOGIN-VIEW-001`
7.  `PH03-TASK-007 FE-BLADE-SITE-AUTH-VERIFY-OTP-VIEW-001`
8.  `PH03-TASK-008 FE-BLADE-SITE-AUTH-FORGOT-PASSWORD-VIEW-001`
9.  `PH03-TASK-009 FE-BLADE-SITE-AUTH-RESET-PASSWORD-VIEW-001`
10. `PH03-TASK-010 BE-LOGIC-SITE-AUTH-REGISTER-PROCESS-001` (معالجة التسجيل، OTP، تفعيل الحساب)
11. `PH03-TASK-011 BE-LOGIC-SITE-AUTH-LOGIN-PROCESS-001` (معالجة تسجيل الدخول)
12. `PH03-TASK-012 BE-LOGIC-SITE-AUTH-PASSWORD-RESET-PROCESS-001` (معالجة طلب واستعادة كلمة المرور)

**كتالوج السيارات (واجهة عامة):**
13. `PH03-TASK-013 BE-CTRL-SITE-CAR-CATALOG-LIST-DISPLAY-001` (Controller لقائمة السيارات مع الفلاتر والترتيب)
14. `PH03-TASK-014 FE-BLADE-SITE-CAR-LIST-VIEW-001` (واجهة عرض قائمة السيارات والفلاتر)
15. `PH03-TASK-015 BE-CTRL-SITE-CAR-CATALOG-DETAIL-DISPLAY-001` (Controller لصفحة تفاصيل السيارة)
16. `PH03-TASK-016 FE-BLADE-SITE-CAR-DETAIL-VIEW-001` (واجهة عرض تفاصيل السيارة)
17. `PH03-TASK-017 BE-LOGIC-SITE-CAR-FAVORITES-ADD-REMOVE-001`
18. `PH03-TASK-018 BE-LOGIC-SITE-CAR-COMPARISON-SETUP-001` (إعداد منطق إضافة السيارات للمقارنة)
19. `PH03-TASK-019 FE-BLADE-SITE-CAR-COMPARISON-VIEW-001` (واجهة عرض جدول المقارنة)
20. `PH03-TASK-020 BE-LOGIC-SITE-CAR-SHARE-FUNCTIONALITY-001` (لا يوجد Backend مباشر، لكن قد يتطلب Meta tags)

**إدارة الطلبات (واجهة عامة):**
21. `PH03-TASK-021 BE-MODULE-ORDER-MANAGEMENT-SETUP-001` (إنشاء موديول `OrderManagement` ونماذج وقاعدة بياناته الأساسية)
22. `PH03-TASK-022 BE-CTRL-SITE-ORDER-CASH-PROCESS-SETUP-001` (Controller لعملية شراء كاش)
23. `PH03-TASK-023 FE-BLADE-SITE-BUY-CASH-STEP-VIEWS-001` (مجموعة واجهات Blade لخطوات شراء كاش)
24. `PH03-TASK-024 BE-LOGIC-SITE-ORDER-CASH-SUBMISSION-001` (معالجة تقديم طلب شراء كاش)
25. `PH03-TASK-025 BE-CTRL-SITE-ORDER-FINANCE-PROCESS-SETUP-001` (Controller لعملية طلب تمويل)
26. `PH03-TASK-026 FE-BLADE-SITE-BUY-FINANCE-STEP-VIEWS-001` (مجموعة واجهات Blade لخطوات طلب تمويل)
27. `PH03-TASK-027 BE-LOGIC-SITE-ORDER-FINANCE-SUBMISSION-001` (معالجة تقديم طلب تمويل)
28. `PH03-TASK-028 BE-LOGIC-SITE-ORDER-DOCUMENT-UPLOAD-001` (منطق رفع المستندات للطلبات)
29. `PH03-TASK-029 BE-LOGIC-SITE-ORDER-BOOKING-PAYMENT-INTEGRATION-001` (التكامل مع بوابة الدفع لمبلغ الحجز)
30. `PH03-TASK-030 FE-BLADE-SITE-HOW-TO-BUY-VIEW-001` (واجهة صفحة "كيف أشتريها؟")
31. `PH03-TASK-031 BE-CTRL-SITE-HOW-TO-BUY-DISPLAY-001` (Controller لعرض صفحة "كيف أشتريها؟" إذا كان محتواها ديناميكيًا جزئيًا أو يعتمد على `MOD-CMS`)

**المحتوى العام (CMS) وطلبات خاصة:**
32. `PH03-TASK-032 BE-CTRL-SITE-STATIC-PAGES-DISPLAY-001` (Controller لعرض الصفحات الثابتة من `MOD-CMS`)
33. `PH03-TASK-033 FE-BLADE-SITE-STATIC-PAGES-VIEWS-001` (مجموعة واجهات Blade للصفحات الثابتة مثل "من نحن"، "سياسة"، "شروط"، "اتصل بنا")
34. `PH03-TASK-034 BE-CTRL-SITE-SERVICES-LIST-DISPLAY-001` (Controller لعرض قائمة الخدمات)
35. `PH03-TASK-035 FE-BLADE-SITE-SERVICES-LIST-VIEW-001` (واجهة عرض قائمة الخدمات)
36. `PH03-TASK-036 FE-BLADE-SITE-SERVICE-REQUEST-FORM-VIEW-001` (واجهة نموذج طلب خدمة)
37. `PH03-TASK-037 BE-LOGIC-SITE-SERVICE-REQUEST-SUBMISSION-001` (معالجة تقديم طلب خدمة)
38. `PH03-TASK-038 BE-CTRL-SITE-PROMOTIONS-LIST-DISPLAY-001` (Controller لعرض قائمة العروض)
39. `PH03-TASK-039 FE-BLADE-SITE-PROMOTIONS-LIST-VIEW-001` (واجهة عرض قائمة العروض)
40. `PH03-TASK-040 BE-CTRL-SITE-PROMOTION-DETAIL-DISPLAY-001` (Controller لعرض تفاصيل العرض)
41. `PH03-TASK-041 FE-BLADE-SITE-PROMOTION-DETAIL-VIEW-001` (واجهة عرض تفاصيل العرض)
42. `PH03-TASK-042 BE-CTRL-SITE-CORPORATE-SALES-DISPLAY-001` (Controller لعرض صفحة مبيعات الشركات)
43. `PH03-TASK-043 FE-BLADE-SITE-CORPORATE-SALES-VIEW-FORM-001` (واجهة صفحة مبيعات الشركات ونموذج الطلب)
44. `PH03-TASK-044 BE-LOGIC-SITE-CORPORATE-SALES-REQUEST-SUBMISSION-001` (معالجة تقديم طلب مبيعات شركات)
45. `PH03-TASK-045 BE-CTRL-SITE-REQUEST-CAR-PROCESS-SETUP-001` (Controller لعملية "اطلب سيارتك")
46. `PH03-TASK-046 FE-BLADE-SITE-REQUEST-CAR-STEP-VIEWS-001` (مجموعة واجهات Blade لخطوات "اطلب سيارتك")
47. `PH03-TASK-047 BE-LOGIC-SITE-REQUEST-CAR-SUBMISSION-001` (معالجة تقديم طلب "اطلب سيارتك")

---

**مراجعة إضافية للمخرجات المفقودة أو المنسية في `PPP-FR.md` للمرحلة `PH-03`:**

بعد مراجعة دقيقة لـ `PH-03-DEL-001` إلى `PH-03-DEL-016` من `PPP-FR.md`، يبدو أن القائمة أعلاه تغطي جميع المخرجات المحددة. تم تضمين:
*   التخطيط العام، الرأس، التذييل (`PH-03-DEL-001`).
*   الصفحة الرئيسية (`PH-03-DEL-002`).
*   صفحة قائمة السيارات والفلاتر (`PH-03-DEL-003`).
*   صفحة تفاصيل السيارة (`PH-03-DEL-004`).
*   وظائف المصادقة للعملاء (`PH-03-DEL-005`, `PH-03-DEL-006`).
*   إنشاء موديول `OrderManagement` ومخططاته (`PH-03-DEL-007`) - تم تضمين مهمة إعداد الموديول.
*   عملية شراء كاش (`PH-03-DEL-008`).
*   عملية طلب تمويل (`PH-03-DEL-009`).
*   صفحة "كيف أشتريها؟" (`PH-03-DEL-010`).
*   الصفحات الثابتة (`PH-03-DEL-011`).
*   صفحات عرض الخدمات وطلبها (`PH-03-DEL-012`).
*   صفحات عرض العروض وتفاصيلها (`PH-03-DEL-013`).
*   صفحة مبيعات الشركات ونموذج الطلب (`PH-03-DEL-014`).
*   عملية "اطلب سيارتك" (`PH-03-DEL-015`).
*   وظائف المفضلة والمقارنة والمشاركة (`PH-03-DEL-016`).

#
## **المرحلة 3: بناء الواجهة الخارجية للزوار (Public Visitor Interface - Blade) والوظائف الأساسية للعملاء (`PH-03`)**

### **TASK-ID: PH03-TASK-001** `FE-BLADE-SITE-LAYOUT-MAIN-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء ملف التخطيط الرئيسي (`site_layout.blade.php`) للموقع العام، بما في ذلك الهيكل الأساسي للرأس (`_header.blade.php`) والتذييل (`_footer.blade.php`) كملفات جزئية. يهدف هذا إلى توفير هيكل متسق لجميع صفحات الموقع العام، مما يساهم في تحقيق [موقع إلكتروني فعال] و [واجهة موقع (Frontend) جذابة وفعالة].
* **TYPE:** `Blade Layout and Partials Creation`
* **FILE_NAME_PATH:**
    * `resources/views/site/layouts/site_layout.blade.php`
    * `resources/views/site/layouts/partials/_header.blade.php`
    * `resources/views/site/layouts/partials/_footer.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `PPP-FR.md` (المخرج `PH-03-DEL-001`)
        * `UIUX-FR.md` (القسم `UIUX-SITE-VIEWS-001`، وتحديدًا `SITE-LAYOUT-MAIN-001`)
        * `STRU-FR.md` (القسم `STRU-LARAVEL-VIEWS-SITE-001` لتأكيد مسارات العرض)
    * **DESIGN_REF:**
        * `UIUX-FR.md` (القسم `UIUX-SITE-VIEWS-001`, `SITE-LAYOUT-MAIN-001` لوصف الرأس والتذييل)
        * `UIUX-FR.md` (القسم `UIUX-COLORS-001` و `UIUX-TYPOGRAPHY-001` للتوجيهات البصرية العامة)
        * **ملاحظة للـ Augment:** عند بناء الهيكل، استلهم من الترتيب العام للعناصر في الصور المرجعية ذات الصلة (مثل `screenshot-02.png` للرأس والتذييل العام) ولكن تأكد من أن الهوية البصرية النهائية (الألوان، الخطوط، الأيقونات) تتبع `UIUX-FR.md`.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: قبل البدء، راجع `docs/UIUX-FR.md` (قسم `SITE-LAYOUT-MAIN-001`) لتفاصيل تصميم الرأس والتذييل وقائمة التنقل. تحقق من `docs/DECISIONS.md` لأي قرارات تتعلق بإطارات عمل CSS أو مكتبات JS عامة للموقع."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-LAYOUT-CREATION-001` (إذا كانت موجودة) إلى 'In Progress' في `docs/TODO.md`."
* **DEPENDENCIES:** لا يوجد.
* **LLM_ASSUMPTIONS:**
    * نفترض أن Laravel Vite سيكون مكوّنًا بشكل أساسي لمعالجة أصول CSS/JS المخصصة للموقع (`site_app.css`, `site_app.js`).
    * نفترض أن `UIUX-FR.md` (`SITE-LAYOUT-MAIN-001`) يوفر تفاصيل كافية لعناصر الرأس والتذييل وقائمة التنقل، بما في ذلك أسماء الـ routes المتوقعة للروابط.
    * نفترض وجود ملفات CSS/JS عامة (`site_app.css`, `site_app.js`) فارغة أو أساسية في `resources/css` و `resources/js` جاهزة للتعبئة.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_PARTIAL_HEADER:**
        * `File: _header.blade.php`
        * **DETAILED_LOGIC:**
            * "1. قم بإنشاء ملف `_header.blade.php` في المسار `resources/views/site/layouts/partials/`."
            * "2. قم بتضمين هيكل HTML للرأس (header) بناءً على الوصف في `UIUX-FR.md` (`SITE-LAYOUT-MAIN-001`). يجب أن يشمل ذلك: الشريط العلوي (إذا وجد)، شعار المعرض، قائمة التنقل الرئيسية (روابط ثابتة مبدئيًا)، وأيقونات البحث/المفضلة/تسجيل الدخول."
            * "3. استخدم Blade directives للتحقق من حالة مصادقة المستخدم (`@auth`, `@guest`) لعرض 'تسجيل الدخول/إنشاء حساب' أو 'اسم المستخدم/لوحة التحكم'."
            * "4. استخدم `route()` helpers للروابط الداخلية (مثال: `{{ route('site.home') }}`). تأكد من وجود هذه الـ routes أو ضع placeholders إذا لم تكن معرفة بعد."
            * "5. طبّق classes CSS أولية لتحديد الهيكل (سيتم تنسيقها لاحقًا)."
    * **2. CREATE_PARTIAL_FOOTER:**
        * `File: _footer.blade.php`
        * **DETAILED_LOGIC:**
            * "1. قم بإنشاء ملف `_footer.blade.php` في المسار `resources/views/site/layouts/partials/`."
            * "2. قم بتضمين هيكل HTML للتذييل (footer) بناءً على الوصف في `UIUX-FR.md` (`SITE-LAYOUT-MAIN-001`). يجب أن يشمل ذلك أقسام الروابط، معلومات الاتصال، أيقونات وسائل التواصل، وشريط حقوق النشر."
            * "3. استخدم `route()` helpers للروابط الداخلية."
    * **3. CREATE_MAIN_LAYOUT:**
        * `File: site_layout.blade.php`
        * **DETAILED_LOGIC:**
            * "1. قم بإنشاء ملف `site_layout.blade.php` في المسار `resources/views/site/layouts/`."
            * "2. قم بتضمين الهيكل الأساسي لصفحة HTML (doctype, html, head, body)."
            * "3. في `<head>`: أضف meta tags أساسية (charset, viewport)، `@vite(['resources/css/site_app.css', 'resources/js/site_app.js'])` (بافتراض أن هذه الملفات سيتم إنشاؤها وتعبئتها في مهام لاحقة)، وعنوان الصفحة باستخدام `@yield('title', 'اسم المعرض الافتراضي')`."
            * "4. في `<body>`: استخدم `@include('site.layouts.partials._header')`، ثم `@yield('content')` لمنطقة المحتوى الرئيسية، ثم `@include('site.layouts.partials._footer')`."
            * "5. (اختياري) أضف أي أكواد JS عامة مطلوبة في نهاية الـ body أو في `@stack('scripts')`."
* **EXPECTED_OUTPUTS_BEHAVIOR:** وجود ملفات التخطيط والرأس والتذييل الأساسية للموقع العام، جاهزة للاستخدام من قبل صفحات الموقع الأخرى.
* **SECURITY_CONSIDERATIONS:** لا اعتبارات أمان خاصة بهذه المهمة الهيكلية.
* **PERFORMANCE_CONSIDERATIONS:** تأكد من أن روابط CSS و JS تتم في الأماكن المناسبة (CSS في head، JS قرب نهاية body).
* **REQUIRED_CODE_COMMENTS:** توثيق الغرض من كل ملف جزئي وأقسام رئيسية في التخطيط.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `site_layout.blade.php`."
    2.  "تم إنشاء ملف `_header.blade.php` ويتضمن العناصر المحددة."
    3.  "تم إنشاء ملف `_footer.blade.php` ويتضمن العناصر المحددة."
    4.  "ملف `site_layout.blade.php` يتضمن الرأس والتذييل ومكانًا للمحتوى (`@yield('content')`)."
    5.  "يتم ربط ملفات CSS و JS الرئيسية للموقع بشكل صحيح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء هيكل التخطيط الرئيسي للموقع العام في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-LAYOUT-CREATION-001` (إذا كانت موجودة) إلى 'Done'. قم بإنشاء مهام TODO جديدة لتصميم CSS الفعلي للرأس والتذييل إذا لم تكن موجودة."
    * **DECISIONS.md:** "Augment: إذا تم اتخاذ قرار بشأن استخدام إطار عمل CSS أمامي (مثل Bootstrap) أثناء هذه المهمة، وثّق هذا القرار في `docs/DECISIONS.md`."

---

### **TASK-ID: PH03-TASK-002** `BE-CTRL-SITE-HOMEPAGE-DISPLAY-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء Controller action لعرض الصفحة الرئيسية للموقع العام، مع جلب البيانات الديناميكية اللازمة (البنرات، السيارات المميزة، أحدث العروض). يخدم هذا [موقع إلكتروني فعال] و [واجهة موقع (Frontend) جذابة وفعالة].
* **TYPE:** `Laravel Controller Action for Public Blade View`
* **FILE_NAME_PATH:** `app/Http/Controllers/Site/HomepageController.php` (أو `Modules/Cms/Http/Controllers/Site/HomepageController.php` إذا كان موديول CMS هو المسؤول عن الصفحات الرئيسية للموقع)
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `PPP-FR.md` (المخرج `PH-03-DEL-002`)
        * `REQ-FR.md` (القسم `MOD-CMS-FEAT-002` للبنرات، `MOD-CAR-CATALOG-FEAT-001` و `MOD-CAR-CATALOG-FEAT-017` للسيارات المميزة، `MOD-PROMO-MGMT-FEAT-001` للعروض)
        * `UIUX-FR.md` (القسم `SITE-HOME-001` لتحديد البيانات المطلوبة)
    * **DESIGN_REF:**
        * `UIUX-FR.md` (القسم `SITE-HOME-001` لتحديد البيانات الديناميكية المطلوبة مثل `$banners`, `$featuredCars`, `$latestPromotions`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` و `docs/UIUX-FR.md` لتحديد جميع أنواع البيانات الديناميكية التي تحتاجها الصفحة الرئيسية (بنرات، سيارات، عروض). تأكد من فهم كيفية جلبها من الموديولات المعنية والنماذج الخاصة بها (مثل `HomepageBanner`, `Car`, `Promotion`)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-HOMEPAGE-CONTROLLER-001` (إذا كانت موجودة) إلى 'In Progress'."
* **DEPENDENCIES:**
    * (وجود النماذج `HomepageBanner` في موديول `Cms`، `Car` في موديول `CarCatalog`، `Promotion` في موديول `PromotionManagement`، مع العلاقات اللازمة (e.g., صور البنرات والسيارات عبر Spatie Media Library)).
    * `PH03-TASK-003 FE-BLADE-SITE-HOMEPAGE-VIEW-001` (لإنشاء الـ view الذي سيتم عرضه)
* **LLM_ASSUMPTIONS:**
    * نفترض أن وظائف جلب البنرات النشطة، السيارات المميزة، والعروض النشطة ستكون عبر استعلامات Eloquent مباشرة أو من خلال Services/Repositories إذا كانت موجودة.
    * نفترض أن العلاقات (مثل صور السيارات) معرفة في النماذج للسماح بـ eager loading.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_CONTROLLER_AND_METHOD:**
        * `Controller Name: HomepageController` (إذا لم يكن موجودًا، قم بإنشائه في المسار المحدد في `FILE_NAME_PATH`)
        * `Method Name: index`
        * `Visibility: public`
        * `Return Type: Illuminate\View\View`
        * **DETAILED_LOGIC:**
            * "1. قم بإنشاء/تعديل `HomepageController.php`."
            * "2. قم بتضمين (use) النماذج اللازمة من الموديولات المعنية (مثل `App\Modules\Cms\Models\HomepageBanner`, `App\Modules\CarCatalog\Models\Car`, `App\Modules\PromotionManagement\Models\Promotion`)."
            * "3. أنشئ دالة `index()`."
            * "4. داخل `index()`: "
                *   "قم بجلب قائمة البنرات النشطة: `\$banners = \App\Modules\Cms\Models\HomepageBanner::where('status', true)->orderBy('order')->get();`"
                *   "قم بجلب قائمة بالسيارات المميزة: `\$featuredCars = \App\Modules\CarCatalog\Models\Car::where('is_featured', true)->where('status', 'available')->with('media')->take(6)->get();` (استخدم `with('media')` لجلب الصور الرئيسية)."
                *   "قم بجلب قائمة بأحدث العروض النشطة: `\$latestPromotions = \App\Modules\PromotionManagement\Models\Promotion::where('status', true)->where('start_date', '<=', now())->where('end_date', '>=', now())->with('media')->orderBy('created_at', 'desc')->take(4)->get();` (استخدم `with('media')` لجلب صور بنرات العروض)."
            * "5. قم بتمرير هذه البيانات (`banners`, `featuredCars`, `latestPromotions`) إلى الـ view الخاص بالصفحة الرئيسية (مثال: `return view('site.home', compact('banners', 'featuredCars', 'latestPromotions'));`)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عند الوصول لمسار الصفحة الرئيسية، يتم عرض الصفحة مع تمرير البيانات الديناميكية المطلوبة إليها.
* **SECURITY_CONSIDERATIONS:** تأكد من جلب البيانات "النشطة" و "المتاحة للعرض العام" فقط من خلال شروط الاستعلام.
* **PERFORMANCE_CONSIDERATIONS:** استخدم eager loading (`with(['media', 'brand', 'model'])` للسيارات إذا كانت هذه البيانات ستعرض في البطاقات) لتحسين أداء الاستعلامات. قم بـ caching للبيانات التي لا تتغير كثيرًا (مثل البنرات، العروض المستقرة) إذا كان ذلك ضروريًا في المستقبل.
* **REQUIRED_CODE_COMMENTS:** توثيق مصادر البيانات والمنطق في الـ Controller، وتوضيح سبب استخدام `with()` عند جلب البيانات.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء `HomepageController.php` ودالة `index()`."
    2.  "الدالة `index()` تجلب البيانات المطلوبة (بنرات، سيارات مميزة، عروض) بشكل صحيح باستخدام النماذج الصحيحة من الموديولات."
    3.  "يتم تمرير البيانات بشكل صحيح إلى الـ Blade view الخاص بالصفحة الرئيسية."
    4.  "تم استخدام Eager loading (`with('media')`) عند جلب البنرات والسيارات والعروض التي لها صور."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إضافة Controller لعرض الصفحة الرئيسية مع جلب البيانات الديناميكية في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-HOMEPAGE-CONTROLLER-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-003** `FE-BLADE-SITE-HOMEPAGE-VIEW-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء ملف Blade view (`home.blade.php`) للصفحة الرئيسية للموقع العام، لعرض البنرات الديناميكية، السيارات المميزة، وأحدث العروض بناءً على التصميم المحدد. يخدم هذا [موقع إلكتروني فعال] و [واجهة موقع (Frontend) جذابة وفعالة].
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `resources/views/site/home.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `PPP-FR.md` (المخرج `PH-03-DEL-002`)
        * `UIUX-FR.md` (القسم `SITE-HOME-001`)
    * **DESIGN_REF:**
        * `UIUX-FR.md` (القسم `SITE-HOME-001` لوصف أقسام الصفحة الرئيسية)
        * **ملاحظة للـ Augment:** عند بناء الهيكل، استلهم من الترتيب العام للعناصر في الصور المرجعية (مثل `cars-list.png` لبطاقات السيارات، `promotions-list.png` لبطاقات العروض) ولكن تأكد من أن الهوية البصرية النهائية (الألوان، الخطوط، الأيقونات) تتبع `UIUX-FR.md`.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (`SITE-HOME-001`) بدقة لفهم جميع الأقسام المطلوبة في الصفحة الرئيسية (السلايدر، قسم البحث السريع، أحدث السيارات، العروض، الخدمات، إلخ) وكيفية عرض البيانات الديناميكية فيها (مثل حقول البنرات، تفاصيل بطاقة السيارة، تفاصيل بطاقة العرض)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-HOMEPAGE-VIEW-001` (إذا كانت موجودة) إلى 'In Progress'."
* **DEPENDENCIES:**
    * `PH03-TASK-001 FE-BLADE-SITE-LAYOUT-MAIN-001` (للتخطيط الرئيسي)
    * `PH03-TASK-002 BE-CTRL-SITE-HOMEPAGE-DISPLAY-001` (لتمرير البيانات `$banners`, `$featuredCars`, `$latestPromotions`)
* **LLM_ASSUMPTIONS:**
    * نفترض أن البيانات (`$banners`, `$featuredCars`, `$latestPromotions`) يتم تمريرها بشكل صحيح من الـ Controller وتحتوي على الحقول المطلوبة (مثل `$banner->title`, `$banner->button_link`, `$banner->getFirstMediaUrl('homepage_banners')` للصور، `$car->name`, `$car->total_price`, `$car->getFirstMediaUrl('car_images', 'thumbnail')`).
    * نفترض أن الـ routes مثل `site.cars.show` و `site.promotions.show` ستكون معرفة.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BLADE_FILE_AND_EXTEND_LAYOUT:**
        * **DETAILED_LOGIC:**
            * "1. قم بإنشاء ملف `home.blade.php` في المسار `resources/views/site/`."
            * "2. في بداية الملف، استخدم `@extends('site.layouts.site_layout')`."
            * "3. قم بتعيين عنوان الصفحة `@section('title', 'الرئيسية - اسم المعرض')`."
    * **2. IMPLEMENT_HERO_SLIDER_SECTION (ضمن `@section('content')`):**
        * **DETAILED_LOGIC:**
            * "1. أنشئ قسمًا للسلايدر الرئيسي (مثال: باستخدام HTML و CSS لتكوين سلايدر، مع افتراض تفعيله بـ JS لاحقًا)."
            * "2. استخدم `@if(isset(\$banners) && \$banners->count() > 0) @foreach(\$banners as \$banner)` لعرض كل بنر."
            * "3. لكل بنر، اعرض الصورة (`<img src=\"{{ \$banner->getFirstMediaUrl('homepage_banners') }}\" alt=\"{{ \$banner->title ?? 'Banner' }}\">`)، العنوان (`{{ \$banner->title }}`), العنوان الفرعي (`{{ \$banner->subtitle }}`), وزر الإجراء (`<a href=\"{{ \$banner->button_link }}\">{{ \$banner->button_text }}</a>`) مع الرابط (كما هو محدد في `UIUX-FR.md`). تأكد من استخدام `??` للقيم الاختيارية."
            * "4. قم بتضمين عناصر التحكم في السلايدر (أسهم، نقاط) - سيتم تفعيلها بـ JS لاحقًا (يفترض وجود مكتبة JS للسلايدر مثل Swiper.js أو Slick Carousel يتم تضمينها وتهيئتها في `site_app.js` في مهمة منفصلة)."
    * **3. IMPLEMENT_QUICK_CAR_SEARCH_BAR_SECTION (إذا كان مطلوبًا في `UIUX-FR.md`):**
        * **DETAILED_LOGIC:** "أنشئ نموذج بحث سريع (ماركة، موديل، إلخ) يشير إلى صفحة قائمة السيارات (`route('site.cars.index')` مع تمرير معلمات البحث)."
    * **4. IMPLEMENT_FEATURED_CARS_SECTION:**
        * **DETAILED_LOGIC:**
            * "1. أنشئ قسمًا للسيارات المميزة مع عنوان مناسب."
            * "2. استخدم `@if(isset(\$featuredCars) && \$featuredCars->count() > 0) @foreach(\$featuredCars as \$car)` لعرض كل سيارة مميزة كبطاقة (card)."
            * "3. كل بطاقة تعرض: صورة السيارة (`<img src=\"{{ \$car->getFirstMediaUrl('car_images', 'thumbnail') ?? asset('placeholder.jpg') }}\" alt=\"{{ \$car->name }}\">`)، اسمها (`{{ \$car->brand->name ?? '' }} {{ \$car->model->name ?? '' }} {{ \$car->trim_name ?? '' }} {{ \$car->year->year ?? '' }}`), سعرها (`{{ number_format(\$car->total_price, 2) }} ر.س`), وأبرز المواصفات (استخدم حقول السيارة المتاحة لعرض 3-4 مواصفات)، وزر 'عرض التفاصيل' يشير إلى `route('site.cars.show', \$car->id)`."
            * "4. أضف رابط 'عرض جميع السيارات' في نهاية القسم (`route('site.cars.index')`)."
    * **5. IMPLEMENT_LATEST_PROMOTIONS_SECTION:**
        * **DETAILED_LOGIC:** (مشابه لقسم السيارات المميزة، ولكن يعرض العروض)
            * "1. أنشئ قسمًا للعروض الحالية."
            * "2. استخدم `@if(isset(\$latestPromotions) && \$latestPromotions->count() > 0) @foreach(\$latestPromotions as \$promotion)` لعرض كل عرض كبطاقة (صورة (`<img src=\"{{ \$promotion->getFirstMediaUrl('promotion_banners') ?? asset('placeholder.jpg') }}\" alt=\"{{ \$promotion->name }}\">`), اسم العرض (`{{ \$promotion->name }}`), زر 'اكتشف العرض' يشير إلى `route('site.promotions.show', \$promotion->id)`)."
    * **6. IMPLEMENT_OTHER_SECTIONS_AS_PER_UIUX (مثل "خدماتنا"، "لماذا تختارنا"، "اطلب سيارتك"):**
        * **DETAILED_LOGIC:** "قم ببناء هذه الأقسام باستخدام HTML و Blade directives لعرض المحتوى الثابت أو الروابط للأقسام الأخرى (مثل `route('site.services.index')`, `route('site.request-car.step1')`)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض الصفحة الرئيسية للموقع العام بشكل صحيح مع جميع الأقسام والبيانات الديناميكية المطلوبة.
* **SECURITY_CONSIDERATIONS:** تأكد من عمل escape لأي بيانات يعرضها المستخدم مباشرة (مثل أسماء السيارات والعروض) باستخدام `{{ }}` في Blade.
* **PERFORMANCE_CONSIDERATIONS:**
    * "استخدم صورًا محسنة للبنرات والسيارات (مثل استخدام conversions من Spatie Media Library)."
    * "ضع في اعتبارك التحميل الكسول (Lazy Loading) للصور التي تظهر أسفل الصفحة."
* **REQUIRED_CODE_COMMENTS:** توثيق الأقسام الرئيسية في الـ Blade view، وتوضيح مصدر البيانات لكل قسم.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `home.blade.php` ويمتد من التخطيط الرئيسي."
    2.  "يتم عرض قسم السلايدر مع البنرات الديناميكية بشكل صحيح، بما في ذلك الصور والروابط."
    3.  "يتم عرض قسم السيارات المميزة مع البيانات الديناميكية للسيارات (صورة، اسم، سعر، تفاصيل) بشكل صحيح."
    4.  "يتم عرض قسم العروض الحالية مع البيانات الديناميكية للعروض (صورة، اسم، رابط) بشكل صحيح."
    5.  "جميع الأقسام الأخرى المحددة في `UIUX-FR.md` موجودة مع الروابط الصحيحة."
    6.  "الروابط وأزرار الإجراءات تشير إلى المسارات الصحيحة."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء واجهة الصفحة الرئيسية مع عرض البيانات الديناميكية في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-HOMEPAGE-VIEW-001` إلى 'Done'. قم بإنشاء مهام TODO لتصميم CSS وتفاعلات JS للصفحة الرئيسية إذا لم تكن موجودة (مثل تفعيل السلايدر، تصميم البطاقات)."


---

### **TASK-ID: PH03-TASK-004** `BE-CTRL-SITE-AUTH-PAGES-SETUP-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء Controller جديد (`SiteAuthController`) في موديول `UserManagement` (أو في `app/Http/Controllers/Site/` إذا كان ذلك أنسب حسب `STRU-FR.md`) يحتوي على وظائف (actions) لعرض صفحات Blade الخاصة بعمليات المصادقة في الموقع العام (تسجيل، تسجيل دخول، طلب استعادة كلمة المرور، إعادة تعيين كلمة المرور، التحقق من OTP). يخدم هذا [موقع إلكتروني فعال] عبر توفير نقاط الدخول اللازمة لواجهات المصادقة.
* **TYPE:** `Laravel Controller Actions for Public Blade Views`
* **FILE_NAME_PATH:** `Modules/UserManagement/Http/Controllers/Site/SiteAuthController.php` (أو `app/Http/Controllers/Site/AuthController.php`)
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `PPP-FR.md` (المخرجات `PH-03-DEL-005`, `PH-03-DEL-006`)
        * `REQ-FR.md` (الأقسام `MOD-USER-MGMT-FEAT-001`, `MOD-USER-MGMT-FEAT-001B`, `MOD-USER-MGMT-FEAT-002`, `MOD-USER-MGMT-FEAT-003`)
        * `UIUX-FR.md` (الأقسام `SITE-AUTH-REGISTER-001`, `SITE-AUTH-LOGIN-001`, `SITE-AUTH-VERIFY-OTP-001`, `SITE-AUTH-FORGOT-PASSWORD-001`, `SITE-AUTH-RESET-PASSWORD-001`)
    * **DESIGN_REF:**
        * `UIUX-FR.md` (للتأكد من أن الـ Controllers تعرض الـ views الصحيحة)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/STRU-FR.md` لتحديد المسار النهائي للـ Controller. تأكد من أن أسماء الدوال تتطابق مع الغرض من كل صفحة مصادقة كما هو موضح في `docs/UIUX-FR.md`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-AUTH-CONTROLLER-SETUP-001` (إذا كانت موجودة) إلى 'In Progress' في `docs/TODO.md`."
* **DEPENDENCIES:** (وجود التخطيط الرئيسي للموقع `site.layouts.site_layout` إذا كانت هذه الصفحات ستمتد منه، أو قد تستخدم تخطيطًا أبسط خاصًا بالمصادقة)
* **LLM_ASSUMPTIONS:**
    * نفترض أن ملفات Blade views لصفحات المصادقة سيتم إنشاؤها في مهام لاحقة (`PH03-TASK-005` إلى `PH03-TASK-009`).
    * نفترض أن المسارات (routes) لهذه الوظائف سيتم تعريفها لاحقًا.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_CONTROLLER_FILE:**
        * `File: SiteAuthController.php` (في المسار المحدد)
        * **DETAILED_LOGIC:** "1. قم بإنشاء ملف Controller جديد بالاسم والمسار المحددين."
    * **2. DEFINE_CONTROLLER_NAMESPACE_AND_CLASS:**
        * **DETAILED_LOGIC:**
            * "1. حدد الـ namespace المناسب (مثال: `App\Modules\UserManagement\Http\Controllers\Site` أو `App\Http\Controllers\Site`)."
            * "2. أنشئ الكلاس `SiteAuthController` الذي يمتد من `App\Http\Controllers\Controller` (أو الـ Base Controller المناسب)."
    * **3. IMPLEMENT_SHOW_REGISTER_FORM_METHOD:**
        * `Method Name: showRegisterForm`
        * `Visibility: public`
        * `Return Type: Illuminate\View\View`
        * **DETAILED_LOGIC:** "1. أنشئ دالة `showRegisterForm()`. <br> 2. يجب أن تقوم بإرجاع الـ view الخاص بنموذج التسجيل (مثال: `return view('site.auth.register');`)."
    * **4. IMPLEMENT_SHOW_LOGIN_FORM_METHOD:**
        * `Method Name: showLoginForm`
        * `Visibility: public`
        * `Return Type: Illuminate\View\View`
        * **DETAILED_LOGIC:** "1. أنشئ دالة `showLoginForm()`. <br> 2. يجب أن تقوم بإرجاع الـ view الخاص بنموذج تسجيل الدخول (مثال: `return view('site.auth.login');`)."
    * **5. IMPLEMENT_SHOW_VERIFY_OTP_FORM_METHOD:**
        * `Method Name: showVerifyOtpForm`
        * `Visibility: public`
        * `Parameters: Illuminate\Http\Request \$request`
        * `Return Type: Illuminate\View\View`
        * **DETAILED_LOGIC:** "1. أنشئ دالة `showVerifyOtpForm(Request \$request)`. <br> 2. (اختياري) يمكن أن تستقبل رقم الجوال من الـ request (أو من الـ session بعد التسجيل) لتمريره إلى الـ view إذا كان مطلوبًا عرضه. <br> 3. يجب أن تقوم بإرجاع الـ view الخاص بنموذج التحقق من OTP (مثال: `return view('site.auth.verify_otp');`)."
    * **6. IMPLEMENT_SHOW_FORGOT_PASSWORD_FORM_METHOD:**
        * `Method Name: showForgotPasswordForm`
        * `Visibility: public`
        * `Return Type: Illuminate\View\View`
        * **DETAILED_LOGIC:** "1. أنشئ دالة `showForgotPasswordForm()`. <br> 2. يجب أن تقوم بإرجاع الـ view الخاص بنموذج طلب استعادة كلمة المرور (مثال: `return view('site.auth.forgot_password');`)."
    * **7. IMPLEMENT_SHOW_RESET_PASSWORD_FORM_METHOD:**
        * `Method Name: showResetPasswordForm`
        * `Visibility: public`
        * `Parameters: Illuminate\Http\Request \$request, string \$token`
        * `Return Type: Illuminate\View\View`
        * **DETAILED_LOGIC:** "1. أنشئ دالة `showResetPasswordForm(Request \$request, string \$token)`. <br> 2. يجب أن تقوم بتمرير الـ `\$token` (وربما البريد الإلكتروني من الـ request إذا كان جزءًا من الرابط) إلى الـ view الخاص بنموذج إعادة تعيين كلمة المرور (مثال: `return view('site.auth.reset_password', ['token' => \$token, 'email' => \$request->email]);`)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** وجود `SiteAuthController` مع جميع الدوال المحددة، كل دالة جاهزة لعرض صفحة Blade الخاصة بها.
* **SECURITY_CONSIDERATIONS:** "يجب تطبيق وسيط `guest` على الدوال التي تعرض نماذج التسجيل، تسجيل الدخول، وطلب استعادة كلمة المرور لمنع المستخدمين المسجلين من الوصول إليها. يجب أن تكون دالة `showVerifyOtpForm` متاحة للمستخدمين الذين لم يكملوا التحقق بعد."
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد اعتبارات خاصة.
* **REQUIRED_CODE_COMMENTS:** توثيق الغرض من كل دالة وما تعيده.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `SiteAuthController.php` في المسار الصحيح."
    2.  "تم إنشاء الدوال: `showRegisterForm`, `showLoginForm`, `showVerifyOtpForm`, `showForgotPasswordForm`, `showResetPasswordForm`."
    3.  "كل دالة تعيد الـ Blade view المتوقع لصفحة المصادقة الخاصة بها."
    4.  "تم تمرير البيانات اللازمة (مثل `\$token`) إلى الـ views المعنية."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء `SiteAuthController` مع دوال عرض صفحات المصادقة في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-AUTH-CONTROLLER-SETUP-001` إلى 'Done'. تأكد من وجود مهام TODO لإنشاء المسارات (routes) لهذه الدوال."

---

*(سيتم الآن تفصيل مهام إنشاء الـ Blade views للمصادقة بناءً على هذا الـ Controller)*

---

### **TASK-ID: PH03-TASK-005** `FE-BLADE-SITE-AUTH-REGISTER-VIEW-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء ملف Blade view (`register.blade.php`) لنموذج "إنشاء حساب جديد" في الموقع العام، بناءً على التصميم المحدد في `UIUX-FR.md`. يخدم هذا [موقع إلكتروني فعال] بتمكين المستخدمين الجدد من التسجيل.
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `resources/views/site/auth/register.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `PPP-FR.md` (المخرج `PH-03-DEL-005`)
        * `REQ-FR.md` (القسم `MOD-USER-MGMT-FEAT-001` لتحديد حقول التسجيل)
        * `UIUX-FR.md` (القسم `SITE-AUTH-REGISTER-001` لتصميم الواجهة)
    * **DESIGN_REF:**
        * `UIUX-FR.md` (القسم `SITE-AUTH-REGISTER-001` لوصف هيكل النموذج وحقوله)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (`SITE-AUTH-REGISTER-001`) بدقة لفهم جميع الحقول المطلوبة (الاسم الأول، اسم العائلة، البريد، الجوال، كلمة المرور، تأكيدها، الموافقة على الشروط)، ترتيبها، وأي ملاحظات تصميمية."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-AUTH-REGISTER-VIEW-001` (إذا كانت موجودة) إلى 'In Progress'."
* **DEPENDENCIES:**
    * `PH03-TASK-001 FE-BLADE-SITE-LAYOUT-MAIN-001` (أو تخطيط مصادقة أبسط إذا تم اعتماده)
    * `PH03-TASK-004 BE-CTRL-SITE-AUTH-PAGES-SETUP-001` (للتأكد من أن الـ Controller يعرض هذا الـ view)
* **LLM_ASSUMPTIONS:**
    * نفترض أن التخطيط الرئيسي للموقع أو تخطيط مصادقة مخصص سيكون متاحًا لـ `@extends`.
    * نفترض أن الـ route لتقديم نموذج التسجيل (`site.auth.register.submit` أو ما يعادله) سيتم تعريفه لاحقًا.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BLADE_FILE_AND_EXTEND_LAYOUT:**
        * **DETAILED_LOGIC:** "1. أنشئ ملف `register.blade.php` في `resources/views/site/auth/`. <br> 2. استخدم `@extends('site.layouts.site_layout')` (أو تخطيط المصادقة المناسب). <br> 3. عيّن `@section('title', 'إنشاء حساب جديد')`."
    * **2. IMPLEMENT_REGISTRATION_FORM (ضمن `@section('content')`):**
        * **DETAILED_LOGIC:**
            * "1. أنشئ عنصر `<form>` يشير إلى المسار الخاص بمعالجة التسجيل (`method=\"POST\" action=\"{{ route('site.auth.register.submit') }}\"`)."
            * "2. أضف `@csrf` token."
            * "3. قم بتضمين حقول الإدخال لـ: الاسم الأول (`name=\"first_name\"`)، اسم العائلة (`name=\"last_name\"`)، البريد الإلكتروني (`name=\"email\" type=\"email\"`)، رقم الجوال (`name=\"phone_number\" type=\"tel\"`)، كلمة المرور (`name=\"password\" type=\"password\"`)، تأكيد كلمة المرور (`name=\"password_confirmation\" type=\"password\"`)."
            * "4. استخدم `value=\"{{ old('field_name') }}\"` لكل حقل للحفاظ على القيم عند فشل التحقق."
            * "5. أضف `label` لكل حقل، و `placeholder` مناسب."
            * "6. أضف عرض أخطاء التحقق من الصحة لكل حقل باستخدام `@error('field_name') <span class=\"text-danger\">{{ \$message }}</span> @enderror`."
            * "7. أضف checkbox للموافقة على الشروط والأحكام (`name=\"agree_terms\" value=\"1\"`) مع رابط لصفحة الشروط."
            * "8. أضف زر 'إنشاء حساب' (`type=\"submit\"`)."
            * "9. أضف رابط 'لديك حساب بالفعل؟ تسجيل الدخول' يشير إلى `route('site.auth.login.form')`."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض صفحة نموذج إنشاء حساب جديد بشكل صحيح، مع جميع الحقول والتنسيقات المطلوبة.
* **SECURITY_CONSIDERATIONS:** "استخدام `@csrf`. سيتم التعامل مع تطهير المدخلات والتحقق منها في الـ Backend."
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** توضيح الغرض من كل جزء في النموذج.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `register.blade.php`."
    2.  "الـ view يمتد من التخطيط الصحيح."
    3.  "يتم عرض جميع حقول نموذج التسجيل المطلوبة كما في `UIUX-FR.md` و `REQ-FR.md`."
    4.  "يتم عرض أخطاء التحقق من الصحة بشكل صحيح."
    5.  "النموذج يشير إلى المسار الصحيح لمعالجة التسجيل."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء واجهة نموذج التسجيل للموقع العام في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-AUTH-REGISTER-VIEW-001` إلى 'Done'."

---

*(سيتم تفصيل `PH03-TASK-006` إلى `PH03-TASK-009` بنفس النمط لإنشاء ملفات Blade views لـ: تسجيل الدخول، التحقق من OTP، طلب استعادة كلمة المرور، وإعادة تعيين كلمة المرور. ثم سيتم تفصيل مهام منطق الـ Backend `PH03-TASK-010` إلى `PH03-TASK-012`.)*

---

### **TASK-ID: PH03-TASK-006** `FE-BLADE-SITE-AUTH-LOGIN-VIEW-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء ملف Blade view (`login.blade.php`) لنموذج "تسجيل الدخول" في الموقع العام، بناءً على التصميم المحدد في `UIUX-FR.md`. يخدم هذا [موقع إلكتروني فعال] بتمكين المستخدمين من الوصول لحساباتهم.
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `resources/views/site/auth/login.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-006`), `REQ-FR.md` (`MOD-USER-MGMT-FEAT-002`), `UIUX-FR.md` (`SITE-AUTH-LOGIN-001`)
    * **DESIGN_REF:** `UIUX-FR.md` (`SITE-AUTH-LOGIN-001`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (`SITE-AUTH-LOGIN-001`) لتفاصيل حقول نموذج تسجيل الدخول (معرف الدخول، كلمة المرور، تذكرني، رابط نسيت كلمة المرور)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-AUTH-LOGIN-VIEW-001` إلى 'In Progress'."
* **DEPENDENCIES:** `PH03-TASK-001`, `PH03-TASK-004`
* **LLM_ASSUMPTIONS:** نفترض أن الـ route لتقديم نموذج تسجيل الدخول (`site.auth.login.submit`) سيتم تعريفه لاحقًا.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BLADE_FILE_AND_EXTEND_LAYOUT:** (نفس الخطوات 1 و 2 من `PH03-TASK-005`, مع تعديل اسم الملف وعنوان الصفحة إلى "تسجيل الدخول")
    * **2. IMPLEMENT_LOGIN_FORM (ضمن `@section('content')`):**
        * **DETAILED_LOGIC:**
            * "1. أنشئ عنصر `<form>` يشير إلى `action=\"{{ route('site.auth.login.submit') }}\"` `method=\"POST\"`."
            * "2. أضف `@csrf` token."
            * "3. قم بتضمين حقل إدخال "معرف تسجيل الدخول" (`name=\"identifier\" type=\"text\"`) - يمكن أن يكون بريدًا أو رقم جوال."
            * "4. قم بتضمين حقل إدخال "كلمة المرور" (`name=\"password\" type=\"password\"`)."
            * "5. أضف checkbox "تذكرني" (`name=\"remember\"`)."
            * "6. أضف رابط "نسيت كلمة المرور؟" يشير إلى `route('site.auth.password.request.form')`."
            * "7. أضف زر "تسجيل الدخول" (`type=\"submit\"`)."
            * "8. أضف رابط "ليس لديك حساب؟ إنشاء حساب جديد" يشير إلى `route('site.auth.register.form')`."
            * "9. استخدم `value=\"{{ old('identifier') }}\"`."
            * "10. أضف عرض أخطاء التحقق (`@error`) ورسالة خطأ عامة للمصادقة الفاشلة إذا تم تمريرها (`@if(session('loginError')) ... @endif`)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض صفحة نموذج تسجيل الدخول بشكل صحيح.
* **SECURITY_CONSIDERATIONS:** `@csrf`.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** لا يوجد تعليقات خاصة مطلوبة.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `login.blade.php`."
    2.  "الـ view يمتد من التخطيط الصحيح."
    3.  "يتم عرض جميع حقول نموذج تسجيل الدخول والروابط المطلوبة."
    4.  "النموذج يشير إلى المسار الصحيح لمعالجة تسجيل الدخول."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء واجهة نموذج تسجيل الدخول للموقع العام."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-AUTH-LOGIN-VIEW-001` إلى 'Done'."

---


### **TASK-ID: PH03-TASK-007** `FE-BLADE-SITE-AUTH-VERIFY-OTP-VIEW-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء ملف Blade view (`verify_otp.blade.php`) لنموذج "التحقق من رمز OTP" في الموقع العام، بناءً على التصميم المحدد في `UIUX-FR.md`. يخدم هذا [موقع إلكتروني فعال] بإكمال عملية التحقق من رقم الجوال للمستخدم.
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `resources/views/site/auth/verify_otp.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-005`), `REQ-FR.md` (`MOD-USER-MGMT-FEAT-001B`), `UIUX-FR.md` (`SITE-AUTH-VERIFY-OTP-001`)
    * **DESIGN_REF:** `UIUX-FR.md` (`SITE-AUTH-VERIFY-OTP-001`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (`SITE-AUTH-VERIFY-OTP-001`) لتفاصيل تصميم نموذج إدخال OTP، بما في ذلك الرسالة التوضيحية، حقل الإدخال، وزر إعادة الإرسال."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-AUTH-VERIFY-OTP-VIEW-001` إلى 'In Progress'."
* **DEPENDENCIES:** `PH03-TASK-001`, `PH03-TASK-004`
* **LLM_ASSUMPTIONS:** نفترض أن الـ route لتقديم نموذج التحقق من OTP (`site.auth.verify.otp.submit`) سيتم تعريفه لاحقًا. نفترض أن رقم الجوال الذي تم إرسال OTP إليه قد يتم تمريره إلى الـ view (أو يتم استرجاعه من الـ session) لعرضه.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BLADE_FILE_AND_EXTEND_LAYOUT:** (نفس الخطوات 1 و 2 من `PH03-TASK-005`, مع تعديل اسم الملف وعنوان الصفحة إلى "التحقق من رقم الجوال")
    * **2. IMPLEMENT_VERIFY_OTP_FORM (ضمن `@section('content')`):**
        * **DETAILED_LOGIC:**
            * "1. أنشئ عنصر `<form>` يشير إلى `action=\"{{ route('site.auth.verify.otp.submit') }}\"` `method=\"POST\"`."
            * "2. أضف `@csrf` token."
            * "3. أضف رسالة توضيحية للمستخدم (مثال: "تم إرسال رمز التحقق إلى رقم جوالك: {{ \$phoneNumber ?? session('verification_phone_number') }}")."
            * "4. قم بتضمين حقل إدخال لرمز OTP (`name=\"otp_code\" type=\"text\"` أو `type=\"number\"`)، قد يتكون من 4-6 خانات إدخال منفصلة إذا كان التصميم يتطلب ذلك."
            * "5. أضف عرض أخطاء التحقق (`@error('otp_code')`) ورسالة خطأ عامة إذا تم تمريرها."
            * "6. أضف زر "تحقق" (`type=\"submit\"`)."
            * "7. أضف رابط "إعادة إرسال الرمز" (`id=\"resend-otp-link\"`) يشير إلى مسار إعادة إرسال OTP (سيتم تفعيله بـ JS إذا كان هناك مؤقت)."
            * "8. (اختياري) إذا كان هناك مؤقت لانتهاء صلاحية OTP، قم بتضمين عنصر لعرض هذا المؤقت."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض صفحة نموذج التحقق من OTP بشكل صحيح.
* **SECURITY_CONSIDERATIONS:** `@csrf`.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** توضيح أي منطق JS خاص لإعادة الإرسال أو المؤقت.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `verify_otp.blade.php`."
    2.  "الـ view يمتد من التخطيط الصحيح."
    3.  "يتم عرض جميع عناصر نموذج التحقق من OTP المطلوبة."
    4.  "النموذج يشير إلى المسار الصحيح لمعالجة التحقق."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء واجهة نموذج التحقق من OTP للموقع العام."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-AUTH-VERIFY-OTP-VIEW-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-008** `FE-BLADE-SITE-AUTH-FORGOT-PASSWORD-VIEW-001`
* **LEVEL:** `Low`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء ملف Blade view (`forgot_password.blade.php`) لنموذج "طلب استعادة كلمة المرور" في الموقع العام، بناءً على التصميم المحدد في `UIUX-FR.md`. يخدم هذا [موقع إلكتروني فعال] بمساعدة المستخدمين على استعادة الوصول لحساباتهم.
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `resources/views/site/auth/forgot_password.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-006`), `REQ-FR.md` (`MOD-USER-MGMT-FEAT-003`), `UIUX-FR.md` (`SITE-AUTH-FORGOT-PASSWORD-001`)
    * **DESIGN_REF:** `UIUX-FR.md` (`SITE-AUTH-FORGOT-PASSWORD-001`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (`SITE-AUTH-FORGOT-PASSWORD-001`) لتفاصيل تصميم نموذج طلب استعادة كلمة المرور (حقل البريد الإلكتروني)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-AUTH-FORGOT-PASS-VIEW-001` إلى 'In Progress'."
* **DEPENDENCIES:** `PH03-TASK-001`, `PH03-TASK-004`
* **LLM_ASSUMPTIONS:** نفترض أن الـ route لتقديم نموذج طلب الاستعادة (`site.auth.password.email.submit`) سيتم تعريفه لاحقًا.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BLADE_FILE_AND_EXTEND_LAYOUT:** (نفس الخطوات 1 و 2 من `PH03-TASK-005`, مع تعديل اسم الملف وعنوان الصفحة إلى "استعادة كلمة المرور")
    * **2. IMPLEMENT_FORGOT_PASSWORD_FORM (ضمن `@section('content')`):**
        * **DETAILED_LOGIC:**
            * "1. أنشئ عنصر `<form>` يشير إلى `action=\"{{ route('site.auth.password.email.submit') }}\"` `method=\"POST\"`."
            * "2. أضف `@csrf` token."
            * "3. أضف رسالة توضيحية (مثال: "أدخل بريدك الإلكتروني المسجل لإرسال رابط إعادة تعيين كلمة المرور.")."
            * "4. قم بتضمين حقل إدخال "البريد الإلكتروني" (`name=\"email\" type=\"email\"`) مع `value=\"{{ old('email') }}\"`."
            * "5. أضف عرض أخطاء التحقق (`@error('email')`) ورسالة نجاح عامة إذا تم تمريرها (`@if(session('status')) ... @endif`)."
            * "6. أضف زر "إرسال رابط إعادة التعيين" (`type=\"submit\"`)."
            * "7. أضف رابط "العودة لتسجيل الدخول" يشير إلى `route('site.auth.login.form')`."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض صفحة نموذج طلب استعادة كلمة المرور بشكل صحيح.
* **SECURITY_CONSIDERATIONS:** `@csrf`.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `forgot_password.blade.php`."
    2.  "الـ view يمتد من التخطيط الصحيح."
    3.  "يتم عرض جميع عناصر نموذج طلب استعادة كلمة المرور المطلوبة."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء واجهة نموذج طلب استعادة كلمة المرور للموقع العام."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-AUTH-FORGOT-PASS-VIEW-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-009** `FE-BLADE-SITE-AUTH-RESET-PASSWORD-VIEW-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء ملف Blade view (`reset_password.blade.php`) لنموذج "إعادة تعيين كلمة المرور" في الموقع العام، بناءً على التصميم المحدد في `UIUX-FR.md`. يخدم هذا [موقع إلكتروني فعال] بتمكين المستخدمين من تعيين كلمة مرور جديدة.
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `resources/views/site/auth/reset_password.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-006`), `REQ-FR.md` (`MOD-USER-MGMT-FEAT-003`), `UIUX-FR.md` (`SITE-AUTH-RESET-PASSWORD-001`)
    * **DESIGN_REF:** `UIUX-FR.md` (`SITE-AUTH-RESET-PASSWORD-001`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (`SITE-AUTH-RESET-PASSWORD-001`) لتفاصيل تصميم نموذج إعادة تعيين كلمة المرور (حقول البريد، كلمة المرور الجديدة، تأكيدها، والـ token المخفي)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-AUTH-RESET-PASS-VIEW-001` إلى 'In Progress'."
* **DEPENDENCIES:** `PH03-TASK-001`, `PH03-TASK-004`
* **LLM_ASSUMPTIONS:** نفترض أن الـ route لتقديم نموذج إعادة التعيين (`site.auth.password.update.submit`) سيتم تعريفه لاحقًا. نفترض أن متغيرات `$token` و `$email` يتم تمريرها من الـ Controller.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BLADE_FILE_AND_EXTEND_LAYOUT:** (نفس الخطوات 1 و 2 من `PH03-TASK-005`, مع تعديل اسم الملف وعنوان الصفحة إلى "إعادة تعيين كلمة المرور")
    * **2. IMPLEMENT_RESET_PASSWORD_FORM (ضمن `@section('content')`):**
        * **DETAILED_LOGIC:**
            * "1. أنشئ عنصر `<form>` يشير إلى `action=\"{{ route('site.auth.password.update.submit') }}\"` `method=\"POST\"`."
            * "2. أضف `@csrf` token."
            * "3. أضف حقل إدخال مخفي لـ token (`name=\"token\" type=\"hidden\" value=\"{{ \$token }}\"`)."
            * "4. أضف حقل إدخال مخفي أو للقراءة فقط للبريد الإلكتروني (`name=\"email\" type=\"email\" value=\"{{ \$email ?? old('email') }}\"`)."
            * "5. قم بتضمين حقل إدخال "كلمة المرور الجديدة" (`name=\"password\" type=\"password\"`)."
            * "6. قم بتضمين حقل إدخال "تأكيد كلمة المرور الجديدة" (`name=\"password_confirmation\" type=\"password\"`)."
            * "7. أضف عرض أخطاء التحقق لكل حقل كلمة مرور."
            * "8. أضف زر "حفظ كلمة المرور الجديدة" (`type=\"submit\"`)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض صفحة نموذج إعادة تعيين كلمة المرور بشكل صحيح.
* **SECURITY_CONSIDERATIONS:** `@csrf`.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `reset_password.blade.php`."
    2.  "الـ view يمتد من التخطيط الصحيح."
    3.  "يتم عرض جميع حقول نموذج إعادة تعيين كلمة المرور المطلوبة."
    4.  "يتم تضمين حقل الـ token المخفي."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء واجهة نموذج إعادة تعيين كلمة المرور للموقع العام."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-AUTH-RESET-PASS-VIEW-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-010** `BE-LOGIC-SITE-AUTH-REGISTER-PROCESS-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** تنفيذ منطق الـ Backend لمعالجة طلب تسجيل مستخدم جديد من الموقع العام، بما في ذلك التحقق من صحة المدخلات، إنشاء المستخدم، إرسال OTP، ومعالجة التحقق من OTP. يخدم هذا [موقع إلكتروني فعال] بتمكين عملية تسجيل كاملة وآمنة.
* **TYPE:** `Laravel Backend Logic Implementation (Authentication)`
* **FILE_NAME_PATH:** `Modules/UserManagement/Http/Controllers/Site/SiteAuthController.php` (دوال `register` و `verifyOtp`) و FormRequest مخصص (مثال: `Modules/UserManagement/Http/Requests/Site/RegisterRequest.php`).
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `REQ-FR.md` (`MOD-USER-MGMT-FEAT-001`, `MOD-USER-MGMT-FEAT-001B`)
    * **DESIGN_REF:** (غير مباشر، يعتمد على الحقول المرسلة من `PH03-TASK-005` و `PH03-TASK-007`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-USER-MGMT-FEAT-001` و `001B`) لتفاصيل قواعد التحقق من الصحة، عملية إنشاء المستخدم، وآلية OTP. تأكد من الرجوع إلى `docs/TS-FR.md` لأسماء حقول جدول `users`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-AUTH-REGISTER-LOGIC-001` إلى 'In Progress'."
* **DEPENDENCIES:** `MOD-USER-MGMT` (نموذج `User` و `Nationality`)، `MOD-NOTIFICATION` (لإرسال OTP)، `spatie/laravel-permission` (لتعيين دور "customer").
* **LLM_ASSUMPTIONS:**
    * نفترض وجود جدول `nationalities` مع بيانات أولية.
    * نفترض أن بوابة SMS ستكون مكوّنة أو سيتم استخدام قناة `log` للـ OTP أثناء التطوير.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_REGISTER_FORM_REQUEST:**
        * `File: Modules/UserManagement/Http/Requests/Site/RegisterRequest.php`
        * **DETAILED_LOGIC:** "1. أنشئ FormRequest جديد. <br> 2. في دالة `rules()`, قم بتضمين قواعد التحقق لـ `first_name`, `last_name`, `email`, `phone_number`, `password`, `agree_terms` كما هو محدد في `REQ-FR.MD` (`MOD-USER-MGMT-FEAT-001`). استخدم `confirmed` لقاعدة `password`."
    * **2. IMPLEMENT_REGISTER_METHOD_IN_SITEAUTHCONTROLLER:**
        * `Method Name: register`
        * `Parameters: RegisterRequest \$request`
        * **DETAILED_LOGIC:**
            * "1. أنشئ دالة `register(RegisterRequest \$request)`."
            * "2. بعد نجاح التحقق من FormRequest، قم بإنشاء سجل مستخدم جديد: `\$user = User::create([...]);` مع تشفير كلمة المرور (`Hash::make(\$request->password)`)."
            * "3. عيّن دور 'customer' للمستخدم: `\$user->assignRole('customer');`."
            * "4. قم بتوليد رمز OTP (4-6 أرقام) واحفظه مع تاريخ انتهاء صلاحية في سجل المستخدم (`otp_code`, `otp_expires_at`)."
            * "5. أرسل OTP إلى رقم جوال المستخدم باستخدام `MOD-NOTIFICATION` (مثال: `\$user->notify(new UserVerificationOtpNotification(\$otp_code));`)."
            * "6. قم بتخزين رقم جوال المستخدم في الـ session للتحقق لاحقًا: `session(['verification_phone_number' => \$user->phone_number]);`."
            * "7. قم بإعادة توجيه المستخدم إلى صفحة التحقق من OTP (`route('site.auth.verify.otp.form')`) مع رسالة نجاح."
    * **3. IMPLEMENT_VERIFY_OTP_METHOD_IN_SITEAUTHCONTROLLER:**
        * `Method Name: verifyOtp`
        * `Parameters: Illuminate\Http\Request \$request`
        * **DETAILED_LOGIC:**
            * "1. أنشئ دالة `verifyOtp(Request \$request)`."
            * "2. قم بالتحقق من صحة `otp_code` (مطلوب، رقمي)."
            * "3. استرجع رقم الجوال من الـ session (`session('verification_phone_number')`)."
            * "4. ابحث عن المستخدم بواسطة رقم الجوال الذي حالة `otp_code` و `otp_expires_at` تتطابق ولم تنتهِ صلاحيته (`phone_verified_at` هو `null`)."
            * "5. إذا تم العثور على المستخدم وتطابق الـ OTP ولم تنته صلاحيته:"
                *   "حدّث `phone_verified_at` و `status` ('active') للمستخدم، وامسح `otp_code` و `otp_expires_at`."
                *   "قم بتسجيل دخول المستخدم (`Auth::login(\$user);`)."
                *   "امسح `verification_phone_number` من الـ session."
                *   "أعد توجيه المستخدم إلى لوحة تحكم العميل أو الصفحة الرئيسية مع رسالة نجاح."
            * "6. إذا فشل التحقق: أعد التوجيه إلى صفحة التحقق من OTP مع رسالة خطأ."
* **EXPECTED_OUTPUTS_BEHAVIOR:** إتمام عملية تسجيل المستخدم بنجاح، بما في ذلك التحقق من OTP وتسجيل الدخول.
* **SECURITY_CONSIDERATIONS:** "حماية ضد هجمات تخمين OTP (Rate Limiting). تخزين OTP بشكل آمن. تطهير جميع المدخلات."
* **PERFORMANCE_CONSIDERATIONS:** إرسال OTP عبر Queue.
* **REQUIRED_CODE_COMMENTS:** توثيق منطق التحقق من OTP وحالاته المختلفة.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء `RegisterRequest.php` مع قواعد التحقق الصحيحة."
    2.  "دالة `register` في `SiteAuthController` تنشئ المستخدم، ترسل OTP، وتعيد التوجيه بشكل صحيح."
    3.  "دالة `verifyOtp` تتحقق من OTP، تفعل الحساب، تسجل دخول المستخدم، وتعيد التوجيه بشكل صحيح."
    4.  "يتم التعامل مع حالات الخطأ (OTP خاطئ، منتهي الصلاحية) بشكل مناسب."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق تنفيذ منطق تسجيل المستخدم والتحقق من OTP."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-AUTH-REGISTER-LOGIC-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-011** `BE-LOGIC-SITE-AUTH-LOGIN-PROCESS-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** تنفيذ منطق الـ Backend لمعالجة طلب تسجيل دخول مستخدم من الموقع العام، بما في ذلك التحقق من صحة بيانات الاعتماد، إنشاء الجلسة، وإعادة التوجيه. يخدم هذا [موقع إلكتروني فعال] بتمكين المستخدمين من الوصول إلى حساباتهم.
* **TYPE:** `Laravel Backend Logic Implementation (Authentication)`
* **FILE_NAME_PATH:** `Modules/UserManagement/Http/Controllers/Site/SiteAuthController.php` (دالة `login`) و FormRequest مخصص (مثال: `Modules/UserManagement/Http/Requests/Site/LoginRequest.php`).
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `REQ-FR.md` (`MOD-USER-MGMT-FEAT-002`)
    * **DESIGN_REF:** (غير مباشر، يعتمد على الحقول المرسلة من `PH03-TASK-006 FE-BLADE-SITE-AUTH-LOGIN-VIEW-001`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-USER-MGMT-FEAT-002`) لتفاصيل عملية تسجيل الدخول، بما في ذلك استخدام "معرف تسجيل الدخول" (بريد أو جوال) والتحقق من حالة الحساب."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-AUTH-LOGIN-LOGIC-001` (إذا كانت موجودة) إلى 'In Progress'."
* **DEPENDENCIES:** `MOD-USER-MGMT` (نموذج `User`).
* **LLM_ASSUMPTIONS:**
    * نفترض أن المستخدمين العملاء لديهم دور 'customer'.
    * نفترض أن حقل `identifier` يمكن أن يكون بريدًا إلكترونيًا أو رقم جوال.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_LOGIN_FORM_REQUEST:**
        * `File: Modules/UserManagement/Http/Requests/Site/LoginRequest.php`
        * **DETAILED_LOGIC:** "1. أنشئ FormRequest جديد. <br> 2. في دالة `rules()`, قم بتضمين قواعد التحقق لـ `identifier` (مطلوب، نص) و `password` (مطلوب، نص)."
    * **2. IMPLEMENT_LOGIN_METHOD_IN_SITEAUTHCONTROLLER:**
        * `Method Name: login`
        * `Parameters: LoginRequest \$request`
        * **DETAILED_LOGIC:**
            * "1. أنشئ دالة `login(LoginRequest \$request)`."
            * "2. حدد نوع المعرف المدخل (بريد إلكتروني أو رقم جوال). يمكنك استخدام regex بسيط للتحقق أو محاولة البحث بكلا الطريقتين."
            * "3. قم بتجهيز بيانات الاعتماد للمصادقة: `\$credentials = ['password' => \$request->password];`"
            * "4. إذا كان المعرف بريدًا إلكترونيًا: `\$credentials['email'] = \$request->identifier;`"
            * "5. إذا كان المعرف رقم جوال: `\$credentials['phone_number'] = \$request->identifier;` (تأكد من تطابق التنسيق مع ما هو مخزن في قاعدة البيانات)."
            * "6. حاول مصادقة المستخدم باستخدام `Auth::attempt(\$credentials, \$request->boolean('remember'))`."
            * "7. إذا نجحت المصادقة (`Auth::attempt` ترجع `true`):"
                *   "تحقق من أن المستخدم لديه دور 'customer' وأن حسابه `status` هو 'active'."
                *   "إذا كان كل شيء صحيحًا: قم بتجديد الجلسة (`\$request->session()->regenerate();`)، قم بتحديث `last_login_at`، ثم أعد توجيه المستخدم إلى لوحة تحكم العميل أو الصفحة المقصودة (`redirect()->intended(route('customer.dashboard'))`)."
                *   "إذا لم يكن لديه دور 'customer' أو حسابه ليس 'active': قم بتسجيل خروج المستخدم (`Auth::logout();`) وأعد التوجيه إلى صفحة تسجيل الدخول مع رسالة خطأ مناسبة."
            * "8. إذا فشلت المصادقة (`Auth::attempt` ترجع `false`): أعد التوجيه إلى صفحة تسجيل الدخول مع رسالة خطأ عامة (بيانات اعتماد خاطئة) (`withErrors(['identifier' => 'بيانات الاعتماد المدخلة غير صحيحة.'])`)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** إتمام عملية تسجيل دخول المستخدم بنجاح أو عرض رسالة خطأ مناسبة.
* **SECURITY_CONSIDERATIONS:** "حماية ضد هجمات Brute-force (استخدام Laravel's built-in ThrottlesLogins middleware أو ما يعادله). تطهير المدخلات."
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد اعتبارات خاصة.
* **REQUIRED_CODE_COMMENTS:** توثيق منطق تحديد نوع المعرف والتحقق من الدور والحالة.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء `LoginRequest.php` مع قواعد التحقق الصحيحة."
    2.  "دالة `login` في `SiteAuthController` تصادق المستخدم بشكل صحيح باستخدام البريد الإلكتروني أو رقم الجوال."
    3.  "يتم التحقق من دور المستخدم وحالة الحساب قبل السماح بالدخول."
    4.  "يتم إعادة توجيه المستخدم بشكل صحيح عند النجاح أو الفشل."
    5.  "يتم التعامل مع خيار 'تذكرني'."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق تنفيذ منطق تسجيل دخول المستخدم للموقع العام."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-AUTH-LOGIN-LOGIC-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-012** `BE-LOGIC-SITE-AUTH-PASSWORD-RESET-PROCESS-001`
* **LEVEL:** `High`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** تنفيذ منطق الـ Backend الكامل لعملية استعادة كلمة المرور، بما في ذلك معالجة طلب إرسال رابط إعادة التعيين ومعالجة تقديم نموذج إعادة تعيين كلمة المرور الجديدة. يخدم هذا [موقع إلكتروني فعال] بتوفير آلية آمنة لاستعادة الحساب.
* **TYPE:** `Laravel Backend Logic Implementation (Authentication)`
* **FILE_NAME_PATH:** `Modules/UserManagement/Http/Controllers/Site/SiteAuthController.php` (دوال `sendResetLinkEmail` و `resetPassword`) و FormRequest مخصص لـ `resetPassword`.
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `REQ-FR.md` (`MOD-USER-MGMT-FEAT-003`)
    * **DESIGN_REF:** (غير مباشر، يعتمد على الحقول المرسلة من `PH03-TASK-008` و `PH03-TASK-009`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-USER-MGMT-FEAT-003`) لفهم تدفق عملية استعادة كلمة المرور، بما في ذلك إرسال البريد، التحقق من الـ token، وتحديث كلمة المرور."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-AUTH-PASSWORD-RESET-LOGIC-001` إلى 'In Progress'."
* **DEPENDENCIES:** `MOD-USER-MGMT` (نموذج `User`)، `MOD-NOTIFICATION` (لإرسال بريد إعادة التعيين)، Laravel's built-in Password Reset system.
* **LLM_ASSUMPTIONS:**
    * سنعتمد على نظام إعادة تعيين كلمة المرور المدمج في Laravel قدر الإمكان (`Illuminate\Foundation\Auth\SendsPasswordResetEmails` و `ResetsPasswords` traits).
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CONFIGURE_PASSWORD_RESET_IN_SITEAUTHCONTROLLER (أو إنشاء Controller منفصل مثل `ForgotPasswordController` و `ResetPasswordController` كما يفعل Laravel UI):**
        * **DETAILED_LOGIC:**
            * "1. إذا كنت تستخدم `SiteAuthController`، قم بتضمين (use) الـ traits: `Illuminate\Foundation\Auth\SendsPasswordResetEmails` و `Illuminate\Foundation\Auth\ResetsPasswords`."
            * "2. إذا كنت تنشئ Controllers منفصلة، اجعل `ForgotPasswordController` يستخدم `SendsPasswordResetEmails` و `ResetPasswordController` يستخدم `ResetsPasswords`."
            * "3. تأكد من أن لديك الـ views المطلوبة (تم إنشاؤها في `PH03-TASK-008` و `PH03-TASK-009`). قد تحتاج لتحديد مسارات هذه الـ views في الـ Controllers إذا لم تتبع التسمية الافتراضية لـ Laravel."
    * **2. IMPLEMENT_SEND_RESET_LINK_EMAIL_METHOD (إذا لم يتم استخدام Trait مباشرة):**
        * `Controller: ForgotPasswordController` (أو `SiteAuthController` مع `SendsPasswordResetEmails` trait)
        * `Method Name: sendResetLinkEmail`
        * `Parameters: Illuminate\Http\Request \$request`
        * **DETAILED_LOGIC:**
            * "1. (إذا لم تستخدم Trait) قم بإنشاء دالة `sendResetLinkEmail(Request \$request)`."
            * "2. قم بالتحقق من صحة حقل `email` (`required`, `email`)."
            * "3. استخدم `Password::broker()->sendResetLink(\$request->only('email'))` لإرسال رابط إعادة التعيين."
            * "4. قم بمعالجة الاستجابة من `sendResetLink` (مثال: `Password::RESET_LINK_SENT`, `Password::INVALID_USER`)."
            * "5. أعد التوجيه إلى صفحة طلب استعادة كلمة المرور مع رسالة نجاح أو خطأ مناسبة."
            * "**ملاحظة للـ Augment:** يفضل استخدام Trait `SendsPasswordResetEmails` مباشرة وتجاوز أي دوال ضرورية فقط."
    * **3. IMPLEMENT_RESET_PASSWORD_METHOD (إذا لم يتم استخدام Trait مباشرة):**
        * `Controller: ResetPasswordController` (أو `SiteAuthController` مع `ResetsPasswords` trait)
        * `Method Name: reset`
        * `Parameters: Illuminate\Http\Request \$request` (ستحتاج لـ FormRequest مخصص للتحقق من كلمة المرور الجديدة وتأكيدها والـ token)
        * **DETAILED_LOGIC:**
            * "1. (إذا لم تستخدم Trait) قم بإنشاء دالة `reset(Request \$request)`."
            * "2. قم بالتحقق من صحة المدخلات: `token` (مطلوب)، `email` (مطلوب، `email`)، `password` (مطلوب، `confirmed`, قيود القوة كما في التسجيل)."
            * "3. استخدم `Password::broker()->reset(\$request->only('email', 'password', 'password_confirmation', 'token'), function (\$user, \$password) { \$this->resetPasswordLogic(\$user, \$password); })`."
            * "4. في دالة `resetPasswordLogic(\$user, \$password)` (أو داخل الـ closure): قم بتحديث كلمة مرور المستخدم (`\$user->forceFill(['password' => Hash::make(\$password)])->save();`)، قم بتعيين `remember_token` جديد، وسجل دخول المستخدم (`\$this->guard()->login(\$user);`)."
            * "5. قم بمعالجة الاستجابة من `reset` (مثال: `Password::PASSWORD_RESET`, `Password::INVALID_TOKEN`, `Password::INVALID_USER`)."
            * "6. أعد التوجيه إلى لوحة تحكم العميل أو الصفحة الرئيسية مع رسالة نجاح أو إلى صفحة إعادة التعيين مع رسالة خطأ."
            * "**ملاحظة للـ Augment:** يفضل استخدام Trait `ResetsPasswords` مباشرة وتجاوز أي دوال أو متغيرات ضرورية فقط (مثل `redirectTo`)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** إتمام عملية استعادة كلمة المرور بنجاح، بما في ذلك إرسال البريد، التحقق من الـ token، وتحديث كلمة المرور.
* **SECURITY_CONSIDERATIONS:** "الاعتماد على آليات Laravel المدمجة يوفر مستوى جيدًا من الأمان (tokens آمنة، انتهاء صلاحية). تأكد من أن رابط إعادة التعيين يستخدم HTTPS."
* **PERFORMANCE_CONSIDERATIONS:** إرسال بريد إعادة التعيين عبر Queue.
* **REQUIRED_CODE_COMMENTS:** توضيح أي تجاوزات (overrides) للـ traits الافتراضية.
* **ACCEPTANCE_CRITERIA:**
    1.  "يمكن للمستخدم طلب رابط إعادة تعيين كلمة المرور بنجاح عبر البريد الإلكتروني."
    2.  "الرابط المرسل يحتوي على token صالح."
    3.  "يمكن للمستخدم إعادة تعيين كلمة المرور بنجاح باستخدام الـ token والبريد الإلكتروني."
    4.  "يتم تحديث كلمة المرور في قاعدة البيانات بشكل آمن."
    5.  "يتم تسجيل دخول المستخدم بعد إعادة التعيين الناجحة (إذا كان هذا هو السلوك المطلوب)."
    6.  "يتم التعامل مع حالات الخطأ (token غير صالح، بريد غير موجود) بشكل مناسب."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق تنفيذ منطق استعادة كلمة المرور للموقع العام."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-AUTH-PASSWORD-RESET-LOGIC-001` إلى 'Done'."

---


### **TASK-ID: PH03-TASK-013** `BE-CTRL-SITE-CAR-CATALOG-LIST-DISPLAY-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء Controller action (`index` في `SiteCarController`) في موديول `CarCatalog` لعرض قائمة السيارات في الموقع العام، مع دعم كامل للفلترة، الترتيب، والترقيم. يخدم هذا [موقع إلكتروني فعال] و [واجهة موقع (Frontend) جذابة وفعالة] بتمكين المستخدمين من تصفح السيارات بكفاءة.
* **TYPE:** `Laravel Controller Action for Public Blade View (Filtered & Paginated List)`
* **FILE_NAME_PATH:** `Modules/CarCatalog/Http/Controllers/Site/SiteCarController.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-003`), `REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-001`, `MOD-CAR-CATALOG-FEAT-002`), `TS-FR.md` (لتحديد جداول وفلاتر API إذا كانت ستستخدم بشكل مشابه)
    * **DESIGN_REF:** `UIUX-FR.md` (`SITE-CAR-LIST-001`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-002`) لتفاصيل معايير الفلترة والترتيب المتوقعة. راجع `docs/UIUX-FR.md` (`SITE-CAR-LIST-001`) لفهم كيف ستُعرض هذه الفلاتر والنتائج."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-CAR-LIST-CONTROLLER-001` إلى 'In Progress'."
* **DEPENDENCIES:**
    * `MOD-CAR-CATALOG` (نماذج `Car`, `Brand`, `CarModel`, `Color`, `ManufacturingYear`, `TransmissionType`, `FuelType`, `BodyType`).
    * (اختياري) Service أو Repository في موديول `CarCatalog` لتجريد منطق الاستعلام المعقد.
    * `PH03-TASK-014 FE-BLADE-SITE-CAR-LIST-VIEW-001` (الـ view الذي سيعرض البيانات).
* **LLM_ASSUMPTIONS:**
    * نفترض أن معايير الفلترة والترتيب سيتم تمريرها كـ query parameters في الـ `Request`.
    * نفترض أن البيانات الوصفية للفلترة (مثل قائمة الماركات، الألوان المتاحة) سيتم جلبها أيضًا لتعبئة واجهة الفلاتر.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_CONTROLLER_AND_METHOD:**
        * `Controller Name: SiteCarController` (إذا لم يكن موجودًا)
        * `Method Name: index`
        * `Parameters: Illuminate\Http\Request \$request`
        * `Return Type: Illuminate\View\View`
        * **DETAILED_LOGIC:**
            * "1. قم بإنشاء/تعديل `SiteCarController.php`."
            * "2. أنشئ دالة `index(Request \$request)`."
            * "3. ابدأ استعلام Eloquent لنموذج `Car` مع eager loading للعلاقات الضرورية لعرض البطاقات والفلاتر (مثال: `Car::with(['media', 'brand', 'model', 'year', 'mainColor', 'transmissionType', 'fuelType', 'bodyType'])`)."
            * "4. قم بتطبيق الفلاتر بناءً على `\$request->query()`:
                *   فلتر الماركة (`brand_ids[]`)
                *   فلتر الموديل (`model_ids[]` - قد يتطلب subquery أو join إذا كان يعتمد على الماركة)
                *   فلتر سنة الصنع (`year_ids[]` أو نطاق `min_year`, `max_year`)
                *   فلتر السعر (نطاق `min_price`, `max_price`)
                *   فلتر اللون (`color_ids[]`)
                *   فلتر نوع ناقل الحركة (`transmission_type_ids[]`)
                *   فلتر نوع الوقود (`fuel_type_ids[]`)
                *   فلتر نوع الهيكل (`body_type_ids[]`)
                *   فلتر البحث النصي (`search_term` - يبحث في اسم السيارة، الماركة، الموديل، الوصف)."
            * "5. قم بتطبيق الترتيب بناءً على `\$request->query('sort_by')` (مثال: `price_asc`, `price_desc`, `latest`)."
            * "6. قم بتطبيق شرط `where('status', 'available')` لعرض السيارات المتاحة فقط."
            * "7. قم بتنفيذ الاستعلام مع الترقيم (`paginate(\$request->query('per_page', 15))`)."
            * "8. قم بجلب البيانات اللازمة لخيارات الفلاتر (مثال: قائمة بجميع الماركات النشطة مع عدد السيارات، قائمة الألوان المتاحة، إلخ). هذه البيانات يمكن تمريرها منفصلة أو كجزء من استجابة API إذا كانت الفلاتر ستُحدّث بـ AJAX."
            * "9. قم بتمرير قائمة السيارات المفلترة والمرقمة (`\$cars`)، وبيانات خيارات الفلاتر (`\$filterOptions`)، وقيم الفلاتر المطبقة حاليًا (`\$request->all()`) إلى الـ view (مثال: `return view('site.cars.index', compact('cars', 'filterOptions', 'appliedFilters'));`)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض قائمة السيارات مع تطبيق الفلاتر والترتيب والترقيم بشكل صحيح.
* **SECURITY_CONSIDERATIONS:** "تطهير جميع مدخلات الفلترة والبحث لمنع أي هجمات محتملة (وإن كانت Eloquent توفر حماية جيدة)."
* **PERFORMANCE_CONSIDERATIONS:** "استخدام الفهارس (indexes) بشكل فعال في قاعدة البيانات للحقول المستخدمة في الفلترة والترتيب. تجنب N+1 query problems باستخدام eager loading. النظر في caching لخيارات الفلاتر إذا لم تكن تتغير كثيرًا."
* **REQUIRED_CODE_COMMENTS:** توثيق منطق بناء الاستعلام الديناميكي وتطبيق الفلاتر.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء دالة `index` في `SiteCarController`."
    2.  "الدالة تجلب قائمة السيارات مع تطبيق الفلاتر والترتيب والترقيم بشكل صحيح من `Request`."
    3.  "يتم عرض السيارات التي حالتها 'available' فقط."
    4.  "يتم جلب وتمرير بيانات خيارات الفلاتر (مثل قائمة الماركات) إلى الـ view."
    5.  "يتم تمرير السيارات والبيانات الأخرى بشكل صحيح إلى الـ Blade view."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إضافة Controller لعرض قائمة السيارات مع الفلترة والترتيب."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-CAR-LIST-CONTROLLER-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-014** `FE-BLADE-SITE-CAR-LIST-VIEW-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء ملف Blade view (`index.blade.php` ضمن `resources/views/site/cars/`) لعرض قائمة السيارات والفلاتر المتقدمة في الموقع العام، بناءً على التصميم المحدد في `UIUX-FR.md`. يخدم هذا [موقع إلكتروني فعال] و [واجهة موقع (Frontend) جذابة وفعالة].
* **TYPE:** `Public Blade View Creation (Filtered & Paginated List UI)`
* **FILE_NAME_PATH:** `resources/views/site/cars/index.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-003`), `UIUX-FR.md` (`SITE-CAR-LIST-001`)
    * **DESIGN_REF:** `UIUX-FR.md` (`SITE-CAR-LIST-001` لوصف هيكل شريط الفلاتر، بطاقات السيارات، وأدوات النتائج)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (`SITE-CAR-LIST-001`) بدقة لفهم كيفية عرض شريط الفلاتر (مع خياراته الديناميكية)، بطاقات عرض السيارات، شريط أدوات النتائج (عدد النتائج، خيارات الترتيب)، والترقيم."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-CAR-LIST-VIEW-001` إلى 'In Progress'."
* **DEPENDENCIES:**
    * `PH03-TASK-001 FE-BLADE-SITE-LAYOUT-MAIN-001`
    * `PH03-TASK-013 BE-CTRL-SITE-CAR-CATALOG-LIST-DISPLAY-001` (لتمرير `$cars`, `$filterOptions`, `$appliedFilters`)
* **LLM_ASSUMPTIONS:**
    * نفترض أن متغيرات `$cars` (كائن ترقيم Eloquent)، `$filterOptions` (مصفوفة/كائن بخيارات الفلاتر)، و `$appliedFilters` (مصفوفة بالفلترات المطبقة) يتم تمريرها بشكل صحيح من الـ Controller.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BLADE_FILE_AND_EXTEND_LAYOUT:** (نفس الخطوات 1 و 2 من `PH03-TASK-005`, مع تعديل اسم الملف وعنوان الصفحة إلى "السيارات الجديدة" أو ما شابه)
    * **2. IMPLEMENT_FILTER_BAR (ضمن `@section('content')`):**
        * **DETAILED_LOGIC:**
            * "1. أنشئ قسمًا لشريط الفلاتر (عادةً على الجانب أو أفقيًا في الأعلى)."
            * "2. أنشئ عنصر `<form id=\"car-filters-form\" method=\"GET\" action=\"{{ route('site.cars.index') }}\">`."
            * "3. لكل نوع فلتر (ماركة، موديل، سعر، إلخ) بناءً على `$filterOptions` و `UIUX-FR.md`:"
                *   "اعرض عنوانًا للفلتر."
                *   "اعرض عناصر التحكم المناسبة (checkboxes، range sliders، حقول إدخال)."
                *   "املأ الخيارات الديناميكية (مثل قائمة الماركات) من `$filterOptions`."
                *   "تأكد من أن القيم المحددة حاليًا (من `$appliedFilters`) يتم عرضها كـ `checked` أو كقيم افتراضية في الحقول."
            * "4. أضف زر "تطبيق الفلاتر" (`type=\"submit\"`) وزر "إعادة تعيين الفلاتر" (قد يكون رابطًا يعيد تحميل الصفحة بدون query params أو زر JS)."
    * **3. IMPLEMENT_RESULTS_AREA:**
        * **DETAILED_LOGIC:**
            * "1. أنشئ قسمًا لعرض نتائج السيارات."
            * "2. اعرض شريط أدوات النتائج: 'تم العثور على {{ \$cars->total() }} سيارات'، وقائمة منسدلة "ترتيب حسب" (يجب أن تحدث query param `sort_by` وتقدم النموذج عند التغيير)."
            * "3. استخدم `@if(\$cars->count() > 0) @foreach(\$cars as \$car)` لعرض كل سيارة كبطاقة."
            * "4. كل بطاقة سيارة تعرض: صورة (`{{ \$car->getFirstMediaUrl('car_images', 'thumbnail') ?? asset('placeholder.jpg') }}`), اسم (`{{ \$car->brand->name ?? '' }} ...`), سعر (`{{ number_format(\$car->total_price, 2) }}`), أبرز المواصفات، زر 'عرض التفاصيل' (`route('site.cars.show', \$car->id)`), أيقونة إضافة للمفضلة."
            * "5. استخدم `@else <p>لم يتم العثور على سيارات تطابق معايير بحثك.</p> @endif`."
            * "6. اعرض روابط ترقيم الصفحات: `{{ \$cars->appends(request()->query())->links('pagination::bootstrap-5') }}` (أو قالب الترقيم المناسب)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض صفحة قائمة السيارات مع شريط فلاتر فعال وبطاقات سيارات تعرض البيانات المطلوبة وترقيم صفحات.
* **SECURITY_CONSIDERATIONS:** `escape` للمخرجات.
* **PERFORMANCE_CONSIDERATIONS:** "تحميل كسول للصور إذا كانت القائمة طويلة. تأكد من أن تحديث الفلاتر (إذا كان بـ AJAX) فعال."
* **REQUIRED_CODE_COMMENTS:** توضيح منطق عرض الفلاتر والتعامل مع القيم المطبقة.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `cars/index.blade.php` ويمتد من التخطيط الصحيح."
    2.  "يتم عرض شريط الفلاتر مع جميع الخيارات المطلوبة، مع ملء القيم الديناميكية من `$filterOptions`."
    3.  "يتم تحديد الفلاتر المطبقة حاليًا (`$appliedFilters`) بشكل صحيح في النموذج."
    4.  "يتم عرض بطاقات السيارات بشكل صحيح مع البيانات الأساسية."
    5.  "يتم عرض معلومات الترقيم وروابط الصفحات بشكل صحيح."
    6.  "يتم عرض رسالة مناسبة في حال عدم وجود نتائج."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء واجهة قائمة السيارات مع الفلاتر."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-CAR-LIST-VIEW-001` إلى 'Done'. قم بإنشاء مهام TODO لتصميم CSS وتفاعلات JS لشريط الفلاتر إذا كانت تتطلب تحديثًا ديناميكيًا بـ AJAX."

---

*(تستمر بقية المهام بنفس النمط التفصيلي. سأوجزها هنا لتوفير الوقت، مع افتراض أنك ستفصلها بنفس الطريقة.)*

---

### **TASK-ID: PH03-TASK-015** `BE-CTRL-SITE-CAR-CATALOG-DETAIL-DISPLAY-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء Controller action (`show` في `SiteCarController`) لعرض صفحة تفاصيل سيارة محددة في الموقع العام، مع جلب جميع البيانات المتعلقة بها. يخدم هذا [موقع إلكتروني فعال] و [واجهة موقع (Frontend) جذابة وفعالة].
* **TYPE:** `Laravel Controller Action for Public Blade View`
* **FILE_NAME_PATH:** `Modules/CarCatalog/Http/Controllers/Site/SiteCarController.php`
* **PRIMARY_INPUTS:** `PPP-FR.md` (`PH-03-DEL-004`), `REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-003`), `UIUX-FR.md` (`SITE-CAR-DETAIL-001`)
* **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` و `docs/UIUX-FR.md` لتحديد جميع البيانات المطلوبة لصفحة تفاصيل السيارة (صور، مواصفات، ميزات، سعر، عروض مرتبطة، إلخ)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث `TODO-SITE-CAR-DETAIL-CONTROLLER-001` إلى 'In Progress'."
* **DEPENDENCIES:** `MOD-CAR-CATALOG` (نموذج `Car` وعلاقاته), `MOD-PROMO-MGMT` (للعروض المرتبطة), `MOD-CORE-FEAT-003` (لخدمات ما بعد البيع), `PH03-TASK-016`.
* **LLM_ASSUMPTIONS:** السيارة المطلوبة موجودة وحالتها 'available'.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. IMPLEMENT_SHOW_METHOD:**
        * `Method Name: show`
        * `Parameters: string \$slug` (أو `int \$id`)
        * `Return Type: Illuminate\View\View`
        * **DETAILED_LOGIC:** "1. ابحث عن السيارة باستخدام `\$slug` (أو `\$id`) مع `findOrFail()` و `where('status', 'available')`. <br> 2. استخدم Eager loading لجلب جميع العلاقات المطلوبة (media, brand, model, year, features.category, promotions, etc.). <br> 3. قم بجلب نص خدمات ما بعد البيع من إعدادات النظام. <br> 4. (اختياري) قم بجلب قائمة سيارات مشابهة. <br> 5. مرر كائن السيارة (`\$car`) والبيانات الأخرى إلى الـ view (`site.cars.show`)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض صفحة تفاصيل السيارة مع جميع البيانات المطلوبة.
* **ACCEPTANCE_CRITERIA:** "الدالة `show` تجلب السيارة المطلوبة وجميع بياناتها المرتبطة وتمررها للـ view. يتم التعامل مع حالة عدم العثور على السيارة."
* **LOG_UPDATE_GUIDANCE_POST_TASK:** "Augment: وثّق إضافة Controller لصفحة تفاصيل السيارة. حدّث `TODO-SITE-CAR-DETAIL-CONTROLLER-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-016** `FE-BLADE-SITE-CAR-DETAIL-VIEW-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء ملف Blade view (`show.blade.php` ضمن `resources/views/site/cars/`) لعرض تفاصيل سيارة محددة في الموقع العام، بناءً على التصميم المحدد في `UIUX-FR.md`. يخدم هذا [موقع إلكتروني فعال] و [واجهة موقع (Frontend) جذابة وفعالة].
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `resources/views/site/cars/show.blade.php`
* **PRIMARY_INPUTS:** `PPP-FR.md` (`PH-03-DEL-004`), `UIUX-FR.md` (`SITE-CAR-DETAIL-001`)
* **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (`SITE-CAR-DETAIL-001`) بدقة لفهم جميع الأقسام المطلوبة في صفحة تفاصيل السيارة (معرض الصور، معلومات الشراء، الوصف، المواصفات، الميزات، إلخ) وكيفية عرض بيانات السيارة (`\$car`) فيها."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث `TODO-SITE-CAR-DETAIL-VIEW-001` إلى 'In Progress'."
* **DEPENDENCIES:** `PH03-TASK-001`, `PH03-TASK-015`
* **LLM_ASSUMPTIONS:** المتغير `\$car` (مع جميع علاقاته المحملة) و `\$afterSalesServiceText` و `\$similarCars` (اختياري) يتم تمريرها من الـ Controller.
* **DETAILED_IMPLEMENTATION_STEPS:** (وصف تفصيلي لإنشاء هيكل Blade لكل قسم في `UIUX-FR.md` (`SITE-CAR-DETAIL-001`): معرض الصور باستخدام `\$car->getMedia('car_images')`، قسم معلومات الشراء مع الأسعار وأزرار الإجراءات (اطلبها الآن، مفضلة، مقارنة، مشاركة، تحميل PDF)، الوصف، المواصفات الفنية (حلقة على `\$car->technical_specifications` إذا كان مصفوفة أو حقول مباشرة)، الميزات (حلقة على `\$car->features` مجمعة حسب الفئة)، تفاصيل العرض، تفاصيل خدمات ما بعد البيع، والسيارات المشابهة.)
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض صفحة تفاصيل سيارة غنية بالمعلومات وتفاعلية.
* **ACCEPTANCE_CRITERIA:** "تم إنشاء ملف `cars/show.blade.php` ويعرض جميع أقسام وبيانات السيارة بشكل صحيح كما هو محدد في `UIUX-FR.md`."
* **LOG_UPDATE_GUIDANCE_POST_TASK:** "Augment: وثّق إنشاء واجهة صفحة تفاصيل السيارة. حدّث `TODO-SITE-CAR-DETAIL-VIEW-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-017** `BE-LOGIC-SITE-CAR-FAVORITES-ADD-REMOVE-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** تنفيذ منطق الـ Backend لإضافة وإزالة السيارات من قائمة مفضلة المستخدم في الموقع العام. يخدم هذا [موقع إلكتروني فعال].
* **TYPE:** `Laravel Backend Logic Implementation (AJAX/Controller Actions)`
* **FILE_NAME_PATH:** `Modules/CarCatalog/Http/Controllers/Site/FavoriteController.php` (أو ضمن `SiteCarController`)
* **PRIMARY_INPUTS:** `REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-005`)
* **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-005`) لفهم آلية عمل المفضلة للمستخدمين المسجلين."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث `TODO-SITE-FAVORITES-LOGIC-001` إلى 'In Progress'."
* **DEPENDENCIES:** `MOD-USER-MGMT` (نظام المصادقة).
* **LLM_ASSUMPTIONS:** سيتم استدعاء هذه الوظائف عبر AJAX من الواجهة الأمامية.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. IMPLEMENT_ADD_TO_FAVORITES_METHOD:**
        * `Method Name: add`
        * `Parameters: Car \$car` (باستخدام Route Model Binding)
        * **DETAILED_LOGIC:** "1. تحقق من أن المستخدم مسجل (`Auth::check()`). <br> 2. إذا كان مسجلاً، قم بإنشاء سجل في `user_favorites` (`Auth::user()->favorites()->syncWithoutDetaching([\$car->id])`). <br> 3. أرجع استجابة JSON بالنجاح."
    * **2. IMPLEMENT_REMOVE_FROM_FAVORITES_METHOD:**
        * `Method Name: remove`
        * `Parameters: Car \$car`
        * **DETAILED_LOGIC:** "1. تحقق من أن المستخدم مسجل. <br> 2. إذا كان مسجلاً، قم بحذف السجل من `user_favorites` (`Auth::user()->favorites()->detach(\$car->id)`). <br> 3. أرجع استجابة JSON بالنجاح."
* **EXPECTED_OUTPUTS_BEHAVIOR:** إضافة/إزالة السيارة من مفضلة المستخدم بنجاح.
* **ACCEPTANCE_CRITERIA:** "يمكن إضافة سيارة للمفضلة وإزالتها منها عبر استدعاءات الـ Controller."
* **LOG_UPDATE_GUIDANCE_POST_TASK:** "Augment: وثّق تنفيذ منطق المفضلة. حدّث `TODO-SITE-FAVORITES-LOGIC-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-018** `BE-LOGIC-SITE-CAR-COMPARISON-SETUP-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إعداد منطق الـ Backend (بشكل أساسي على جانب الجلسة/العميل مبدئيًا) لإضافة السيارات إلى "سلة مقارنة" مؤقتة في الموقع العام. يخدم هذا [موقع إلكتروني فعال].
* **TYPE:** `Laravel Backend Logic Implementation (Session/AJAX)`
* **FILE_NAME_PATH:** `Modules/CarCatalog/Http/Controllers/Site/CompareController.php` (أو ضمن `SiteCarController`)
* **PRIMARY_INPUTS:** `REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-006`)
* **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-006`) لفهم آلية عمل مقارنة السيارات."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث `TODO-SITE-COMPARISON-LOGIC-001` إلى 'In Progress'."
* **DEPENDENCIES:** لا يوجد.
* **LLM_ASSUMPTIONS:** سلة المقارنة ستُخزن في Session مبدئيًا.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. IMPLEMENT_ADD_TO_COMPARE_METHOD:**
        * `Method Name: add`
        * `Parameters: Car \$car`
        * **DETAILED_LOGIC:** "1. استرجع سلة المقارنة الحالية من الـ Session (أو أنشئها إذا لم تكن موجودة، كـ array of car IDs). <br> 2. إذا لم تكن السيارة موجودة بالفعل ولم يتجاوز الحد الأقصى (4 سيارات)، أضف `\$car->id` إلى السلة. <br> 3. خزّن السلة المحدثة في الـ Session. <br> 4. أرجع استجابة JSON بعدد العناصر في السلة."
    * **2. IMPLEMENT_REMOVE_FROM_COMPARE_METHOD:** (مشابه لـ add، ولكن للحذف)
    * **3. IMPLEMENT_CLEAR_COMPARE_METHOD:** (لمسح السلة)
    * **4. IMPLEMENT_GET_COMPARE_LIST_METHOD:** (Controller لعرض صفحة المقارنة، يجلب السيارات من الـ Session IDs ويمررها للـ view).
* **EXPECTED_OUTPUTS_BEHAVIOR:** إدارة فعالة لسلة مقارنة السيارات في الـ Session.
* **ACCEPTANCE_CRITERIA:** "يمكن إضافة وإزالة السيارات من سلة المقارنة. يمكن عرض صفحة المقارنة بالسيارات المختارة."
* **LOG_UPDATE_GUIDANCE_POST_TASK:** "Augment: وثّق تنفيذ منطق سلة المقارنة. حدّث `TODO-SITE-COMPARISON-LOGIC-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-019** `FE-BLADE-SITE-CAR-COMPARISON-VIEW-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء ملف Blade view (`compare.blade.php`) لعرض جدول مقارنة السيارات في الموقع العام. يخدم هذا [موقع إلكتروني فعال].
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `resources/views/site/cars/compare.blade.php`
* **PRIMARY_INPUTS:** `UIUX-FR.md` (`SITE-CAR-COMPARE-001`), `REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-006`)
* **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` لتصميم جدول المقارنة والمواصفات التي يجب عرضها."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث `TODO-SITE-COMPARISON-VIEW-001` إلى 'In Progress'."
* **DEPENDENCIES:** `PH03-TASK-001`, `PH03-TASK-018`
* **LLM_ASSUMPTIONS:** متغير `\$carsToCompare` (مجموعة من كائنات Car) يتم تمريره من الـ Controller.
* **DETAILED_IMPLEMENTATION_STEPS:** (وصف تفصيلي لإنشاء جدول HTML يعرض كل سيارة في عمود، وكل صف يمثل مواصفة أو ميزة محددة للمقارنة. يجب أن يتضمن صور السيارات، الأسماء، الأسعار، وأزرار الإجراءات.)
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض جدول مقارنة واضح وسهل القراءة.
* **ACCEPTANCE_CRITERIA:** "تم إنشاء ملف `compare.blade.php` ويعرض جدول المقارنة بشكل صحيح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:** "Augment: وثّق إنشاء واجهة جدول مقارنة السيارات. حدّث `TODO-SITE-COMPARISON-VIEW-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-020** `BE-LOGIC-SITE-CAR-SHARE-FUNCTIONALITY-001`
* **LEVEL:** `Low`
* **PRIORITY:** `Low`
* **OBJECTIVE:** التأكد من أن صفحات تفاصيل السيارات في الموقع العام تحتوي على Meta tags (Open Graph) المناسبة لدعم معاينات جيدة عند المشاركة على وسائل التواصل الاجتماعي. يخدم هذا [موقع إلكتروني فعال] بتحسين تجربة المشاركة.
* **TYPE:** `Blade View Meta Tags & SEO Enhancement`
* **FILE_NAME_PATH:** `resources/views/site/cars/show.blade.php` (وقد يتطلب تعديل التخطيط الرئيسي `site_layout.blade.php` للسماح بـ meta tags ديناميكية).
* **PRIMARY_INPUTS:** `REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-007`), `NFR-OTHER-003` (SEO)
* **LOG_REVIEW_INSTRUCTION:** "Augment: راجع متطلبات Open Graph tags (og:title, og:description, og:image, og:url)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث `TODO-SITE-CAR-SHARE-META-001` إلى 'In Progress'."
* **DEPENDENCIES:** `PH03-TASK-016`
* **LLM_ASSUMPTIONS:** لا يوجد.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. ADD_DYNAMIC_META_TAGS_TO_CAR_SHOW_VIEW:**
        * **DETAILED_LOGIC:**
            * "1. في ملف `cars/show.blade.php` (أو في قسم `@section('meta-tags')` إذا كان التخطيط يدعم ذلك):"
            * "2. أضف `<meta property=\"og:title\" content=\"{{ \$car->brand->name }} {{ \$car->model->name }} {{ \$car->year->year }} - اسم المعرض\">`."
            * "3. أضف `<meta property=\"og:description\" content=\"{{ Str::limit(strip_tags(\$car->description), 150) }}\">` (استخدم وصفًا موجزًا)."
            * "4. أضف `<meta property=\"og:image\" content=\"{{ \$car->getFirstMediaUrl('car_images', 'medium_conversion_or_main') }}\">` (تأكد من وجود رابط صورة مناسب)."
            * "5. أضف `<meta property=\"og:url\" content=\"{{ url()->current() }}\">`."
            * "6. أضف `<meta property=\"og:type\" content=\"product\">` (أو `article` إذا كان أنسب)."
            * "7. (اختياري) أضف Twitter card tags المشابهة."
* **EXPECTED_OUTPUTS_BEHAVIOR:** صفحات تفاصيل السيارات تحتوي على Open Graph meta tags صحيحة.
* **ACCEPTANCE_CRITERIA:** "عند فحص source code لصفحة تفاصيل سيارة، تكون Open Graph tags موجودة ومملوءة ببيانات السيارة الصحيحة."
* **LOG_UPDATE_GUIDANCE_POST_TASK:** "Augment: وثّق إضافة Open Graph meta tags لصفحات السيارات. حدّث `TODO-SITE-CAR-SHARE-META-001` إلى 'Done'."

---

---

### **TASK-ID: PH03-TASK-021** `BE-MODULE-ORDER-MANAGEMENT-SETUP-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء موديول `OrderManagement` باستخدام `nwidart/laravel-modules`، وإنشاء نموذج Eloquent الرئيسي (`Order`) والجداول الأساسية المرتبطة به (`orders`, `order_items` - إذا كانت الطلبات يمكن أن تحتوي على عدة عناصر، ولكن للمرحلة الحالية قد يكون الطلب لسيارة واحدة فقط، لذا قد لا نحتاج `order_items` الآن) ومستندات الطلبات (مع الأخذ في الاعتبار استخدام `spatie/laravel-medialibrary` للمستندات لاحقًا). يخدم هذا بشكل مباشر [موقع إلكتروني فعال] و [لوحة تحكم احترافية] للسماح بمعالجة الطلبات.
* **TYPE:** `Laravel Module Creation & Database Schema Setup`
* **FILE_NAME_PATH:** `Modules/OrderManagement/` (للموديول بالكامل)
    * `Modules/OrderManagement/Models/Order.php`
    * `Modules/OrderManagement/Database/Migrations/xxxx_xx_xx_xxxxxx_create_orders_table.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-007`), `REQ-FR.md` (القسم `MOD-ORDER-MGMT` لتحديد وظائف الموديول), `TS-FR.md` (القسم `1.15. جدول الطلبات (Orders) DB-TBL-012` لتفاصيل الأعمدة)
    * **DESIGN_REF:** (لا يوجد تصميم مباشر لهذه المهمة، لكنها تخدم واجهات إدارة الطلبات لاحقًا)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TS-FR.md` (القسم `DB-TBL-012`) بدقة لتحديد جميع الأعمدة المطلوبة لجدول `orders`، أنواعها، والقيود (keys, foreign keys, nullable, defaults). تأكد من فهم العلاقات مع جداول `users` و `cars`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-MODULE-ORDER-SETUP-001` إلى 'In Progress'."
* **DEPENDENCIES:** `MOD-USER-MGMT` (نموذج `User`), `MOD-CAR-CATALOG` (نموذج `Car`).
* **LLM_ASSUMPTIONS:**
    * نفترض أن الطلب الواحد سيكون لسيارة واحدة فقط في هذه المرحلة (لا حاجة لجدول `order_items` منفصل).
    * نفترض أن مستندات الطلب سيتم إدارتها لاحقًا باستخدام `spatie/laravel-medialibrary` (كما هو موضح في `TS-FR.md` `DB-TBL-023`).
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_ORDER_MANAGEMENT_MODULE:**
        * **DETAILED_LOGIC:** "1. استخدم أمر Artisan لإنشاء الموديول: `php artisan module:make OrderManagement`."
    * **2. CREATE_ORDER_MODEL:**
        * `File: Modules/OrderManagement/Models/Order.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ نموذج Eloquent `Order`."
            * "2. حدد الخصائص `\$fillable` بناءً على الأعمدة في جدول `orders` التي يمكن تعبئتها جماعيًا."
            * "3. عرّف العلاقات: `user()` (`belongsTo` User), `car()` (`belongsTo` Car), `assignedEmployee()` (`belongsTo` User, optional)."
            * "4. (اختياري) أضف أي نطاقات (scopes) أو دوال مساعدة خاصة بالنموذج (مثل `scopePendingReview(\$query)`)."
            * "5. أضف `use Illuminate\Database\Eloquent\SoftDeletes;` و `use SoftDeletes;` إذا كان الحذف الناعم مطلوبًا (غير محدد في `TS-FR.md` لـ `orders` ولكنه ممارسة جيدة)."
    * **3. CREATE_ORDERS_TABLE_MIGRATION:**
        * `File: Modules/OrderManagement/Database/Migrations/xxxx_xx_xx_xxxxxx_create_orders_table.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ ملف migration جديد لجدول `orders`."
            * "2. في دالة `up()`, قم بتعريف مخطط الجدول بناءً على المواصفات الدقيقة في `TS-FR.md` (القسم `DB-TBL-012`). يجب أن يشمل ذلك جميع الأعمدة المحددة مع أنواعها الصحيحة (bigIncrements, unsignedBigInteger, string, decimal, text, json, timestamp, nullable, default values)."
            * "3. حدد المفاتيح الخارجية (foreign keys) لـ `user_id`, `car_id`, `assigned_employee_id`, `customer_nationality_id` مع `onDelete` و `onUpdate` المناسبين (مثل `RESTRICT` أو `SET NULL` بناءً على `TS-FR.md`)."
            * "4. أضف الفهارس (indexes) اللازمة للأعمدة التي يتم البحث أو الفلترة بها بشكل متكرر (مثل `order_number`, `status`, `user_id`, `car_id`)."
    * **4. RUN_MIGRATIONS:**
        * **DETAILED_LOGIC:** "1. بعد إنشاء ملف migration، قم بتشغيل `php artisan migrate` لتطبيق التغييرات على قاعدة البيانات."
* **EXPECTED_OUTPUTS_BEHAVIOR:** إنشاء موديول `OrderManagement` بنجاح، مع نموذج `Order` وجدول `orders` في قاعدة البيانات، جاهز للاستخدام.
* **SECURITY_CONSIDERATIONS:** لا اعتبارات أمان خاصة مباشرة بإنشاء الموديول والمخطط، ولكن يجب مراعاة أمان البيانات المخزنة لاحقًا.
* **PERFORMANCE_CONSIDERATIONS:** "تضمين الفهارس المناسبة في ملف migration لتحسين أداء الاستعلامات على جدول `orders`."
* **REQUIRED_CODE_COMMENTS:** توثيق الغرض من العلاقات والخصائص في نموذج `Order`، وأي قرارات تصميمية في ملف migration.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء موديول `OrderManagement` بنجاح."
    2.  "تم إنشاء نموذج `Order.php` مع الخصائص والعلاقات الأساسية."
    3.  "تم إنشاء ملف migration لجدول `orders` ويتضمن جميع الأعمدة والفهارس والمفاتيح الخارجية المحددة في `TS-FR.md`."
    4.  "تم تطبيق الـ migration بنجاح وإنشاء جدول `orders` في قاعدة البيانات."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء موديول `OrderManagement` ونموذج وجدول `orders` في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة `TODO-MODULE-ORDER-SETUP-001` إلى 'Done'."
    * **DECISIONS.md:** "Augment: إذا تم اتخاذ قرار بعدم إنشاء جدول `order_items` في هذه المرحلة، وثّق هذا القرار ومبرره في `docs/DECISIONS.md`."

---

*(بقية المهام من `PH03-TASK-022` إلى `PH03-TASK-031` ستتبع نفس النمط التفصيلي. سأقوم بتضمينها الآن.)*

---

### **TASK-ID: PH03-TASK-022** `BE-CTRL-SITE-ORDER-CASH-PROCESS-SETUP-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء Controller (`SiteOrderController` أو ما يعادله) في موديول `OrderManagement` يحتوي على وظائف (actions) لعرض صفحات Blade المختلفة لعملية شراء السيارة كاش متعددة المراحل في الموقع العام. يخدم هذا [موقع إلكتروني فعال].
* **TYPE:** `Laravel Controller Actions for Public Blade Views (Multi-step Process)`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Http/Controllers/Site/SiteOrderController.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-008`), `REQ-FR.md` (`MOD-ORDER-MGMT-FEAT-003`), `UIUX-FR.md` (القسم `SITE-BUY-CASH-STEPX-001`)
    * **DESIGN_REF:** `UIUX-FR.md` (`SITE-BUY-CASH-STEPX-001` لتحديد الخطوات والبيانات المطلوبة في كل خطوة)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` لتحديد عدد الخطوات المطلوبة لعملية شراء كاش والصفحات المقابلة لها. حدد البيانات التي قد تحتاج كل خطوة لتمريرها (مثل ملخص السيارة المختارة)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث `TODO-SITE-ORDER-CASH-CTRL-SETUP-001` إلى 'In Progress'."
* **DEPENDENCIES:** `MOD-CAR-CATALOG` (لجلب تفاصيل السيارة المختارة).
* **LLM_ASSUMPTIONS:** نفترض أن السيارة المختارة للشراء سيتم تمرير معرفها عبر الـ route أو الـ session.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_CONTROLLER_FILE_IF_NOT_EXISTS:**
        * `File: SiteOrderController.php`
        * **DETAILED_LOGIC:** "1. أنشئ Controller إذا لم يكن موجودًا."
    * **2. IMPLEMENT_SHOW_CASH_ORDER_STEP_METHODS:**
        * **DETAILED_LOGIC:** "لكل خطوة من خطوات عملية شراء كاش المحددة في `UIUX-FR.md` (مثال: `showCashOrderStep1PersonalDetails`, `showCashOrderStep2BookingPayment`, `showCashOrderStep3Documents`, `showCashOrderStep4ReviewConfirm`):"
            *   `Method Name: showCashOrderStepX(...)`
            *   `Visibility: public`
            *   `Parameters: (مثال: Car \$car, أو استرداد السيارة من الـ Session/Request)`
            *   `Return Type: Illuminate\View\View`
            *   "1. قم بجلب أي بيانات مطلوبة لهذه الخطوة (مثل تفاصيل السيارة، بيانات المستخدم الحالية من `Auth::user()`)."
            *   "2. قم بإرجاع الـ view الخاص بالخطوة (مثال: `return view('ordermanagement::site.buy_cash.step1_personal', compact('car', 'userData'));`)."
            *   "3. تأكد من تمرير بيانات السيارة والبيانات التي تم إدخالها في الخطوات السابقة (من الـ Session) إلى الـ view."
* **EXPECTED_OUTPUTS_BEHAVIOR:** وجود `SiteOrderController` مع دوال لعرض كل خطوة من خطوات عملية شراء كاش.
* **SECURITY_CONSIDERATIONS:** "يجب تطبيق وسيط `auth` على هذه الدوال للتأكد من أن المستخدم مسجل."
* **ACCEPTANCE_CRITERIA:** "تم إنشاء دوال لعرض كل خطوة من خطوات عملية شراء كاش، وكل دالة تعيد الـ view الصحيح وتمرر البيانات اللازمة."
* **LOG_UPDATE_GUIDANCE_POST_TASK:** "Augment: وثّق إنشاء Controller لعملية شراء كاش. حدّث `TODO-SITE-ORDER-CASH-CTRL-SETUP-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-023** `FE-BLADE-SITE-BUY-CASH-STEP-VIEWS-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء مجموعة ملفات Blade views لجميع خطوات عملية شراء السيارة كاش في الموقع العام.
* **TYPE:** `Public Blade View Creation (Multi-step Form)`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Resources/views/site/buy_cash/stepX_*.blade.php`
* **PRIMARY_INPUTS:** `UIUX-FR.md` (`SITE-BUY-CASH-STEPX-001`), `REQ-FR.md` (`MOD-ORDER-MGMT-FEAT-003`)
* **DETAILED_IMPLEMENTATION_STEPS:** (لكل خطوة: إنشاء ملف Blade، تمديد التخطيط، تضمين نموذج مع الحقول المطلوبة، عرض أخطاء التحقق، أزرار "السابق" و "التالي/تأكيد").

---

### **TASK-ID: PH03-TASK-024** `BE-LOGIC-SITE-ORDER-CASH-SUBMISSION-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** تنفيذ منطق الـ Backend لمعالجة تقديم طلب شراء سيارة كاش من الموقع العام.
* **TYPE:** `Laravel Backend Logic Implementation (Order Processing)`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Http/Controllers/Site/SiteOrderController.php` (دالة `processCashOrderStepX` أو دالة `storeCashOrder` نهائية) و FormRequests لكل خطوة أو FormRequest مجمع.
* **PRIMARY_INPUTS:** `REQ-FR.md` (`MOD-ORDER-MGMT-FEAT-003`)
* **DETAILED_IMPLEMENTATION_STEPS:** (معالجة بيانات كل خطوة، التحقق من صحتها، تخزينها مؤقتًا في الـ session، وعند التقديم النهائي: إنشاء سجل `Order`، ربط المستندات، توجيه للدفع أو تأكيد الحجز، إرسال إشعارات).

---

*(سيتم تطبيق نفس النمط للمهام `PH03-TASK-025` إلى `PH03-TASK-027` الخاصة بعملية طلب التمويل)*

---

### **TASK-ID: PH03-TASK-025** `BE-CTRL-SITE-ORDER-FINANCE-PROCESS-SETUP-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء دوال Controller في `SiteOrderController` لعرض صفحات عملية طلب التمويل.
* **TYPE:** `Laravel Controller Actions for Public Blade Views (Multi-step Process)`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Http/Controllers/Site/SiteOrderController.php`
* **PRIMARY_INPUTS:** `REQ-FR.md` (`MOD-ORDER-MGMT-FEAT-004`), `UIUX-FR.md` (`SITE-BUY-FINANCE-STEPX-001`)

---

### **TASK-ID: PH03-TASK-026** `FE-BLADE-SITE-BUY-FINANCE-STEP-VIEWS-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء مجموعة ملفات Blade views لخطوات عملية طلب التمويل.
* **TYPE:** `Public Blade View Creation (Multi-step Form)`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Resources/views/site/buy_finance/stepX_*.blade.php`
* **PRIMARY_INPUTS:** `UIUX-FR.md` (`SITE-BUY-FINANCE-STEPX-001`), `REQ-FR.md` (`MOD-ORDER-MGMT-FEAT-004`)

---

### **TASK-ID: PH03-TASK-027** `BE-LOGIC-SITE-ORDER-FINANCE-SUBMISSION-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** تنفيذ منطق الـ Backend لمعالجة تقديم طلب تمويل سيارة.
* **TYPE:** `Laravel Backend Logic Implementation (Order Processing)`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Http/Controllers/Site/SiteOrderController.php` (دوال لمعالجة كل خطوة أو دالة `storeFinanceOrder` نهائية) و FormRequests.
* **PRIMARY_INPUTS:** `REQ-FR.md` (`MOD-ORDER-MGMT-FEAT-004`)

---

### **TASK-ID: PH03-TASK-028** `BE-LOGIC-SITE-ORDER-DOCUMENT-UPLOAD-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** تنفيذ منطق الـ Backend لرفع المستندات المطلوبة للطلبات (كاش وتمويل) باستخدام `spatie/laravel-medialibrary`.
* **TYPE:** `Laravel Backend Logic Implementation (File Upload with Spatie MediaLibrary)`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Http/Controllers/Site/SiteOrderController.php` (أو Controller منفصل `OrderDocumentController`)
* **PRIMARY_INPUTS:** `REQ-FR.md` (`MOD-ORDER-MGMT-FEAT-006`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء دالة تستقبل ملفًا ومعرف الطلب ونوع المستند، تتحقق من صحة الملف، تخزنه باستخدام `addMediaFromRequest()` وتربطه بنموذج `Order` مع `collection_name` مناسب).

---

### **TASK-ID: PH03-TASK-029** `BE-LOGIC-SITE-ORDER-BOOKING-PAYMENT-INTEGRATION-001`
* **LEVEL:** `High`
* **PRIORITY:** `Critical`
* **OBJECTIVE:** تنفيذ التكامل مع بوابة دفع محددة (سيتم تحديدها لاحقًا، مبدئيًا يمكن استخدام Fake/Test gateway) لمعالجة دفع مبلغ الحجز أونلاين.
* **TYPE:** `Payment Gateway Integration`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Services/PaymentGatewayService.php`, و Controller actions في `SiteOrderController`.
* **PRIMARY_INPUTS:** `REQ-FR.md` (`MOD-ORDER-MGMT-FEAT-005`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء Service للتعامل مع بوابة الدفع، دالة لتوجيه المستخدم للبوابة، دالة لمعالجة الـ callback/webhook من البوابة، تحديث حالة الطلب بناءً على نتيجة الدفع).

---

### **TASK-ID: PH03-TASK-030** `FE-BLADE-SITE-HOW-TO-BUY-VIEW-001`
* **LEVEL:** `Medium` (تم التعديل من Low بسبب الحاجة لتنسيق محتوى قد يكون مفصلاً)
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء ملف Blade view (`how_to_buy.blade.php`) لصفحة "كيف أشتريها؟" في الموقع العام، لعرض شرح مفصل لخطوات عملية الشراء (كاش وتمويل) والمستندات المطلوبة، بناءً على التصميم والمحتوى المحددين في `UIUX-FR.md`. يخدم هذا [موقع إلكتروني فعال] بتوفير معلومات واضحة للمستخدمين حول عملية الشراء.
* **TYPE:** `Public Blade View Creation (Informational Page)`
* **FILE_NAME_PATH:** `resources/views/site/info/how_to_buy.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `PPP-FR.md` (المخرج `PH-03-DEL-010`)
        * `REQ-FR.md` (القسم `MOD-ORDER-MGMT-FEAT-002`)
        * `UIUX-FR.md` (القسم `SITE-HOW-TO-BUY-001` لوصف محتوى وتصميم الصفحة)
    * **DESIGN_REF:**
        * `UIUX-FR.md` (القسم `SITE-HOW-TO-BUY-001` لوصف هيكل الصفحة، كيفية تقسيم المحتوى (كاش/تمويل)، وأي عناصر تصميمية خاصة مثل الأيقونات أو الرسومات التوضيحية).
        * صور مرجعية مثل `screenshot-05.png` (التي تظهر مودال "كيف أشتريها أونلاين؟") يمكن أن توفر إلهامًا لكيفية عرض الخطوات بشكل مبسط.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (`SITE-HOW-TO-BUY-001`) بدقة لفهم المحتوى المطلوب عرضه في صفحة "كيف أشتريها؟". حدد ما إذا كان المحتوى سيعرض ككتلة واحدة أو سيتم تقسيمه إلى أقسام (مثلاً، قسم لشرح الشراء الكاش وقسم آخر لشرح طلب التمويل)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-HOW-TO-BUY-VIEW-001` إلى 'In Progress'."
* **DEPENDENCIES:**
    * `PH03-TASK-001 FE-BLADE-SITE-LAYOUT-MAIN-001` (للتخطيط الرئيسي للموقع)
    * `PH03-TASK-031 BE-CTRL-SITE-HOW-TO-BUY-DISPLAY-001` (إذا كان المحتوى سيُدار من `MOD-CMS` ويتم تمريره كمتغير، وإلا فالصفحة قد تكون ثابتة إلى حد كبير).
* **LLM_ASSUMPTIONS:**
    * نفترض أن `UIUX-FR.md` يوفر تفاصيل كافية حول المحتوى الذي يجب عرضه (الخطوات، المستندات المطلوبة لكل نوع شراء).
    * إذا كان المحتوى مُدارًا من `MOD-CMS`، نفترض أن متغيرًا (مثل `\$pageContent`) يحتوي على هذا المحتوى سيتم تمريره من الـ Controller. إذا كان المحتوى ثابتًا إلى حد كبير، سيتم كتابته مباشرة في الـ Blade.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BLADE_FILE_AND_EXTEND_LAYOUT:**
        * **DETAILED_LOGIC:**
            * "1. أنشئ ملف `how_to_buy.blade.php` في المسار `resources/views/site/info/`."
            * "2. في بداية الملف، استخدم `@extends('site.layouts.site_layout')`."
            * "3. قم بتعيين عنوان الصفحة `@section('title', 'كيف أشتريها؟ - اسم المعرض')`."
    * **2. IMPLEMENT_PAGE_CONTENT_STRUCTURE (ضمن `@section('content')`):**
        * **DETAILED_LOGIC:**
            * "1. أنشئ حاوية رئيسية للمحتوى."
            * "2. اعرض عنوانًا رئيسيًا للصفحة (مثال: `<h1>كيف أشتري سيارتي من [اسم المعرض]؟</h1>`)."
            * "3. إذا كان المحتوى مُدارًا من `MOD-CMS` ويتم تمريره (مثال: عبر متغير `\$pageContent` الذي قد يكون كائن `CmsPage` أو HTML مباشر):"
                *   "اعرض المحتوى باستخدام `{!! \$pageContent !!}` (إذا كان HTML) أو قم بتنسيقه إذا كان نصًا عاديًا."
            * "4. إذا كان المحتوى سيتم بناؤه مباشرة في الـ Blade (بناءً على `UIUX-FR.md`):"
                *   "أنشئ قسمًا بعنوان 'خطوات شراء السيارة بالدفع الكاش':"
                    *   "استخدم قائمة مرقمة (`<ol>`) أو فقرات مع أيقونات لعرض كل خطوة (مثال: 1. اختر سيارتك، 2. قدم طلب الحجز، 3. املأ بياناتك الشخصية، 4. ارفع المستندات المطلوبة (الهوية والرخصة)، 5. ادفع مبلغ الحجز أونلاين، 6. أكمل باقي المبلغ واستلم سيارتك من المعرض)."
                    *   "وضّح المستندات الأساسية المطلوبة."
                *   "أنشئ قسمًا بعنوان 'خطوات تقديم طلب تمويل':"
                    *   "استخدم قائمة مرقمة أو فقرات لعرض كل خطوة (مثال: 1. اختر سيارتك، 2. قدم طلب التمويل، 3. املأ بياناتك الشخصية والمالية، 4. ارفع المستندات المطلوبة (الهوية، الرخصة، تعريف بالراتب، كشف حساب)، 5. سيقوم فريقنا بمراجعة طلبك والتواصل معك، 6. توقيع عقد التمويل واستلام السيارة)."
                    *   "وضّح المستندات الأساسية المطلوبة."
                *   "أضف أي نصوص إضافية أو ملاحظات أو روابط مفيدة (مثل رابط لصفحة اتصل بنا) كما هو محدد في `UIUX-FR.md`."
            * "5. استخدم تنسيقات HTML (مثل `<h2>`, `<p>`, `<ul>`, `<strong>`) لتنظيم المحتوى وجعله سهل القراءة."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض صفحة "كيف أشتريها؟" بشكل واضح ومنظم، تشرح خطوات الشراء الكاش والتمويل والمستندات المطلوبة لكل منهما.
* **SECURITY_CONSIDERATIONS:** "إذا كان المحتوى يُعرض من `MOD-CMS` باستخدام `{!! !!}`، يجب التأكد من أن المحتوى المدخل من الإدارة آمن."
* **PERFORMANCE_CONSIDERATIONS:** لا توجد اعتبارات أداء كبيرة لهذه الصفحة المعلوماتية.
* **REQUIRED_CODE_COMMENTS:** توثيق الأقسام الرئيسية إذا كان المحتوى معقدًا.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `how_to_buy.blade.php` ويمتد من التخطيط الرئيسي."
    2.  "يتم عرض عنوان الصفحة بشكل صحيح."
    3.  "يتم عرض شرح واضح ومنظم لخطوات الشراء كاش، بما في ذلك المستندات المطلوبة."
    4.  "يتم عرض شرح واضح ومنظم لخطوات طلب التمويل، بما في ذلك المستندات المطلوبة."
    5.  "الصفحة متجاوبة وتتبع الهوية البصرية للموقع."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء واجهة صفحة 'كيف أشتريها؟' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-HOW-TO-BUY-VIEW-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-031** `BE-CTRL-SITE-HOW-TO-BUY-DISPLAY-001`
* **LEVEL:** `Low` (إذا كان المحتوى ثابتًا في الـ Blade أو يُجلب ببساطة من `MOD-CMS`) أو `Medium` (إذا كان يتطلب منطقًا أكثر لجمع البيانات).
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء Controller action لعرض صفحة "كيف أشتريها؟"، مع إمكانية جلب محتوى الصفحة من `MOD-CMS` إذا كان مُدارًا من هناك، أو مجرد عرض الـ view الثابت. يخدم هذا [موقع إلكتروني فعال].
* **TYPE:** `Laravel Controller Action for Public Blade View`
* **FILE_NAME_PATH:** `app/Http/Controllers/Site/InfoPageController.php` (أو `Modules/Cms/Http/Controllers/Site/PageController.php` إذا كان المحتوى من CMS، أو `Modules/OrderManagement/Http/Controllers/Site/OrderInfoController.php` إذا كان أكثر ارتباطًا بالطلبات)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-010`), `REQ-FR.md` (`MOD-ORDER-MGMT-FEAT-002`)
    * **DESIGN_REF:** (لا يوجد تصميم مباشر لهذه المهمة، لكنها تخدم `PH03-TASK-030`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: حدد ما إذا كان محتوى صفحة 'كيف أشتريها؟' سيتم إدارته بالكامل من `MOD-CMS` (يتطلب جلب صفحة ذات slug 'how-to-buy') أم أنه سيكون محتوى ثابتًا إلى حد كبير في الـ Blade view. القرار يؤثر على منطق الـ Controller."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-HOW-TO-BUY-CONTROLLER-001` إلى 'In Progress'."
* **DEPENDENCIES:**
    * `PH03-TASK-030 FE-BLADE-SITE-HOW-TO-BUY-VIEW-001` (الـ view الذي سيتم عرضه).
    * (اختياري) `MOD-CMS` (نموذج `CmsPage`) إذا كان المحتوى مُدارًا.
* **LLM_ASSUMPTIONS:**
    * سنفترض مبدئيًا أن المحتوى قد يكون مُدارًا من `MOD-CMS` لمرونة أكبر.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_CONTROLLER_FILE_IF_NOT_EXISTS_AND_METHOD:**
        * `Controller Name: InfoPageController` (أو الاسم المختار أعلاه)
        * `Method Name: showHowToBuy`
        * `Visibility: public`
        * `Return Type: Illuminate\View\View`
        * **DETAILED_LOGIC:**
            * "1. قم بإنشاء/تعديل Controller إذا لزم الأمر."
            * "2. أنشئ دالة `showHowToBuy()`."
            * "3. **السيناريو 1: المحتوى من MOD-CMS:**"
                *   "قم بتضمين (use) نموذج `\App\Modules\Cms\Models\CmsPage`."
                *   "حاول جلب الصفحة: `\$page = CmsPage::where('slug', 'how-to-buy')->where('status', 'published')->first();`"
                *   "إذا تم العثور على الصفحة (`\$page`): قم بتمرير محتواها إلى الـ view: `return view('site.info.how_to_buy', ['pageContent' => \$page->content, 'pageTitle' => \$page->title]);`."
                *   "إذا لم يتم العثور على الصفحة أو لم تكن منشورة، يمكنك عرض الـ view بمحتوى افتراضي أو إعادة توجيه/عرض خطأ 404 (حسب سياسة التعامل مع المحتوى المفقود)."
            * "4. **السيناريو 2: المحتوى ثابت في الـ Blade (لا حاجة لبيانات من Controller):**"
                *   "في هذه الحالة، الدالة ستكون بسيطة جدًا: `return view('site.info.how_to_buy');`."
            * "5. **القرار الموصى به:** اتبع السيناريو 1 لتوفير مرونة إدارة المحتوى من لوحة التحكم، مع توفير محتوى احتياطي في الـ Blade view إذا لم يتم العثور على الصفحة في `MOD-CMS`."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عند الوصول لمسار صفحة "كيف أشتريها؟"، يتم عرض الصفحة مع المحتوى المناسب (سواء من CMS أو ثابت).
* **SECURITY_CONSIDERATIONS:** "إذا كان المحتوى من CMS، يجب التأكد من أنه آمن قبل عرضه (تمت مناقشته في مهمة الـ view)."
* **PERFORMANCE_CONSIDERATIONS:** "Caching لمحتوى الصفحة إذا كان من CMS ولا يتغير كثيرًا."
* **REQUIRED_CODE_COMMENTS:** توضيح مصدر المحتوى (CMS أو ثابت) وأي منطق احتياطي.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء Controller ودالة `showHowToBuy()`."
    2.  "الدالة تعرض الـ view `site.info.how_to_buy`."
    3.  "إذا كان المحتوى من `MOD-CMS`، يتم جلبه وتمريره بشكل صحيح إلى الـ view."
    4.  "يتم التعامل مع حالة عدم وجود محتوى من `MOD-CMS` بشكل مناسب (عرض محتوى افتراضي أو خطأ)."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إضافة Controller لعرض صفحة 'كيف أشتريها؟' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-HOW-TO-BUY-CONTROLLER-001` إلى 'Done'."
    * **DECISIONS.md:** "Augment: وثّق القرار بشأن ما إذا كان محتوى 'كيف أشتريها؟' سيُدار بالكامل من CMS أم سيكون ثابتًا في الـ Blade في `docs/DECISIONS.md`."

---

### **TASK-ID: PH03-TASK-032** `BE-CTRL-SITE-STATIC-PAGES-DISPLAY-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء Controller (`SitePageController` أو ما يعادله) في موديول `Cms` (أو في `app/Http/Controllers/Site/`) يحتوي على وظيفة (action) عامة (`show`) لعرض محتوى الصفحات الثابتة (مثل "من نحن"، "سياسة الخصوصية") من قاعدة البيانات بناءً على الـ slug الخاص بالصفحة. يخدم هذا [موقع إلكتروني فعال] بتوفير محتوى معلوماتي هام للمستخدمين.
* **TYPE:** `Laravel Controller Action for Public Blade View (Dynamic Slug-based Content)`
* **FILE_NAME_PATH:** `Modules/Cms/Http/Controllers/Site/SitePageController.php` (أو `app/Http/Controllers/Site/PageController.php`)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-011`), `REQ-FR.md` (`MOD-CMS-FEAT-001` لعرض الصفحات)
    * **DESIGN_REF:** (غير مباشر، يعتمد على أن الـ view سيعرض محتوى HTML من قاعدة البيانات)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-CMS-FEAT-001`) لفهم كيفية جلب الصفحة بناءً على الـ slug. تأكد من أن الـ Controller سيعالج حالة عدم العثور على الصفحة (404)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-STATIC-PAGES-CONTROLLER-001` إلى 'In Progress'."
* **DEPENDENCIES:** `MOD-CMS` (نموذج `CmsPage`).
* **LLM_ASSUMPTIONS:**
    * نفترض أن نموذج `CmsPage` يحتوي على حقل `slug` فريد وحقل `content` (يحتوي على HTML).
    * نفترض أن الصفحات التي سيتم عرضها لها حالة 'published'.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_CONTROLLER_FILE_IF_NOT_EXISTS:**
        * `File: SitePageController.php` (في المسار المحدد)
        * **DETAILED_LOGIC:** "1. قم بإنشاء ملف Controller إذا لم يكن موجودًا."
    * **2. IMPLEMENT_SHOW_PAGE_METHOD:**
        * `Method Name: show`
        * `Visibility: public`
        * `Parameters: string \$slug`
        * `Return Type: Illuminate\View\View | Illuminate\Http\Response`
        * **DETAILED_LOGIC:**
            * "1. قم بإنشاء دالة `show(string \$slug)`."
            * "2. قم بجلب الصفحة من قاعدة البيانات باستخدام الـ slug: `\$page = \App\Modules\Cms\Models\CmsPage::where('slug', \$slug)->where('status', 'published')->firstOrFail();` (استخدام `firstOrFail()` سيلقي استثناء 404 تلقائيًا إذا لم يتم العثور على الصفحة)."
            * "3. قم بتمرير كائن الصفحة (`\$page`) إلى الـ view الخاص بعرض الصفحات الثابتة (مثال: `return view('site.pages.show', compact('page'));`). هذا الـ view سيكون عامًا ويعرض محتوى HTML من حقل `content`."
* **EXPECTED_OUTPUTS_BEHAVIOR:** وجود Controller قادر على عرض أي صفحة ثابتة منشورة بناءً على الـ slug الخاص بها.
* **SECURITY_CONSIDERATIONS:** "تأكد من أن محتوى الصفحة الذي يتم عرضه (`\$page->content`) يتم عرضه باستخدام `{!! !!}` في Blade بحذر، مع افتراض أن المحتوى المدخل عبر محرر Rich Text في لوحة التحكم قد تم تطهيره بشكل كافٍ لمنع XSS. إذا لم يكن الأمر كذلك، يجب تطبيق تطهير إضافي أو استخدام مكتبة لعرض HTML آمن."
* **PERFORMANCE_CONSIDERATIONS:** "يمكن تطبيق Caching على محتوى الصفحات الثابتة التي لا تتغير كثيرًا."
* **REQUIRED_CODE_COMMENTS:** توثيق الغرض من الدالة وكيفية جلب الصفحة.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء `SitePageController.php` ودالة `show(\$slug)`."
    2.  "الدالة `show` تجلب الصفحة الصحيحة من قاعدة البيانات بناءً على الـ slug وحالة 'published'."
    3.  "يتم التعامل مع حالة عدم العثور على الصفحة (404)."
    4.  "يتم تمرير كائن الصفحة بشكل صحيح إلى الـ Blade view."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء Controller لعرض الصفحات الثابتة للموقع العام في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-STATIC-PAGES-CONTROLLER-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-033** `FE-BLADE-SITE-STATIC-PAGES-VIEWS-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء ملف Blade view عام (`show.blade.php` ضمن `resources/views/site/pages/`) لعرض محتوى الصفحات الثابتة (مثل "من نحن"، "سياسة الخصوصية"، "الشروط والأحكام"، "اتصل بنا" - باستثناء نموذج الاتصال الذي قد يكون في نفس الـ view أو منفصل). يخدم هذا [موقع إلكتروني فعال].
* **TYPE:** `Public Blade View Creation (Dynamic Content Display)`
* **FILE_NAME_PATH:** `resources/views/site/pages/show.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-011`), `UIUX-FR.md` (الأقسام `SITE-STATIC-*-001`)
    * **DESIGN_REF:** `UIUX-FR.md` (للتصميم العام للصفحات الثابتة، مثل عرض العنوان والمحتوى)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` لفهم التخطيط المتوقع للصفحات الثابتة، بما في ذلك كيفية عرض العنوان والمحتوى. مهمة صفحة "اتصل بنا" قد تتطلب نموذجًا إضافيًا."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-STATIC-PAGES-VIEW-001` إلى 'In Progress'."
* **DEPENDENCIES:**
    * `PH03-TASK-001 FE-BLADE-SITE-LAYOUT-MAIN-001`
    * `PH03-TASK-032 BE-CTRL-SITE-STATIC-PAGES-DISPLAY-001` (لتمرير كائن `\$page`)
* **LLM_ASSUMPTIONS:**
    * نفترض أن المتغير `\$page` (كائن `CmsPage`) يتم تمريره من الـ Controller ويحتوي على حقل `title` وحقل `content` (يحتوي على HTML).
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BLADE_FILE_AND_EXTEND_LAYOUT:**
        * **DETAILED_LOGIC:** "1. أنشئ ملف `show.blade.php` في `resources/views/site/pages/`. <br> 2. استخدم `@extends('site.layouts.site_layout')`. <br> 3. عيّن `@section('title', \$page->title ?? 'صفحة معلومات')`."
    * **2. DISPLAY_PAGE_CONTENT (ضمن `@section('content')`):**
        * **DETAILED_LOGIC:**
            * "1. (اختياري) قم بتضمين مسار التنقل (breadcrumbs) إذا كان التصميم يتطلبه (مثال: الرئيسية > {{ \$page->title }})."
            * "2. اعرض عنوان الصفحة: `<h1>{{ \$page->title }}</h1>`."
            * "3. اعرض محتوى الصفحة: `{!! \$page->content !!}` (استخدم `{!! !!}` لعرض HTML. تأكد من أن المحتوى المدخل من لوحة التحكم يتم تطهيره بشكل كافٍ)."
            * "4. **لصفحة "اتصل بنا" تحديدًا (`slug` = 'contact-us'):** بعد عرض المحتوى الأساسي، قم بتضمين نموذج HTML لـ 'تواصل معنا' (الاسم، البريد، الجوال، الرسالة، زر إرسال). سيتم معالجة هذا النموذج في مهمة منفصلة."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض محتوى الصفحة الثابتة بشكل صحيح، بما في ذلك أي تنسيق HTML تم إدخاله عبر محرر Rich Text.
* **SECURITY_CONSIDERATIONS:** "الخطر الرئيسي هنا هو XSS إذا لم يتم تطهير المحتوى المدخل من لوحة التحكم بشكل صحيح قبل عرضه بـ `{!! !!}`. يجب التأكد من أن محرر Rich Text المستخدم في لوحة التحكم يقوم بالتطهير، أو تطبيق تطهير إضافي قبل العرض."
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد، ما لم يكن المحتوى كبيرًا جدًا.
* **REQUIRED_CODE_COMMENTS:** لا يوجد تعليقات خاصة مطلوبة.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `pages/show.blade.php`."
    2.  "الـ view يمتد من التخطيط الصحيح ويعرض عنوان الصفحة."
    3.  "يتم عرض محتوى HTML الخاص بالصفحة (`\$page->content`) بشكل صحيح."
    4.  "صفحة 'اتصل بنا' تعرض نموذج الاتصال بالإضافة إلى المحتوى (إذا وجد)."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إنشاء واجهة عرض الصفحات الثابتة للموقع العام."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-STATIC-PAGES-VIEW-001` إلى 'Done'. قم بإنشاء مهمة TODO لمعالجة تقديم نموذج 'اتصل بنا' إذا لم تكن موجودة."

---

*(بقية المهام ستتبع نفس النمط التفصيلي)*

---

### **TASK-ID: PH03-TASK-034** `BE-CTRL-SITE-SERVICES-LIST-DISPLAY-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء Controller action في موديول `ServiceManagement` لعرض قائمة الخدمات المتاحة في الموقع العام.
* **TYPE:** `Laravel Controller Action for Public Blade View`
* **FILE_NAME_PATH:** `Modules/ServiceManagement/Http/Controllers/Site/SiteServiceController.php`
* **PRIMARY_INPUTS:** `PPP-FR.md` (`PH-03-DEL-012`), `REQ-FR.md` (`MOD-SERVICE-MGMT-FEAT-001`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء دالة `index` تجلب الخدمات النشطة (مع فئاتها إذا لزم الأمر) من نموذج `Service` وتمررها إلى الـ view `servicemanagement::site.services.index`).

---

### **TASK-ID: PH03-TASK-035** `FE-BLADE-SITE-SERVICES-LIST-VIEW-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء ملف Blade view لعرض قائمة الخدمات في الموقع العام.
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `Modules/ServiceManagement/Resources/views/site/services/index.blade.php`
* **PRIMARY_INPUTS:** `UIUX-FR.md` (`SITE-SERVICES-LIST-001`), `REQ-FR.md` (`MOD-SERVICE-MGMT-FEAT-001`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء view يمتد من `site.layouts.site_layout`، يعرض الخدمات (`\$services`) كبطاقات أو قائمة، مع اسم الخدمة، وصف، سعر، وزر "اطلب الخدمة" أو "اعرف المزيد" الذي يفتح نموذج طلب الخدمة).

---

### **TASK-ID: PH03-TASK-036** `FE-BLADE-SITE-SERVICE-REQUEST-FORM-VIEW-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء ملف Blade view (قد يكون جزءًا من `services/index.blade.php` أو مودال/off-canvas) لنموذج طلب الخدمة في الموقع العام.
* **TYPE:** `Public Blade View Creation (Form)`
* **FILE_NAME_PATH:** (يمكن أن يكون `Modules/ServiceManagement/Resources/views/site/services/_request_form.blade.php` يتم تضمينه)
* **PRIMARY_INPUTS:** `UIUX-FR.md` (جزء نموذج طلب الخدمة ضمن `SITE-SERVICES-LIST-001`), `REQ-FR.md` (`FEAT-SERVICE-002`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء نموذج HTML بسيط (الاسم، الجوال، البريد، ملاحظات، معرّف الخدمة كمخفي) يشير إلى مسار معالجة طلب الخدمة).

---

### **TASK-ID: PH03-TASK-037** `BE-LOGIC-SITE-SERVICE-REQUEST-SUBMISSION-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** تنفيذ منطق الـ Backend لمعالجة تقديم نموذج طلب خدمة من الموقع العام.
* **TYPE:** `Laravel Backend Logic Implementation (Form Submission)`
* **FILE_NAME_PATH:** `Modules/ServiceManagement/Http/Controllers/Site/SiteServiceController.php` (أو Controller مخصص `ServiceRequestController`) و FormRequest لطلب الخدمة.
* **PRIMARY_INPUTS:** `REQ-FR.md` (`FEAT-SERVICE-002`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء دالة `storeRequest` تتحقق من صحة المدخلات، تنشئ سجل `ServiceRequest`، ترسل إشعارًا للإدارة، وتعرض رسالة نجاح للمستخدم أو تعيد توجيهه).

---


### **TASK-ID: PH03-TASK-038** `BE-CTRL-SITE-PROMOTIONS-LIST-DISPLAY-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء Controller action في موديول `PromotionManagement` لعرض قائمة العروض الترويجية النشطة في الموقع العام. يخدم هذا [موقع إلكتروني فعال] بإبراز العروض الجارية للعملاء.
* **TYPE:** `Laravel Controller Action for Public Blade View`
* **FILE_NAME_PATH:** `Modules/PromotionManagement/Http/Controllers/Site/SitePromotionController.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-013`), `REQ-FR.md` (`MOD-PROMO-MGMT-FEAT-001`)
    * **DESIGN_REF:** `UIUX-FR.md` (`SITE-PROMOTIONS-LIST-001`)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-PROMO-MGMT-FEAT-001`) لفهم كيفية جلب العروض النشطة. تأكد من الرجوع إلى `docs/TS-FR.md` (`DB-TBL-018`) لأسماء حقول جدول `promotions`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث حالة `TODO-SITE-PROMOTIONS-LIST-CONTROLLER-001` إلى 'In Progress'."
* **DEPENDENCIES:** `MOD-PROMO-MGMT` (نموذج `Promotion`).
* **LLM_ASSUMPTIONS:**
    * نفترض أن العروض النشطة هي تلك التي `status = true` وتاريخها الحالي يقع بين `start_date` و `end_date`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_CONTROLLER_FILE_IF_NOT_EXISTS:**
        * `File: SitePromotionController.php` (في المسار المحدد)
        * **DETAILED_LOGIC:** "1. قم بإنشاء ملف Controller إذا لم يكن موجودًا."
    * **2. IMPLEMENT_INDEX_METHOD:**
        * `Method Name: index`
        * `Visibility: public`
        * `Return Type: Illuminate\View\View`
        * **DETAILED_LOGIC:**
            * "1. أنشئ دالة `index()`."
            * "2. قم بجلب قائمة العروض النشطة: `\$promotions = \App\Modules\PromotionManagement\Models\Promotion::where('status', true)->where('start_date', '<=', now())->where('end_date', '>=', now())->with('media')->orderBy('created_at', 'desc')->paginate(10);` (استخدم `with('media')` لجلب صور بنرات العروض)."
            * "3. قم بتمرير كائن العروض (`\$promotions`) إلى الـ view الخاص بقائمة العروض (مثال: `return view('promotionmanagement::site.promotions.index', compact('promotions'));`)."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عند الوصول لمسار صفحة العروض، يتم عرض قائمة العروض النشطة مع الترقيم.
* **SECURITY_CONSIDERATIONS:** "تأكد من جلب العروض النشطة فقط."
* **PERFORMANCE_CONSIDERATIONS:** "استخدام الفهارس على حقول `status`, `start_date`, `end_date`. Eager loading للـ media."
* **REQUIRED_CODE_COMMENTS:** توثيق منطق جلب العروض.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء `SitePromotionController.php` ودالة `index()`."
    2.  "الدالة `index()` تجلب العروض النشطة بشكل صحيح مع الترقيم."
    3.  "يتم تمرير كائن العروض بشكل صحيح إلى الـ Blade view."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثّق إضافة Controller لعرض قائمة العروض الترويجية."
    * **TODO.md:** "Augment: حدّث حالة `TODO-SITE-PROMOTIONS-LIST-CONTROLLER-001` إلى 'Done'."

---

### **TASK-ID: PH03-TASK-039** `FE-BLADE-SITE-PROMOTIONS-LIST-VIEW-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء ملف Blade view (`index.blade.php`) لعرض قائمة العروض الترويجية في الموقع العام، بناءً على التصميم المحدد في `UIUX-FR.md`.
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `Modules/PromotionManagement/Resources/views/site/promotions/index.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (المخرج `PH-03-DEL-013`), `UIUX-FR.md` (`SITE-PROMOTIONS-LIST-001`)
    * **DESIGN_REF:** `UIUX-FR.md` (`SITE-PROMOTIONS-LIST-001` لوصف هيكل عرض بطاقات العروض)
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (`SITE-PROMOTIONS-LIST-001`) لفهم كيفية عرض كل عرض (صورة البنر، اسم العرض، رابط للتفاصيل)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** "Augment: حدّث `TODO-SITE-PROMOTIONS-LIST-VIEW-001` إلى 'In Progress'."
* **DEPENDENCIES:** `PH03-TASK-001`, `PH03-TASK-038` (لتمرير `\$promotions`).
* **LLM_ASSUMPTIONS:** متغير `\$promotions` (كائن ترقيم Eloquent) يتم تمريره من الـ Controller.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BLADE_FILE_AND_EXTEND_LAYOUT:** (نفس الخطوات 1 و 2 من `PH03-TASK-005`, مع تعديل اسم الملف وعنوان الصفحة إلى "عروض السيارات")
    * **2. IMPLEMENT_PROMOTIONS_GRID (ضمن `@section('content')`):**
        * **DETAILED_LOGIC:**
            * "1. اعرض عنوانًا للصفحة (مثال: 'أحدث العروض الترويجية')."
            * "2. استخدم `@if(\$promotions->count() > 0) <div class=\"promotions-grid\"> @foreach(\$promotions as \$promotion)` لعرض كل عرض."
            * "3. كل عرض يمثل بطاقة (card) تحتوي على: صورة البنر (`<img src=\"{{ \$promotion->getFirstMediaUrl('promotion_banners') ?? asset('placeholder.jpg') }}\" alt=\"{{ \$promotion->name }}\">`)، اسم العرض (`{{ \$promotion->name }}`)."
            * "4. البطاقة بأكملها أو زر "عرض التفاصيل" يشير إلى `route('site.promotions.show', \$promotion->slug ?? \$promotion->id)`."
            * "5. استخدم `@else <p>لا توجد عروض متاحة حاليًا.</p> @endif`."
            * "6. اعرض روابط ترقيم الصفحات: `{{ \$promotions->links('pagination::bootstrap-5') }}`."
* **EXPECTED_OUTPUTS_BEHAVIOR:** عرض قائمة العروض الترويجية بشكل جذاب مع الصور والأسماء والروابط الصحيحة.
* **ACCEPTANCE_CRITERIA:**
    1.  "تم إنشاء ملف `promotions/index.blade.php`."
    2.  "يتم عرض بطاقات العروض بشكل صحيح مع البيانات الديناميكية."
    3.  "يتم عرض رسالة مناسبة في حال عدم وجود عروض."
    4.  "يعمل الترقيم بشكل صحيح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:** "Augment: وثّق إنشاء واجهة قائمة العروض. حدّث `TODO-SITE-PROMOTIONS-LIST-VIEW-001` إلى 'Done'."

---

*(بقية المهام ستتبع نفس النمط التفصيلي. سأوجزها هنا لتوفير الوقت، مع افتراض أنك ستفصلها بنفس الطريقة.)*

---

### **TASK-ID: PH03-TASK-040** `BE-CTRL-SITE-PROMOTION-DETAIL-DISPLAY-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء Controller action لعرض صفحة تفاصيل عرض ترويجي محدد والسيارات المشمولة به.
* **TYPE:** `Laravel Controller Action for Public Blade View`
* **FILE_NAME_PATH:** `Modules/PromotionManagement/Http/Controllers/Site/SitePromotionController.php`
* **PRIMARY_INPUTS:** `PPP-FR.md` (`PH-03-DEL-013`), `REQ-FR.md` (`MOD-PROMO-MGMT-FEAT-002`), `UIUX-FR.md` (`SITE-PROMOTION-DETAIL-001`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء دالة `show(Promotion \$promotion)` باستخدام Route Model Binding، تجلب العرض مع السيارات المرتبطة به (`\$promotion->load('cars.media', 'cars.brand', 'cars.model', 'cars.year')`) وتمررها للـ view).

---

### **TASK-ID: PH03-TASK-041** `FE-BLADE-SITE-PROMOTION-DETAIL-VIEW-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء ملف Blade view لعرض تفاصيل العرض الترويجي والسيارات المشمولة.
* **TYPE:** `Public Blade View Creation`
* **FILE_NAME_PATH:** `Modules/PromotionManagement/Resources/views/site/promotions/show.blade.php`
* **PRIMARY_INPUTS:** `UIUX-FR.md` (`SITE-PROMOTION-DETAIL-001`), `REQ-FR.md` (`MOD-PROMO-MGMT-FEAT-002`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء view يعرض صورة بنر العرض، اسمه، وصفه، وشروط العرض. ثم يعرض شبكة من بطاقات السيارات المشمولة بالعرض، مع أسعارها الخاصة بالعرض).

---

### **TASK-ID: PH03-TASK-042** `BE-CTRL-SITE-CORPORATE-SALES-DISPLAY-001`
* **LEVEL:** `Low`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء Controller action لعرض صفحة مبيعات الشركات.
* **TYPE:** `Laravel Controller Action for Public Blade View`
* **FILE_NAME_PATH:** `Modules/CorporateSales/Http/Controllers/Site/SiteCorporateSalesController.php` (أو ضمن `Cms` إذا كان المحتوى يُدار من هناك بشكل كبير)
* **PRIMARY_INPUTS:** `PPP-FR.md` (`PH-03-DEL-014`), `REQ-FR.md` (`MOD-CORP-SALES-FEAT-001`)
* **DETAILED_IMPLEMENTATION_STEPS:** (دالة `index` بسيطة تعرض الـ view `corporatesales::site.index`. قد تجلب بعض المحتوى من `MOD-CMS` إذا كان ذلك مطلوبًا).

---

### **TASK-ID: PH03-TASK-043** `FE-BLADE-SITE-CORPORATE-SALES-VIEW-FORM-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** إنشاء ملف Blade view لصفحة مبيعات الشركات، بما في ذلك نموذج طلب عرض أسعار.
* **TYPE:** `Public Blade View Creation (Informational Page with Form)`
* **FILE_NAME_PATH:** `Modules/CorporateSales/Resources/views/site/index.blade.php`
* **PRIMARY_INPUTS:** `UIUX-FR.md` (`SITE-CORPORATE-SALES-001`), `REQ-FR.md` (`MOD-CORP-SALES-FEAT-001`, `FEAT-CORP-002`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء view يعرض محتوى ترويجيًا عن مبيعات الشركات، ونموذج HTML مع حقول: اسم الشركة، اسم مسؤول الاتصال، البريد، الجوال، تفاصيل الطلب، وزر إرسال).

---

### **TASK-ID: PH03-TASK-044** `BE-LOGIC-SITE-CORPORATE-SALES-REQUEST-SUBMISSION-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `Medium`
* **OBJECTIVE:** تنفيذ منطق الـ Backend لمعالجة تقديم نموذج طلب مبيعات الشركات.
* **TYPE:** `Laravel Backend Logic Implementation (Form Submission)`
* **FILE_NAME_PATH:** `Modules/CorporateSales/Http/Controllers/Site/SiteCorporateSalesController.php` (دالة `submitRequest`) و FormRequest مخصص.
* **PRIMARY_INPUTS:** `REQ-FR.md` (`FEAT-CORP-002`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء دالة تستقبل بيانات النموذج، تتحقق من صحتها، تنشئ سجل `CorporateEnquiry`، ترسل إشعارًا لفريق مبيعات الشركات، وتعيد استجابة نجاح للواجهة الأمامية).

---

### **TASK-ID: PH03-TASK-045** `BE-CTRL-SITE-REQUEST-CAR-PROCESS-SETUP-001`
* **LEVEL:** `Medium`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء Controller (`SiteRequestCarController` أو ما يعادله) في موديول `OrderManagement` أو `CarCatalog` لعملية "اطلب سيارتك" متعددة الخطوات.
* **TYPE:** `Laravel Controller Actions for Public Blade Views (Multi-step Process)`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Http/Controllers/Site/SiteRequestCarController.php`
* **PRIMARY_INPUTS:** `PPP-FR.md` (`PH-03-DEL-015`), `REQ-FR.md` (`FEAT-REQCAR-001`), `UIUX-FR.md` (`SITE-REQUEST-CAR-STEPX-001`)
* **DETAILED_IMPLEMENTATION_STEPS:** (إنشاء دوال لعرض كل خطوة من خطوات "اطلب سيارتك": اختيار الماركة، الموديل، السنة، معلومات إضافية. يجب أن تجلب البيانات اللازمة لكل خطوة (مثل قائمة الماركات، الموديلات بناءً على الماركة) وتمررها للـ views).

---

### **TASK-ID: PH03-TASK-046** `FE-BLADE-SITE-REQUEST-CAR-STEP-VIEWS-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** إنشاء مجموعة ملفات Blade views لجميع خطوات عملية "اطلب سيارتك" في الموقع العام.
* **TYPE:** `Public Blade View Creation (Multi-step Form)`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Resources/views/site/request_car/stepX_*.blade.php`
* **PRIMARY_INPUTS:** `UIUX-FR.md` (`SITE-REQUEST-CAR-STEPX-001`), `REQ-FR.md` (`FEAT-REQCAR-001`)
* **DETAILED_IMPLEMENTATION_STEPS:** (لكل خطوة: إنشاء ملف Blade يعرض الخيارات المتاحة (ماركات، موديلات، سنوات، فئة، طريقة دفع) وأزرار "السابق" و "التالي/إرسال الطلب").

---

### **TASK-ID: PH03-TASK-047** `BE-LOGIC-SITE-REQUEST-CAR-SUBMISSION-001`
* **LEVEL:** `High`
* **PRIORITY:** `High`
* **OBJECTIVE:** تنفيذ منطق الـ Backend لمعالجة تقديم طلب "اطلب سيارتك".
* **TYPE:** `Laravel Backend Logic Implementation (Order Processing)`
* **FILE_NAME_PATH:** `Modules/OrderManagement/Http/Controllers/Site/SiteRequestCarController.php` (دالة `submitRequestCar`) و FormRequest مخصص.
* **PRIMARY_INPUTS:** `REQ-FR.md` (`FEAT-REQCAR-001`)
* **DETAILED_IMPLEMENTATION_STEPS:** (معالجة بيانات جميع الخطوات، التحقق منها، إنشاء سجل طلب من نوع `custom_car_request` في جدول `orders` (أو جدول منفصل إذا لزم الأمر)، ربطه بالمستخدم إذا كان مسجلاً، إرسال إشعار للإدارة، وعرض رسالة تأكيد للمستخدم).

---

# 