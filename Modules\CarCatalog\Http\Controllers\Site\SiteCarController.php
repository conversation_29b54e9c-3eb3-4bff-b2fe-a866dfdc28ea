<?php

namespace Modules\CarCatalog\Http\Controllers\Site;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\View\View;
use Modules\CarCatalog\Models\BodyType;
use Modules\CarCatalog\Models\Brand;
use Modules\CarCatalog\Models\Car;
use Modules\CarCatalog\Models\Color;
use Modules\CarCatalog\Models\FuelType;
use Modules\CarCatalog\Models\ManufacturingYear;
use Modules\CarCatalog\Models\TransmissionType;

/**
 * وحدة تحكم عرض السيارات في الموقع العام.
 *
 * تتعامل هذه الوحدة مع عرض قائمة السيارات للزوار مع دعم كامل للفلترة والترتيب والترقيم
 * تعرض السيارات المتاحة فقط (غير المباعة والنشطة) مع إمكانية البحث والفلترة المتقدمة
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024
 */
class SiteCarController extends Controller
{
    /**
     * عرض قائمة السيارات مع الفلترة والترتيب والترقيم.
     *
     * يدعم الفلاتر التالية:
     * - فلتر الماركة (brand_ids[])
     * - فلتر الموديل (model_ids[])
     * - فلتر سنة الصنع (year_ids[] أو نطاق min_year, max_year)
     * - فلتر السعر (نطاق min_price, max_price)
     * - فلتر اللون (color_ids[])
     * - فلتر نوع ناقل الحركة (transmission_type_ids[])
     * - فلتر نوع الوقود (fuel_type_ids[])
     * - فلتر نوع الهيكل (body_type_ids[])
     * - البحث النصي (search_term)
     *
     * يدعم الترتيب التالي:
     * - price_asc: السعر من الأقل للأعلى
     * - price_desc: السعر من الأعلى للأقل
     * - latest: الأحدث أولاً
     * - oldest: الأقدم أولاً
     * - featured: المميزة أولاً
     *
     * @param Request $request طلب HTTP يحتوي على معايير الفلترة والترتيب
     * @return View عرض قائمة السيارات
     */
    public function index(Request $request): View
    {
        // بناء استعلام السيارات مع العلاقات الضرورية
        $query = Car::with([
            'media',
            'brand',
            'carModel',
            'manufacturingYear',
            'mainColor',
            'transmissionType',
            'fuelType',
            'bodyType'
        ]);

        // تطبيق شرط عرض السيارات المتاحة فقط (غير مباعة ونشطة)
        $query->where('is_sold', false)
              ->where('is_active', true);

        // تطبيق فلتر البحث النصي
        if ($request->filled('search_term')) {
            $searchTerm = $request->input('search_term');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description', 'LIKE', "%{$searchTerm}%")
                  ->orWhereHas('brand', function ($brandQuery) use ($searchTerm) {
                      $brandQuery->where('name', 'LIKE', "%{$searchTerm}%");
                  })
                  ->orWhereHas('carModel', function ($modelQuery) use ($searchTerm) {
                      $modelQuery->where('name', 'LIKE', "%{$searchTerm}%");
                  });
            });
        }

        // تطبيق فلتر الماركات
        if ($request->filled('brand_ids') && is_array($request->input('brand_ids'))) {
            $query->whereIn('brand_id', $request->input('brand_ids'));
        }

        // تطبيق فلتر الموديلات
        if ($request->filled('model_ids') && is_array($request->input('model_ids'))) {
            $query->whereIn('car_model_id', $request->input('model_ids'));
        }

        // تطبيق فلتر سنوات الصنع
        if ($request->filled('year_ids') && is_array($request->input('year_ids'))) {
            $query->whereIn('manufacturing_year_id', $request->input('year_ids'));
        }

        // تطبيق فلتر نطاق سنوات الصنع
        if ($request->filled('min_year')) {
            $query->whereHas('manufacturingYear', function ($yearQuery) use ($request) {
                $yearQuery->where('year', '>=', $request->input('min_year'));
            });
        }

        if ($request->filled('max_year')) {
            $query->whereHas('manufacturingYear', function ($yearQuery) use ($request) {
                $yearQuery->where('year', '<=', $request->input('max_year'));
            });
        }

        // تطبيق فلتر نطاق الأسعار
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->input('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->input('max_price'));
        }

        // تطبيق فلتر الألوان
        if ($request->filled('color_ids') && is_array($request->input('color_ids'))) {
            $query->whereIn('main_color_id', $request->input('color_ids'));
        }

        // تطبيق فلتر أنواع ناقل الحركة
        if ($request->filled('transmission_type_ids') && is_array($request->input('transmission_type_ids'))) {
            $query->whereIn('transmission_type_id', $request->input('transmission_type_ids'));
        }

        // تطبيق فلتر أنواع الوقود
        if ($request->filled('fuel_type_ids') && is_array($request->input('fuel_type_ids'))) {
            $query->whereIn('fuel_type_id', $request->input('fuel_type_ids'));
        }

        // تطبيق فلتر أنواع الهيكل
        if ($request->filled('body_type_ids') && is_array($request->input('body_type_ids'))) {
            $query->whereIn('body_type_id', $request->input('body_type_ids'));
        }

        // تطبيق الترتيب
        $sortBy = $request->input('sort_by', 'latest');
        switch ($sortBy) {
            case 'price_asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price_desc':
                $query->orderBy('price', 'desc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'featured':
                $query->orderBy('is_featured', 'desc')->orderBy('created_at', 'desc');
                break;
            case 'latest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // تطبيق الترقيم
        $perPage = $request->input('per_page', 15);
        $perPage = in_array($perPage, [12, 15, 24, 48]) ? $perPage : 15; // قيم مسموحة للترقيم
        $cars = $query->paginate($perPage)->withQueryString();

        // جلب بيانات خيارات الفلاتر
        $filterOptions = $this->getFilterOptions();

        // تمرير الفلاتر المطبقة حالياً
        $appliedFilters = $request->all();

        return view('site.cars.index', compact('cars', 'filterOptions', 'appliedFilters'));
    }

    /**
     * جلب بيانات خيارات الفلاتر مع عدد السيارات لكل خيار.
     *
     * @return array مصفوفة تحتوي على جميع خيارات الفلاتر
     */
    private function getFilterOptions(): array
    {
        // جلب الماركات النشطة مع عدد السيارات المتاحة
        $brands = Brand::where('status', true)
            ->withCount(['cars as available_cars_count' => function ($query) {
                $query->where('is_sold', false)->where('is_active', true);
            }])
            ->having('available_cars_count', '>', 0)
            ->orderBy('name')
            ->get();

        // جلب الألوان مع عدد السيارات المتاحة
        $colors = Color::where('status', true)
            ->withCount(['cars as available_cars_count' => function ($query) {
                $query->where('is_sold', false)->where('is_active', true);
            }])
            ->having('available_cars_count', '>', 0)
            ->orderBy('name')
            ->get();

        // جلب سنوات الصنع مع عدد السيارات المتاحة
        $years = ManufacturingYear::where('status', true)
            ->withCount(['cars as available_cars_count' => function ($query) {
                $query->where('is_sold', false)->where('is_active', true);
            }])
            ->having('available_cars_count', '>', 0)
            ->orderBy('year', 'desc')
            ->get();

        // جلب أنواع ناقل الحركة مع عدد السيارات المتاحة
        $transmissionTypes = TransmissionType::where('status', true)
            ->withCount(['cars as available_cars_count' => function ($query) {
                $query->where('is_sold', false)->where('is_active', true);
            }])
            ->having('available_cars_count', '>', 0)
            ->orderBy('name')
            ->get();

        // جلب أنواع الوقود مع عدد السيارات المتاحة
        $fuelTypes = FuelType::where('status', true)
            ->withCount(['cars as available_cars_count' => function ($query) {
                $query->where('is_sold', false)->where('is_active', true);
            }])
            ->having('available_cars_count', '>', 0)
            ->orderBy('name')
            ->get();

        // جلب أنواع الهيكل مع عدد السيارات المتاحة
        $bodyTypes = BodyType::where('status', true)
            ->withCount(['cars as available_cars_count' => function ($query) {
                $query->where('is_sold', false)->where('is_active', true);
            }])
            ->having('available_cars_count', '>', 0)
            ->orderBy('name')
            ->get();

        // حساب نطاق الأسعار
        $priceRange = Car::where('is_sold', false)
            ->where('is_active', true)
            ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
            ->first();

        return [
            'brands' => $brands,
            'colors' => $colors,
            'years' => $years,
            'transmission_types' => $transmissionTypes,
            'fuel_types' => $fuelTypes,
            'body_types' => $bodyTypes,
            'price_range' => $priceRange,
        ];
    }

    /**
     * عرض تفاصيل سيارة محددة.
     *
     * يعرض صفحة تفاصيل شاملة لسيارة محددة مع جميع البيانات المتعلقة بها
     * بما في ذلك الصور، المواصفات، الميزات، السعر، والعروض المرتبطة
     *
     * @param string $id معرف السيارة
     * @return View عرض صفحة تفاصيل السيارة
     */
    public function show(string $id): View
    {
        // البحث عن السيارة مع جميع العلاقات المطلوبة
        $car = Car::with([
            'media',
            'brand',
            'carModel',
            'manufacturingYear',
            'bodyType',
            'transmissionType',
            'fuelType',
            'mainColor',
            'interiorColor',
            'features.category',
        ])
        ->where('is_sold', false)
        ->where('is_active', true)
        ->findOrFail($id);

        // جلب نص خدمات ما بعد البيع من إعدادات النظام
        $afterSalesServiceText = setting(
            'after_sales_service_text',
            'نحن نقدم خدمة ما بعد البيع المتميزة لضمان رضاكم التام عن سياراتكم.'
        );

        // جلب السيارات المشابهة (نفس الماركة أو نفس الفئة السعرية)
        $similarCars = Car::with(['media', 'brand', 'carModel', 'manufacturingYear', 'mainColor'])
            ->where('id', '!=', $car->id)
            ->where('is_sold', false)
            ->where('is_active', true)
            ->where(function ($query) use ($car) {
                $query->where('brand_id', $car->brand_id)
                      ->orWhereBetween('price', [
                          $car->price * 0.8, // 20% أقل
                          $car->price * 1.2  // 20% أكثر
                      ]);
            })
            ->limit(4)
            ->get();

        return view('site.cars.show', compact('car', 'afterSalesServiceText', 'similarCars'));
    }

    /**
     * جلب الموديلات حسب الماركة (AJAX endpoint).
     *
     * يستخدم هذا المسار لتحديث قائمة الموديلات ديناميكياً عند اختيار ماركة معينة
     * في نموذج الفلترة، مما يحسن تجربة المستخدم
     *
     * @param Brand $brand الماركة المحددة
     * @return JsonResponse قائمة الموديلات بصيغة JSON
     */
    public function getModelsByBrand(Brand $brand): JsonResponse
    {
        $models = $brand->carModels()
            ->where('status', true)
            ->withCount(['cars as available_cars_count' => function ($query) {
                $query->where('is_sold', false)->where('is_active', true);
            }])
            ->having('available_cars_count', '>', 0)
            ->orderBy('name')
            ->get(['id', 'name']);

        return response()->json([
            'success' => true,
            'models' => $models,
        ]);
    }
}
