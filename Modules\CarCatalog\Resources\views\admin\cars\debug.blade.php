@extends('dashboard::layouts.admin_layout')

@section('title', 'تشخيص مشكلة إرسال النماذج')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-bug me-2"></i>
                        صفحة تشخيص مشكلة إرسال النماذج
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات التشخيص:</h6>
                        <ul class="mb-0">
                            <li><strong>المستخدم:</strong> {{ auth()->user()->email }}</li>
                            <li><strong>الدور:</strong> {{ auth()->user()->roles->pluck('name')->join(', ') }}</li>
                            <li><strong>الصلاحيات:</strong> {{ auth()->user()->permissions->pluck('name')->join(', ') }}</li>
                            <li><strong>CSRF Token:</strong> {{ csrf_token() }}</li>
                        </ul>
                    </div>

                    <!-- اختبار 1: نموذج بسيط -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">اختبار 1: نموذج بسيط (POST)</h6>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('admin.cars.test-submission') }}" method="POST" id="simpleForm">
                                @csrf
                                <div class="mb-3">
                                    <label for="test_title" class="form-label">عنوان اختبار</label>
                                    <input type="text" class="form-control" id="test_title" name="title" value="اختبار بسيط">
                                </div>
                                <button type="submit" class="btn btn-primary">إرسال اختبار بسيط</button>
                            </form>
                        </div>
                    </div>

                    <!-- اختبار 2: نموذج مع ملفات -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">اختبار 2: نموذج مع ملفات (POST + multipart)</h6>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('admin.cars.test-submission') }}" method="POST" enctype="multipart/form-data" id="fileForm">
                                @csrf
                                <div class="mb-3">
                                    <label for="test_title2" class="form-label">عنوان اختبار</label>
                                    <input type="text" class="form-control" id="test_title2" name="title" value="اختبار مع ملفات">
                                </div>
                                <div class="mb-3">
                                    <label for="test_file" class="form-label">ملف اختبار</label>
                                    <input type="file" class="form-control" id="test_file" name="test_file">
                                </div>
                                <button type="submit" class="btn btn-success">إرسال اختبار مع ملفات</button>
                            </form>
                        </div>
                    </div>

                    <!-- اختبار 3: نموذج PUT -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">اختبار 3: نموذج PUT (محاكاة تحديث)</h6>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('admin.cars.test-submission') }}" method="POST" id="putForm">
                                @csrf
                                @method('PUT')
                                <div class="mb-3">
                                    <label for="test_title3" class="form-label">عنوان اختبار</label>
                                    <input type="text" class="form-control" id="test_title3" name="title" value="اختبار PUT">
                                </div>
                                <button type="submit" class="btn btn-warning">إرسال اختبار PUT</button>
                            </form>
                        </div>
                    </div>

                    <!-- نتائج الاختبار -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">نتائج الاختبار</h6>
                        </div>
                        <div class="card-body">
                            <div id="testResults" class="alert alert-secondary">
                                لم يتم تشغيل أي اختبار بعد...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مراقبة لجميع النماذج
    const forms = ['simpleForm', 'fileForm', 'putForm'];
    
    forms.forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault(); // منع الإرسال الافتراضي
                
                console.log(`=== ${formId} SUBMISSION ===`);
                console.log('Form action:', this.action);
                console.log('Form method:', this.method);
                console.log('Form enctype:', this.enctype);
                
                const formData = new FormData(this);
                console.log('Form data:');
                for (let [key, value] of formData.entries()) {
                    console.log(`${key}: ${value}`);
                }
                
                // إرسال AJAX
                fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    document.getElementById('testResults').innerHTML = `
                        <div class="alert alert-success">
                            <h6>نجح اختبار ${formId}!</h6>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('testResults').innerHTML = `
                        <div class="alert alert-danger">
                            <h6>فشل اختبار ${formId}!</h6>
                            <p>${error.message}</p>
                        </div>
                    `;
                });
            });
        }
    });
});
</script>
@endpush
