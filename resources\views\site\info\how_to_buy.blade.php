@extends('site.layouts.site_layout')

@section('title', 'كيف أشتريها؟ - موتور لاين')

@section('meta_description', 'تعرف على خطوات شراء السيارة بالكاش أو التمويل والمستندات المطلوبة في موتور لاين')

@section('meta_keywords', 'شراء سيارة, خطوات الشراء, تمويل سيارات, مستندات مطلوبة, موتور لاين')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
<style>
    .how-to-buy-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 4rem 0;
        margin-bottom: 3rem;
    }
    
    .step-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 2rem;
        margin-bottom: 2rem;
        border-right: 4px solid var(--secondary-color);
        transition: all var(--transition-speed) ease;
    }
    
    .step-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .step-number {
        width: 50px;
        height: 50px;
        background: var(--secondary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    
    .process-section {
        margin-bottom: 4rem;
    }
    
    .process-title {
        color: var(--primary-color);
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
    }
    
    .process-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        right: 50%;
        transform: translateX(50%);
        width: 80px;
        height: 3px;
        background: var(--accent-color);
    }
    
    .document-list {
        background: rgba(30, 58, 138, 0.05);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 1rem;
    }
    
    .document-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
    }
    
    .document-item:last-child {
        margin-bottom: 0;
    }
    
    .document-icon {
        color: var(--success-color);
        margin-left: 0.75rem;
        font-size: 1.1rem;
    }
    
    .info-box {
        background: rgba(249, 115, 22, 0.1);
        border: 1px solid var(--accent-color);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .info-box-title {
        color: var(--accent-color);
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }
    
    .info-box-icon {
        margin-left: 0.5rem;
        font-size: 1.2rem;
    }
    
    .contact-section {
        background: var(--light-bg);
        border-radius: var(--border-radius);
        padding: 2rem;
        text-align: center;
        margin-top: 3rem;
    }
    
    .contact-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 1.5rem;
    }
    
    .btn-contact {
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        text-decoration: none;
        font-weight: 600;
        transition: all var(--transition-speed) ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-contact-primary {
        background: var(--secondary-color);
        color: white;
    }
    
    .btn-contact-primary:hover {
        background: var(--primary-color);
        color: white;
        transform: translateY(-2px);
    }
    
    .btn-contact-whatsapp {
        background: #25D366;
        color: white;
    }
    
    .btn-contact-whatsapp:hover {
        background: #128C7E;
        color: white;
        transform: translateY(-2px);
    }
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="how-to-buy-hero">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h1 class="display-4 fw-bold mb-3">كيف أشتري سيارتي من موتور لاين؟</h1>
                <p class="lead">دليلك الشامل لخطوات شراء السيارة بالكاش أو التمويل بكل سهولة ووضوح</p>
            </div>
        </div>
    </div>
</section>

<div class="container">
    <!-- عملية الشراء الكاش -->
    <section class="process-section">
        <h2 class="process-title">خطوات شراء السيارة بالدفع الكاش</h2>
        
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h4 class="fw-bold mb-3">اختر سيارتك المفضلة</h4>
                    <p class="text-muted">تصفح مجموعتنا الواسعة من السيارات الجديدة واستخدم الفلاتر للعثور على السيارة المناسبة لك من حيث الماركة والموديل والسعر.</p>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="step-card">
                    <div class="step-number">2</div>
                    <h4 class="fw-bold mb-3">قدم طلب الحجز</h4>
                    <p class="text-muted">اضغط على "اطلبها الآن" واختر "شراء كاش" ثم املأ بياناتك الشخصية بدقة كما هي مسجلة في الهوية الوطنية.</p>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="step-card">
                    <div class="step-number">3</div>
                    <h4 class="fw-bold mb-3">ارفع المستندات المطلوبة</h4>
                    <p class="text-muted">قم برفع صور واضحة للمستندات المطلوبة لإتمام عملية الحجز.</p>
                    
                    <div class="document-list">
                        <h6 class="fw-bold mb-3">المستندات المطلوبة:</h6>
                        <div class="document-item">
                            <i class="fas fa-check-circle document-icon"></i>
                            <span>صورة الهوية الوطنية أو الإقامة (الوجه الأمامي والخلفي)</span>
                        </div>
                        <div class="document-item">
                            <i class="fas fa-check-circle document-icon"></i>
                            <span>صورة رخصة القيادة سارية المفعول</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="step-card">
                    <div class="step-number">4</div>
                    <h4 class="fw-bold mb-3">ادفع مبلغ الحجز أونلاين</h4>
                    <p class="text-muted">ادفع مبلغ الحجز المطلوب عبر بوابة الدفع الآمنة باستخدام بطاقة مدى أو الفيزا أو الماستركارد.</p>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="step-card">
                    <div class="step-number">5</div>
                    <h4 class="fw-bold mb-3">أكمل باقي المبلغ واستلم سيارتك</h4>
                    <p class="text-muted">توجه إلى المعرض في الموعد المحدد لإكمال باقي المبلغ وإنهاء الإجراءات واستلام سيارتك الجديدة.</p>
                </div>
            </div>
        </div>
        
        <div class="info-box">
            <div class="info-box-title">
                <i class="fas fa-info-circle info-box-icon"></i>
                معلومة مهمة
            </div>
            <p class="mb-0">مبلغ الحجز يُخصم من إجمالي سعر السيارة، وسيتم التواصل معك خلال 24 ساعة لتحديد موعد استلام السيارة من المعرض.</p>
        </div>
    </section>

    <!-- عملية طلب التمويل -->
    <section class="process-section">
        <h2 class="process-title">خطوات تقديم طلب التمويل</h2>
        
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h4 class="fw-bold mb-3">اختر سيارتك وقدم طلب التمويل</h4>
                    <p class="text-muted">اختر السيارة المناسبة واضغط على "اطلبها الآن" ثم اختر "تقديم طلب تمويل" لبدء عملية التمويل.</p>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="step-card">
                    <div class="step-number">2</div>
                    <h4 class="fw-bold mb-3">املأ البيانات الشخصية والمالية</h4>
                    <p class="text-muted">أدخل بياناتك الشخصية ومعلوماتك المالية بدقة، بما في ذلك الراتب الشهري والالتزامات المالية الحالية.</p>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="step-card">
                    <div class="step-number">3</div>
                    <h4 class="fw-bold mb-3">ارفع المستندات المطلوبة للتمويل</h4>
                    <p class="text-muted">قم برفع جميع المستندات المطلوبة لدراسة طلب التمويل.</p>
                    
                    <div class="document-list">
                        <h6 class="fw-bold mb-3">المستندات المطلوبة للتمويل:</h6>
                        <div class="document-item">
                            <i class="fas fa-check-circle document-icon"></i>
                            <span>صورة الهوية الوطنية أو الإقامة (الوجه الأمامي والخلفي)</span>
                        </div>
                        <div class="document-item">
                            <i class="fas fa-check-circle document-icon"></i>
                            <span>صورة رخصة القيادة سارية المفعول</span>
                        </div>
                        <div class="document-item">
                            <i class="fas fa-check-circle document-icon"></i>
                            <span>تعريف بالراتب من جهة العمل</span>
                        </div>
                        <div class="document-item">
                            <i class="fas fa-check-circle document-icon"></i>
                            <span>كشف حساب بنكي لآخر 3 أشهر</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="step-card">
                    <div class="step-number">4</div>
                    <h4 class="fw-bold mb-3">مراجعة الطلب والتواصل معك</h4>
                    <p class="text-muted">سيقوم فريقنا المختص بمراجعة طلبك والتواصل معك خلال 48 ساعة لإعلامك بنتيجة الطلب والخطوات التالية.</p>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="step-card">
                    <div class="step-number">5</div>
                    <h4 class="fw-bold mb-3">توقيع عقد التمويل واستلام السيارة</h4>
                    <p class="text-muted">عند الموافقة على طلب التمويل، توجه إلى المعرض لتوقيع عقد التمويل ودفع الدفعة الأولى واستلام سيارتك.</p>
                </div>
            </div>
        </div>
        
        <div class="info-box">
            <div class="info-box-title">
                <i class="fas fa-info-circle info-box-icon"></i>
                ملاحظة مهمة حول التمويل
            </div>
            <p class="mb-0">نتعامل مع أفضل البنوك وشركات التمويل في المملكة لضمان حصولك على أفضل شروط التمويل. قد تختلف شروط التمويل حسب الراتب والالتزامات المالية الحالية.</p>
        </div>
    </section>

    <!-- قسم التواصل والمساعدة -->
    <section class="contact-section">
        <h3 class="fw-bold mb-3">هل تحتاج مساعدة إضافية؟</h3>
        <p class="text-muted mb-0">فريقنا المختص جاهز لمساعدتك في أي وقت للإجابة على استفساراتك ومساعدتك في اختيار السيارة المناسبة</p>
        
        <div class="contact-buttons">
            <a href="tel:+966123456789" class="btn-contact btn-contact-primary">
                <i class="fas fa-phone"></i>
                اتصل بنا
            </a>
            <a href="https://wa.me/966123456789" class="btn-contact btn-contact-whatsapp" target="_blank">
                <i class="fab fa-whatsapp"></i>
                واتساب
            </a>
            <a href="{{ route('site.contact') }}" class="btn-contact btn-contact-primary">
                <i class="fas fa-envelope"></i>
                راسلنا
            </a>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الحركة عند التمرير
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // تطبيق التأثير على بطاقات الخطوات
    document.querySelectorAll('.step-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>
@endpush
