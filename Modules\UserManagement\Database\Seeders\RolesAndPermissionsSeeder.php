<?php

namespace Modules\UserManagement\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

/**
 * RolesAndPermissionsSeeder
 *
 * هذا الـ Seeder مسؤول عن إنشاء الأدوار والصلاحيات الأولية في النظام
 */
class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * تشغيل عملية إضافة البيانات
     *
     * @return void
     */
    public function run()
    {
        // مسح ذاكرة التخزين المؤقت للصلاحيات
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // إنشاء الصلاحيات الأولية
        $this->createPermissions();

        // إنشاء الأدوار وتعيين الصلاحيات لها
        $this->createRolesWithPermissions();

        // إنشاء مستخدم Super Admin افتراضي
        $this->createDefaultSuperAdmin();
    }

    /**
     * إنشاء الصلاحيات الأولية
     *
     * @return void
     */
    private function createPermissions()
    {
        // صلاحية الوصول إلى لوحة تحكم الإدارة
        Permission::firstOrCreate([
            'name' => 'access_admin_dashboard',
            'guard_name' => 'web',
        ], [
            'name' => 'access_admin_dashboard',
            'guard_name' => 'web',
        ]);

        // صلاحية الوصول إلى لوحة تحكم العميل
        Permission::firstOrCreate([
            'name' => 'access_customer_dashboard',
            'guard_name' => 'web',
        ], [
            'name' => 'access_customer_dashboard',
            'guard_name' => 'web',
        ]);

        // صلاحية عرض السيارات في لوحة تحكم الإدارة
        Permission::firstOrCreate([
            'name' => 'view_cars_admin',
            'guard_name' => 'web',
        ], [
            'name' => 'view_cars_admin',
            'guard_name' => 'web',
        ]);

        // صلاحية إدارة السيارات في لوحة تحكم الإدارة
        Permission::firstOrCreate([
            'name' => 'manage_cars_admin',
            'guard_name' => 'web',
        ], [
            'name' => 'manage_cars_admin',
            'guard_name' => 'web',
        ]);

        // صلاحية إدارة الأدوار والصلاحيات
        Permission::firstOrCreate([
            'name' => 'manage_roles_permissions',
            'guard_name' => 'web',
        ], [
            'name' => 'manage_roles_permissions',
            'guard_name' => 'web',
        ]);

        // صلاحية إدارة بيانات السيارات الوصفية
        Permission::firstOrCreate([
            'name' => 'manage_car_metadata',
            'guard_name' => 'web',
        ], [
            'name' => 'manage_car_metadata',
            'guard_name' => 'web',
        ]);

        // صلاحية إدارة إعدادات النظام
        Permission::firstOrCreate([
            'name' => 'manage_system_settings',
            'guard_name' => 'web',
        ], [
            'name' => 'manage_system_settings',
            'guard_name' => 'web',
        ]);

        // صلاحية إدارة الموظفين
        Permission::firstOrCreate([
            'name' => 'manage_employees_admin',
            'guard_name' => 'web',
        ], [
            'name' => 'manage_employees_admin',
            'guard_name' => 'web',
        ]);
    }

    /**
     * إنشاء الأدوار وتعيين الصلاحيات لها
     *
     * @return void
     */
    private function createRolesWithPermissions()
    {
        // دور Super Admin - له جميع الصلاحيات
        $superAdminRole = Role::firstOrCreate([
            'name' => 'Super Admin',
            'guard_name' => 'web',
        ], [
            'name' => 'Super Admin',
            'guard_name' => 'web',
        ]);
        $superAdminRole->syncPermissions(Permission::all());

        // دور Employee - له صلاحيات محددة
        $employeeRole = Role::firstOrCreate([
            'name' => 'Employee',
            'guard_name' => 'web',
        ], [
            'name' => 'Employee',
            'guard_name' => 'web',
        ]);
        $employeeRole->syncPermissions([
            'access_admin_dashboard',
            'view_cars_admin',
            'manage_car_metadata'
        ]);

        // دور Customer - له صلاحية الوصول إلى لوحة تحكم العميل فقط
        $customerRole = Role::firstOrCreate([
            'name' => 'Customer',
            'guard_name' => 'web',
        ], [
            'name' => 'Customer',
            'guard_name' => 'web',
        ]);
        $customerRole->syncPermissions(['access_customer_dashboard']);
    }

    /**
     * إنشاء مستخدم Super Admin افتراضي
     *
     * @return void
     */
    private function createDefaultSuperAdmin()
    {
        $superAdminUser = \Modules\UserManagement\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'phone_number' => '0500000000', // رقم افتراضي، تأكد من أنه فريد إذا كان هناك قيد صارم
                'password' => bcrypt('password'), // هام: يجب تغيير كلمة المرور هذه في بيئة الإنتاج
                'email_verified_at' => now(),
                'phone_verified_at' => now(),
                'status' => 'active',
            ]
        );

        // تعيين دور Super Admin للمستخدم
        $superAdminUser->assignRole('Super Admin');
    }
}
