{{-- نموذج إنشاء/تعديل الأدوار --}}

{{-- قسم المعلومات الأساسية --}}
<div class="row mb-4">
    <div class="col-12">
        <div class="border rounded p-3 bg-light">
            <h6 class="fw-bold mb-3 text-primary">
                <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label fw-semibold">
                            اسم الدور <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="brand-form-control @error('name') is-invalid @enderror"
                               id="name" name="name" value="{{ old('name', $role->name ?? '') }}"
                               placeholder="مثال: مدير المبيعات" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">أدخل اسماً وصفياً للدور يوضح مهامه ومسؤولياته</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-semibold">معلومات إضافية</label>
                        <div class="p-2 bg-white rounded border">
                            <small class="text-muted">
                                <i class="fas fa-lightbulb text-warning me-1"></i>
                                <strong>نصيحة:</strong> اختر اسماً واضحاً يعبر عن طبيعة الدور ومستوى الصلاحيات المطلوبة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- قسم الصلاحيات --}}
<div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="fw-bold mb-0 text-primary">
            <i class="fas fa-shield-alt me-2"></i>الصلاحيات والأذونات
        </h6>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-sm btn-brand-primary" onclick="selectAllPermissions()">
                <i class="fas fa-check-square me-1"></i>تحديد الكل
            </button>
            <button type="button" class="btn btn-sm btn-brand-secondary" onclick="deselectAllPermissions()">
                <i class="fas fa-square me-1"></i>إلغاء تحديد الكل
            </button>
        </div>
    </div>
    <div class="alert alert-info d-flex align-items-start">
        <div class="me-3">
            <i class="fas fa-info-circle fa-lg"></i>
        </div>
        <div class="flex-grow-1">
            <strong>تنبيه:</strong> اختر الصلاحيات التي تريد تعيينها لهذا الدور بعناية. يمكن للمستخدمين الذين لديهم هذا الدور الوصول إلى الميزات المحددة فقط.
            <br><small class="text-muted mt-1 d-block">تم تحديد <span id="permission-counter-inline">0</span> من أصل <span id="total-permissions">0</span> صلاحية</small>
        </div>
    </div>

    @if($permissions->count() > 0)
        <div class="row">
            @foreach($permissions as $group => $groupPermissions)
                @php
                    $groupTranslation = get_permission_group_translation($group);
                @endphp
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 permission-group-card shadow-sm">
                        <div class="card-header bg-gradient-primary text-white">
                            <div class="d-flex align-items-center">
                                <div class="icon-circle-light me-2">
                                    <i class="fas fa-{{ $groupTranslation['icon'] }}"></i>
                                </div>
                                <h6 class="mb-0 fw-bold">{{ $groupTranslation['name'] }}</h6>
                            </div>
                        </div>
                        <div class="card-body p-3">
                            @foreach($groupPermissions as $permission)
                                @php
                                    $permissionTranslation = get_permission_translation($permission->name);
                                @endphp
                                <div class="permission-item mb-3 p-3 border rounded-3 bg-light" onclick="togglePermission('permission_{{ $permission->id }}')">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               name="permissions[]" value="{{ $permission->name }}"
                                               id="permission_{{ $permission->id }}"
                                               @if(isset($rolePermissions) && in_array($permission->name, $rolePermissions)) checked @endif>
                                        <label class="form-check-label w-100" for="permission_{{ $permission->id }}">
                                            <div class="fw-bold text-dark mb-1">{{ $permissionTranslation['name'] }}</div>
                                            <small class="text-muted">{{ $permissionTranslation['description'] }}</small>
                                        </label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

    @else
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            لا توجد صلاحيات متاحة في النظام.
        </div>
    @endif
</div>

{{-- أزرار الحفظ والإلغاء --}}
<div class="row mt-4">
    <div class="col-12">
        <div class="border-top pt-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    تأكد من مراجعة جميع الصلاحيات قبل الحفظ
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.roles.index') }}" class="btn btn-brand-secondary">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </a>
                    <button type="submit" class="btn btn-brand-primary">
                        <i class="fas fa-save me-1"></i>{{ isset($role) ? 'تحديث الدور' : 'حفظ الدور' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* تحسينات خاصة بنموذج الأدوار */
.permission-group-card {
    transition: all var(--transition-speed) ease;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.permission-group-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.bg-gradient-primary {
    background: var(--secondary-color);
}

.icon-circle {
    width: 30px;
    height: 30px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-circle-light {
    width: 30px;
    height: 30px;
    background: var(--secondary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.permission-item {
    transition: all 0.2s ease;
    cursor: pointer;
    border-radius: var(--border-radius);
    background-color: rgb(248, 249, 250);
    border-color: rgb(222, 226, 230);
}

.permission-item:hover {
    background-color: rgba(30, 58, 138, 0.1) !important;
    border-color: var(--secondary-color) !important;
    transform: translateX(5px);
}

.permission-item input[type="checkbox"]:checked + label {
    color: var(--secondary-color);
    font-weight: 600;
}

.permission-item input[type="checkbox"]:checked {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.permission-item.selected {
    background-color: rgba(30, 58, 138, 0.05);
    border-color: var(--secondary-color);
}
</style>
@endpush

@push('scripts')
<script>
function togglePermission(checkboxId) {
    const checkbox = document.getElementById(checkboxId);
    if (checkbox) {
        checkbox.checked = !checkbox.checked;
        updatePermissionItemStyle(checkbox, checkbox.checked);
        updatePermissionCounter();
    }
}

function selectAllPermissions() {
    document.querySelectorAll('input[name="permissions[]"]').forEach(function(checkbox) {
        checkbox.checked = true;
        updatePermissionItemStyle(checkbox, true);
    });

    updatePermissionCounter();
    showToast('تم تحديد جميع الصلاحيات', 'success');
}

function deselectAllPermissions() {
    document.querySelectorAll('input[name="permissions[]"]').forEach(function(checkbox) {
        checkbox.checked = false;
        updatePermissionItemStyle(checkbox, false);
    });

    updatePermissionCounter();
    showToast('تم إلغاء تحديد جميع الصلاحيات', 'info');
}

function updatePermissionItemStyle(checkbox, isChecked) {
    const permissionItem = checkbox.closest('.permission-item');
    if (isChecked) {
        permissionItem.classList.add('selected');
    } else {
        permissionItem.classList.remove('selected');
    }
}

function showToast(message, type) {
    // إنشاء toast بسيط
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); border-radius: 8px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle me-2"></i>
        ${message}
    `;

    document.body.appendChild(toast);

    // إزالة التوست بعد 3 ثوان
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات للصلاحيات
    document.querySelectorAll('input[name="permissions[]"]').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            updatePermissionItemStyle(this, this.checked);
            updatePermissionCounter();
        });

        // تطبيق التأثير على الصلاحيات المحددة مسبقاً
        if (checkbox.checked) {
            updatePermissionItemStyle(checkbox, true);
        }
    });

    // تحسين تجربة النموذج
    const form = document.getElementById('roleForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    }

    // إضافة عداد للصلاحيات المحددة
    updatePermissionCounter();
});

function updatePermissionCounter() {
    const totalPermissions = document.querySelectorAll('input[name="permissions[]"]').length;
    const selectedPermissions = document.querySelectorAll('input[name="permissions[]"]:checked').length;

    // تحديث العداد في التنبيه
    const counterInline = document.getElementById('permission-counter-inline');
    const totalElement = document.getElementById('total-permissions');

    if (counterInline) {
        counterInline.textContent = selectedPermissions;
    }

    if (totalElement) {
        totalElement.textContent = totalPermissions;
    }

    // تحديث العداد القديم إذا وجد
    const oldCounter = document.getElementById('permission-counter');
    if (oldCounter) {
        oldCounter.innerHTML = `تم تحديد ${selectedPermissions} من أصل ${totalPermissions} صلاحية`;
    }
}
</script>
@endpush
