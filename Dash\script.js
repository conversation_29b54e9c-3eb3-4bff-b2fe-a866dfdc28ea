/**
 * script.js - كود جافاسكريبت للتحكم في القائمة الجانبية
 * يتضمن وظائف فتح وإغلاق القائمة الجانبية والتبديل بين الوضع العادي والمصغر
 */

document.addEventListener('DOMContentLoaded', function() {
    // العناصر الرئيسية
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    const mobileSidebarClose = document.getElementById('mobile-sidebar-close');
    const mainContent = document.getElementById('main-content');
    const sidebarToggler = document.getElementById('sidebar-toggler');
    const footer = document.querySelector('.footer');
    
    // تعيين السنة الحالية في تذييل الصفحة
    const currentYearElement = document.getElementById('current-year');
    if (currentYearElement) {
        currentYearElement.textContent = new Date().getFullYear();
    }
    
    
    // التحقق من حجم الشاشة
    function isMobile() {
        return window.innerWidth < 992; // Bootstrap lg breakpoint
    }
    
    // فتح القائمة الجانبية في الأجهزة المحمولة
    function openMobileSidebar() {
        if (isMobile()) {
            sidebar.classList.add('mobile-show');
            sidebarOverlay.classList.add('show');
            document.body.style.overflow = 'hidden'; // منع التمرير في الصفحة الخلفية
        }
    }
    
    // إغلاق القائمة الجانبية في الأجهزة المحمولة
    function closeMobileSidebar() {
        sidebar.classList.remove('mobile-show');
        sidebarOverlay.classList.remove('show');
        document.body.style.overflow = ''; // إعادة تمكين التمرير
    }
    
    // تبديل وضع القائمة المصغرة
    function toggleMiniMode() {
        if (!isMobile()) {
            sidebar.classList.toggle('mini-mode');
            if (mainContent) mainContent.classList.toggle('mini-sidebar');
            if (footer) footer.classList.toggle('mini-sidebar');
            
            // حفظ حالة القائمة في التخزين المحلي
            localStorage.setItem('sidebarMiniMode', sidebar.classList.contains('mini-mode'));
            
            // إغلاق جميع القوائم الفرعية عند تبديل وضع القائمة
            closeAllSubmenus();
        }
    }
    
    // إغلاق جميع القوائم الفرعية
    function closeAllSubmenus() {
        const openSubmenus = document.querySelectorAll('.collapse.show');
        openSubmenus.forEach(submenu => {
            const bsCollapse = bootstrap.Collapse.getInstance(submenu);
            if (bsCollapse) bsCollapse.hide();
            
            // إعادة تدوير أيقونات الأسهم
            const parentLink = document.querySelector(`[href="#${submenu.id}"]`);
            if (parentLink) {
                const chevron = parentLink.querySelector('.chevron');
                if (chevron) chevron.classList.remove('rotate');
            }
        });
    }
    
    // تهيئة حالة القائمة الجانبية
    function initSidebarState() {
        if (isMobile()) {
            // الأجهزة المحمولة: إزالة وضع القائمة المصغرة
            sidebar.classList.remove('mini-mode', 'hover-expand');
            if (mainContent) mainContent.classList.remove('mini-sidebar');
            if (footer) footer.classList.remove('mini-sidebar');
        } else {
            // الشاشات الكبيرة: استعادة الحالة المحفوظة
            const savedState = localStorage.getItem('sidebarMiniMode');
            
            if (savedState === 'true') {
                sidebar.classList.add('mini-mode');
                if (mainContent) mainContent.classList.add('mini-sidebar');
                if (footer) footer.classList.add('mini-sidebar');
            }
        }
    }
    
    // إضافة مستمعي الأحداث
    
    // زر تبديل القائمة الجانبية
    if (sidebarToggler) {
        sidebarToggler.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (isMobile()) {
                openMobileSidebar();
            } else {
                toggleMiniMode();
            }
        });
    }
    
    // إغلاق القائمة الجانبية عند النقر على الخلفية المعتمة
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', closeMobileSidebar);
    }
    
    // زر إغلاق القائمة الجانبية في الأجهزة المحمولة
    if (mobileSidebarClose) {
        mobileSidebarClose.addEventListener('click', closeMobileSidebar);
    }
    
    // تفعيل القوائم الفرعية
    const subMenuToggles = document.querySelectorAll('.nav-link[data-bs-toggle="collapse"]');
    
    subMenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            // تبديل أيقونة السهم
            const chevron = this.querySelector('.chevron');
            if (chevron) {
                chevron.classList.toggle('rotate');
            }
            
            // في وضع القائمة المصغرة على الشاشات الكبيرة، نضمن أن القائمة تظل موسعة عند النقر
            if (!isMobile() && sidebar.classList.contains('mini-mode')) {
                sidebar.classList.add('hover-expand');
            }
        });
    });
    
    // تفعيل وضع التوسع عند تحويم الماوس (للوضع المصغر فقط)
    sidebar.addEventListener('mouseenter', function() {
        if (!isMobile() && sidebar.classList.contains('mini-mode')) {
            sidebar.classList.add('hover-expand');
        }
    });
    
    sidebar.addEventListener('mouseleave', function() {
        if (!isMobile() && sidebar.classList.contains('mini-mode')) {
            sidebar.classList.remove('hover-expand');
            
            // إغلاق القوائم الفرعية عند مغادرة القائمة في الوضع المصغر
            closeAllSubmenus();
        }
    });
    
    // تعديل سلوك القائمة عند تغيير حجم النافذة
    window.addEventListener('resize', function() {
        initSidebarState();
    });
    
    // تهيئة حالة القائمة عند تحميل الصفحة
    initSidebarState();
    
    // تهيئة الرسوم البيانية
    if (typeof initCharts === 'function') {
        initCharts();
    } // Closing brace for the 'if (customerChartElement)' block

    // 2. Brands Pie Chart
    const brandsPieChartElement = document.getElementById('brandsPieChart');
    if (brandsPieChartElement) {
        const brandsData = {
            labels: ['تويوتا', 'هوندا', 'نيسان', 'هيونداي', 'كيا', 'مرسيدس'],
            datasets: [
                {
                    data: [25, 20, 15, 15, 15, 10],
                    backgroundColor: [
                        colors.primary,
                        colors.secondary,
                        colors.accent,
                        colors.success,
                        colors.warning,
                        colors.info
                    ],
                    borderWidth: 0,
                    hoverOffset: 10
                }
            ]
        };

        const brandsOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(brandsPieChartElement, {
            type: 'pie',
            data: brandsData,
            options: brandsOptions
        });
    }

    // 3. Top Cars Bar Chart
    const topCarsChartElement = document.getElementById('topCarsChart');
    if (topCarsChartElement) {
        const topCarsData = {
            labels: ['تويوتا كامري', 'هوندا أكورد', 'نيسان التيما', 'هيونداي سوناتا', 'كيا K5'],
            datasets: [
                {
                    label: 'المبيعات',
                    data: [152, 138, 124, 119, 105],
                    backgroundColor: [
                        colors.primary,
                        colors.primary,
                        colors.primary,
                        colors.primary,
                        colors.primary
                    ],
                    hoverBackgroundColor: colors.accent,
                    borderRadius: 6
                }
            ]
        };

        const topCarsOptions = {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'عدد السيارات المباعة'
                    }
                },
                y: {
                    reverse: false
                }
            }
        };

        new Chart(topCarsChartElement, {
            type: 'bar',
            data: topCarsData,
            options: topCarsOptions
        });
    }

    // 4. Categories Doughnut Chart
    const categoriesChartElement = document.getElementById('categoriesChart');
    if (categoriesChartElement) {
        const categoriesData = {
            labels: ['سيدان', 'دفع رباعي', 'كوبيه', 'هاتشباك', 'كروس أوفر'],
            datasets: [
                {
                    data: [40, 30, 10, 10, 10],
                    backgroundColor: [
                        colors.secondary,
                        colors.success,
                        colors.warning,
                        colors.accent,
                        colors.info
                    ],
                    borderWidth: 0,
                    hoverOffset: 8
                }
            ]
        };

        const categoriesOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(categoriesChartElement, {
            type: 'doughnut',
            data: categoriesData,
            options: categoriesOptions
        });
    }

    // 5. Payment Methods Doughnut Chart
    const paymentMethodsChartElement = document.getElementById('paymentMethodsChart');
    if (paymentMethodsChartElement) {
        const paymentMethodsData = {
            labels: ['كاش', 'تمويل بنكي', 'بطاقة ائتمان', 'تحويل بنكي'],
            datasets: [
                {
                    data: [45, 35, 10, 10],
                    backgroundColor: [
                        colors.accent,
                        colors.primary,
                        colors.success,
                        colors.info
                    ],
                    borderWidth: 0,
                    hoverOffset: 8
                }
            ]
        };

        const paymentMethodsOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '50%',
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(paymentMethodsChartElement, {
            type: 'doughnut',
            data: paymentMethodsData,
            options: paymentMethodsOptions
        });
    }

    // 6. New Customers Registration Chart
    const newCustomersChartElement = document.getElementById('newCustomersChart');
    if (newCustomersChartElement) {
        const ctx_new_customers = newCustomersChartElement.getContext('2d');
        const newCustomersGradient = ctx_new_customers.createLinearGradient(0, 0, 0, 250);
        newCustomersGradient.addColorStop(0, 'rgba(99, 102, 241, 0.2)');
        newCustomersGradient.addColorStop(1, 'rgba(99, 102, 241, 0.05)');

        const newCustomersData = {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [
                {
                    label: 'تسجيل العملاء الجدد',
                    data: [45, 86, 74, 52, 48, 68],
                    borderColor: '#6366f1',
                    backgroundColor: newCustomersGradient,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#6366f1',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }
            ]
        };

        const newCustomersOptions = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    },
                    ticks: {
                        display: false
                    }
                },
                x: {
                    reverse: true, // RTL support
                    grid: {
                        drawBorder: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            },
            elements: {
                line: {
                    borderWidth: 3
                }
            }
        };

        new Chart(newCustomersChartElement, {
            type: 'line',
            data: newCustomersData,
            options: newCustomersOptions
        });
    }

    // 7. Financing Status Chart
    const financingStatusChartElement = document.getElementById('financingStatusChart');
    if (financingStatusChartElement) {
        const financingStatusData = {
            labels: ['موافق عليها', 'مرفوضة', 'معلقة'],
            datasets: [
                {
                    data: [65, 15, 20],
                    backgroundColor: [
                        colors.success,
                        colors.danger,
                        colors.warning
                    ],
                    borderWidth: 0,
                    hoverOffset: 10
                }
            ]
        };

        const financingStatusOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(financingStatusChartElement, {
            type: 'doughnut',
            data: financingStatusData,
            options: financingStatusOptions
        });
    }
});

// دالة تهيئة الرسوم البيانية (نحتفظ بها من الكود الأصلي)
function initCharts() {
    // التحقق من وجود Chart.js
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js غير متوفر. لا يمكن تهيئة الرسوم البيانية.');
        return;
    }

    // التحقق من وجود عناصر الرسوم البيانية
    const salesChartElement = document.getElementById('salesChart');
    const inventoryChartElement = document.getElementById('inventoryChart');
    const customerChartElement = document.getElementById('customerChart');
    
    // Common Options
    Chart.defaults.font.family = 'IBM Plex Sans Arabic, Tahoma, Geneva, Verdana, sans-serif';
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#64748b';
    Chart.defaults.plugins.legend.position = 'bottom';
    Chart.defaults.plugins.legend.labels.boxWidth = 12;
    Chart.defaults.plugins.legend.labels.usePointStyle = true;
    Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(15, 23, 42, 0.8)';
    Chart.defaults.plugins.tooltip.titleFont = { size: 13 };
    Chart.defaults.plugins.tooltip.bodyFont = { size: 12 };
    Chart.defaults.plugins.tooltip.padding = 10;
    Chart.defaults.plugins.tooltip.cornerRadius = 6;
    
    // Colors for Charts
    const colors = {
        primary: '#1e3a8a',
        secondary: '#2563eb',
        accent: '#f97316',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444',
        info: '#06b6d4',
        light: '#f8fafc',
        dark: '#0f172a',
        purple: '#8b5cf6',
        pink: '#ec4899',
        indigo: '#4f46e5',
        teal: '#14b8a6'
    };
    
    // تهيئة الرسم البياني للمبيعات
    if (salesChartElement) {
        const ctx1 = salesChartElement.getContext('2d');
        const salesGradient = ctx1.createLinearGradient(0, 0, 0, 400);
        salesGradient.addColorStop(0, 'rgba(37, 99, 235, 0.2)');
        salesGradient.addColorStop(1, 'rgba(37, 99, 235, 0)');
        
        const salesData = {
            labels: ['نوفمبر', 'ديسمبر', 'يناير', 'فبراير', 'مارس', 'أبريل'],
            datasets: [
                {
                    label: 'المبيعات',
                    data: [3200000, 2800000, 3500000, 4200000, 3800000, 4250000],
                    borderColor: colors.secondary,
                    backgroundColor: salesGradient,
                    tension: 0.3,
                    fill: true,
                    pointBackgroundColor: colors.secondary,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 7
                },
                {
                    label: 'الطلبات',
                    data: [120, 98, 135, 155, 140, 165],
                    borderColor: colors.accent,
                    borderDash: [5, 5],
                    tension: 0.3,
                    pointBackgroundColor: colors.accent,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    yAxisID: 'y1'
                }
            ]
        };
        
        const salesOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    position: 'top',
                    align: 'end'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.dataset.label === 'المبيعات') {
                                return `المبيعات: ${context.parsed.y.toLocaleString()} ر.س`;
                            } else {
                                return `الطلبات: ${context.parsed.y} طلب`;
                            }
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'المبيعات (ر.س)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ر.س';
                        }
                    }
                },
                y1: {
                    beginAtZero: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'عدد الطلبات'
                    },
                    grid: {
                        drawOnChartArea: false
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            }
        };
        
        new Chart(ctx1, {
            type: 'line',
            data: salesData,
            options: salesOptions
        });
    }
    
    // تهيئة الرسم البياني للمخزون
    if (inventoryChartElement) {
        const inventoryData = {
            labels: ['تويوتا', 'هيونداي', 'كيا', 'نيسان', 'هوندا', 'مرسيدس', 'بي إم دبليو', 'أخرى'],
            datasets: [{
                data: [25, 18, 15, 12, 10, 8, 7, 5],
                backgroundColor: [
                    colors.primary,
                    colors.secondary,
                    colors.accent,
                    colors.success,
                    colors.warning,
                    colors.info,
                    colors.purple,
                    colors.teal
                ],
                borderWidth: 0,
                borderRadius: 4
            }]
        };
        
        const inventoryOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        padding: 15
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} سيارة (${percentage}%)`;
                        }
                    }
                }
            }
        };
        
        new Chart(inventoryChartElement, {
            type: 'doughnut',
            data: inventoryData,
            options: inventoryOptions
        });
    }
    
    // تهيئة الرسم البياني للعملاء
    if (customerChartElement) {
        const customerData = {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'عملاء جدد',
                data: [45, 52, 38, 60, 55, 68],
                backgroundColor: colors.primary,
                borderRadius: 6,
                barThickness: 12
            }, {
                label: 'عملاء عائدون',
                data: [30, 42, 33, 45, 47, 50],
                backgroundColor: colors.secondary,
                borderRadius: 6,
                barThickness: 12
            }]
        };
        
        const customerOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'عدد العملاء'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        };
        
        new Chart(customerChartElement, {
            type: 'bar',
            data: customerData,
            options: customerOptions
        });
    } // Closing brace for the 'if (customerChartElement)' block

    // 2. Brands Pie Chart
    const brandsPieChartElement = document.getElementById('brandsPieChart');
    if (brandsPieChartElement) {
        const brandsData = {
            labels: ['تويوتا', 'هوندا', 'نيسان', 'هيونداي', 'كيا', 'مرسيدس'],
            datasets: [
                {
                    data: [25, 20, 15, 15, 15, 10],
                    backgroundColor: [
                        colors.primary,
                        colors.secondary,
                        colors.accent,
                        colors.success,
                        colors.warning,
                        colors.info
                    ],
                    borderWidth: 0,
                    hoverOffset: 10
                }
            ]
        };

        const brandsOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(brandsPieChartElement, {
            type: 'pie',
            data: brandsData,
            options: brandsOptions
        });
    }

    // 3. Top Cars Bar Chart
    const topCarsChartElement = document.getElementById('topCarsChart');
    if (topCarsChartElement) {
        const topCarsData = {
            labels: ['تويوتا كامري', 'هوندا أكورد', 'نيسان التيما', 'هيونداي سوناتا', 'كيا K5'],
            datasets: [
                {
                    label: 'المبيعات',
                    data: [152, 138, 124, 119, 105],
                    backgroundColor: [
                        colors.primary,
                        colors.primary,
                        colors.primary,
                        colors.primary,
                        colors.primary
                    ],
                    hoverBackgroundColor: colors.accent,
                    borderRadius: 6
                }
            ]
        };

        const topCarsOptions = {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'عدد السيارات المباعة'
                    }
                },
                y: {
                    reverse: false
                }
            }
        };

        new Chart(topCarsChartElement, {
            type: 'bar',
            data: topCarsData,
            options: topCarsOptions
        });
    }

    // 4. Categories Doughnut Chart
    const categoriesChartElement = document.getElementById('categoriesChart');
    if (categoriesChartElement) {
        const categoriesData = {
            labels: ['سيدان', 'دفع رباعي', 'كوبيه', 'هاتشباك', 'كروس أوفر'],
            datasets: [
                {
                    data: [40, 30, 10, 10, 10],
                    backgroundColor: [
                        colors.secondary,
                        colors.success,
                        colors.warning,
                        colors.accent,
                        colors.info
                    ],
                    borderWidth: 0,
                    hoverOffset: 8
                }
            ]
        };

        const categoriesOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(categoriesChartElement, {
            type: 'doughnut',
            data: categoriesData,
            options: categoriesOptions
        });
    }

    // 5. Payment Methods Doughnut Chart
    const paymentMethodsChartElement = document.getElementById('paymentMethodsChart');
    if (paymentMethodsChartElement) {
        const paymentMethodsData = {
            labels: ['كاش', 'تمويل بنكي', 'بطاقة ائتمان', 'تحويل بنكي'],
            datasets: [
                {
                    data: [45, 35, 10, 10],
                    backgroundColor: [
                        colors.accent,
                        colors.primary,
                        colors.success,
                        colors.info
                    ],
                    borderWidth: 0,
                    hoverOffset: 8
                }
            ]
        };

        const paymentMethodsOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '50%',
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(paymentMethodsChartElement, {
            type: 'doughnut',
            data: paymentMethodsData,
            options: paymentMethodsOptions
        });
    }

    // 6. New Customers Registration Chart
    const newCustomersChartElement = document.getElementById('newCustomersChart');
    if (newCustomersChartElement) {
        const ctx_new_customers = newCustomersChartElement.getContext('2d');
        const newCustomersGradient = ctx_new_customers.createLinearGradient(0, 0, 0, 250);
        newCustomersGradient.addColorStop(0, 'rgba(99, 102, 241, 0.2)');
        newCustomersGradient.addColorStop(1, 'rgba(99, 102, 241, 0.05)');

        const newCustomersData = {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [
                {
                    label: 'تسجيل العملاء الجدد',
                    data: [45, 86, 74, 52, 48, 68],
                    borderColor: '#6366f1',
                    backgroundColor: newCustomersGradient,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#6366f1',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }
            ]
        };

        const newCustomersOptions = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    },
                    ticks: {
                        display: false
                    }
                },
                x: {
                    reverse: true, // RTL support
                    grid: {
                        drawBorder: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            },
            elements: {
                line: {
                    borderWidth: 3
                }
            }
        };

        new Chart(newCustomersChartElement, {
            type: 'line',
            data: newCustomersData,
            options: newCustomersOptions
        });
    }

    // 7. Financing Status Chart
    const financingStatusChartElement = document.getElementById('financingStatusChart');
    if (financingStatusChartElement) {
        const financingStatusData = {
            labels: ['موافق عليها', 'مرفوضة', 'معلقة'],
            datasets: [
                {
                    data: [65, 15, 20],
                    backgroundColor: [
                        colors.success,
                        colors.danger,
                        colors.warning
                    ],
                    borderWidth: 0,
                    hoverOffset: 10
                }
            ]
        };

        const financingStatusOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(financingStatusChartElement, {
            type: 'doughnut',
            data: financingStatusData,
            options: financingStatusOptions
        });
    }
}
