<?php

namespace Modules\OrderManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * طلب التحقق من صحة بيانات الخطوة الأولى لطلب التمويل
 * 
 * يتحقق من البيانات الشخصية للعميل
 * بناءً على MOD-ORDER-MGMT-FEAT-004 في REQ-FR.md
 */
class FinanceOrderStep1Request extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // البيانات الشخصية الأساسية
            'full_name' => [
                'required',
                'string',
                'min:3',
                'max:100',
                'regex:/^[\p{L}\s\-\'\.]+$/u'
            ],
            
            'national_id' => [
                'required',
                'string',
                'regex:/^[12]\d{9}$/', // رقم هوية سعودي صالح
                'unique:orders,customer_national_id'
            ],
            
            'date_of_birth' => [
                'required',
                'date',
                'before:' . now()->subYears(18)->format('Y-m-d'), // يجب أن يكون فوق 18 سنة
                'after:' . now()->subYears(100)->format('Y-m-d')  // حد أقصى 100 سنة
            ],
            
            'nationality_id' => [
                'required',
                'integer',
                'exists:nationalities,id'
            ],
            
            'phone_number' => [
                'required',
                'string',
                'regex:/^05\d{8}$/', // رقم جوال سعودي صالح
                'unique:users,phone_number,' . auth()->id()
            ],
            
            'email' => [
                'required',
                'email',
                'max:255',
                'unique:users,email,' . auth()->id()
            ],
            
            // العنوان
            'address_details' => [
                'required',
                'string',
                'min:10',
                'max:500'
            ],
            
            'city' => [
                'required',
                'string',
                'min:2',
                'max:50'
            ],
            
            'district' => [
                'required',
                'string',
                'min:2',
                'max:50'
            ],
            
            // معلومات الاتصال الإضافية
            'emergency_contact_name' => [
                'nullable',
                'string',
                'min:3',
                'max:100'
            ],
            
            'emergency_contact_phone' => [
                'nullable',
                'string',
                'regex:/^05\d{8}$/'
            ],
            
            // الموافقات المطلوبة
            'terms_accepted' => [
                'required',
                'accepted'
            ],
            
            'privacy_policy_accepted' => [
                'required',
                'accepted'
            ]
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'full_name.required' => 'الاسم الكامل مطلوب',
            'full_name.min' => 'الاسم الكامل يجب أن يكون 3 أحرف على الأقل',
            'full_name.max' => 'الاسم الكامل يجب ألا يزيد عن 100 حرف',
            'full_name.regex' => 'الاسم الكامل يجب أن يحتوي على أحرف فقط',
            
            'national_id.required' => 'رقم الهوية الوطنية مطلوب',
            'national_id.regex' => 'رقم الهوية الوطنية غير صحيح',
            'national_id.unique' => 'رقم الهوية الوطنية مستخدم مسبقاً',
            
            'date_of_birth.required' => 'تاريخ الميلاد مطلوب',
            'date_of_birth.date' => 'تاريخ الميلاد غير صحيح',
            'date_of_birth.before' => 'يجب أن يكون العمر 18 سنة أو أكثر',
            'date_of_birth.after' => 'تاريخ الميلاد غير صحيح',
            
            'nationality_id.required' => 'الجنسية مطلوبة',
            'nationality_id.exists' => 'الجنسية المختارة غير صحيحة',
            
            'phone_number.required' => 'رقم الجوال مطلوب',
            'phone_number.regex' => 'رقم الجوال غير صحيح',
            'phone_number.unique' => 'رقم الجوال مستخدم مسبقاً',
            
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.unique' => 'البريد الإلكتروني مستخدم مسبقاً',
            
            'address_details.required' => 'تفاصيل العنوان مطلوبة',
            'address_details.min' => 'تفاصيل العنوان يجب أن تكون 10 أحرف على الأقل',
            'address_details.max' => 'تفاصيل العنوان يجب ألا تزيد عن 500 حرف',
            
            'city.required' => 'المدينة مطلوبة',
            'city.min' => 'المدينة يجب أن تكون حرفين على الأقل',
            'city.max' => 'المدينة يجب ألا تزيد عن 50 حرف',
            
            'district.required' => 'الحي مطلوب',
            'district.min' => 'الحي يجب أن يكون حرفين على الأقل',
            'district.max' => 'الحي يجب ألا يزيد عن 50 حرف',
            
            'emergency_contact_name.min' => 'اسم جهة الاتصال الطارئ يجب أن يكون 3 أحرف على الأقل',
            'emergency_contact_name.max' => 'اسم جهة الاتصال الطارئ يجب ألا يزيد عن 100 حرف',
            
            'emergency_contact_phone.regex' => 'رقم جهة الاتصال الطارئ غير صحيح',
            
            'terms_accepted.required' => 'يجب الموافقة على الشروط والأحكام',
            'terms_accepted.accepted' => 'يجب الموافقة على الشروط والأحكام',
            
            'privacy_policy_accepted.required' => 'يجب الموافقة على سياسة الخصوصية',
            'privacy_policy_accepted.accepted' => 'يجب الموافقة على سياسة الخصوصية'
        ];
    }

    /**
     * أسماء الحقول المخصصة للعرض في رسائل الخطأ
     */
    public function attributes(): array
    {
        return [
            'full_name' => 'الاسم الكامل',
            'national_id' => 'رقم الهوية الوطنية',
            'date_of_birth' => 'تاريخ الميلاد',
            'nationality_id' => 'الجنسية',
            'phone_number' => 'رقم الجوال',
            'email' => 'البريد الإلكتروني',
            'address_details' => 'تفاصيل العنوان',
            'city' => 'المدينة',
            'district' => 'الحي',
            'emergency_contact_name' => 'اسم جهة الاتصال الطارئ',
            'emergency_contact_phone' => 'رقم جهة الاتصال الطارئ',
            'terms_accepted' => 'الموافقة على الشروط والأحكام',
            'privacy_policy_accepted' => 'الموافقة على سياسة الخصوصية'
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'full_name' => trim($this->full_name ?? ''),
            'national_id' => trim($this->national_id ?? ''),
            'phone_number' => trim($this->phone_number ?? ''),
            'email' => trim($this->email ?? ''),
            'address_details' => trim($this->address_details ?? ''),
            'city' => trim($this->city ?? ''),
            'district' => trim($this->district ?? ''),
            'emergency_contact_name' => trim($this->emergency_contact_name ?? ''),
            'emergency_contact_phone' => trim($this->emergency_contact_phone ?? ''),
        ]);
    }
}
