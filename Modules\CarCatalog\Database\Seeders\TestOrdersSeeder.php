<?php

namespace Modules\CarCatalog\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * TestOrdersSeeder.
 *
 * هذا الـ Seeder مسؤول عن إنشاء طلبات وهمية لاختبار إحصائيات لوحة البيانات
 * ملاحظة: هذا Seeder مؤقت حتى يتم إنشاء OrderManagement module في المرحلة القادمة
 */
class TestOrdersSeeder extends Seeder
{
    /**
     * تشغيل عملية إضافة البيانات.
     */
    public function run()
    {
        // إنشاء جدول مؤقت للطلبات إذا لم يكن موجوداً
        $this->createTemporaryOrdersTable();

        // إنشاء طلبات وهمية
        $this->createTestOrders();
    }

    /**
     * إنشاء جدول مؤقت للطلبات.
     */
    private function createTemporaryOrdersTable()
    {
        // التحقق من وجود الجدول
        if (!DB::getSchemaBuilder()->hasTable('temp_orders')) {
            DB::statement('
                CREATE TABLE temp_orders (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    customer_name VARCHAR(255) NOT NULL,
                    car_title VARCHAR(255) NOT NULL,
                    total_amount DECIMAL(10,2) NOT NULL,
                    status ENUM("pending", "confirmed", "cancelled", "completed") DEFAULT "pending",
                    order_type ENUM("purchase", "financing") DEFAULT "purchase",
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ');
        }
    }

    /**
     * إنشاء طلبات وهمية.
     */
    private function createTestOrders()
    {
        $customerNames = [
            'عبدالله الأحمد', 'محمد السعيد', 'أحمد العلي', 'علي المحمد', 'عبدالرحمن العتيبي',
            'يوسف القحطاني', 'عمر الغامدي', 'خالد الزهراني', 'فاطمة الشهري', 'عائشة العمري',
            'خديجة الحربي', 'زينب المطيري', 'مريم الدوسري', 'نورا الخالد', 'سارة السلمان',
        ];

        $carTitles = [
            'تويوتا كامري 2024', 'هوندا أكورد 2023', 'نيسان التيما 2024', 'هيونداي سوناتا 2023',
            'كيا أوبتيما 2024', 'مرسيدس بنز C-Class 2024', 'بي إم دبليو 3 Series 2023',
            'أودي A4 2024', 'لكزس ES 2023', 'إنفينيتي Q50 2024',
        ];

        $orders = [];

        // طلبات اليوم (5-10 طلبات)
        for ($i = 0; $i < rand(5, 10); $i++) {
            $orders[] = [
                'customer_name' => $customerNames[array_rand($customerNames)],
                'car_title'     => $carTitles[array_rand($carTitles)],
                'total_amount'  => rand(80000, 300000),
                'status'        => ['pending', 'confirmed'][array_rand(['pending', 'confirmed'])],
                'order_type'    => ['purchase', 'financing'][array_rand(['purchase', 'financing'])],
                'created_at'    => now()->format('Y-m-d H:i:s'),
                'updated_at'    => now()->format('Y-m-d H:i:s'),
            ];
        }

        // طلبات الأسبوع الماضي (10-20 طلب)
        for ($i = 0; $i < rand(10, 20); $i++) {
            $orders[] = [
                'customer_name' => $customerNames[array_rand($customerNames)],
                'car_title'     => $carTitles[array_rand($carTitles)],
                'total_amount'  => rand(80000, 300000),
                'status'        => ['pending', 'confirmed', 'completed', 'cancelled'][array_rand(['pending', 'confirmed', 'completed', 'cancelled'])],
                'order_type'    => ['purchase', 'financing'][array_rand(['purchase', 'financing'])],
                'created_at'    => now()->subDays(rand(1, 7))->format('Y-m-d H:i:s'),
                'updated_at'    => now()->subDays(rand(1, 7))->format('Y-m-d H:i:s'),
            ];
        }

        // طلبات الشهر الماضي (30-50 طلب)
        for ($i = 0; $i < rand(30, 50); $i++) {
            $orders[] = [
                'customer_name' => $customerNames[array_rand($customerNames)],
                'car_title'     => $carTitles[array_rand($carTitles)],
                'total_amount'  => rand(80000, 300000),
                'status'        => ['pending', 'confirmed', 'completed', 'cancelled'][array_rand(['pending', 'confirmed', 'completed', 'cancelled'])],
                'order_type'    => ['purchase', 'financing'][array_rand(['purchase', 'financing'])],
                'created_at'    => now()->subDays(rand(8, 30))->format('Y-m-d H:i:s'),
                'updated_at'    => now()->subDays(rand(8, 30))->format('Y-m-d H:i:s'),
            ];
        }

        // طلبات الأشهر السابقة (50-100 طلب)
        for ($i = 0; $i < rand(50, 100); $i++) {
            $orders[] = [
                'customer_name' => $customerNames[array_rand($customerNames)],
                'car_title'     => $carTitles[array_rand($carTitles)],
                'total_amount'  => rand(80000, 300000),
                'status'        => ['confirmed', 'completed', 'cancelled'][array_rand(['confirmed', 'completed', 'cancelled'])],
                'order_type'    => ['purchase', 'financing'][array_rand(['purchase', 'financing'])],
                'created_at'    => now()->subDays(rand(31, 365))->format('Y-m-d H:i:s'),
                'updated_at'    => now()->subDays(rand(31, 365))->format('Y-m-d H:i:s'),
            ];
        }

        // إدراج الطلبات في قاعدة البيانات
        foreach (array_chunk($orders, 50) as $chunk) {
            DB::table('temp_orders')->insert($chunk);
        }
    }
}
