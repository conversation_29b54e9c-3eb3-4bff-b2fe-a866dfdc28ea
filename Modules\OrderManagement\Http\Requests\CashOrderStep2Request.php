<?php

namespace Modules\OrderManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب التحقق من صحة بيانات الخطوة الثانية لطلب شراء سيارة كاش
 * 
 * يتحقق من بيانات الحجز وطريقة الدفع
 * بناءً على MOD-ORDER-MGMT-FEAT-003 في REQ-FR.md
 */
class CashOrderStep2Request extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return auth()->check() && session()->has('cash_order_car_id');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // تأكيد السيارة المختارة
            'car_id' => [
                'required',
                'integer',
                'exists:cars,id',
                function ($attribute, $value, $fail) {
                    if ($value != session('cash_order_car_id')) {
                        $fail('السيارة المختارة غير صحيحة');
                    }
                }
            ],
            
            // تأكيد السعر
            'car_price' => [
                'required',
                'numeric',
                'min:1000',
                'max:10000000'
            ],
            
            // مبلغ الحجز
            'reservation_amount' => [
                'required',
                'numeric',
                'min:1000',
                'max:100000',
                function ($attribute, $value, $fail) {
                    $carPrice = $this->input('car_price', 0);
                    if ($value > $carPrice * 0.5) {
                        $fail('مبلغ الحجز لا يمكن أن يزيد عن 50% من سعر السيارة');
                    }
                }
            ],
            
            // طريقة دفع الحجز
            'payment_method' => [
                'required',
                'string',
                Rule::in(['online_payment', 'showroom_payment'])
            ],
            
            // بوابة الدفع (مطلوبة فقط للدفع الإلكتروني)
            'payment_gateway' => [
                'required_if:payment_method,online_payment',
                'nullable',
                'string',
                Rule::in(['visa_mastercard', 'mada', 'apple_pay', 'stc_pay'])
            ],
            
            // تفضيلات التسليم
            'delivery_preference' => [
                'required',
                'string',
                Rule::in(['showroom_pickup', 'home_delivery'])
            ],
            
            // عنوان التسليم (مطلوب للتسليم المنزلي)
            'delivery_address' => [
                'required_if:delivery_preference,home_delivery',
                'nullable',
                'string',
                'min:10',
                'max:500'
            ],
            
            'delivery_city' => [
                'required_if:delivery_preference,home_delivery',
                'nullable',
                'string',
                'min:2',
                'max:50'
            ],
            
            'delivery_district' => [
                'required_if:delivery_preference,home_delivery',
                'nullable',
                'string',
                'min:2',
                'max:50'
            ],
            
            // ملاحظات إضافية
            'notes' => [
                'nullable',
                'string',
                'max:1000'
            ],
            
            // الموافقة على شروط الحجز
            'booking_terms_accepted' => [
                'required',
                'accepted'
            ]
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'car_id.required' => 'يجب اختيار السيارة',
            'car_id.exists' => 'السيارة المختارة غير موجودة',
            
            'car_price.required' => 'سعر السيارة مطلوب',
            'car_price.numeric' => 'سعر السيارة يجب أن يكون رقماً',
            'car_price.min' => 'سعر السيارة غير صالح',
            'car_price.max' => 'سعر السيارة غير صالح',
            
            'reservation_amount.required' => 'مبلغ الحجز مطلوب',
            'reservation_amount.numeric' => 'مبلغ الحجز يجب أن يكون رقماً',
            'reservation_amount.min' => 'مبلغ الحجز يجب أن يكون على الأقل 1000 ريال',
            'reservation_amount.max' => 'مبلغ الحجز يجب ألا يزيد عن 100000 ريال',
            
            'payment_method.required' => 'طريقة الدفع مطلوبة',
            'payment_method.in' => 'طريقة الدفع المختارة غير صالحة',
            
            'payment_gateway.required_if' => 'بوابة الدفع مطلوبة للدفع الإلكتروني',
            'payment_gateway.in' => 'بوابة الدفع المختارة غير صالحة',
            
            'delivery_preference.required' => 'تفضيل التسليم مطلوب',
            'delivery_preference.in' => 'تفضيل التسليم المختار غير صالح',
            
            'delivery_address.required_if' => 'عنوان التسليم مطلوب للتسليم المنزلي',
            'delivery_address.min' => 'عنوان التسليم يجب أن يكون على الأقل 10 أحرف',
            'delivery_address.max' => 'عنوان التسليم يجب ألا يزيد عن 500 حرف',
            
            'delivery_city.required_if' => 'مدينة التسليم مطلوبة للتسليم المنزلي',
            'delivery_district.required_if' => 'حي التسليم مطلوب للتسليم المنزلي',
            
            'notes.max' => 'الملاحظات يجب ألا تزيد عن 1000 حرف',
            
            'booking_terms_accepted.required' => 'يجب الموافقة على شروط الحجز',
            'booking_terms_accepted.accepted' => 'يجب الموافقة على شروط الحجز'
        ];
    }

    /**
     * أسماء الحقول المخصصة للعرض في رسائل الخطأ
     */
    public function attributes(): array
    {
        return [
            'car_id' => 'السيارة',
            'car_price' => 'سعر السيارة',
            'reservation_amount' => 'مبلغ الحجز',
            'payment_method' => 'طريقة الدفع',
            'payment_gateway' => 'بوابة الدفع',
            'delivery_preference' => 'تفضيل التسليم',
            'delivery_address' => 'عنوان التسليم',
            'delivery_city' => 'مدينة التسليم',
            'delivery_district' => 'حي التسليم',
            'notes' => 'الملاحظات',
            'booking_terms_accepted' => 'الموافقة على شروط الحجز'
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'car_price' => (float) str_replace(',', '', $this->car_price ?? 0),
            'reservation_amount' => (float) str_replace(',', '', $this->reservation_amount ?? 0),
            'notes' => trim($this->notes ?? ''),
            'delivery_address' => trim($this->delivery_address ?? ''),
            'delivery_city' => trim($this->delivery_city ?? ''),
            'delivery_district' => trim($this->delivery_district ?? ''),
        ]);
    }
}
