**START OF `PPP-FR.md`**
---

## PPP-FR.md - خطة المراحل التنفيذية للمشروع المتكامل (نسخة نهائية معتمدة ذاتيًا)

**تاريخ الإنشاء:** (تاريخ اليوم)
**إصدار المستند:** 1.0 (نهائي معتمد ذاتيًا)

### مقدمة

**معرف القسم:** `PPP-INTRO-001`

تهدف هذه الخطة التنفيذية إلى تحديد المراحل والخطوات الرئيسية لتطوير منصة معرض السيارات الإلكترونية المتكاملة. تستند هذه الخطة إلى التحليل الشامل للمتطلبات والمواصفات المحددة في وثائق المرحلة 01 المعتمدة، مع التركيز على تحقيق **[المخرجات النهائية الأربعة المتوقعة للمشروع بالكامل]** بكفاءة وجودة عالية. تم تصميم هذه الخطة لتكون موجهة نحو الموديولات وقابلة للتحليل الآلي من قبل أنظمة LLM أخرى لتوجيه مراحل التنفيذ التفصيلية.

**المخرجات النهائية المتوقعة للمشروع بالكامل (المرجع الأساسي والنهائي):**
1.  **موقع إلكتروني فعال:** مرفوع على خادم عبر الإنترنت، جاهز لاستقبال الزوار بدون مشاكل.
2.  **لوحة تحكم احترافية (مخصصة من الصفر باستخدام أصول Dash HTML/CSS/JS):** (للإدارة و/أو العملاء) جاهزة للاستخدام، توفر تجربة مستخدم ممتازة، وتدعم اللغة العربية (RTL) بشكل كامل.
3.  **واجهة موقع (Frontend) جذابة وفعالة:** (باستخدام Blade إذا كانت مختلفة عن لوحة التحكم) جاهزة لاستقبال الزوار وتوفير تجربة مستخدم سلسة.
4.  **تطبيق موبايل (Flutter) كامل (إذا كان مطلوبًا):** جاهز للرفع على متاجر التطبيقات ويعمل بشكل صحيح ومتكامل.

### المكدس التقني (Technology Stack)

**(معرف القسم: `PPP-TECHSTACK-001`)**

*   **الواجهة الخلفية (Backend):** Laravel 10.
*   **تنظيم الكود في Backend:** حزمة `nwidart/laravel-modules`.
*   **قاعدة البيانات:** MySQL (أو PostgreSQL حسب التفضيل النهائي، مع مراعاة التوافق مع Eloquent).
*   **لوحات التحكم (إدارة وعملاء):** واجهات Laravel Blade مخصصة، مبنية على أصول Dash الثابتة (HTML/CSS/JS من مجلد `Dash/` في جذر المشروع)، مع تخصيصات CSS/JS إضافية (مثل `dash_custom.css` و `dash_app.js`) وتجميع الأصول باستخدام Laravel Vite (أو Mix).
*   **واجهة الموقع العامة (Public Frontend):** Laravel Blade مع CSS/JS مخصصين.
*   **تطبيق الموبايل (إذا كان مطلوبًا):** Flutter.
*   **خادم الويب:** Nginx أو Apache.
*   **إدارة الإصدارات:** Git (مع مستودع مركزي مثل GitHub/GitLab).
*   **الاختبار:** PHPUnit (Backend)، Laravel Dusk أو Playwright/Cypress (Dash UI)، Flutter Testing Framework.

### تقسيم المراحل التنفيذية (Phased Execution Breakdown)

**(معرف القسم: `PPP-PHASES-001`)**

سيتم تنفيذ المشروع عبر المراحل التالية، مع مراعاة بناء وتخصيص لوحة تحكم Dash المخصصة ضمن هذه المراحل.

#### **المرحلة 1: بناء الأساس (Backend Foundation) وتجهيز بيئة Dash المخصصة**

*   **معرف المرحلة:** `PH-01`
*   **اسم المرحلة:** الأساسيات وتجهيز بيئة Dash.
*   **الهدف الرئيسي للمرحلة:** إعداد البنية التحتية الأساسية للـ Backend، تهيئة مشروع Laravel، تثبيت الحزم الأساسية، إنشاء الموديولات الأولية، وتجهيز الهيكل الأساسي لدمج لوحة تحكم Dash المخصصة كواجهات Blade.
*   **المدة التقديرية:** (تُحدد لاحقًا بناءً على الموارد).
*   **الموديولات الرئيسية التي يتم التركيز عليها:**
    *   `MOD-CORE` (النواة)
    *   `MOD-USER-MGMT` (إدارة المستخدمين - الأساسيات)
    *   إعداد هيكل موديول `MOD-DASHBOARD` (مبدئيًا).
*   **المخرجات الرئيسية التنفيذية:**
    1.  `PH-01-DEL-001`: تثبيت وتكوين مشروع Laravel 10 جديد.
    2.  `PH-01-DEL-002`: تثبيت وتكوين حزمة `nwidart/laravel-modules`.
    3.  `PH-01-DEL-003`: إنشاء موديول `Core` (`STRU-MOD-CORE-001`) مع الأصناف الأساسية (`MOD-CORE-FEAT-001`) والدوال المساعدة (`MOD-CORE-FEAT-002`).
    4.  `PH-01-DEL-004`: إنشاء موديول `UserManagement` (`STRU-MOD-USERMANAGEMENT-001`) مع مخطط قاعدة بيانات المستخدمين (`DB-TBL-001`, `DB-TBL-020`)، ونموذج `User`.
    5.  `PH-01-DEL-005`: تنفيذ وظائف المصادقة الأساسية للموظفين/المديرين للوصول إلى لوحة التحكم (تسجيل الدخول، استعادة كلمة المرور عبر الويب - `MOD-USER-MGMT-FEAT-002`, `MOD-USER-MGMT-FEAT-003`).
    6.  `PH-01-DEL-006`: تثبيت وتكوين حزمة `spatie/laravel-permission` وإنشاء الأدوار الأولية (مدير نظام، موظف).
    7.  `PH-01-DEL-007`: إعداد هيكل Blade الأساسي للوحة تحكم الإدارة Dash (`admin_layout.blade.php` ضمن `STRU-LARAVEL-VIEWS-ADMIN-001`) بناءً على `Dash/index.html`. يجب أن يتضمن هذا الهيكل ربط ملفات `Dash/style.css` و `Dash/script.js` (أو الأصول المجمعة منها). (يخدم `MOD-DASHBOARD-FEAT-001` جزئيًا).
    8.  `PH-01-DEL-008`: إنشاء مكونات Blade أولية للشريط العلوي (`_topbar.blade.php`) والقائمة الجانبية (`_sidebar.blade.php`) فارغة أو بعناصر ثابتة مؤقتة، مدمجة في `admin_layout.blade.php`. (يخدم `MOD-DASHBOARD-FEAT-001` جزئيًا).
    9.  `PH-01-DEL-009`: صفحة تسجيل دخول أساسية للوحة تحكم الإدارة (`DASH-ADMIN-LOGIN-001` - واجهة Blade بسيطة).
    10. `PH-01-DEL-010`: إعداد نظام إدارة الإعدادات (`MOD-CORE-FEAT-003` - مخطط قاعدة البيانات `DB-TBL-CORE-001` والخدمة الأساسية، بدون واجهة إدارة بعد).
    11. `PH-01-DEL-011`: إنشاء صفحة `dashboard.blade.php` أولية (ضمن `resources/views/admin/`) تعرض محتوى ثابتًا بسيطًا أو رسالة "قيد الإنشاء"، تستخدم `admin_layout.blade.php`، ويمكن الوصول إليها بعد تسجيل دخول المدير. الهدف هو اختبار عمل التخطيط الأساسي للوحة التحكم.
*   **معايير القبول:**
    *   مشروع Laravel يعمل بشكل صحيح مع هيكل الموديولات.
    *   يمكن للمدير تسجيل الدخول إلى صفحة فارغة مبدئية للوحة التحكم (تستخدم `admin_layout.blade.php` مع عناصر Dash الثابتة).
    *   وجود موديولي `Core` و `UserManagement` مع الوظائف الأساسية المذكورة.

#### **المرحلة 2: بناء لوحة تحكم الإدارة Dash المخصصة (الأساسية) ووظائف الكتالوج**

*   **معرف المرحلة:** `PH-02`
*   **اسم المرحلة:** لوحة تحكم الإدارة (Dash) - الأساسيات وإدارة الكتالوج.
*   **الهدف الرئيسي للمرحلة:** تطوير الوظائف الأساسية للوحة تحكم الإدارة باستخدام أصول Dash المخصصة، مع التركيز على إدارة كتالوج السيارات، والبيانات الوصفية المرتبطة به.
*   **المدة التقديرية:** (تُحدد لاحقًا).
*   **الموديولات الرئيسية التي يتم التركيز عليها:**
    *   `MOD-DASHBOARD` (بناء وتخصيص الواجهات)
    *   `MOD-CAR-CATALOG` (إدارة السيارات وبياناتها)
    *   `MOD-USER-MGMT` (إدارة الموظفين والأدوار بشكل كامل)
    *   `MOD-NOTIFICATION` (الأساسيات)
*   **المخرجات الرئيسية التنفيذية:**
    1.  `PH-02-DEL-001`: تطوير واجهة لوحة البيانات الرئيسية للإدارة (`DASH-ADMIN-HOME-001`) مع عرض بطاقات إحصائية، وتهيئة الرسوم البيانية ببيانات وهمية من `Dash/script.js` أو `dash_app.js` كجزء من بناء الواجهة. (يخدم `UIUX-DASH-CUSTOMIZATION-001`).
    2.  `PH-02-DEL-002`: تطوير القائمة الجانبية الديناميكية (`_sidebar.blade.php`) والشريط العلوي (`_topbar.blade.php`) للوحة تحكم الإدارة Dash، مع عرض عناصر القائمة بناءً على أدوار وصلاحيات المستخدم (`spatie/laravel-permission`)، وعرض اسم المستخدم والإشعارات (مبدئيًا). (يخدم `MOD-DASHBOARD-FEAT-001`، `MOD-NOTIFICATION-FEAT-002` - جزء الواجهة).
    3.  `PH-02-DEL-003`: إنشاء موديول `CarCatalog` (`STRU-MOD-CARCATALOG-001`) مع مخططات قاعدة البيانات اللازمة (ماركات `DB-TBL-002`, موديلات `DB-TBL-003`, سنوات `DB-TBL-004`, ألوان `DB-TBL-005`, أنواع ناقل حركة `DB-TBL-006`, أنواع وقود `DB-TBL-007`, أنواع هياكل `DB-TBL-021`, ميزات `DB-TBL-008`, فئات ميزات `DB-TBL-022`, جدول السيارات `DB-TBL-009`, جدول ربط الميزات `DB-TBL-010`).
    4.  `PH-02-DEL-004`: تطوير واجهات CRUD كاملة (نماذج وجداول عرض ضمن لوحة تحكم Dash) لإدارة البيانات الوصفية للسيارات (ماركات `DASH-BRANDS-MGMT-001`, موديلات, ألوان, سنوات, أنواع نواقل, أنواع وقود, ميزات, فئات ميزات) كما هو محدد في `UIUX-FR.md` (القسم 4.1.5) ومتطلبات `MOD-CAR-CATALOG-FEAT-008` إلى `FEAT-CAR-014`.
    5.  `PH-02-DEL-005`: تطوير واجهة إضافة/تعديل السيارة (`DASH-CAR-CRUD-001`, `MOD-CAR-CATALOG-FEAT-018`) باستخدام Stepper المكون من 5 خطوات (المبني على `bs-stepper` من `dashboard.html`) مع ربط البيانات بالقوائم المنسدلة من الخطوة السابقة، والتحقق من الصحة، ورفع الصور (`spatie/laravel-medialibrary`).
    6.  `PH-02-DEL-006`: تطوير واجهة قائمة السيارات (`DASH-CAR-LIST-001`) في لوحة التحكم Dash مع البحث الأساسي، الفلترة البسيطة، والترقيم.
    7.  `PH-02-DEL-007`: تطوير إدارة الموظفين والأدوار والصلاحيات بشكل كامل (`DASH-EMPLOYEES-LIST-001`, `DASH-ROLES-PERMISSIONS-MGMT-001` - `MOD-USER-MGMT-FEAT-010`).
    8.  `PH-02-DEL-008`: إنشاء موديول `Notification` (`STRU-MOD-NOTIFICATION-001`) وتنفيذ إرسال الإشعارات الأساسية (ترحيب، تأكيد) عبر البريد وقاعدة البيانات (`MOD-NOTIFICATION-FEAT-001`).
    9.  `PH-02-DEL-009`: تطوير واجهة إعدادات النظام الأساسية (`DASH-SYSTEM-SETTINGS-001` - جزء الإعدادات العامة و SEO) لإدارة `MOD-CORE-FEAT-003`.
    10. `PH-02-DEL-010`: تكييف `Dash/script.js` أو إنشاء `dash_app.js` (كما هو موضح في `UIUX-DASH-CUSTOMIZATION-001`) لاستبدال البيانات الوهمية في الرسوم البيانية في لوحة البيانات (`DASH-ADMIN-HOME-001`) ببيانات ديناميكية حقيقية من `MOD-DASHBOARD-FEAT-002` و `TS-FR.md`.
*   **معايير القبول:**
    *   يمكن للمدير إضافة وتعديل وحذف جميع البيانات الوصفية للسيارات.
    *   يمكن للمدير إضافة وتعديل سيارة جديدة بكامل تفاصيلها وصورها عبر Stepper.
    *   تعرض لوحة البيانات الرئيسية إحصائيات ورسوم بيانية ديناميكية أساسية.
    *   القائمة الجانبية ديناميكية وتعمل بشكل صحيح.
    *   يمكن إدارة الموظفين والأدوار.

#### **المرحلة 3: بناء الواجهة الخارجية للزوار (Public Visitor Interface - Blade) والوظائف الأساسية للعملاء**

*   **معرف المرحلة:** `PH-03`
*   **اسم المرحلة:** واجهة الموقع العامة ووظائف العملاء الأساسية.
*   **الهدف الرئيسي للمرحلة:** تطوير واجهة الموقع العامة (باستخدام Blade) التي تمكن الزوار من تصفح السيارات وتقديم الطلبات، بالإضافة إلى وظائف تسجيل وإنشاء حساب العملاء.
*   **المدة التقديرية:** (تُحدد لاحقًا).
*   **الموديولات الرئيسية التي يتم التركيز عليها:**
    *   `MOD-CAR-CATALOG` (عرض السيارات للعامة)
    *   `MOD-USER-MGMT` (تسجيل العملاء ومصادقتهم)
    *   `MOD-ORDER-MGMT` (عمليات الشراء الأساسية)
    *   `MOD-CMS` (الصفحات الثابتة ومحتوى الصفحة الرئيسية)
    *   `MOD-PROMO-MGMT` (عرض العروض)
    *   `MOD-SERVICE-MGMT` (عرض الخدمات)
    *   `MOD-CORP-SALES` (صفحة مبيعات الشركات)
*   **المخرجات الرئيسية التنفيذية:**
    1.  `PH-03-DEL-001`: تطوير التخطيط العام للموقع (`SITE-LAYOUT-MAIN-001`) والرأس والتذييل.
    2.  `PH-03-DEL-002`: تطوير الصفحة الرئيسية للموقع (`SITE-HOME-001`) مع عرض بنرات ديناميكية (`MOD-CMS-FEAT-002`)، سيارات مميزة، وأحدث العروض.
    3.  `PH-03-DEL-003`: تطوير صفحة قائمة السيارات (`SITE-CAR-LIST-001`) مع الفلاتر المتقدمة (`MOD-CAR-CATALOG-FEAT-001`, `MOD-CAR-CATALOG-FEAT-002`).
    4.  `PH-03-DEL-004`: تطوير صفحة تفاصيل السيارة (`SITE-CAR-DETAIL-001`) مع معرض الصور، المواصفات، الميزات، وزر "اطلبها الآن" (`MOD-CAR-CATALOG-FEAT-003`).
    5.  `PH-03-DEL-005`: تطوير وظائف تسجيل حساب جديد للعملاء (`SITE-AUTH-REGISTER-001`, `MOD-USER-MGMT-FEAT-001`) مع التحقق من OTP (`SITE-AUTH-VERIFY-OTP-001`, `MOD-USER-MGMT-FEAT-001B`).
    6.  `PH-03-DEL-006`: تطوير وظائف تسجيل الدخول واستعادة كلمة المرور للعملاء (`SITE-AUTH-LOGIN-001`, `SITE-AUTH-FORGOT-PASSWORD-001`, `MOD-USER-MGMT-FEAT-002`, `MOD-USER-MGMT-FEAT-003`).
    7.  `PH-03-DEL-007`: إنشاء موديول `OrderManagement` (`STRU-MOD-ORDERMANAGEMENT-001`) مع مخططات قاعدة البيانات (`DB-TBL-012`).
    8.  `PH-03-DEL-008`: تطوير عملية شراء السيارة كاش (`SITE-BUY-CASH-STEPX-001`, `MOD-ORDER-MGMT-FEAT-001`, `MOD-ORDER-MGMT-FEAT-003`) بما في ذلك رفع المستندات (`MOD-ORDER-MGMT-FEAT-006`) وتكامل دفع مبلغ الحجز أونلاين (`MOD-ORDER-MGMT-FEAT-005`) (مع إرسال إشعارات تأكيد/إخطار ذات صلة عبر `MOD-NOTIFICATION`).
    9.  `PH-03-DEL-009`: تطوير عملية طلب التمويل (`SITE-BUY-FINANCE-STEPX-001`, `MOD-ORDER-MGMT-FEAT-004`) (مع إرسال إشعارات تأكيد/إخطار ذات صلة عبر `MOD-NOTIFICATION`).
    10. `PH-03-DEL-010`: تطوير صفحة "كيف أشتريها؟" (`SITE-HOW-TO-BUY-001`, `MOD-ORDER-MGMT-FEAT-002`).
    11. `PH-03-DEL-011`: تطوير الصفحات الثابتة الأساسية (من نحن، سياسة، شروط، تواصل معنا) من `MOD-CMS` (`SITE-STATIC-*-001`).
    12. `PH-03-DEL-012`: تطوير صفحات عرض الخدمات وطلبها (`SITE-SERVICES-LIST-001`, `MOD-SERVICE-MGMT-FEAT-001`, `FEAT-SERVICE-002`).
    13. `PH-03-DEL-013`: تطوير صفحات عرض العروض وتفاصيلها (`SITE-PROMOTIONS-LIST-001`, `SITE-PROMOTION-DETAIL-001`, `MOD-PROMO-MGMT-FEAT-001`, `FEAT-PROMO-002`).
    14. `PH-03-DEL-014`: تطوير صفحة مبيعات الشركات ونموذج الطلب (`SITE-CORPORATE-SALES-001`, `MOD-CORP-SALES-FEAT-001`, `FEAT-CORP-002`).
    15. `PH-03-DEL-015`: تطوير عملية "اطلب سيارتك" متعددة الخطوات (`SITE-REQUEST-CAR-STEPX-001`, `FEAT-REQCAR-001`) (مع إرسال إشعارات تأكيد/إخطار ذات صلة عبر `MOD-NOTIFICATION`).
    16. `PH-03-DEL-016`: تطوير وظائف المفضلة والمقارنة والمشاركة للسيارات (`MOD-CAR-CATALOG-FEAT-005`, `FEAT-CAR-006`, `FEAT-CAR-007`).
*   **معايير القبول:**
    *   يمكن للزوار تصفح السيارات، استخدام الفلاتر، وعرض تفاصيل السيارات.
    *   يمكن للعملاء تسجيل حسابات جديدة، تسجيل الدخول، وتقديم طلبات شراء (كاش وتمويل) وطلبات سيارات مخصصة.
    *   الصفحات الثابتة والعروض والخدمات ومبيعات الشركات تعمل وتعرض المحتوى بشكل صحيح.
    *   الموقع العام متجاوب وفعال.

#### **المرحلة 4: بناء لوحة تحكم العملاء Dash المخصصة وإكمال لوحة تحكم الإدارة**

*   **معرف المرحلة:** `PH-04`
*   **اسم المرحلة:** لوحة تحكم العملاء (Dash) واستكمال لوحة الإدارة.
*   **الهدف الرئيسي للمرحلة:** تطوير لوحة تحكم العملاء باستخدام أصول Dash المخصصة، وإكمال جميع الوظائف المتبقية في لوحة تحكم الإدارة (إدارة الطلبات، العملاء، المحتوى، الإعدادات المتقدمة، التقارير).
*   **المدة التقديرية:** (تُحدد لاحقًا).
*   **الموديولات الرئيسية التي يتم التركيز عليها:**
    *   `MOD-USER-MGMT` (لوحة تحكم العميل، إدارة العملاء)
    *   `MOD-ORDER-MGMT` (إدارة الطلبات في Dash)
    *   `MOD-DASHBOARD` (استكمال واجهات الإدارة)
    *   `MOD-CMS` (إدارة المحتوى بالكامل)
    *   `MOD-SERVICE-MGMT` (إدارة الخدمات والطلبات)
    *   `MOD-PROMO-MGMT` (إدارة العروض)
    *   `MOD-CORP-SALES` (إدارة طلبات الشركات)
*   **المخرجات الرئيسية التنفيذية:**
    1.  `PH-04-DEL-001`: تطوير هيكل لوحة تحكم العميل Dash (`customer_layout.blade.php`, `_customer_sidebar.blade.php`). سيتم بناء هذه الواجهات بنفس منهجية تكييف أصول Dash المستخدمة في لوحة تحكم الإدارة، مع تطبيق التخطيطات والمكونات المحددة للعميل في `UIUX-FR.md`.
    2.  `PH-04-DEL-002`: تطوير صفحات لوحة تحكم العميل (`DASH-CUSTOMER-HOME-001` إلى `DASH-CUSTOMER-PROFILE-001` كما هو مفصل في `UIUX-FR.md`، القسم 4.2) وتشمل: ملخص الحساب، طلباتي وتفاصيلها، المفضلة، رشح عميل، المعاملات المالية، الإشعارات، والملف الشخصي. (يخدم `MOD-USER-MGMT-FEAT-004` إلى `FEAT-008`). سيتم بناء هذه الواجهات بنفس منهجية تكييف أصول Dash.
    3.  `PH-04-DEL-003`: تطوير واجهة إدارة الطلبات (كاش وتمويل) في لوحة تحكم الإدارة Dash (`DASH-ORDERS-LIST-001`, `DASH-ORDER-DETAIL-001`) مع إمكانية تحديث الحالة، إضافة ملاحظات، وعرض المستندات. (يخدم `MOD-ORDER-MGMT-FEAT-007`, `FEAT-ORDER-008`).
    4.  `PH-04-DEL-004`: تطوير واجهة إدارة العملاء في لوحة تحكم الإدارة Dash (`DASH-CUSTOMERS-LIST-001`, `DASH-CUSTOMER-DETAIL-001`).
    5.  `PH-04-DEL-005`: تطوير واجهات إدارة المحتوى بالكامل في لوحة التحكم Dash (`DASH-CMS-PAGES-LIST-001`, `DASH-CMS-BANNERS-LIST-001`, `MOD-CMS-FEAT-001`, `MOD-CMS-FEAT-002`).
    6.  `PH-04-DEL-006`: تطوير واجهات إدارة الخدمات وفئاتها وطلباتها في لوحة التحكم Dash. (يخدم `FEAT-SERVICE-003` إلى `FEAT-SERVICE-005`).
    7.  `PH-04-DEL-007`: تطوير واجهات إدارة العروض الترويجية في لوحة التحكم Dash. (يخدم `FEAT-PROMO-003`).
    8.  `PH-04-DEL-008`: تطوير واجهات إدارة طلبات الشركات في لوحة التحكم Dash. (يخدم `FEAT-CORP-003`).
    9.  `PH-04-DEL-009`: استكمال واجهة إعدادات النظام في لوحة التحكم Dash (`DASH-SYSTEM-SETTINGS-001` - إعدادات الدفع، الإشعارات، الحدود). (يخدم `FEAT-ADMIN-003`).
    10. `PH-04-DEL-010`: تطوير واجهة إدارة التقارير الأساسية في لوحة التحكم Dash (إذا كانت جزءًا من `FEAT-ADMIN-005` ولم تغطها لوحة البيانات).
    11. `PH-04-DEL-011`: تطوير واجهة النسخ الاحتياطي والاستعادة (إذا كانت مطلوبة كواجهة مستخدم - `FEAT-ADMIN-004`).
*   **معايير القبول:**
    *   يمكن للعميل الوصول إلى لوحة تحكمه وإدارة ملفه الشخصي وطلباته ومفضلته.
    *   يمكن للمدير إدارة جميع جوانب الطلبات، العملاء، المحتوى، والخدمات والعروض من لوحة التحكم.
    *   جميع إعدادات النظام قابلة للإدارة.
    *   لوحة تحكم الإدارة والعملاء تعمل بشكل كامل وتدعم اللغة العربية (RTL) بشكل كامل وتوفر تجربة مستخدم ممتازة كما هو محدد في `UIUX-FR.md`.

#### **المرحلة 5: بناء تطبيق الموبايل (Mobile Application - Flutter) (إذا كان مطلوبًا)**

*   **معرف المرحلة:** `PH-05`
*   **اسم المرحلة:** تطوير تطبيق Flutter.
*   **الهدف الرئيسي للمرحلة:** تطوير ونشر تطبيق موبايل Flutter متكامل للعملاء يعكس الوظائف الأساسية للموقع.
*   **المدة التقديرية:** (تُحدد لاحقًا).
*   **الموديولات الرئيسية التي يتم التركيز عليها (Backend):** `MOD-API` (توفير جميع نقاط النهاية المطلوبة للتطبيق).
*   **المخرجات الرئيسية التنفيذية (Frontend - Flutter):**
    1.  `PH-05-DEL-001`: إعداد مشروع Flutter مع الهيكل المقترح (`STRU-FLUTTER-APP-001`).
    2.  `PH-05-DEL-002`: تطوير شاشات المصادقة (`FLUTTER-AUTH-LOGIN-001` إلى `FLUTTER-AUTH-FORGOT-PASSWORD-001`).
    3.  `PH-05-DEL-003`: تطوير الشاشة الرئيسية للتطبيق (`FLUTTER-HOME-001`).
    4.  `PH-05-DEL-004`: تطوير شاشات كتالوج السيارات (قائمة `FLUTTER-CAR-LIST-001`, فلاتر `FLUTTER-CAR-FILTER-001`, تفاصيل `FLUTTER-CAR-DETAIL-001`).
    5.  `PH-05-DEL-005`: تطوير شاشات عملية "اطلب سيارتك" (`FLUTTER-REQUEST-CAR-STEPX-001`).
    6.  `PH-05-DEL-006`: تطوير شاشات عملية الشراء (كاش وتمويل) (`FLUTTER-BUY-CASH-STEPX-001`, `FLUTTER-BUY-FINANCE-STEPX-001`).
    7.  `PH-05-DEL-007`: تطوير شاشات لوحة تحكم العميل المصغرة (`FLUTTER-CUSTOMER-ORDERS-001` إلى `FLUTTER-NOTIFICATIONS-LIST-001`).
    8.  `PH-05-DEL-008`: تطوير شاشات عرض الخدمات وطلبها (`FLUTTER-SERVICES-LIST-001`, `FLUTTER-SERVICE-REQUEST-FORM-001`).
    9.  `PH-05-DEL-009`: تطوير شاشات عرض العروض وتفاصيلها (`FLUTTER-PROMOTIONS-LIST-001`, `FLUTTER-PROMOTION-DETAIL-001`).
    10. `PH-05-DEL-010`: تطوير شاشات المحتوى العام (تواصل معنا، الصفحات الثابتة - `FLUTTER-CONTACT-US-001`, `FLUTTER-CMS-PAGE-001`).
    11. `PH-05-DEL-011`: اختبار التطبيق على منصتي iOS و Android.
    12. `PH-05-DEL-012`: تجهيز التطبيق ورفعه على متاجر التطبيقات (Google Play, Apple App Store).
*   **معايير القبول:**
    *   تطبيق Flutter يعمل بشكل صحيح ومتكامل مع الـ Backend API.
    *   الوظائف الأساسية (تصفح، شراء، إدارة حساب) متاحة وتعمل بسلاسة.
    *   التطبيق جاهز للنشر على المتاجر.

### التبعيات بين المراحل

**(معرف القسم: `PPP-DEPENDENCIES-001`)**

*   `PH-02` يعتمد على اكتمال `PH-01`.
*   `PH-03` يعتمد على اكتمال `PH-01` وأجزاء من `MOD-CAR-CATALOG` و `MOD-USER-MGMT` من `PH-02` (مثل وجود سيارات لعرضها ونظام مصادقة).
*   `PH-04` يعتمد على اكتمال `PH-03` (لتوفير بيانات الطلبات والعملاء لإدارتها) وأجزاء من `PH-02` (مثل وجود هياكل Dash الأساسية).
*   `PH-05` يعتمد على اكتمال جميع نقاط نهاية `MOD-API` المطلوبة، والتي بدورها تعتمد على اكتمال وظائف الموديولات الخلفية المقابلة لها من المراحل `PH-01` إلى `PH-04`.
*   تطوير نقاط نهاية `MOD-API` (التي تخدمها الموديولات المطورة في المراحل 1-4) يمكن أن يبدأ بشكل متوازٍ أو في نهاية كل مرحلة خلفية ذات صلة، ليكون جاهزًا للمرحلة 5.

---
**END OF `PPP-FR.md`**
