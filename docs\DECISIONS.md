# Decision Log

This document records significant architectural and design decisions made during the project.

## General Decisions
- **DECISION-PROJ-NAME-001:** Project name is `car_showroom_platform`. (Date: YYYY-MM-DD, Task: PH01-TASK-001)
- **DECISION-DB-NAME-001:** Development database name is `motorline_10_db`. (Date: YYYY-MM-DD, Task: PH01-TASK-001)

## Module Structure
- **DECISION-MODULES-MODEL-PATH-001:** Module models will reside in a `Models` directory instead of `Entities`. (Date: 2024-07-10, Task: PH01-TASK-002)

## Core Module
- **DECISION-CORE-BASEREPOSITORY-DEFERRED-001:** Implementation of `BaseRepository` is deferred for now. Focus on `BaseController`, `BaseModel`, `BaseRequest`. (Date: 2024-07-27, Task: PH01-TASK-004)

## User Management
- **DECISION-INITIAL-ROLES-PERMS-001:** Initial roles: 'Super Admin', 'Employee', 'Customer'. Initial permissions: 'access_admin_dashboard', 'access_customer_dashboard', 'view_cars_admin', 'manage_cars_admin'. (Date: 2025-05-23, Task: PH01-TASK-008)
- **DECISION-DEFAULT-ADMIN-CREDENTIALS-001:** Default Super Admin: `<EMAIL>` / `password`. (IMPORTANT: Must be changed post-setup). (Date: 2025-05-23, Task: PH01-TASK-008A)
- **DECISION-SPATIE-MIDDLEWARE-ALIASES-001:** Using standard aliases for Spatie Permission middleware: 'role', 'permission', 'role_or_permission' as recommended in the official documentation. (Date: 2025-05-23, Task: PH01-TASK-018)


## Dash Panel
- **DECISION-DASH-ASSET-TEMP-PATH-001:** Dash assets (CSS, JS, images, fonts) will be temporarily copied to `public/vendor/dash/` for initial layout setup. To be replaced by Vite/Mix processing later. (Date: 2024-07-28, Task: PH01-TASK-009)
- **DECISION-DASH-LAYOUT-LOCATION-001:** The main admin dashboard layout (`admin_layout.blade.php`) and its associated partials will be located within the `Dashboard` module (`Modules/Dashboard/Resources/views/layouts/`). (Date: 2024-07-28, Task: PH01-TASK-009)
- **DECISION-DASH-LOGOUT-FORM-001:** The logout button in the admin dashboard topbar needs to be updated to use a form with POST method to properly call the logout route. This will be implemented in a future task. (Date: 2024-07-30, Task: PH01-TASK-017)

## Car Catalog Module
- **DECISION-BRAND-LOGO-COLLECTION-001:** Using collection name `brand_logos` for brand logos with `thumb` conversion at 100x100px. (Date: 2025-05-25, Task: PH02-TASK-005)
- **DECISION-BRAND-DELETE-RESTRICTION-001:** Brands with associated car models cannot be deleted, only deactivated. (Date: 2025-05-25, Task: PH02-TASK-005)
- **DECISION-CAR-IMAGES-COLLECTIONS-001:** Using collection name `car_images` for car gallery images and `car_thumbnail` for main car image. (Date: 2025-05-25, Task: PH02-TASK-014)
- **DECISION-CAR-AJAX-MODELS-001:** Using AJAX endpoint to fetch car models dynamically based on selected brand to improve user experience in Stepper interface. (Date: 2025-05-25, Task: PH02-TASK-014)
- **DECISION-CAR-STEPPER-STEPS-001:** Car creation/editing uses 5-step Stepper: 1) Basic Info, 2) Technical Specs, 3) Features, 4) Images & Video, 5) Price & Status. (Date: 2025-05-25, Task: PH02-TASK-014)

## Car Image Upload Debugging
- **DECISION-CAR-UPLOAD-DEBUG-FLOW-001:** After comprehensive debugging, confirmed that car image upload form configuration, validation rules, and request flow are working correctly. The issue lies in `spatie/laravel-medialibrary` processing or database storage, not in the form/validation layer. (Date: 2024-12-19, Task: PH02-FIX-CAR-IMAGES-003)
- **DECISION-CAR-MAIN-IMAGE-HANDLING-001:** Fixed main image handling in CarController by replacing `addMediaFromUrl()` with direct file path approach using `addMedia($originalPath)` for local development environment. The URL-based approach fails in local development due to localhost URL accessibility issues. (Date: 2024-12-19, Task: PH02-FIX-CAR-IMAGES-004)
