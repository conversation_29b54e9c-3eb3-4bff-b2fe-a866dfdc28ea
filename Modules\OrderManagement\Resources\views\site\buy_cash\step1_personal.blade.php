{{-- 
    الخطوة 1: البيانات الشخصية - عملية شراء السيارة كاش
    
    يعرض هذا الـ view نموذج لجمع البيانات الشخصية للعميل
    بناءً على UIUX-FR.md (SITE-BUY-CASH-STEPX-001) و REQ-FR.md (MOD-ORDER-MGMT-FEAT-003)
--}}

@extends('site.layouts.site_layout')

@section('title', 'شراء السيارة - البيانات الشخصية')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            
            {{-- عنوان الصفحة ومؤشر التقدم --}}
            <div class="text-center mb-4">
                <h2 class="mb-3">شراء السيارة كاش</h2>
                
                {{-- مؤشر التقدم (Stepper) --}}
                <div class="progress-stepper mb-4">
                    <div class="step active">
                        <div class="step-number">1</div>
                        <div class="step-title">البيانات الشخصية</div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-title">تفاصيل الحجز</div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">المستندات</div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-title">المراجعة والتأكيد</div>
                    </div>
                </div>
            </div>

            <div class="row">
                {{-- ملخص السيارة المختارة --}}
                <div class="col-lg-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                السيارة المختارة
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($car->getFirstMediaUrl('car_main_image') || $car->getFirstMediaUrl('car_images'))
                                <img src="{{ $car->getFirstMediaUrl('car_main_image') ?: $car->getFirstMediaUrl('car_images') }}" 
                                     alt="{{ $car->title }}" 
                                     class="img-fluid rounded mb-3">
                            @endif
                            
                            <h6 class="fw-bold">{{ $car->title }}</h6>
                            <p class="text-muted mb-2">
                                {{ $car->brand->name ?? '' }} - {{ $car->carModel->name ?? '' }}
                            </p>
                            <p class="text-muted mb-2">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $car->manufacturingYear->year ?? '' }}
                            </p>
                            <p class="text-muted mb-3">
                                <i class="fas fa-palette me-1"></i>
                                {{ $car->mainColor->name ?? '' }}
                            </p>
                            
                            <div class="price-section">
                                <h4 class="text-primary fw-bold">
                                    {{ number_format($car->price, 0) }} {{ $car->currency }}
                                </h4>
                                @if($car->offer_price && $car->offer_price < $car->price)
                                    <small class="text-decoration-line-through text-muted">
                                        {{ number_format($car->offer_price, 0) }} {{ $car->currency }}
                                    </small>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                {{-- نموذج البيانات الشخصية --}}
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                البيانات الشخصية
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('site.order.cash.step2') }}" method="GET" id="personalDataForm">
                                
                                <div class="row">
                                    {{-- الاسم الكامل --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">
                                            الاسم الكامل <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="full_name" 
                                               name="full_name" 
                                               value="{{ old('full_name', $userData['full_name'] ?? '') }}" 
                                               required>
                                        <div class="form-text">كما هو مكتوب في الهوية الوطنية</div>
                                    </div>

                                    {{-- رقم الهوية --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="national_id" class="form-label">
                                            رقم الهوية/الإقامة <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="national_id" 
                                               name="national_id" 
                                               value="{{ old('national_id', $userData['national_id'] ?? '') }}" 
                                               pattern="[0-9]{10}" 
                                               maxlength="10" 
                                               required>
                                        <div class="form-text">10 أرقام</div>
                                    </div>
                                </div>

                                <div class="row">
                                    {{-- تاريخ الميلاد --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="date_of_birth" class="form-label">
                                            تاريخ الميلاد <span class="text-danger">*</span>
                                        </label>
                                        <input type="date" 
                                               class="form-control" 
                                               id="date_of_birth" 
                                               name="date_of_birth" 
                                               value="{{ old('date_of_birth', $userData['date_of_birth'] ?? '') }}" 
                                               required>
                                    </div>

                                    {{-- رقم الجوال --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            رقم الجوال <span class="text-danger">*</span>
                                        </label>
                                        <input type="tel" 
                                               class="form-control" 
                                               id="phone" 
                                               name="phone" 
                                               value="{{ old('phone', $userData['phone'] ?? '') }}" 
                                               pattern="[0-9]{10}" 
                                               required>
                                        <div class="form-text">مثال: 0501234567</div>
                                    </div>
                                </div>

                                <div class="row">
                                    {{-- البريد الإلكتروني --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            البريد الإلكتروني <span class="text-danger">*</span>
                                        </label>
                                        <input type="email" 
                                               class="form-control" 
                                               id="email" 
                                               name="email" 
                                               value="{{ old('email', $userData['email'] ?? '') }}" 
                                               required>
                                    </div>

                                    {{-- المدينة --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="city" class="form-label">
                                            المدينة <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="city" name="city" required>
                                            <option value="">اختر المدينة</option>
                                            <option value="الرياض" {{ old('city', $userData['city'] ?? '') == 'الرياض' ? 'selected' : '' }}>الرياض</option>
                                            <option value="جدة" {{ old('city', $userData['city'] ?? '') == 'جدة' ? 'selected' : '' }}>جدة</option>
                                            <option value="الدمام" {{ old('city', $userData['city'] ?? '') == 'الدمام' ? 'selected' : '' }}>الدمام</option>
                                            <option value="مكة المكرمة" {{ old('city', $userData['city'] ?? '') == 'مكة المكرمة' ? 'selected' : '' }}>مكة المكرمة</option>
                                            <option value="المدينة المنورة" {{ old('city', $userData['city'] ?? '') == 'المدينة المنورة' ? 'selected' : '' }}>المدينة المنورة</option>
                                        </select>
                                    </div>
                                </div>

                                {{-- العنوان التفصيلي --}}
                                <div class="mb-3">
                                    <label for="address" class="form-label">
                                        العنوان التفصيلي <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control" 
                                              id="address" 
                                              name="address" 
                                              rows="3" 
                                              required>{{ old('address', $userData['address'] ?? '') }}</textarea>
                                    <div class="form-text">الحي، الشارع، رقم المبنى</div>
                                </div>

                                {{-- أزرار التنقل --}}
                                <div class="d-flex justify-content-between mt-4">
                                    <a href="{{ route('site.cars.show', $car->id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة للسيارة
                                    </a>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        التالي: تفاصيل الحجز
                                        <i class="fas fa-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress-stepper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.5;
}

.step.active {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.step.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    text-align: center;
}
</style>
@endpush
