<div id="price-status-part" class="content" role="tabpanel" aria-labelledby="price-status-part-trigger">
    <h6 class="mb-4 text-primary">
        <i class="fas fa-tag me-2"></i>
        السعر وحالة السيارة
    </h6>

    <div class="row g-3">
        <!-- السعر الأساسي -->
        <div class="col-md-4">
            <label for="price" class="form-label">السعر الأساسي <span class="text-danger">*</span></label>
            <div class="input-group">
                <input type="number" class="form-control @error('price') is-invalid @enderror"
                       id="price" name="price"
                       value="{{ old('price', isset($car) ? $car->price : '') }}"
                       placeholder="مثال: 125000"
                       min="0" step="0.01" required>
                <select class="form-select @error('currency') is-invalid @enderror"
                        id="currency" name="currency" style="max-width: 120px;" required>
                    <option value="SAR" {{ old('currency', isset($car) ? $car->currency : 'SAR') == 'SAR' ? 'selected' : '' }}>ريال سعودي</option>
                    <option value="USD" {{ old('currency', isset($car) ? $car->currency : '') == 'USD' ? 'selected' : '' }}>دولار أمريكي</option>
                    <option value="EUR" {{ old('currency', isset($car) ? $car->currency : '') == 'EUR' ? 'selected' : '' }}>يورو</option>
                </select>
            </div>
            <div class="form-text">السعر الأساسي للسيارة</div>
            @error('price')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            @error('currency')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- سعر العرض -->
        <div class="col-md-4">
            <label for="offer_price" class="form-label">سعر العرض (اختياري)</label>
            <input type="number" class="form-control @error('offer_price') is-invalid @enderror"
                   id="offer_price" name="offer_price"
                   value="{{ old('offer_price', isset($car) ? $car->offer_price : '') }}"
                   placeholder="مثال: 115000"
                   min="0" step="0.01">
            <div class="form-text">سعر العرض الخاص (يجب أن يكون أقل من السعر الأساسي)</div>
            @error('offer_price')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- نسبة الخصم (محسوبة تلقائياً) -->
        <div class="col-md-4">
            <label class="form-label">نسبة الخصم</label>
            <div class="form-control bg-light" id="discount_percentage">0%</div>
            <div class="form-text">يتم حسابها تلقائياً بناءً على السعر الأساسي وسعر العرض</div>
        </div>

        <!-- تاريخ بداية العرض -->
        <div class="col-md-6">
            <label for="offer_start_date" class="form-label">تاريخ بداية العرض</label>
            <input type="date" class="form-control @error('offer_start_date') is-invalid @enderror"
                   id="offer_start_date" name="offer_start_date"
                   value="{{ old('offer_start_date', isset($car) && $car->offer_start_date ? $car->offer_start_date->format('Y-m-d') : '') }}"
                   min="{{ date('Y-m-d') }}">
            <div class="form-text">تاريخ بداية العرض الخاص (اختياري)</div>
            @error('offer_start_date')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- تاريخ نهاية العرض -->
        <div class="col-md-6">
            <label for="offer_end_date" class="form-label">تاريخ نهاية العرض</label>
            <input type="date" class="form-control @error('offer_end_date') is-invalid @enderror"
                   id="offer_end_date" name="offer_end_date"
                   value="{{ old('offer_end_date', isset($car) && $car->offer_end_date ? $car->offer_end_date->format('Y-m-d') : '') }}">
            <div class="form-text">تاريخ نهاية العرض الخاص (اختياري)</div>
            @error('offer_end_date')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- حالة السيارة -->
        <div class="col-12">
            <h6 class="text-secondary mb-3 mt-4">
                <i class="fas fa-toggle-on me-2"></i>
                حالة السيارة في النظام
            </h6>
        </div>

        <!-- سيارة مميزة -->
        <div class="col-md-4">
            <div class="form-check form-switch">
                <input class="form-check-input @error('is_featured') is-invalid @enderror"
                       type="checkbox" id="is_featured" name="is_featured" value="1"
                       {{ old('is_featured', isset($car) ? $car->is_featured : false) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_featured">
                    <strong>سيارة مميزة</strong>
                    <small class="d-block text-muted">ستظهر في قسم السيارات المميزة</small>
                </label>
            </div>
            @error('is_featured')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- سيارة مباعة -->
        <div class="col-md-4">
            <div class="form-check form-switch">
                <input class="form-check-input @error('is_sold') is-invalid @enderror"
                       type="checkbox" id="is_sold" name="is_sold" value="1"
                       {{ old('is_sold', isset($car) ? $car->is_sold : false) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_sold">
                    <strong>سيارة مباعة</strong>
                    <small class="d-block text-muted">تحديد السيارة كمباعة</small>
                </label>
            </div>
            @error('is_sold')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- سيارة نشطة -->
        <div class="col-md-4">
            <div class="form-check form-switch">
                <input class="form-check-input @error('is_active') is-invalid @enderror"
                       type="checkbox" id="is_active" name="is_active" value="1"
                       {{ old('is_active', isset($car) ? $car->is_active : true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                    <strong>سيارة نشطة</strong>
                    <small class="d-block text-muted">ستظهر للعملاء في الموقع</small>
                </label>
            </div>
            @error('is_active')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- ملخص السيارة -->
        <div class="col-12 mt-4">
            <div class="card bg-light">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clipboard-list me-2"></i>
                        ملخص بيانات السيارة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>العنوان:</strong> <span id="summary_title">-</span></p>
                            <p class="mb-1"><strong>الماركة:</strong> <span id="summary_brand">-</span></p>
                            <p class="mb-1"><strong>الموديل:</strong> <span id="summary_model">-</span></p>
                            <p class="mb-1"><strong>السنة:</strong> <span id="summary_year">-</span></p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>السعر:</strong> <span id="summary_price">-</span></p>
                            <p class="mb-1"><strong>سعر العرض:</strong> <span id="summary_offer_price">-</span></p>
                            <p class="mb-1"><strong>الحالة:</strong> <span id="summary_condition">-</span></p>
                            <p class="mb-1"><strong>نوع الوقود:</strong> <span id="summary_fuel">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4 d-flex justify-content-between">
        <button type="button" class="btn btn-secondary" onclick="stepper.previous()">
            <i class="fas fa-arrow-right me-1"></i>
            السابق: الصور والفيديو
        </button>
        <div>
            @if(!isset($isEdit))
            <button type="button" class="btn btn-warning me-2" onclick="testFormSubmission()">
                <i class="fas fa-bug me-1"></i>
                اختبار الإرسال
            </button>
            @endif
            <button type="submit" class="btn btn-success btn-lg">
                <i class="fas fa-save me-2"></i>
                {{ isset($isEdit) ? 'تحديث السيارة' : 'حفظ السيارة' }}
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // حساب نسبة الخصم
    function calculateDiscount() {
        const price = parseFloat(document.getElementById('price').value) || 0;
        const offerPrice = parseFloat(document.getElementById('offer_price').value) || 0;
        const discountElement = document.getElementById('discount_percentage');

        if (price > 0 && offerPrice > 0 && offerPrice < price) {
            const discount = ((price - offerPrice) / price * 100).toFixed(1);
            discountElement.textContent = discount + '%';
            discountElement.className = 'form-control bg-success text-white';
        } else {
            discountElement.textContent = '0%';
            discountElement.className = 'form-control bg-light';
        }
    }

    // ربط الأحداث
    document.getElementById('price').addEventListener('input', calculateDiscount);
    document.getElementById('offer_price').addEventListener('input', calculateDiscount);

    // تحديث الملخص
    function updateSummary() {
        // العنوان
        const title = document.getElementById('title').value || '-';
        document.getElementById('summary_title').textContent = title;

        // الماركة
        const brandSelect = document.getElementById('brand_id');
        const brandText = brandSelect.options[brandSelect.selectedIndex]?.text || '-';
        document.getElementById('summary_brand').textContent = brandText;

        // الموديل
        const modelSelect = document.getElementById('car_model_id');
        const modelText = modelSelect.options[modelSelect.selectedIndex]?.text || '-';
        document.getElementById('summary_model').textContent = modelText;

        // السنة
        const yearSelect = document.getElementById('manufacturing_year_id');
        const yearText = yearSelect.options[yearSelect.selectedIndex]?.text || '-';
        document.getElementById('summary_year').textContent = yearText;

        // السعر
        const price = document.getElementById('price').value;
        const currency = document.getElementById('currency').value;
        const priceText = price ? `${price} ${currency}` : '-';
        document.getElementById('summary_price').textContent = priceText;

        // سعر العرض
        const offerPrice = document.getElementById('offer_price').value;
        const offerPriceText = offerPrice ? `${offerPrice} ${currency}` : '-';
        document.getElementById('summary_offer_price').textContent = offerPriceText;

        // الحالة
        const conditionSelect = document.getElementById('condition');
        const conditionText = conditionSelect.options[conditionSelect.selectedIndex]?.text || '-';
        document.getElementById('summary_condition').textContent = conditionText;

        // نوع الوقود
        const fuelSelect = document.getElementById('fuel_type_id');
        const fuelText = fuelSelect.options[fuelSelect.selectedIndex]?.text || '-';
        document.getElementById('summary_fuel').textContent = fuelText;
    }

    // ربط أحداث تحديث الملخص
    ['title', 'brand_id', 'car_model_id', 'manufacturing_year_id', 'price', 'currency', 'offer_price', 'condition', 'fuel_type_id'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', updateSummary);
            element.addEventListener('input', updateSummary);
        }
    });

    // تحديث أولي
    calculateDiscount();
    updateSummary();

    // التحقق من تواريخ العرض
    const startDateInput = document.getElementById('offer_start_date');
    const endDateInput = document.getElementById('offer_end_date');

    if (startDateInput && endDateInput) {
        startDateInput.addEventListener('change', function() {
            endDateInput.min = this.value;
        });
    }
});

// دالة اختبار إرسال الـ form
function testFormSubmission() {
    console.log('Testing form submission...');

    const form = document.getElementById('carForm');
    if (!form) {
        alert('لم يتم العثور على الـ form!');
        return;
    }

    // إنشاء FormData من الـ form الحالي
    const formData = new FormData(form);

    // إضافة بيانات اختبار بسيطة
    formData.set('title', 'اختبار السيارة');
    formData.set('price', '100000');
    formData.set('currency', 'SAR');

    console.log('Test form data:');
    for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
    }

    // إرسال طلب AJAX للـ test route
    fetch('{{ route("admin.cars.test-submission") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => {
        console.log('Test response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Test response data:', data);
        alert('اختبار الإرسال نجح! تحقق من الـ logs للتفاصيل.');
    })
    .catch(error => {
        console.error('Test submission error:', error);
        alert('فشل اختبار الإرسال: ' + error.message);
    });
}
</script>
