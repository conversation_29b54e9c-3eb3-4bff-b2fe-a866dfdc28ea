/*
 * Brand Identity CSS - نظام الهوية البصرية الموحد
 * يحتوي على جميع الأنماط المطلوبة للحفاظ على الهوية البصرية عبر المشروع
 */

/* متغيرات الألوان الأساسية - مطابقة لـ Dashboard */
:root {
    --primary-color: #0f172a;
    --secondary-color: #1e3a8a;
    --accent-color: #f97316;
    --light-bg: #f8fafc;
    --dark-bg: #020617;
    --text-color: #334155;
    --text-light: #f8fafc;
    --text-muted: #94a3b8;
    --border-radius: 10px;
    --transition-speed: 0.3s;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
}

/* الأزرار الموحدة */
.btn-brand-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
    font-weight: 500;
}

.btn-brand-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(30, 58, 138, 0.3);
}

.btn-brand-secondary {
    background-color: transparent;
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
    font-weight: 500;
}

.btn-brand-secondary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-brand-accent {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
    font-weight: 500;
}

.btn-brand-accent:hover {
    background-color: #ea580c;
    border-color: #ea580c;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(249, 115, 22, 0.3);
}

.btn-brand-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.btn-brand-success:hover {
    background-color: #059669;
    border-color: #059669;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.btn-brand-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.btn-brand-warning:hover {
    background-color: #d97706;
    border-color: #d97706;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
}

.btn-brand-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.btn-brand-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.btn-brand-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: white;
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.btn-brand-info:hover {
    background-color: #0891b2;
    border-color: #0891b2;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(6, 182, 212, 0.3);
}

/* أزرار الإجراءات الموحدة */
.action-btn {
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all var(--transition-speed) ease;
    border: none;
    font-size: 0.875rem;
}

.action-btn.edit {
    background-color: var(--warning-color);
    color: white;
}

.action-btn.edit:hover {
    background-color: #d97706;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
}

.action-btn.delete {
    background-color: var(--danger-color);
    color: white;
}

.action-btn.delete:hover {
    background-color: #dc2626;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.action-btn.view {
    background-color: var(--info-color);
    color: white;
}

.action-btn.view:hover {
    background-color: #0891b2;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(6, 182, 212, 0.3);
}

/* الكروت الموحدة */
.brand-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    transition: all var(--transition-speed) ease;
    overflow: hidden;
}

.brand-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.brand-card-header {
    background: var(--secondary-color);
    color: white;
    padding: 1rem 1.5rem;
    border-bottom: none;
    font-weight: 600;
}

.brand-card-body {
    padding: 1.5rem;
}

/* النماذج الموحدة */
.brand-form-control {
    border-radius: var(--border-radius);
    border: 1px solid #e2e8f0;
    transition: all var(--transition-speed) ease;
    font-size: 0.95rem;
}

.brand-form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
    outline: none;
}

.brand-form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

/* التنبيهات الموحدة */
.brand-alert {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 500;
}

.brand-alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.brand-alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.brand-alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.brand-alert-info {
    background-color: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* الجداول الموحدة */
.brand-table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background: white;
}

.brand-table thead th {
    background-color: var(--secondary-color);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.brand-table tbody tr {
    transition: all var(--transition-speed) ease;
}

.brand-table tbody tr:hover {
    background-color: rgba(30, 58, 138, 0.05);
    transform: translateY(-1px);
}

.brand-table tbody td {
    padding: 1rem;
    border-color: #f1f5f9;
}

/* الأيقونات الموحدة */
.brand-icon-wrapper {
    width: 50px;
    height: 50px;
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.brand-icon-primary {
    background: var(--primary-color);
}

.brand-icon-secondary {
    background: var(--secondary-color);
}

.brand-icon-success {
    background: var(--success-color);
}

.brand-icon-warning {
    background: var(--warning-color);
}

.brand-icon-danger {
    background: var(--danger-color);
}

.brand-icon-info {
    background: var(--info-color);
}

/* الروابط الموحدة */
.brand-link {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color var(--transition-speed) ease;
    font-weight: 500;
}

.brand-link:hover {
    color: var(--accent-color);
    text-decoration: none;
}

/* الـ Breadcrumb الموحد */
.brand-breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.brand-breadcrumb .breadcrumb-item a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color var(--transition-speed) ease;
}

.brand-breadcrumb .breadcrumb-item a:hover {
    color: var(--accent-color);
}

.brand-breadcrumb .breadcrumb-item.active {
    color: var(--text-muted);
}

/* الـ Status Badges الموحدة */
.brand-status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.brand-status-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.brand-status-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.brand-status-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.brand-status-info {
    background-color: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
}

.brand-status-primary {
    background-color: rgba(30, 58, 138, 0.1);
    color: var(--secondary-color);
}

/* الـ Action Buttons الموحدة */
.brand-action-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed) ease;
    font-size: 0.875rem;
    margin: 0 2px;
}

.brand-action-view {
    background-color: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
}

.brand-action-view:hover {
    background-color: var(--info-color);
    color: white;
    transform: scale(1.1);
}

.brand-action-edit {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.brand-action-edit:hover {
    background-color: var(--warning-color);
    color: white;
    transform: scale(1.1);
}

.brand-action-delete {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.brand-action-delete:hover {
    background-color: var(--danger-color);
    color: white;
    transform: scale(1.1);
}

/* تأثيرات الحركة الموحدة */
.brand-fade-in {
    animation: brandFadeIn 0.5s ease-in-out;
}

@keyframes brandFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.brand-slide-up {
    animation: brandSlideUp 0.3s ease-out;
}

@keyframes brandSlideUp {
    from {
        transform: translateY(10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* الـ Loading States الموحدة */
.brand-loading {
    position: relative;
    overflow: hidden;
}

.brand-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: brandLoading 1.5s infinite;
}

@keyframes brandLoading {
    0% { left: -100%; }
    100% { left: 100%; }
}
