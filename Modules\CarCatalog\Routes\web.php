<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Modules\CarCatalog\Http\Controllers\Site\FavoriteController;
use Modules\CarCatalog\Http\Controllers\Site\CompareController;
use Modules\CarCatalog\Http\Controllers\Site\SiteCarController;

// مسارات عرض السيارات في الموقع العام
Route::prefix('cars')->name('site.cars.')->group(function () {
    // عرض قائمة السيارات
    Route::get('/', [SiteCarController::class, 'index'])->name('index');

    // عرض تفاصيل سيارة محددة
    Route::get('/{car}', [SiteCarController::class, 'show'])->name('show');

    // AJAX: جلب الموديلات حسب الماركة
    Route::get('/brands/{brand}/models', [SiteCarController::class, 'getModelsByBrand'])
        ->name('brands.models.get');
});

// مسارات المفضلة (تتطلب تسجيل دخول)
Route::prefix('favorites')->name('site.favorites.')->middleware(['web'])->group(function () {
    // إضافة سيارة للمفضلة
    Route::post('/add/{car}', [FavoriteController::class, 'add'])->name('add');

    // إزالة سيارة من المفضلة
    Route::delete('/remove/{car}', [FavoriteController::class, 'remove'])->name('remove');

    // تبديل حالة السيارة في المفضلة
    Route::post('/toggle/{car}', [FavoriteController::class, 'toggle'])->name('toggle');

    // الحصول على عدد السيارات في المفضلة
    Route::get('/count', [FavoriteController::class, 'count'])->name('count');

    // التحقق من حالة سيارة في المفضلة
    Route::get('/status/{car}', [FavoriteController::class, 'status'])->name('status');
});

// مسارات المقارنة (لا تتطلب تسجيل دخول - تستخدم Session)
Route::prefix('compare')->name('site.compare.')->middleware(['web'])->group(function () {
    // عرض صفحة المقارنة
    Route::get('/', [CompareController::class, 'index'])->name('index');

    // إضافة سيارة للمقارنة
    Route::post('/add/{car}', [CompareController::class, 'add'])->name('add');

    // إزالة سيارة من المقارنة
    Route::delete('/remove/{car}', [CompareController::class, 'remove'])->name('remove');

    // مسح سلة المقارنة
    Route::delete('/clear', [CompareController::class, 'clear'])->name('clear');

    // الحصول على عدد السيارات في المقارنة
    Route::get('/count', [CompareController::class, 'count'])->name('count');

    // التحقق من حالة سيارة في المقارنة
    Route::get('/status/{car}', [CompareController::class, 'status'])->name('status');

    // الحصول على قائمة السيارات في المقارنة
    Route::get('/list', [CompareController::class, 'list'])->name('list');
});

// مسارات قديمة (للتوافق مع الإصدارات السابقة)
Route::prefix('carcatalog')->group(function () {
    Route::get('/', 'CarCatalogController@index');
});
