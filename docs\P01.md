## phase_01_foundation_and_dash_setup_instructions_final_revised.md - تعليمات تنفيذ المرحلة 01: بناء الأساس وتجهيز بيئة Dash (نهائية معتمدة ذاتيًا)

**تاريخ الإنشاء:** 2024-07-27
**إصدار المستند:** 1.0 (نهائي معتمد ذاتيًا)
**المرحلة المستهدفة من `PPP-FR.md`:** `PH-01` (الأساسيات وتجهيز بيئة Dash)


**الترتيب المعدل للمهام في المرحلة الأولى (PH-01):**

### **TASK-ID:PH01-TASK-001** `LARAVEL-PROJECT-VERIFY-AND-LOGS-SETUP-001`
* **LEVEL:** `Low`
* **OBJECTIVE:** التحقق من صحة إعداد مشروع Laravel 10 الحالي وتكوين قاعدة البيانات، وإنشاء ملفات السجلات الأساسية (`CHANGELOG.md`, `TODO.md`, `DECISIONS.md`). يخدم هذا بشكل مباشر [الموقع الإلكتروني الفعال]، [لوحة التحكم الاحترافية Dash]، [واجهة الموقع Blade]، و [API تطبيق Flutter] من خلال ضمان بيئة خلفية سليمة وموثقة.
* **TYPE:** `Laravel Project Verification & Documentation Setup`
* **FILE_NAME_PATH:**
    * (جذر المشروع) - للتحقق
    * `docs/CHANGELOG.md`
    * `docs/TODO.md`
    * `docs/DECISIONS.md`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-001` - ضمنيًا، التحقق من التثبيت والتكوين).
    * **DESIGN_REF:** لا ينطبق.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: لا توجد سجلات سابقة لمراجعتها لهذه المهمة بالذات، حيث أن هذه المهمة تتضمن إنشاء السجلات."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** يفترض أن مشروع Laravel 10 قد تم إنشاؤه وتكوين اتصال قاعدة البيانات بنجاح مسبقًا.
* **LLM_ASSUMPTIONS:**
    * مشروع Laravel 10 موجود بالفعل.
    * قاعدة بيانات (مثل MySQL) تم إنشاؤها وربطها بالمشروع عبر ملف `.env`.
    * Git تم تهيئته في المشروع.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. VERIFY_LARAVEL_PROJECT_AND_DATABASE:**
        * **DETAILED_LOGIC:**
            * "1. تأكد من أن المشروع في جذر الدليل الحالي هو مشروع Laravel 10."
            * "2. تحقق من محتويات ملف `.env`. تأكد من أن `APP_NAME`, `APP_ENV=local`, `APP_DEBUG=true`, `APP_URL` (مثل `http://localhost:8000`) معرفة."
            * "3. تحقق من أن إعدادات `DB_CONNECTION`, `DB_HOST`, `DB_PORT`, `DB_DATABASE`, `DB_USERNAME`, `DB_PASSWORD` صحيحة وتسمح بالاتصال بقاعدة البيانات."
            * "4. قم بتشغيل `php artisan migrate:status`. يجب أن يظهر 'No migrations found' أو قائمة بالـ migrations الأساسية لـ Laravel إذا تم تشغيلها مسبقًا، دون أخطاء اتصال."
            * "5. قم بتشغيل `php artisan serve` وتأكد من أن الصفحة الترحيبية لـ Laravel تعمل بدون مشاكل اتصال بقاعدة البيانات (إذا كانت الصفحة الترحيبية تتطلب ذلك)."
    * **2. CREATE_DOCUMENTATION_DIRECTORY_AND_LOG_FILES:**
        * **DETAILED_LOGIC:**
            * "1. في جذر المشروع، قم بإنشاء مجلد جديد باسم `docs` إذا لم يكن موجودًا."
            * "2. داخل مجلد `docs`, قم بإنشاء الملفات الفارغة التالية:"
                * `CHANGELOG.md`
                * `TODO.md`
                * `DECISIONS.md`
            * "3. قم بإضافة محتوى أولي لهذه الملفات:"
                *   **`CHANGELOG.md`:**
                    ```markdown
                    # Changelog

                    All notable changes to this project will be documented in this file.

                    ## [Unreleased]
                    ```
                *   **`TODO.md`:**
                    ```markdown
                    # TODO List

                    ## Phase 01: Foundation and Dash Setup
                    - [ ] TASK-ID:PH01-TASK-001 LARAVEL-PROJECT-VERIFY-AND-LOGS-SETUP-001 (This task)
                    - [ ] TASK-ID:PH01-TASK-002 LARAVEL-MODULES-SETUP-001
                    - [ ] TASK-ID:PH01-TASK-003 CORE-MODULE-CREATION-001
                    - [ ] TASK-ID:PH01-TASK-004 CORE-MODULE-BASE-CLASSES-001
                    - [ ] TASK-ID:PH01-TASK-005 CORE-MODULE-GLOBAL-HELPERS-001
                    - [ ] TASK-ID:PH01-TASK-006 USER-MANAGEMENT-MODULE-SETUP-001
                    - [ ] TASK-ID:PH01-TASK-008 SPATIE-PERMISSION-SETUP-001
                    - [ ] TASK-ID:PH01-TASK-009 DASH-LAYOUT-SETUP-001
                    - [ ] TASK-ID:PH01-TASK-010 DASH-LAYOUT-PARTIALS-SETUP-001
                    - [ ] TASK-ID:PH01-TASK-011 DASH-ADMIN-LOGIN-VIEW-001
                    - [ ] TASK-ID:PH01-TASK-007 ADMIN-AUTH-IMPLEMENTATION-001
                    - [ ] TASK-ID:PH01-TASK-012 CORE-MODULE-SETTINGS-DB-SERVICE-001
                    - [ ] TASK-ID:PH01-TASK-015 DASH-ADMIN-PASSWORD-RESET-VIEWS-001
                    - [ ] TASK-ID:PH01-TASK-008A SPATIE-PERMISSION-DEFAULT-ADMIN-USER-001
                    - [ ] TASK-ID:PH01-TASK-013 DASH-ADMIN-INITIAL-DASHBOARD-VIEW-001
                    - [ ] TASK-ID:PH01-TASK-014 ADMIN-DASHBOARD-BASIC-ROUTING-CONTROLLER-001
                    
                    ## Future Phases / General Todos
                    - [ ] TODO-DASH-ASSETS-VITE-INTEGRATION-001: Configure Laravel Vite/Mix for Dash assets.
                    - [ ] TODO-SPATIE-PERMISSION-USER-INTEG-001: Fully integrate Spatie Permission with User model.
                    - [ ] TODO-SPATIE-MEDIALIB-USER-INTEG-001: Fully integrate Spatie Media Library with User model for profile photos.
                    - [ ] TODO-DASH-FORGOT-PASS-VIEW-001: Create Blade view for admin 'Forgot Password' page.
                    - [ ] TODO-DASH-RESET-PASS-VIEW-001: Create Blade view for admin 'Reset Password' page.
                    - [ ] TODO-DASH-SETTINGS-UI-001: Create UI in Dash admin panel for managing system settings.
                    - [ ] TODO-DASHBOARD-DYNAMIC-CONTENT-001: Implement dynamic content for the main admin dashboard (charts, stats).
                    - [ ] TODO-DASH-SIDEBAR-DYNAMIC-001: Implement dynamic sidebar for Dash admin panel.
                    - [ ] TODO-DASH-TOPBAR-DYNAMIC-001: Implement dynamic topbar for Dash admin panel (user info, notifications).
                    ```
                *   **`DECISIONS.md`:**
                    ```markdown
                    # Decision Log

                    This document records significant architectural and design decisions made during the project.

                    ## General Decisions
                    - **DECISION-PROJ-NAME-001:** Project name is `car_showroom_platform`. (Date: YYYY-MM-DD, Task: PH01-TASK-001)
                    - **DECISION-DB-NAME-001:** Development database name is `car_showroom_db`. (Date: YYYY-MM-DD, Task: PH01-TASK-001)

                    ## Module Structure
                    - **DECISION-MODULES-MODEL-PATH-001:** Module models will reside in a `Models` directory instead of `Entities`. (Date: YYYY-MM-DD, Task: PH01-TASK-002)

                    ## Core Module
                    - **DECISION-CORE-BASEREPOSITORY-DEFERRED-001:** Implementation of `BaseRepository` is deferred for now. Focus on `BaseController`, `BaseModel`, `BaseRequest`. (Date: YYYY-MM-DD, Task: PH01-TASK-004)

                    ## User Management
                    - **DECISION-INITIAL-ROLES-PERMS-001:** Initial roles: 'Super Admin', 'Employee', 'Customer'. Initial permissions: 'access_admin_dashboard', 'access_customer_dashboard', 'view_cars_admin', 'manage_cars_admin'. (Date: YYYY-MM-DD, Task: PH01-TASK-008)
                    - **DECISION-DEFAULT-ADMIN-CREDENTIALS-001:** Default Super Admin: `<EMAIL>` / `password`. (IMPORTANT: Must be changed post-setup). (Date: YYYY-MM-DD, Task: PH01-TASK-008A)


                    ## Dash Panel
                    - **DECISION-DASH-ASSET-TEMP-PATH-001:** Dash assets (CSS, JS, images, fonts) will be temporarily copied to `public/vendor/dash/` for initial layout setup. To be replaced by Vite/Mix processing later. (Date: YYYY-MM-DD, Task: PH01-TASK-009)
                    - **DECISION-DASH-LAYOUT-LOCATION-001:** The main admin dashboard layout (`admin_layout.blade.php`) and its associated partials will be located within the `Dashboard` module (`Modules/Dashboard/Resources/views/layouts/`). (Date: YYYY-MM-DD, Task: PH01-TASK-009)
                    ```
    * **4. COMMIT_LOG_FILES:**
        * **DETAILED_LOGIC:** "1. قم بإضافة مجلد `docs` وملفاته إلى Git."
        * "2. قم بعمل commit لهذه الملفات (e.g., `git add docs/` ثم `git commit -m \"Initialize project log files (CHANGELOG, TODO, DECISIONS)\"`)."
* **REFERENCE_CODE_SNIPPETS:** (محتوى الملفات كما هو موضح أعلاه).
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم التحقق من أن مشروع Laravel يعمل وقاعدة البيانات متصلة. تم إنشاء مجلد `docs` وملفات `CHANGELOG.md`, `TODO.md`, و `DECISIONS.md` بالمحتوى الأولي المقترح، وتم إضافتها إلى Git.
* **SECURITY_CONSIDERATIONS:** تأكد من أن ملف `.env` لا يتم تضمينه في مستودع Git (وهو السلوك الافتراضي لـ `.gitignore` الخاص بـ Laravel).
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** لا يوجد تعليقات كود محددة لهذه المهمة.
* **ACCEPTANCE_CRITERIA:**
    1. "تم التحقق من أن مشروع Laravel يعمل بدون أخطاء اتصال بقاعدة البيانات."
    2. "تم إنشاء مجلد `docs` في جذر المشروع."
    3. "تم إنشاء ملف `CHANGELOG.md` بالمحتوى الأولي المقترح."
    4. "تم إنشاء ملف `TODO.md` بالمحتوى الأولي المقترح، متضمنًا قائمة مهام المرحلة الأولى."
    5. "تم إنشاء ملف `DECISIONS.md` بالمحتوى الأولي المقترح."
    6. "تم عمل commit لملفات السجلات الجديدة."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: أضف إدخالاً: 'Verified Laravel project setup and initialized log files (CHANGELOG.md, TODO.md, DECISIONS.md).' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: ضع علامة [x] بجانب `TASK-ID:PH01-TASK-001 LARAVEL-PROJECT-VERIFY-AND-LOGS-SETUP-001` في `docs/TODO.md`."

---

### **TASK-ID:PH01-TASK-002** `LARAVEL-MODULES-SETUP-001`
* **LEVEL:** `Medium`
* **OBJECTIVE:** تثبيت وتكوين حزمة `nwidart/laravel-modules` لتنظيم الكود في موديولات منفصلة، مما يعزز قابلية الصيانة والتوسع لجميع [المخرجات النهائية الأربعة].
* **TYPE:** `Package Installation and Configuration`
* **FILE_NAME_PATH:** `composer.json`, `config/modules.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-002`), `STRU-FR.md` (الإشارة إلى استخدام الحزمة وتغيير `Entities` إلى `Models`).
    * **DESIGN_REF:** لا ينطبق.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/DECISIONS.md` لأي قرارات سابقة حول تكوين `nwidart/laravel-modules`، وخاصة تأكيد استخدام `Models` بدلاً من `Entities` للمجلدات داخل الموديولات."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-001 LARAVEL-PROJECT-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * نفترض أن الاتصال بالإنترنت متاح لتنزيل الحزمة.
    * القرار بتغيير اسم مجلد `Entities` إلى `Models` (كما هو محدد في `STRU-FR.md`) سيتم تطبيقه.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. INSTALL_PACKAGE:**
        * **DETAILED_LOGIC:** "1. استخدم Composer لتثبيت الحزمة: `composer require nwidart/laravel-modules`."
    * **2. PUBLISH_CONFIGURATION:**
        * **DETAILED_LOGIC:** "1. قم بنشر ملف التهيئة الخاص بالحزمة: `php artisan vendor:publish --provider=\"Nwidart\Modules\LaravelModulesServiceProvider\" --tag=config`."
        * "2. قم بنشر ملفات stubs (اختياري، لكن يفضل للمرونة المستقبلية): `php artisan vendor:publish --provider=\"Nwidart\Modules\LaravelModulesServiceProvider\" --tag=stubs`."
    * **3. CONFIGURE_MODULES_SETTINGS (في `config/modules.php`):**
        * **DETAILED_LOGIC:** "1. افتح ملف `config/modules.php`."
        * "2. ضمن مصفوفة `paths.generator`، قم بتغيير قيمة `model.path` من `Entities` إلى `Models`. تأكد من أن قيمة `model.generate` مضبوطة على `true`."
        * "3. تأكد من أن قيمة `paths.modules` هي `base_path('Modules')`."
        * "4. تأكد من أن قيمة `namespace` هي `Modules`."
    * **4. AUTOLOAD_MODULES:**
        * **DETAILED_LOGIC:** "1. عدّل ملف `composer.json` الرئيسي للتطبيق. في قسم `autoload.psr-4`، أضف المسار للموديولات:
          ```json
          \"autoload\": {
              \"psr-4\": {
                  \"App\\\": \"app/\",
                  \"Database\\Factories\\\": \"database/factories/\",
                  \"Database\\Seeders\\\": \"database/seeders/\",
                  \"Modules\\\": \"Modules/\"
              }
          },
          ```"
        * "2. قم بتشغيل `composer dump-autoload` لتحديث ملفات التحميل التلقائي."
* **REFERENCE_CODE_SNIPPETS:** لا يوجد.
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم تثبيت وتكوين حزمة `nwidart/laravel-modules` بنجاح، مع تغيير مسار إنشاء النماذج إلى `Models`. النظام جاهز لإنشاء الموديولات.
* **SECURITY_CONSIDERATIONS:** لا يوجد.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد تأثير مباشر في هذه المرحلة.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1. "تم تثبيت حزمة `nwidart/laravel-modules`."
    2. "تم نشر ملف التهيئة `config/modules.php`."
    3. "تم تعديل `config/modules.php` لاستخدام `Models` كمسار للنماذج ضمن `paths.generator.model.path`."
    4. "تم تحديث `composer.json` بشكل صحيح وتم تشغيل `composer dump-autoload`."
    5. "عند إنشاء موديول جديد، يتم إنشاء مجلد `Models` بدلاً من `Entities`."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Installed and configured nwidart/laravel-modules package. Configured to use 'Models' directory for module models.' في `docs/CHANGELOG.md`."
    * **DECISIONS.md:** "Augment: وثق قرار استخدام `Models` بدلاً من `Entities` بشكل صريح كـ `DECISION-MODULES-MODEL-PATH-001` في `docs/DECISIONS.md` إذا لم يكن موثقًا بالفعل."

---

### **TASK-ID:PH01-TASK-003** `CORE-MODULE-CREATION-001`
* **LEVEL:** `Low`
* **OBJECTIVE:** إنشاء موديول `Core` (`STRU-MOD-CORE-001`) الذي سيحتوي على الوظائف الأساسية المشتركة، مثل الأصناف الأساسية والدوال المساعدة، مما يساهم في بناء بنية خلفية قوية لجميع [المخرجات النهائية الأربعة].
* **TYPE:** `Module Creation`
* **FILE_NAME_PATH:** `Modules/Core/`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-003`), `STRU-FR.md` (`STRU-MOD-CORE-001`).
    * **DESIGN_REF:** لا ينطبق.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TODO.md` لتحديد ما إذا كانت هناك مهام محددة يجب تضمينها مبدئيًا في موديول `Core` والتي قد تؤثر على هيكله الأولي."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-002 LARAVEL-MODULES-SETUP-001`.
* **LLM_ASSUMPTIONS:** لا يوجد.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_MODULE:**
        * **DETAILED_LOGIC:** "1. استخدم أمر Artisan لإنشاء موديول `Core`: `php artisan module:make Core`."
    * **2. VERIFY_MODULE_STRUCTURE:**
        * **DETAILED_LOGIC:** "1. تأكد من إنشاء مجلد `Modules/Core` بالهيكل القياسي (بما في ذلك مجلد `Models` بدلاً من `Entities`)، بالإضافة إلى المجلدات الأخرى القياسية مثل `Http`, `Database`, `Resources`, `Routes`, `Providers`."
* **REFERENCE_CODE_SNIPPETS:** لا يوجد.
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم إنشاء موديول `Core` بنجاح بالهيكل المتوقع.
* **SECURITY_CONSIDERATIONS:** لا يوجد.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء موديول `Core` بنجاح في المسار `Modules/Core`."
    2. "يحتوي الموديول على الهيكل القياسي المتوقع من `nwidart/laravel-modules` مع مجلد `Models`."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created Core module.' في `docs/CHANGELOG.md`."

---

### **TASK-ID:PH01-TASK-004** `CORE-MODULE-BASE-CLASSES-001`
* **LEVEL:** `High`
* **OBJECTIVE:** تنفيذ الأصناف الأساسية (BaseController, BaseModel, BaseRequest) ضمن موديول `Core` كما هو محدد في `REQ-FR.md` (`MOD-CORE-FEAT-001`), لتوفير أساس برمجي مشترك وقابل لإعادة الاستخدام، مما يدعم جودة وصيانة [المخرجات النهائية الأربعة]. (سيتم تأجيل BaseRepository حاليًا).
* **TYPE:** `PHP Class Implementation (Base Classes)`
* **FILE_NAME_PATH:**
    * `Modules/Core/Http/Controllers/BaseController.php`
    * `Modules/Core/Models/BaseModel.php`
    * `Modules/Core/Http/Requests/BaseRequest.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `REQ-FR.md` (`MOD-CORE-FEAT-001`).
    * **DESIGN_REF:** لا ينطبق.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/DECISIONS.md` لأي قرارات سابقة بشأن نمط Repository أو وظائف محددة يجب تضمينها في Base Classes. بناءً على `00-FR.md` (قسم 7)، نمط Repository اختياري ويمكن تأجيل `BaseRepository`. ركز على `BaseController`, `BaseModel`, و `BaseRequest`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: إذا كانت هذه المهمة تعالج `TODO-CORE-BASECLASSES-IMPL-001`، قم بتحديث حالته."
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-003 CORE-MODULE-CREATION-001`.
* **LLM_ASSUMPTIONS:**
    * سيتم تأجيل إنشاء `BaseRepository` و `BaseRepositoryInterface` حاليًا.
    * `BaseController` سيوفر دوال مساعدة للاستجابات الموحدة.
    * `BaseModel` سيستخدم `SoftDeletes` و `HasFactory`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_BASE_CONTROLLER:**
        * **FILE_NAME_PATH:** `Modules/Core/Http/Controllers/BaseController.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ كلاس `BaseController` داخل `Modules/Core/Http/Controllers/` يرث من `App\Http\Controllers\Controller`."
            * "2. أضف دوال مساعدة محمية (protected) للاستجابات الموحدة JSON:
                ```php
                protected function sendSuccessResponse($data = [], string $message = 'Operation successful.', int $statusCode = 200): \Illuminate\Http\JsonResponse
                {
                    return response()->json([
                        'success' => true,
                        'data'    => $data,
                        'message' => $message,
                    ], $statusCode);
                }

                protected function sendErrorResponse(string $message, array $errors = [], int $statusCode = 400): \Illuminate\Http\JsonResponse
                {
                    $response = [
                        'success' => false,
                        'message' => $message,
                    ];

                    if (!empty($errors)) {
                        $response['errors'] = $errors;
                    }

                    return response()->json($response, $statusCode);
                }
                ```"
    * **2. CREATE_BASE_MODEL:**
        * **FILE_NAME_PATH:** `Modules/Core/Models/BaseModel.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ كلاس `BaseModel` داخل `Modules/Core/Models/` يرث من `Illuminate\Database\Eloquent\Model`."
            * "2. قم بتضمين `use Illuminate\Database\Eloquent\SoftDeletes;` و `use Illuminate\Database\Eloquent\Factories\HasFactory;` واستخدم traits `SoftDeletes, HasFactory`."
    * **3. CREATE_BASE_REQUEST:**
        * **FILE_NAME_PATH:** `Modules/Core/Http/Requests/BaseRequest.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ كلاس `BaseRequest` داخل `Modules/Core/Http/Requests/` يرث من `Illuminate\Foundation\Http\FormRequest`."
            * "2. في دالة `authorize()`, اجعلها تعيد `true` افتراضيًا."
            * "3. قم بتجاوز دالة `failedValidation(Validator $validator)` لتخصيص استجابة خطأ التحقق للـ API (إرجاع JSON response مع رمز 422) إذا كان الطلب يتوقع JSON:
              ```php
              protected function failedValidation(\Illuminate\Contracts\Validation\Validator \$validator)
              {
                  if (\$this->expectsJson()) {
                      throw new \Illuminate\Http\Exceptions\HttpResponseException(
                          response()->json([
                              'success' => false,
                              'message' => 'Validation errors occurred.',
                              'errors' => \$validator->errors()
                          ], 422)
                      );
                  }
                  parent::failedValidation(\$validator);
              }
              ```"
* **REFERENCE_CODE_SNIPPETS:** (كما هو موضح أعلاه).
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم إنشاء الأصناف الأساسية (`BaseController`, `BaseModel`, `BaseRequest`) الموثقة والقابلة للتوسع في موديول `Core`.
* **SECURITY_CONSIDERATIONS:** لا يوجد اعتبارات خاصة مباشرة.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** إضافة DocBlocks لجميع الكلاسات والدوال توضح غرضها، معاملاتها، وما تعيده.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء `BaseController.php` مع دوال `sendSuccessResponse` و `sendErrorResponse`."
    2. "تم إنشاء `BaseModel.php` مع `SoftDeletes` و `HasFactory` traits."
    3. "تم إنشاء `BaseRequest.php` يرث من `FormRequest` مع `authorize()` تعيد `true` افتراضيًا ومعالجة `failedValidation` مخصصة للـ JSON."
    4. "جميع الكلاسات والدوال موثقة بشكل جيد."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created BaseController, BaseModel, and BaseRequest in Core module.' في `docs/CHANGELOG.md`."
    * **DECISIONS.md:** "Augment: وثق قرار تأجيل `BaseRepository` حاليًا كـ `DECISION-CORE-BASEREPOSITORY-DEFERRED-001` في `docs/DECISIONS.md`."

---

### **TASK-ID:PH01-TASK-005** `CORE-MODULE-GLOBAL-HELPERS-001`
* **LEVEL:** `Medium`
* **OBJECTIVE:** تنفيذ دوال مساعدة عامة (Global Helpers) ضمن موديول `Core` كما هو محدد في `REQ-FR.md` (`MOD-CORE-FEAT-002`)، لتبسيط المهام المتكررة وزيادة كفاءة تطوير [المخرجات النهائية الأربعة].
* **TYPE:** `PHP Helper Functions Implementation`
* **FILE_NAME_PATH:** `Modules/Core/Helpers/helpers.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `REQ-FR.md` (`MOD-CORE-FEAT-002`).
    * **DESIGN_REF:** لا ينطبق.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/DECISIONS.md` لأي قرارات بشأن دوال مساعدة محددة تم طلبها أو الاتفاق عليها مسبقًا (مثل تنسيقات معينة للعملة أو التاريخ)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-003 CORE-MODULE-CREATION-001`.
* **LLM_ASSUMPTIONS:**
    * الدوال المساعدة الأساسية المطلوبة هي لتنسيق العملة، التاريخ، وتوليد OTP.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_HELPERS_FILE_AND_DIRECTORY:**
        * **DETAILED_LOGIC:** "1. قم بإنشاء مجلد `Helpers` داخل `Modules/Core/` إذا لم يكن موجودًا."
        * "2. قم بإنشاء ملف `helpers.php` داخل `Modules/Core/Helpers/`."
    * **2. IMPLEMENT_HELPER_FUNCTIONS:**
        * **DETAILED_LOGIC:**
            * "1. قم بتنفيذ الدوال المساعدة التالية ضمن ملف `helpers.php`. تأكد من أن كل دالة محاطة بـ `if (!function_exists('function_name'))`."
            * **`format_currency(float $amount, string $currencySymbol = 'ر.س', int $decimals = 2, string $decimalSeparator = '.', string $thousandsSeparator = ','): string`**: "تنسق الرقم كعملة. مثال: `format_currency(12500.75)` يجب أن تعيد `'12,500.75 ر.س'`."
            * **`format_datetime_for_display(?string $datetimeString, string $format = 'Y-m-d h:i A'): ?string`**: "تحول سلسلة تاريخ ووقت (يفترض أنها UTC) إلى تنسيق عرض محدد باستخدام Carbon. تعيد `null` إذا كان الإدخال `null` أو غير صالح. مثال: `format_datetime_for_display('2024-07-27 14:30:00')` يجب أن تعيد `'2024-07-27 02:30 PM'` (كمثال، يجب أن يكون التنسيق الافتراضي متسقًا)."
            * **`generate_otp(int $length = 6): string`**: "تولد رمز OTP رقمي عشوائي بالطول المحدد. يجب أن يتكون من أرقام فقط."
            * "2. تأكد من أن الدوال عامة قدر الإمكان."
            * "3. قم بتوثيق كل دالة باستخدام DocBlocks (المدخلات، المخرجات، الغرض)."
    * **3. AUTOLOAD_HELPERS_FILE:**
        * **DETAILED_LOGIC:** "1. افتح ملف `Modules/Core/composer.json`."
        * "2. عدّل قسم `autoload` ليشمل `files`:
          ```json
          {
              // ... (other composer.json content for the module)
              "autoload": {
                  "psr-4": {
                      "Modules\\Core\\": "",
                      "Modules\\Core\\App\\": "app/",
                      "Modules\\Core\\Database\\Factories\\": "database/factories/",
                      "Modules\\Core\\Database\\Seeders\\": "database/seeders/"
                  },
                  "files": [
                      "Helpers/helpers.php"
                  ]
              }
              // ...
          }
          ```"
        * "3. قم بتشغيل `composer dump-autoload` من جذر مشروع Laravel."
* **REFERENCE_CODE_SNIPPETS:**
    ```php
    // In Modules/Core/Helpers/helpers.php
    if (!function_exists('format_currency')) {
        function format_currency(float $amount, string $currencySymbol = 'ر.س', int $decimals = 2, string $decimalSeparator = '.', string $thousandsSeparator = ','): string
        {
            return number_format($amount, $decimals, $decimalSeparator, $thousandsSeparator) . ' ' . $currencySymbol;
        }
    }

    if (!function_exists('format_datetime_for_display')) {
        function format_datetime_for_display(?string $datetimeString, string $format = 'Y-m-d h:i A'): ?string
        {
            if (is_null($datetimeString)) {
                return null;
            }
            try {
                return \Carbon\Carbon::parse($datetimeString)->translatedFormat($format); // Use translatedFormat for potential localization
            } catch (\Exception $e) {
                return null;
            }
        }
    }

    if (!function_exists('generate_otp')) {
        function generate_otp(int $length = 6): string
        {
            $generator = '1234567890';
            $result = '';
            for ($i = 1; $i <= $length; $i++) {
                $result .= substr($generator, (rand() % (strlen($generator))), 1);
            }
            return $result;
        }
    }
    ```
* **EXPECTED_OUTPUTS_BEHAVIOR:** توفر دوال مساعدة موثوقة، سهلة الاستخدام، ويمكن الوصول إليها من أي مكان في التطبيق.
* **SECURITY_CONSIDERATIONS:** دالة `generate_otp` بسيطة، لمشاريع حقيقية قد يُفضل استخدام مكتبة أكثر أمانًا لتوليد الأرقام العشوائية إذا كانت متطلبات الأمان عالية جدًا.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد تأثير كبير.
* **REQUIRED_CODE_COMMENTS:** DocBlocks لكل دالة مساعدة.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء ملف `Modules/Core/Helpers/helpers.php`."
    2. "تم تنفيذ الدوال المساعدة `format_currency`, `format_datetime_for_display`, `generate_otp` بشكل صحيح وموثق."
    3. "يتم تحميل ملف `helpers.php` تلقائيًا ويمكن استدعاء الدوال بنجاح من أي مكان في التطبيق (مثل `php artisan tinker`)."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented global helper functions (currency, datetime, OTP) in Core module.' في `docs/CHANGELOG.md`."

---

### **TASK-ID:PH01-TASK-006** `USER-MANAGEMENT-MODULE-SETUP-001`
* **LEVEL:** `High`
* **OBJECTIVE:** إنشاء موديول `UserManagement` (`STRU-MOD-USERMANAGEMENT-001`)، وإنشاء نموذج `User` ومخططات قاعدة البيانات لجداول `users` (`DB-TBL-001`) و `nationalities` (`DB-TBL-020`)، لتوفير الأساس لإدارة المستخدمين، وهو أمر حيوي لـ [لوحة التحكم الاحترافية Dash]، [الموقع الإلكتروني الفعال]، و [تطبيق Flutter].
* **TYPE:** `Module Creation, Model Implementation, Database Migration`
* **FILE_NAME_PATH:** `Modules/UserManagement/`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-004`), `STRU-FR.md` (`STRU-MOD-USERMANAGEMENT-001`), `TS-FR.md` (مخططات `DB-TBL-001`, `DB-TBL-020`).
    * **DESIGN_REF:** لا ينطبق مباشرة.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TS-FR.md` بدقة لتفاصيل أعمدة جدولي `users` و `nationalities`. انتبه بشكل خاص للأنواع، القيود (NOT NULL, UNIQUE)، القيم الافتراضية، والمفاتيح الخارجية (خاصة `profile_photo_id` و `nationality_id` في جدول `users`)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-002 LARAVEL-MODULES-SETUP-001`, `TASK-ID:PH01-TASK-004 CORE-MODULE-BASE-CLASSES-001`.
* **LLM_ASSUMPTIONS:**
    * سيتم استخدام `bigIncrements` كـ PK.
    * نموذج `User` سيرث من `Illuminate\Foundation\Auth\User` وسيستخدم traits من `BaseModel` عند الحاجة.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_USER_MANAGEMENT_MODULE:**
        * **DETAILED_LOGIC:** "1. استخدم أمر Artisan لإنشاء موديول `UserManagement`: `php artisan module:make UserManagement`."
    * **2. CREATE_NATIONALITY_MODEL_AND_MIGRATION:**
        * **DETAILED_LOGIC:**
            * "1. أنشئ نموذج `Nationality` داخل `Modules/UserManagement/Models/Nationality.php` يرث من `Modules\Core\Models\BaseModel`."
            * "2. حدد `$fillable = ['name_ar', 'name_en', 'iso_code'];`."
            * "3. حدد `$timestamps = false;` إذا لم تكن هناك حاجة لـ `created_at`/`updated_at` في جدول الجنسيات (عادةً ما تكون بيانات ثابتة)."
            * "4. أنشئ migration لجدول `nationalities`: `php artisan module:make-migration create_nationalities_table UserManagement`."
            * "5. في ملف migration، عرّف الأعمدة كما في `TS-FR.md` (`DB-TBL-020`): `id` (PK, bigIncrements), `name_ar` (string, 100, NOT NULL, UNIQUE), `name_en` (string, 100, NULLABLE, UNIQUE), `iso_code` (string, 2, NULLABLE, UNIQUE)."
    * **3. CREATE_USER_MODEL_AND_MIGRATION:**
        * **DETAILED_LOGIC:**
            * "1. أنشئ نموذج `User` داخل `Modules/UserManagement/Models/User.php`. اجعله يرث من `Illuminate\Foundation\Auth\User`."
            * "2. أضف `use Illuminate\Notifications\Notifiable; use Illuminate\Database\Eloquent\SoftDeletes; use Illuminate\Database\Eloquent\Factories\HasFactory; use Spatie\Permission\Traits\HasRoles; use Spatie\MediaLibrary\HasMedia; use Spatie\MediaLibrary\InteractsWithMedia;`."
            * "3. استخدم traits: `HasFactory, Notifiable, SoftDeletes, HasRoles, HasMedia, InteractsWithMedia`."
            * "4. حدد `$fillable` لـ `DB-COL-U-002` إلى `DB-COL-U-006`, `DB-COL-U-009`, `DB-COL-U-010`, `DB-COL-U-011`, `DB-COL-U-013` إلى `DB-COL-U-014`, `DB-COL-U-018` إلى `DB-COL-U-022`."
            * "5. حدد `$hidden = ['password', 'remember_token', 'otp_code'];`."
            * "6. حدد `$casts` لـ: `email_verified_at` => `datetime`, `phone_verified_at` => `datetime`, `last_login_at` => `datetime`, `can_refer_customer` => `boolean`, `date_of_birth` => `date`, `otp_expires_at` => `datetime`."
            * "7. عرّف العلاقة `nationality()`: `public function nationality(): \Illuminate\Database\Eloquent\Relations\BelongsTo { return \$this->belongsTo(Nationality::class); }`."
            * "8. قم بإنشاء migration لجدول `users`: إذا كان جدول `users` الأساسي من Laravel موجودًا، قم بإنشاء migration لتعديله: `php artisan module:make-migration update_users_table_for_showroom_app UserManagement`. إذا لم يكن، أنشئ migration جديد: `php artisan module:make-migration create_users_table UserManagement`."
            * "9. في ملف migration، تأكد من وجود الأعمدة الأساسية (`id`, `name` (سنقسمه لـ `first_name`, `last_name`), `email`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`). ثم أضف/عدّل الأعمدة لتطابق `TS-FR.md` (`DB-TBL-001`):"
                * "غير `name` إلى `first_name` (string, 100) و أضف `last_name` (string, 100)."
                * "أضف `phone_number` (string, 20, NOT NULL, UNIQUE)."
                * "أضف `phone_verified_at` (timestamp, NULLABLE)."
                * "أضف `profile_photo_id` (unsignedBigInteger, NULLABLE). لا تقم بإنشاء مفتاح خارجي هنا مباشرة إلى `media` الآن؛ سيتم التعامل معه عبر Spatie لاحقًا أو إذا كان هناك قرار محدد لاستخدام مفتاح خارجي مباشر، يجب أن يكون إلى `media(id)` مع `onDelete('set null')`."
                * "أضف `status` (string, 50, NOT NULL, DEFAULT 'pending_verification')."
                * "أضف `can_refer_customer` (boolean, NOT NULL, DEFAULT false)."
                * "أضف `last_login_at` (timestamp, NULLABLE)."
                * "أضف `address_line1` (string, 255, NULLABLE)."
                * "أضف `city` (string, 100, NULLABLE)."
                * "أضف `national_id` (string, 20, NULLABLE)."
                * "أضف `date_of_birth` (date, NULLABLE)."
                * "أضف `nationality_id` (foreignIdFor Nationality::class, nullable, constrained()->onDelete('set null'))."
                * "أضف `otp_code` (string, 10, NULLABLE)."
                * "أضف `otp_expires_at` (timestamp, NULLABLE)."
                * "أضف `deleted_at` (softDeletes)."
                * "تأكد من وجود الفهارس (indexes) المطلوبة في `TS-FR.md`."
    * **5. RUN_MIGRATIONS:**
        * **DETAILED_LOGIC:** "1. قم بتشغيل `php artisan migrate`."
* **REFERENCE_CODE_SNIPPETS:** لا يوجد.
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم إنشاء موديول `UserManagement`، ونماذج `User` و `Nationality`، وملفات migration للجداول المعنية تم إنشاؤها وتشغيلها بنجاح.
* **SECURITY_CONSIDERATIONS:** تشفير كلمات المرور سيتم التعامل معه لاحقًا.
* **PERFORMANCE_CONSIDERATIONS:** الفهارس المحددة في `TS-FR.md` مهمة.
* **REQUIRED_CODE_COMMENTS:** DocBlocks للنماذج والعلاقات.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء موديول `UserManagement`."
    2. "تم إنشاء نموذج `User.php` يرث من `Illuminate\Foundation\Auth\User` ويستخدم Traits المطلوبة، مع الخصائص `$fillable`, `$hidden`, `$casts` والعلاقات الأولية."
    3. "تم إنشاء نموذج `Nationality.php` مع `$fillable`."
    4. "تم إنشاء ملفات migration لجداول `users` (أو تعديلها لتشمل جميع الحقول) و `nationalities` بشكل صحيح وتعكس جميع الأعمدة في `TS-FR.md`."
    5. "تم تشغيل الـ migrations بنجاح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created UserManagement module, User and Nationality models, and corresponding database migrations.' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: أضف مهام TODO لتثبيت وتكوين Spatie Permission و Spatie Media Library ودمجهما بشكل كامل مع نموذج `User` إذا لم تكن موجودة (مثل `TODO-SPATIE-PERMISSION-USER-INTEG-001`, `TODO-SPATIE-MEDIALIB-USER-INTEG-001`)."
    * **TS-FR.md (Internal Check for Augment):** "Augment: قم بمراجعة ذاتية للتأكد من أن جميع أعمدة `DB-TBL-001` و `DB-TBL-020` من `TS-FR.md` تم تضمينها بدقة في الـ migrations مع القيود الصحيحة."

---

### **TASK-ID:PH01-TASK-008** `SPATIE-PERMISSION-SETUP-001`
* **LEVEL:** `Medium`
* **OBJECTIVE:** تثبيت وتكوين حزمة `spatie/laravel-permission`، وإنشاء الأدوار الأولية (Super Admin, Employee, Customer) والصلاحيات الأساسية، لتوفير نظام إدارة أدوار وصلاحيات فعال لـ [لوحة التحكم الاحترافية Dash] وباقي أجزاء النظام.
* **TYPE:** `Package Installation, Configuration, Database Seeding`
* **FILE_NAME_PATH:** `composer.json`, `config/permission.php`, `Modules/UserManagement/Database/Seeders/RolesAndPermissionsSeeder.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-006`), `REQ-FR.md` (الإشارة إلى الأدوار الأولية والصلاحيات الأساسية ضمن `MOD-USER-MGMT-FEAT-010`).
    * **DESIGN_REF:** لا ينطبق مباشرة.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-USER-MGMT-FEAT-010`) لتفاصيل الأدوار الأولية المقترحة (Super Admin, Employee, Customer) والصلاحيات الأساسية (access_admin_dashboard, access_customer_dashboard). تحقق من `docs/DECISIONS.md` لأي قرارات بشأن أسماء الأدوار أو الصلاحيات الأولية المحددة."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-001 LARAVEL-PROJECT-SETUP-001`, `TASK-ID:PH01-TASK-006 USER-MANAGEMENT-MODULE-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * الأدوار الأولية هي "Super Admin", "Employee", "Customer".
    * الصلاحيات الأساسية الأولية ستكون `access_admin_dashboard`, `access_customer_dashboard`, `view_cars_admin`, `manage_cars_admin`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. INSTALL_PACKAGE:**
        * **DETAILED_LOGIC:** "1. استخدم Composer لتثبيت الحزمة: `composer require spatie/laravel-permission`."
    * **2. PUBLISH_CONFIGURATION_AND_MIGRATIONS:**
        * **DETAILED_LOGIC:** "1. قم بنشر ملف التهيئة وملفات migration: `php artisan vendor:publish --provider=\"Spatie\Permission\PermissionServiceProvider\"`."
    * **3. RUN_MIGRATIONS:**
        * **DETAILED_LOGIC:** "1. قم بتشغيل `php artisan migrate` لإنشاء جداول الأدوار والصلاحيات."
    * **4. CONFIGURE_USER_MODEL:**
        * **FILE_NAME_PATH:** `Modules/UserManagement/Models/User.php`
        * **DETAILED_LOGIC:** "1. افتح نموذج `User.php`."
        * "2. تأكد من أن `use Spatie\Permission\Traits\HasRoles;` موجود وأن trait `HasRoles` مستخدم داخل الكلاس."
    * **5. CREATE_INITIAL_ROLES_AND_PERMISSIONS_SEEDER:**
        * **FILE_NAME_PATH:** `Modules/UserManagement/Database/Seeders/RolesAndPermissionsSeeder.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ `RolesAndPermissionsSeeder.php` داخل `Modules/UserManagement/Database/Seeders/`."
            * "2. في دالة `run()`: "
                * "أضف `app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();` في البداية."
                * "قم بإنشاء الصلاحيات الأولية (`guard_name` يجب أن يكون `'web'`):"
                    * `Permission::firstOrCreate(['name' => 'access_admin_dashboard', 'guard_name' => 'web']);`
                    * `Permission::firstOrCreate(['name' => 'access_customer_dashboard', 'guard_name' => 'web']);`
                    * `Permission::firstOrCreate(['name' => 'view_cars_admin', 'guard_name' => 'web']);`
                    * `Permission::firstOrCreate(['name' => 'manage_cars_admin', 'guard_name' => 'web']);`
                * "قم بإنشاء الأدوار الأولية (`guard_name` يجب أن يكون `'web'`):"
                    * `$superAdminRole = Role::firstOrCreate(['name' => 'Super Admin', 'guard_name' => 'web']);`
                    * `$employeeRole = Role::firstOrCreate(['name' => 'Employee', 'guard_name' => 'web']);`
                    * `$customerRole = Role::firstOrCreate(['name' => 'Customer', 'guard_name' => 'web']);`
                * "قم بتعيين جميع الصلاحيات لدور 'Super Admin': `$superAdminRole->syncPermissions(Permission::all());`"
                * "قم بتعيين صلاحيات محددة لدور 'Employee': `$employeeRole->syncPermissions(['access_admin_dashboard', 'view_cars_admin']);`"
                * "قم بتعيين صلاحية `access_customer_dashboard` لدور 'Customer': `$customerRole->syncPermissions(['access_customer_dashboard']);`"
    * **6. REGISTER_AND_RUN_SEEDER:**
        * **DETAILED_LOGIC:** "1. في `Modules/UserManagement/Database/Seeders/UserManagementDatabaseSeeder.php` (أنشئه إذا لم يكن موجودًا)، استدع `\$this->call(RolesAndPermissionsSeeder::class);`."
        * "2. في `database/seeders/DatabaseSeeder.php` الرئيسي، استدع `\$this->call(\Modules\UserManagement\Database\Seeders\UserManagementDatabaseSeeder::class);`."
        * "3. قم بتشغيل `php artisan db:seed`."
* **REFERENCE_CODE_SNIPPETS:** لا يوجد.
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم تثبيت وتكوين حزمة `spatie/laravel-permission`، وتم إنشاء الأدوار والصلاحيات الأولية في قاعدة البيانات.
* **SECURITY_CONSIDERATIONS:** هذا هو أساس نظام التحكم في الوصول.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد تأثير كبير في هذه المرحلة.
* **REQUIRED_CODE_COMMENTS:** تعليقات في Seeder توضح الغرض من كل دور وصلاحية.
* **ACCEPTANCE_CRITERIA:**
    1. "تم تثبيت حزمة `spatie/laravel-permission` وتشغيل migrations الخاصة بها بنجاح."
    2. "نموذج `User` في `Modules/UserManagement/Models/User.php` يستخدم `HasRoles` trait."
    3. "تم إنشاء الأدوار الأولية ('Super Admin', 'Employee', 'Customer') والصلاحيات المحددة في قاعدة البيانات."
    4. "تم تعيين الصلاحيات المناسبة للأدوار الأولية."
    5. "تم إنشاء وتشغيل Seeder مخصص لتعبئة الأدوار والصلاحيات بنجاح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Installed and configured Spatie Laravel Permission. Seeded initial roles (Super Admin, Employee, Customer) and basic permissions (access_admin_dashboard, access_customer_dashboard, view_cars_admin, manage_cars_admin).' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: أضف مهمة `TODO-CREATE-DEFAULT-ADMIN-USER-001` لإنشاء مستخدم Super Admin افتراضي إذا لم يتم ذلك، وتعيين دور 'Super Admin' له."
    * **DECISIONS.md:** "Augment: وثق قائمة الأدوار والصلاحيات الأولية التي تم إنشاؤها كـ `DECISION-INITIAL-ROLES-PERMS-001` في `docs/DECISIONS.md`."

---

### **TASK-ID:PH01-TASK-008A** `SPATIE-PERMISSION-DEFAULT-ADMIN-USER-001`
* **LEVEL:** `Low`
* **OBJECTIVE:** إنشاء مستخدم مدير نظام افتراضي (Super Admin) وتعيين دور "Super Admin" له كجزء من عملية `seed`، لتمكين اختبار وظائف المصادقة والوصول إلى [لوحة التحكم الاحترافية Dash].
* **TYPE:** `Database Seeding`
* **FILE_NAME_PATH:** `Modules/UserManagement/Database/Seeders/RolesAndPermissionsSeeder.php` (أو Seeder منفصل للمستخدمين الافتراضيين).
* **PRIMARY_INPUTS:**
    * **TASK_REF:** ضمني لـ `PH-01-DEL-005` و `PH-01-DEL-006`.
    * **DESIGN_REF:** لا ينطبق.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: تحقق مما إذا كان هناك أي قرار مسبق بشأن بيانات اعتماد المدير الافتراضي في `docs/DECISIONS.md`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-008 SPATIE-PERMISSION-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * سيتم استخدام بيانات اعتماد افتراضية بسيطة (مثل `<EMAIL>` / `password`) مع ملاحظة لتغييرها فورًا.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_DEFAULT_SUPER_ADMIN:**
        * **DETAILED_LOGIC:** "1. في `RolesAndPermissionsSeeder.php` (أو Seeder مخصص للمستخدمين يتم استدعاؤه بعد `RolesAndPermissionsSeeder`)، قم بإنشاء مستخدم جديد:"
          ```php
          \$superAdminUser = \Modules\UserManagement\Models\User::firstOrCreate(
              ['email' => '<EMAIL>'],
              [
                  'first_name' => 'Super',
                  'last_name' => 'Admin',
                  'phone_number' => '0500000000', // Placeholder, ensure unique if constraint is strict
                  'password' => bcrypt('password'), // IMPORTANT: Change this in production
                  'email_verified_at' => now(),
                  'phone_verified_at' => now(),
                  'status' => 'active',
              ]
          );
          \$superAdminUser->assignRole('Super Admin');
          ```
        * "2. تأكد من أن هذا الـ Seeder يتم تشغيله كجزء من `php artisan db:seed`."
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم إنشاء مستخدم Super Admin افتراضي في قاعدة البيانات مع الدور المناسب.
* **SECURITY_CONSIDERATIONS:** يجب تغيير كلمة المرور الافتراضية فورًا بعد الإعداد الأول.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** تعليق حول ضرورة تغيير كلمة المرور الافتراضية.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء مستخدم Super Admin افتراضي ببيانات اعتماد محددة."
    2. "تم تعيين دور 'Super Admin' لهذا المستخدم."
    3. "يمكن تسجيل الدخول بهذا المستخدم إلى لوحة التحكم (بعد اكتمال `PH01-TASK-007`)."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created default Super Admin user via seeder.'."
    * **DECISIONS.md:** "Augment: وثق بيانات اعتماد المدير الافتراضي المستخدمة كـ `DECISION-DEFAULT-ADMIN-CREDENTIALS-001` مع ملاحظة حول ضرورة تغييرها."

---

### **TASK-ID:PH01-TASK-009** `DASH-LAYOUT-SETUP-001`
* **LEVEL:** `High`
* **OBJECTIVE:** إعداد هيكل Blade الأساسي (`admin_layout.blade.php`) للوحة تحكم الإدارة Dash بناءً على `Dash/index.html`، مع ربط أصول `Dash/style.css` و `Dash/script.js`. **سيتم وضع هذا التخطيط ضمن موديول `Dashboard`**. يخدم هذا بشكل مباشر بناء [لوحة التحكم الاحترافية Dash].
* **TYPE:** `Dash Asset Integration (Blade Layout)`
* **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/layouts/admin_layout.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-007`), `STRU-FR.md` (`STRU-LARAVEL-VIEWS-ADMIN-001` يشير إلى `resources/views/admin/layouts` ولكن القرار المحدث هو وضعه في موديول `Dashboard`, `STRU-DASH-ASSETS-ROOT-001`), `UIUX-FR.md` (القسم 3.9).
    * **DESIGN_REF:** ملف `Dash/index.html` هو المرجع الأساسي للهيكل. ملفات `Dash/style.css` و `Dash/script.js` هي الأصول التي سيتم ربطها.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `Dash/index.html` بدقة لتحديد الأقسام الرئيسية (head, body, div.sidebar, nav.top-navbar, div.main-content, footer, script includes). تحقق من `STRU-FR.md` لتحديد المسار الدقيق لمجلد `Dash/` في جذر المشروع. الأصول سيتم نسخها مؤقتًا إلى `public/vendor/dash/` في هذه المهمة."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **DECISIONS.md:** "Augment: وثق القرار `DECISION-DASH-LAYOUT-LOCATION-001`: 'سيتم وضع تخطيط لوحة التحكم الإدارية الرئيسي (`admin_layout.blade.php`) والـ partials المرتبطة به ضمن `Modules/Dashboard/Resources/views/layouts/`'."
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-001 LARAVEL-PROJECT-SETUP-001`, (سيتم إنشاء موديول `Dashboard` إذا لم يكن موجودًا كجزء من هذه المهمة أو `PH01-TASK-013`).
* **LLM_ASSUMPTIONS:**
    * مجلد `Dash/` موجود في جذر المشروع.
    * سيتم نسخ الأصول إلى `public/vendor/dash/` مؤقتًا.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. ENSURE_DASHBOARD_MODULE_EXISTS:**
        * **DETAILED_LOGIC:** "1. تحقق من وجود موديول `Dashboard`. إذا لم يكن موجودًا، قم بإنشائه: `php artisan module:make Dashboard`."
    * **2. COPY_DASH_ASSETS_TO_PUBLIC_VENDOR:**
        * **DETAILED_LOGIC:** "1. قم بإنشاء المجلد `public/vendor/dash/`."
        * "2. انسخ `Dash/style.css` إلى `public/vendor/dash/style.css`."
        * "3. انسخ `Dash/script.js` إلى `public/vendor/dash/script.js`."
        * "4. انسخ أي مجلدات أصول فرعية ضرورية (مثل `img`, `fonts` التي تستخدمها `style.css`) من `Dash/` إلى `public/vendor/dash/` مع الحفاظ على هيكلها."
    * **3. CREATE_ADMIN_LAYOUT_BLADE_FILE:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/layouts/admin_layout.blade.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ المجلدات `Modules/Dashboard/Resources/views/layouts/partials/`."
            * "2. أنشئ ملف `admin_layout.blade.php` في `Modules/Dashboard/Resources/views/layouts/`."
            * "3. ابدأ بهيكل HTML5: `<!DOCTYPE html><html lang=\"ar\" dir=\"rtl\"><head>...</head><body>...</body></html>`."
            * "4. في `<head>`:"
                * "أضف meta tags: `<meta charset=\"UTF-8\">`, `<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">`."
                * "أضف `<title>@yield('title', config('app.name', 'Laravel') . ' - لوحة التحكم')</title>`."
                * "اربط CSS: Bootstrap RTL (CDN), Font Awesome (CDN), Google Fonts (IBM Plex Sans Arabic - CDN), `{{ asset('vendor/dash/style.css') }}`, `bs-stepper.min.css` (CDN)."
                * "أضف `@stack('styles')`."
            * "5. في `<body>`:"
                * "`<div class=\"sidebar-overlay\" id=\"sidebar-overlay\"></div>`."
                * "`@include('dashboard::layouts.partials._sidebar')`"
                * "`<div class=\"main-content\" id=\"main-content\">`"
                * "`    @include('dashboard::layouts.partials._topbar')`"
                * "`    <div class=\"container-fluid\">@yield('content')</div>`"
                * "`    @include('dashboard::layouts.partials._footer')`"
                * "`</div>`"
            * "6. في نهاية `<body>`، اربط JS: Bootstrap bundle (CDN), Chart.js (CDN), `{{ asset('vendor/dash/script.js') }}`, `bs-stepper.min.js` (CDN)."
                * "أضف `@stack('scripts')`."
* **REFERENCE_CODE_SNIPPETS:** (مشابه لما ورد في الرد السابق، مع تعديل مسارات `@include` لتستخدم `dashboard::` namespace).
* **EXPECTED_OUTPUTS_BEHAVIOR:** ملف تخطيط Blade رئيسي (`admin_layout.blade.php`) تم إنشاؤه في موديول `Dashboard`، يعكس هيكل `Dash/index.html` ويربط بأصول Dash المؤقتة.
* **SECURITY_CONSIDERATIONS:** استخدام نسخ آمنة من CDN.
* **PERFORMANCE_CONSIDERATIONS:** الربط المباشر للأصول حاليًا.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء موديول `Dashboard` إذا لم يكن موجودًا."
    2. "تم نسخ أصول Dash (`style.css`, `script.js`, والمجلدات الفرعية الضرورية) إلى `public/vendor/dash/`."
    3. "تم إنشاء ملف `admin_layout.blade.php` في `Modules/Dashboard/Resources/views/layouts/`."
    4. "الهيكل العام للملف يطابق الأقسام الرئيسية في `Dash/index.html`."
    5. "يتم ربط ملفات CSS و JS من CDN ومن `public/vendor/dash/` بشكل صحيح."
    6. "يتم استخدام `@yield` و `@include` مع الـ namespace الصحيح للموديول (e.g., `dashboard::layouts.partials._sidebar`)."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created base Blade layout (admin_layout.blade.php) for Dash admin panel within Dashboard module, incorporating structure and asset links from Dash/index.html. Copied Dash assets to public/vendor/dash/ as a temporary measure.' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: أضف/تأكد من وجود مهمة `TODO-DASH-ASSETS-VITE-INTEGRATION-001` لتكوين Laravel Vite/Mix لمعالجة أصول Dash وتحديث روابط الأصول في `dashboard::layouts.admin_layout`."
    * **DECISIONS.md:** "Augment: تم تأكيد قرار وضع التخطيط الرئيسي للوحة التحكم (`admin_layout.blade.php`) والـ partials المرتبطة به ضمن `Modules/Dashboard/Resources/views/layouts/` بناءً على `DECISION-DASH-LAYOUT-LOCATION-001`."

---

### **TASK-ID:PH01-TASK-010** `DASH-LAYOUT-PARTIALS-SETUP-001`
* **LEVEL:** `High`
* **OBJECTIVE:** إنشاء مكونات Blade أولية (`_sidebar.blade.php`, `_topbar.blade.php`, `_footer.blade.php`) للشريط العلوي والقائمة الجانبية والتذييل للوحة تحكم الإدارة Dash، مدمجة في `admin_layout.blade.php` ضمن موديول `Dashboard`. هذه الـ partials ستستخدم HTML الثابت من `Dash/index.html` كمرحلة أولى. يخدم هذا بناء [لوحة التحكم الاحترافية Dash].
* **TYPE:** `Dash Asset Integration (Blade Partials)`
* **FILE_NAME_PATH:**
    * `Modules/Dashboard/Resources/views/layouts/partials/_sidebar.blade.php`
    * `Modules/Dashboard/Resources/views/layouts/partials/_topbar.blade.php`
    * `Modules/Dashboard/Resources/views/layouts/partials/_footer.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-008`), `STRU-FR.md` (مسارات الـ partials ضمن موديول `Dashboard`).
    * **DESIGN_REF:** ملف `Dash/index.html` لتحديد هيكل ومحتوى هذه الأقسام.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: قم بتحليل `Dash/index.html` بدقة. استخرج HTML الخاص بالـ `div.sidebar` بالكامل (بما في ذلك `sidebar-header` وكل `ul.nav`). استخرج HTML الخاص بالـ `nav.top-navbar` بالكامل. استخرج HTML الخاص بالـ `footer.footer` الموجود داخل `div.main-content`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-009 DASH-LAYOUT-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * `admin_layout.blade.php` تم إنشاؤه في `Modules/Dashboard/Resources/views/layouts/` ويتضمن استدعاءات `@include` لهذه الـ partials باستخدام `dashboard::layouts.partials.*`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_SIDEBAR_PARTIAL:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/layouts/partials/_sidebar.blade.php`
        * **DETAILED_LOGIC:** "1. أنشئ الملف. 2. انسخ قسم الـ HTML الكامل المقابل للـ sidebar من `Dash/index.html` (عادةً `<div class=\"sidebar\" id=\"sidebar\">...</div>`) والصقه في هذا الملف. تأكد من أن المعرفات (IDs) مثل `id=\"sidebar\"` يتم الاحتفاظ بها أو تعديلها إذا كان الـ Layout الرئيسي يتعامل معها بشكل مختلف. للاستخدام كـ partial، قد لا تحتاج إلى الـ div الخارجي `id=\"sidebar\"` إذا كان موجودًا بالفعل في `admin_layout.blade.php`."
    * **2. CREATE_TOPBAR_PARTIAL:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/layouts/partials/_topbar.blade.php`
        * **DETAILED_LOGIC:** "1. أنشئ الملف. 2. انسخ قسم الـ HTML الكامل المقابل للـ top navbar من `Dash/index.html` (عادةً `<nav class=\"navbar top-navbar\">...</nav>`) والصقه في هذا الملف."
    * **3. CREATE_FOOTER_PARTIAL:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/layouts/partials/_footer.blade.php`
        * **DETAILED_LOGIC:** "1. أنشئ الملف. 2. انسخ قسم الـ HTML الكامل المقابل للـ footer (عادةً `<footer class=\"footer ...\">...</footer>`) الموجود داخل `div.main-content` في `Dash/index.html` والصقه في هذا الملف."
* **REFERENCE_CODE_SNIPPETS:** لا يوجد (سيكون نسخ HTML مباشر من `Dash/index.html` لكل ملف).
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم إنشاء ملفات partials تحتوي على HTML الثابت المنسوخ من `Dash/index.html` ضمن موديول `Dashboard`. عند عرض صفحة تستخدم `dashboard::layouts.admin_layout`، يجب أن يظهر هيكل القائمة الجانبية والشريط العلوي والتذييل بشكل صحيح.
* **SECURITY_CONSIDERATIONS:** لا يوجد.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** تعليقات لتحديد بداية ونهاية كل partial.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء ملفات `_sidebar.blade.php`, `_topbar.blade.php`, و `_footer.blade.php` في المسار `Modules/Dashboard/Resources/views/layouts/partials/`."
    2. "كل ملف partial يحتوي على HTML المنسوخ بدقة من القسم المقابل في `Dash/index.html`."
    3. "يتم تضمين هذه الـ partials بشكل صحيح في `dashboard::layouts.admin_layout`."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created static Blade partials (_sidebar, _topbar, _footer) for Dash admin layout within Dashboard module, populated from Dash/index.html.' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: أضف مهام `TODO-DASH-SIDEBAR-DYNAMIC-001` و `TODO-DASH-TOPBAR-DYNAMIC-001` لجعل القائمة الجانبية والشريط العلوي ديناميكيين. قم بالإشارة إلى `TASK-ID:PH02-ITEM-002` من `UIR-FR.md`."

---

### **TASK-ID:PH01-TASK-011** `DASH-ADMIN-LOGIN-VIEW-001`
* **LEVEL:** `Medium`
* **OBJECTIVE:** إنشاء واجهة Blade (`login.blade.php`) لصفحة تسجيل الدخول للوحة تحكم الإدارة Dash، بناءً على تصميم `DASH-ADMIN-LOGIN-001` من `UIUX-FR.md`. تخدم هذه الواجهة [لوحة التحكم الاحترافية Dash].
* **TYPE:** `Dash Custom Blade View Creation (Login Page)`
* **FILE_NAME_PATH:** `Modules/UserManagement/Resources/views/admin/auth/login.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-009`), `UIUX-FR.md` (`DASH-ADMIN-LOGIN-001`).
    * **DESIGN_REF:** `UIUX-FR.md` (`DASH-ADMIN-LOGIN-001`) لوصف الهيكل السلكي النصي وتطبيق ألوان وخطوط Dash المحددة في `UIUX-FR.md` (الأقسام 3.2 و 3.3).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `UIUX-FR.md` (`DASH-ADMIN-LOGIN-001`) بدقة لتفاصيل تصميم صفحة تسجيل الدخول. تأكد من تطبيق نظام الألوان والخطوط المعتمد للمشروع كما هو محدد في `UIUX-FR.md`، واستخدام أيقونات Font Awesome المحددة."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-007 ADMIN-AUTH-IMPLEMENTATION-001` (لوجود Controller يعرض هذا الـ view والمسار إليه).
* **LLM_ASSUMPTIONS:**
    * تصميم `DASH-ADMIN-LOGIN-001` في `UIUX-FR.md` يوفر تفاصيل كافية.
    * صفحة تسجيل الدخول هذه لن تستخدم التخطيط الكامل للوحة التحكم (`dashboard::layouts.admin_layout`) بل ستكون صفحة مستقلة بتصميم أبسط ولكن متوافق مع هوية Dash.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_LOGIN_BLADE_FILE:**
        * **FILE_NAME_PATH:** `Modules/UserManagement/Resources/views/admin/auth/login.blade.php`
        * **DETAILED_LOGIC:** "1. أنشئ الملف."
    * **2. IMPLEMENT_LOGIN_PAGE_STRUCTURE:**
        * **DETAILED_LOGIC:**
            * "1. ابدأ بهيكل HTML5 بسيط."
            * "2. في `<head>`، قم بتضمين روابط CSS: Bootstrap RTL (CDN), Font Awesome (CDN), Google Fonts (IBM Plex Sans Arabic - CDN), وملف `{{ asset('vendor/dash/style.css') }}` إذا كانت هناك أنماط عامة من Dash ترغب في تطبيقها على هذه الصفحة أيضًا، أو أنشئ ملف CSS مخصص بسيط (`dash_auth_style.css`) لتطبيق ألوان وخطوط Dash المحددة في `UIUX-FR.md`."
            * "3. قم ببناء هيكل الصفحة كما هو موصف في `UIUX-FR.md` (`DASH-ADMIN-LOGIN-001`): حاوية مركزية (e.g., `div.container > div.row.justify-content-center > div.col-md-6.col-lg-4`), شعار المعرض، عنوان 'تسجيل الدخول إلى لوحة التحكم الإدارية'."
            * "4. نموذج تسجيل الدخول (`<form method=\"POST\" action=\"{{ route('admin.login.store') }}\">`):"
                * "`@csrf` token."
                * "حقل 'البريد الإلكتروني أو رقم الجوال' (input `name='identifier'`)."
                * "حقل 'كلمة المرور' (input `name='password'`)."
                * "خيار 'تذكرني' (checkbox `name='remember'`)."
                * "رابط 'نسيت كلمة المرور؟' (يشير إلى `route('admin.password.request')`)."
                * "زر 'تسجيل الدخول' (type='submit')."
            * "5. طبق الأنماط (الألوان، الخطوط، الأيقونات) كما هو محدد في `UIUX-FR.md` (`DASH-ADMIN-LOGIN-001`)."
            * "6. قم بعرض أخطاء التحقق من الصحة وأي رسائل خطأ عامة للمصادقة."
* **REFERENCE_CODE_SNIPPETS:** (يمكن الرجوع إلى المقتطف في الرد السابق لهذه المهمة، مع التأكيد على تطبيق الأنماط البصرية من `UIUX-FR.md`).
* **EXPECTED_OUTPUTS_BEHAVIOR:** يتم عرض صفحة تسجيل دخول وظيفية للوحة تحكم الإدارة Dash، متوافقة بصريًا مع هوية Dash المحددة في `UIUX-FR.md`.
* **SECURITY_CONSIDERATIONS:** استخدام `@csrf`.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء ملف `login.blade.php` في `Modules/UserManagement/Resources/views/admin/auth/`."
    2. "الصفحة تعرض نموذج تسجيل الدخول بالحقول، الأيقونات، والترتيب كما هو محدد في `UIUX-FR.md` (`DASH-ADMIN-LOGIN-001`)."
    3. "تم تطبيق الألوان والخطوط المعتمدة للمشروع (من `UIUX-FR.md`) على الصفحة."
    4. "يتم عرض رسائل الخطأ بشكل صحيح."
    5. "النموذج يرسل البيانات إلى المسار الصحيح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created admin dashboard login page Blade view (DASH-ADMIN-LOGIN-001) with specified UI/UX styling.' في `docs/CHANGELOG.md`."

---


### **TASK-ID:PH01-TASK-007** `ADMIN-AUTH-IMPLEMENTATION-001`
* **LEVEL:** `High`
* **OBJECTIVE:** تنفيذ وظائف المصادقة الأساسية للموظفين/المديرين للوصول إلى لوحة تحكم Dash المخصصة (تسجيل الدخول، استعادة كلمة المرور عبر الويب)، وذلك ضمن موديول `UserManagement`. يخدم هذا بشكل مباشر أمان ووصول [لوحة التحكم الاحترافية Dash].
* **TYPE:** `Laravel Authentication Logic (Controllers, Routes for Web/Dash)`
* **FILE_NAME_PATH:**
    * `Modules/UserManagement/Http/Controllers/Admin/AuthenticatedSessionController.php`
    * `Modules/UserManagement/Http/Controllers/Admin/PasswordResetLinkController.php`
    * `Modules/UserManagement/Http/Controllers/Admin/NewPasswordController.php`
    * `Modules/UserManagement/Http/Requests/Admin/LoginRequest.php`
    * `Modules/UserManagement/Routes/admin_auth.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-005`), `REQ-FR.md` (`MOD-USER-MGMT-FEAT-002`, `MOD-USER-MGMT-FEAT-003` - الجزء الخاص بالموظفين/المديرين).
    * **DESIGN_REF:** `UIUX-FR.md` (`DASH-ADMIN-LOGIN-001` لصفحة تسجيل الدخول).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` لفهم متطلبات تسجيل الدخول (البريد أو رقم الجوال) واستعادة كلمة المرور للمديرين. ارجع إلى `docs/UIUX-FR.md` (`DASH-ADMIN-LOGIN-001`) للتأكد من أن وحدات التحكم ستدعم الواجهة المصممة. تحقق من `docs/DECISIONS.md` لأي قرارات تخص مسارات أو حراس المصادقة، وتحديد أن `identifier` هو اسم الحقل لتسجيل الدخول."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-006 USER-MANAGEMENT-MODULE-SETUP-001`, `TASK-ID:PH01-TASK-011 DASH-ADMIN-LOGIN-VIEW-001` (يجب أن يكون view تسجيل الدخول جاهزًا).
* **LLM_ASSUMPTIONS:**
    * سيتم استخدام مصادقة الويب (Session-based) مع الحارس `web`.
    * الموظفون والمديرون هم جزء من جدول `users`.
    * سيتم بناء وحدات التحكم والمسارات بشكل مخصص داخل الموديول، مستلهمة من بنية Laravel Breeze/Fortify.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_ADMIN_LOGIN_REQUEST:**
        * **FILE_NAME_PATH:** `Modules/UserManagement/Http/Requests/Admin/LoginRequest.php`
        * **DETAILED_LOGIC:** "1. أنشئ `LoginRequest.php` يرث من `Modules\Core\Http\Requests\BaseRequest`."
        * "2. في دالة `rules()`, عرف قواعد التحقق: `['identifier' => ['required', 'string'], 'password' => ['required', 'string']]`."
        * "3. أضف دالة `authenticate()` كما في Laravel Breeze (أو ما يعادلها):
          ```php
          public function authenticate(): void
          {
              \$this->ensureIsNotRateLimited();
              // Attempt to authenticate with email
              if (!Auth::attempt(\$this->only('email', 'password'), \$this->boolean('remember'))) {
                  // If email fails, attempt with phone_number
                  if (!Auth::attempt(['phone_number' => \$this->input('identifier'), 'password' => \$this->input('password')], \$this->boolean('remember'))) {
                      RateLimiter::hit(\$this->throttleKey());
                      throw ValidationException::withMessages([
                          'identifier' => __('auth.failed'),
                      ]);
                  }
              }
              // Check if the authenticated user has one of the required roles
              if (!Auth::user()->hasAnyRole(['Super Admin', 'Employee'])) {
                  Auth::logout(); // Log out if not authorized
                  RateLimiter::hit(\$this->throttleKey());
                  throw ValidationException::withMessages([
                      'identifier' => __('auth.failed'), // Or a more specific message like 'Unauthorized access.'
                  ]);
              }
              RateLimiter::clear(\$this->throttleKey());
          }
          public function throttleKey(): string { return Str::transliterate(Str::lower(\$this->input('identifier')).'|'.\$this->ip()); }
          ```
          (تذكر إضافة `use Illuminate\Support\Facades\Auth; use Illuminate\Support\Facades\RateLimiter; use Illuminate\Support\Str; use Illuminate\Validation\ValidationException;`)"
    * **2. CREATE_ADMIN_AUTHENTICATED_SESSION_CONTROLLER:**
        * **FILE_NAME_PATH:** `Modules/UserManagement/Http/Controllers/Admin/AuthenticatedSessionController.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ `AuthenticatedSessionController` يرث من `Modules\Core\Http\Controllers\BaseController`."
            * "2. دالة `create(): \Illuminate\View\View` لعرض `usermanagement::admin.auth.login`."
            * "3. دالة `store(LoginRequest $request): \Illuminate\Http\RedirectResponse`:"
                * "`\$request->authenticate();`"
                * "`\$request->session()->regenerate();`"
                * "`return redirect()->intended(route('admin.dashboard'));`"
            * "4. دالة `destroy(Request $request): \Illuminate\Http\RedirectResponse`:"
                * "`Auth::guard('web')->logout();`"
                * "`\$request->session()->invalidate();`"
                * "`\$request->session()->regenerateToken();`"
                * "`return redirect(route('admin.login'));`"
    * **3. CREATE_ADMIN_PASSWORD_RESET_CONTROLLERS:**
        * **FILE_NAME_PATHs:**
            * `Modules/UserManagement/Http/Controllers/Admin/PasswordResetLinkController.php`
            * `Modules/UserManagement/Http/Controllers/Admin/NewPasswordController.php`
        * **DETAILED_LOGIC:** "1. أنشئ هذه وحدات التحكم بشكل مشابه لوحدات التحكم المقابلة في Laravel Breeze/Fortify، مع تعديل مسارات الـ views لتشير إلى واجهات لوحة تحكم Dash (التي سيتم إنشاؤها لاحقًا إذا لم تكن موجودة) واستخدام `PasswordBroker` المناسب."
    * **4. DEFINE_ADMIN_AUTH_ROUTES:**
        * **FILE_NAME_PATH:** `Modules/UserManagement/Routes/admin_auth.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ ملف `admin_auth.php`."
            * "2. قم بتعريف المسارات داخل `Route::prefix(config('modules.AppConfig.admin_prefix', 'admin').'/auth')->name('admin.')->group(...)` (استخدم `config('modules.AppConfig.admin_prefix', 'admin')` إذا تم تعريف prefix عام للمسارات الإدارية في ملف تهيئة مخصص، وإلا استخدم `admin` مباشرة):"
                * `Route::get('login', [AuthenticatedSessionController::class, 'create'])->middleware('guest:web')->name('login');`
                * `Route::post('login', [AuthenticatedSessionController::class, 'store'])->middleware('guest:web')->name('login.store');`
                * `Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])->middleware('auth:web')->name('logout');`
                * `Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])->middleware('guest:web')->name('password.request');`
                * `Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])->middleware('guest:web')->name('password.email');`
                * `Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])->middleware('guest:web')->name('password.reset');`
                * `Route::post('reset-password', [NewPasswordController::class, 'store'])->middleware('guest:web')->name('password.update');` // تم تغيير الاسم ليكون متسقًا
            * "3. في `Modules/UserManagement/Providers/RouteServiceProvider.php`, قم بتحميل هذا الملف. مثال لمحتوى `mapAdminAuthRoutes` (أو اسم مشابه):
              ```php
              protected function mapAdminAuthRoutes()
              {
                  Route::middleware('web') // Or specific admin middleware group
                       ->group(module_path('UserManagement', '/Routes/admin_auth.php'));
              }
              ```
              وتأكد من استدعاء `\$this->mapAdminAuthRoutes();` ضمن دالة `map()`."
* **REFERENCE_CODE_SNIPPETS:** (مقتطفات من هياكل وحدات التحكم والمسارات كما هو موضح).
* **EXPECTED_OUTPUTS_BEHAVIOR:** يمكن للمديرين/الموظفين ذوي الأدوار الصحيحة تسجيل الدخول والخروج من لوحة تحكم Dash، واستعادة كلمات المرور الخاصة بهم.
* **SECURITY_CONSIDERATIONS:** تطبيق Rate Limiting على محاولات تسجيل الدخول. استخدام CSRF protection. تأمين عملية استعادة كلمة المرور. التحقق من دور المستخدم بعد المصادقة.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد تأثير كبير.
* **REQUIRED_CODE_COMMENTS:** DocBlocks للدوال والمسارات الهامة.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء وحدات التحكم الخاصة بمصادقة لوحة التحكم Dash (`AuthenticatedSessionController`, `PasswordResetLinkController`, `NewPasswordController`) في موديول `UserManagement`."
    2. "تم إنشاء `LoginRequest.php` مع منطق `authenticate()` للتحقق من البريد/الجوال والدور."
    3. "تم تعريف المسارات اللازمة في `Modules/UserManagement/Routes/admin_auth.php` وتم تحميلها بشكل صحيح، وهي مسبوقة بـ `/admin/auth` (أو prefix مشابه)."
    4. "يمكن للمستخدم الوصول إلى صفحة تسجيل الدخول للوحة التحكم الإدارية."
    5. "تسجيل الدخول الناجح (لمستخدم بدور 'Super Admin' أو 'Employee') يوجه إلى `route('admin.dashboard')`."
    6. "محاولة تسجيل الدخول بمستخدم ليس له دور 'Super Admin' أو 'Employee' تفشل مع رسالة مناسبة."
    7. "تسجيل الخروج يعمل ويوجه إلى صفحة تسجيل الدخول الإدارية."
    8. "عملية استعادة كلمة المرور (طلب الرابط وإعادة التعيين) تعمل بشكل كامل للوحة التحكم الإدارية."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented admin dashboard authentication (login, logout, password reset) with role check (Super Admin, Employee) within UserManagement module.' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: أضف مهام `TODO-DASH-FORGOT-PASS-VIEW-001` و `TODO-DASH-RESET-PASS-VIEW-001` لإنشاء واجهات Blade لصفحات 'طلب إعادة تعيين كلمة المرور' و 'إعادة تعيين كلمة المرور الجديدة' للوحة التحكم Dash إذا لم تكن مغطاة."

---

### **TASK-ID:PH01-TASK-012** `CORE-MODULE-SETTINGS-DB-SERVICE-001`
* **LEVEL:** `High`
* **OBJECTIVE:** إعداد نظام إدارة الإعدادات (`MOD-CORE-FEAT-003`) في موديول `Core`، بما في ذلك مخطط قاعدة البيانات (`DB-TBL-CORE-001`) ونموذج Eloquent، بالإضافة إلى Service ودالة مساعدة (helper) للوصول إلى الإعدادات وتعديلها برمجيًا، مع دعم التخزين المؤقت (Caching). يخدم هذا [لوحة التحكم الاحترافية Dash] (لإدارة الإعدادات لاحقًا) و [الموقع الإلكتروني الفعال] و [تطبيق Flutter] (لاستهلاك الإعدادات).
* **TYPE:** `Database Migration, Eloquent Model, PHP Service Implementation, Helper Function`
* **FILE_NAME_PATH:**
    * `Modules/Core/Database/Migrations/xxxx_xx_xx_xxxxxx_create_settings_table.php`
    * `Modules/Core/Models/Setting.php`
    * `Modules/Core/Services/SettingsService.php`
    * `Modules/Core/Helpers/settings_helper.php` (أو يُدمج في `helpers.php` العام)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-010`), `REQ-FR.md` (`MOD-CORE-FEAT-003`), `TS-FR.md` (`DB-TBL-CORE-001`).
    * **DESIGN_REF:** لا ينطبق.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `TS-FR.md` (`DB-TBL-CORE-001`) بدقة لأعمدة جدول `settings` وأنواعها والقيود. انتبه بشكل خاص إلى `key` (unique), `value` (text), `group_name`, `display_name`, `type`, `options` (json), `is_translatable`. تأكد من فهم آلية الوصول المقترحة (`setting('key', 'default')`) من `REQ-FR.md`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-003 CORE-MODULE-CREATION-001`.
* **LLM_ASSUMPTIONS:**
    * سيتم إنشاء نموذج Eloquent (`Setting.php`).
    * دالة المساعدة `setting()` ستكون الطريقة المفضلة للوصول إلى الإعدادات.
    * سيتم تجاهل `is_translatable` في هذه المرحلة الأولية للخدمة والمساعد، مع التركيز على جلب `value` مباشرة.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_SETTINGS_MIGRATION:**
        * **DETAILED_LOGIC:** "1. قم بإنشاء migration لجدول `settings` داخل موديول `Core`: `php artisan module:make-migration create_settings_table Core`."
        * "2. قم بتعديل ملف migration ليشمل جميع الأعمدة والقيود كما هو محدد في `TS-FR.md` (`DB-TBL-CORE-001`): `id`, `key` (string, 255, unique), `value` (text, nullable), `group_name` (string, 100, nullable, index), `display_name` (string, 255, nullable), `type` (string, 50, default: 'text'), `options` (json, nullable), `is_translatable` (boolean, default: false), `timestamps()`."
    * **2. CREATE_SETTING_MODEL:**
        * **FILE_NAME_PATH:** `Modules/Core/Models/Setting.php`
        * **DETAILED_LOGIC:** "1. أنشئ نموذج `Setting` يرث من `Modules\Core\Models\BaseModel`."
        * "2. حدد `$fillable = ['key', 'value', 'group_name', 'display_name', 'type', 'options', 'is_translatable'];`."
        * "3. حدد `$casts = ['options' => 'array', 'is_translatable' => 'boolean'];`."
    * **3. IMPLEMENT_SETTINGS_SERVICE:**
        * **FILE_NAME_PATH:** `Modules/Core/Services/SettingsService.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ `SettingsService.php`."
            * "2. أضف `use Illuminate\Support\Facades\Cache; use Modules\Core\Models\Setting;`."
            * "3. دالة `public function get(string \$key, \$default = null)`:"
                * "استخدم `return Cache::rememberForever(\"setting.\$key\", function () use (\$key, \$default) { \$setting = Setting::where('key', \$key)->first(); return \$setting ? \$setting->value : \$default; });`."
            * "4. دالة `public function set(string \$key, \$value, string \$group = null, string \$displayName = null, string \$type = 'text', ?array \$options = null, bool \$isTranslatable = false): Setting`:"
                * "`\$setting = Setting::updateOrCreate(['key' => \$key], ['value' => \$value, 'group_name' => \$group, 'display_name' => \$displayName, 'type' => \$type, 'options' => \$options, 'is_translatable' => \$isTranslatable]);`."
                * "`Cache::forget(\"setting.\$key\");`."
                * "`return \$setting;`."
            * "5. قم بتسجيل هذا الـ Service كـ singleton في `Modules/Core/Providers/CoreServiceProvider.php` (أنشئه إذا لم يكن موجودًا: `php artisan module:make-provider CoreServiceProvider Core` ثم عدل `register()` method):
              ```php
              // In CoreServiceProvider.php
              public function register()
              {
                  \$this->app->singleton(\Modules\Core\Services\SettingsService::class, function (\$app) {
                      return new \Modules\Core\Services\SettingsService();
                  });
              }
              ```"
    * **4. CREATE_SETTINGS_HELPER_FUNCTION:**
        * **FILE_NAME_PATH:** `Modules/Core/Helpers/settings_helper.php` (أو أضف إلى `helpers.php` العام).
        * **DETAILED_LOGIC:** "1. أنشئ ملف `settings_helper.php` إذا لم يكن ملف `helpers.php` العام موجودًا أو إذا كنت تفضل فصله."
        * "2. عرّف الدالة `setting()` كما هو موضح في التعليمات الأصلية، مع التأكد من أنها تستدعي `app(\Modules\Core\Services\SettingsService::class)->get(\$key, \$default);`."
        * "3. تأكد من تحميل ملف `settings_helper.php` (أو `helpers.php` العام) عبر `composer.json` الخاص بموديول `Core` أو الرئيسي."
    * **5. RUN_MIGRATIONS:**
        * **DETAILED_LOGIC:** "1. قم بتشغيل `php artisan migrate`."
* **REFERENCE_CODE_SNIPPETS:** (كما هو موضح أعلاه).
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم إنشاء جدول `settings`، ونموذج `Setting`، و`SettingsService`، ودالة مساعدة `setting()` تعمل بشكل صحيح مع التخزين المؤقت.
* **SECURITY_CONSIDERATIONS:** لا يوجد مباشرة، ولكن حماية واجهة إدارة الإعدادات ستكون مهمة لاحقًا.
* **PERFORMANCE_CONSIDERATIONS:** التخزين المؤقت ضروري.
* **REQUIRED_CODE_COMMENTS:** DocBlocks للنموذج، الخدمة، والدالة المساعدة.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء migration لجدول `settings` وتشغيله بنجاح."
    2. "تم إنشاء نموذج `Setting.php` بشكل صحيح."
    3. "تم تنفيذ `SettingsService` مع دوال `get` و `set` تعمل مع التخزين المؤقت وتم تسجيله كـ singleton."
    4. "تم إنشاء دالة المساعدة `setting()` ويمكن استخدامها لجلب الإعدادات."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented core settings management system (DB, Model, Service, Helper) with caching.' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: أضف مهمة `TODO-DASH-SETTINGS-UI-001` لإنشاء واجهة مستخدم في لوحة التحكم Dash لإدارة هذه الإعدادات، مع الإشارة إلى `FEAT-ADMIN-003` و `UIUX-FR.md`."

---

### **TASK-ID:PH01-TASK-013** `DASH-ADMIN-INITIAL-DASHBOARD-VIEW-001`
* **LEVEL:** `Low`
* **OBJECTIVE:** إنشاء صفحة `home.blade.php` أولية للوحة تحكم الإدارة Dash (ضمن موديول `Dashboard`) تعرض محتوى ثابتًا بسيطًا، وتستخدم `dashboard::layouts.admin_layout`. الهدف هو اختبار عمل التخطيط الأساسي للوحة التحكم بعد تسجيل دخول المدير. يخدم هذا [لوحة التحكم الاحترافية Dash].
* **TYPE:** `Dash Custom Blade View Creation`
* **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/admin/home.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-011`).
    * **DESIGN_REF:** لا يوجد تصميم محدد لهذه الصفحة الأولية في `UIUX-FR.md`.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: تأكد أن موديول `Dashboard` وتخطيطه الرئيسي (`admin_layout.blade.php` داخل `Modules/Dashboard/Resources/views/layouts/`) قد تم إنشاؤهما في `PH01-TASK-009`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:**
    * `TASK-ID:PH01-TASK-009 DASH-LAYOUT-SETUP-001` (لوجود `dashboard::layouts.admin_layout`).
    * `TASK-ID:PH01-TASK-007 ADMIN-AUTH-IMPLEMENTATION-001` (لإمكانية الوصول بعد تسجيل الدخول).
* **LLM_ASSUMPTIONS:**
    * `dashboard::layouts.admin_layout` جاهز للاستخدام.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_INITIAL_DASHBOARD_BLADE_FILE:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/admin/home.blade.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ المجلد `Modules/Dashboard/Resources/views/admin/` إذا لم يكن موجودًا."
            * "2. أنشئ ملف `home.blade.php` في المسار المحدد."
            * "3. اجعل هذا الـ view يمتد من `dashboard::layouts.admin_layout`."
            * "4. قم بتعريف `@section('title', 'لوحة التحكم الرئيسية')`."
            * "5. ضمن `@section('content')`، أضف محتوى بسيطًا مثل:
              ```html
              <div class="container-fluid py-4"> {{-- Added padding for better spacing --}}
                  <h4 class="fw-bold mb-3">مرحباً بك في لوحة التحكم</h4>
                  <div class="card"> {{-- Using a card structure similar to Dash --}}
                      <div class="card-body">
                          <p class="card-text">هذه هي الصفحة الرئيسية للوحة التحكم الإدارية. سيتم إضافة الإحصائيات والرسوم البيانية هنا في المرحلة القادمة.</p>
                          <p class="card-text">يتم استخدام هيكل وأصول Dash المخصصة، والمبنية على Blade.</p>
                          <p class="card-text">القائمة الجانبية والشريط العلوي هما حاليًا من النسخ الثابت لأصول Dash، وسيتم تحويلهما إلى عناصر ديناميكية في المراحل القادمة.</p>
                      </div>
                  </div>
              </div>
              ```"
* **REFERENCE_CODE_SNIPPETS:** (كما هو موضح أعلاه).
* **EXPECTED_OUTPUTS_BEHAVIOR:** عند تسجيل دخول المدير والوصول إلى المسار المخصص للوحة البيانات الرئيسية، يتم عرض هذه الصفحة الأولية بشكل صحيح ضمن تخطيط لوحة تحكم Dash.
* **SECURITY_CONSIDERATIONS:** يجب أن يكون الوصول لهذه الصفحة محميًا بوسيط المصادقة والتفويض.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء ملف `home.blade.php` في `Modules/Dashboard/Resources/views/admin/`."
    2. "الـ view يمتد من `dashboard::layouts.admin_layout`."
    3. "الـ view يعرض محتوى ثابتًا بسيطًا كما هو موضح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created initial admin dashboard home page Blade view (home.blade.php) within Dashboard module.' في `docs/CHANGELOG.md`."

---

### **TASK-ID:PH01-TASK-014** `ADMIN-DASHBOARD-BASIC-ROUTING-CONTROLLER-001`
* **LEVEL:** `Medium`
* **OBJECTIVE:** إنشاء وحدة تحكم أساسية (`DashboardController`) في موديول `Dashboard` ومسار للوصول إلى صفحة لوحة البيانات الأولية (`home.blade.php`) للوحة تحكم الإدارة Dash. يخدم هذا [لوحة التحكم الاحترافية Dash].
* **TYPE:** `Laravel Controller Action, Routing`
* **FILE_NAME_PATH:**
    * `Modules/Dashboard/Http/Controllers/Admin/DashboardController.php`
    * `Modules/Dashboard/Routes/admin.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-011` - الوصول للوحة البيانات).
    * **DESIGN_REF:** لا ينطبق.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `STRU-FR.md` (`STRU-LARAVEL-ROUTES-ADMIN-001` و `STRU-MOD-CTRL-ADMIN-001`) لفهم كيفية هيكلة المسارات ووحدات التحكم الإدارية ضمن الموديولات."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:**
    * `TASK-ID:PH01-TASK-013 DASH-ADMIN-INITIAL-DASHBOARD-VIEW-001` (لوجود واجهة لوحة البيانات الأولية).
    * `TASK-ID:PH01-TASK-007 ADMIN-AUTH-IMPLEMENTATION-001` (للتأكد من أن إعادة التوجيه بعد تسجيل الدخول صحيحة).
* **LLM_ASSUMPTIONS:**
    * موديول `Dashboard` موجود (تم التأكد منه في `PH01-TASK-013`).
    * اسم المسار للوحة البيانات الرئيسية سيكون `admin.dashboard`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_DASHBOARD_CONTROLLER (Admin):**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Http/Controllers/Admin/DashboardController.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ `DashboardController.php` داخل `Modules/Dashboard/Http/Controllers/Admin/`."
            * "2. اجعل الـ Controller يرث من `Modules\Core\Http\Controllers\BaseController`."
            * "3. قم بتنفيذ دالة `public function home(): \Illuminate\View\View`:"
              ```php
              <?php

              namespace Modules\Dashboard\Http\Controllers\Admin;

              use Illuminate\View\View;
              use Modules\Core\Http\Controllers\BaseController;

              class DashboardController extends BaseController
              {
                  public function home(): View
                  {
                      return view('dashboard::admin.home');
                  }
              }
              ```
    * **2. DEFINE_ADMIN_DASHBOARD_ROUTE:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Routes/admin.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ ملف `admin.php` ضمن `Modules/Dashboard/Routes/` إذا لم يكن موجودًا."
            * "2. قم بتعريف المسار للوحة البيانات الرئيسية داخل هذا الملف:"
              ```php
              <?php

              use Illuminate\Support\Facades\Route;
              use Modules\Dashboard\Http\Controllers\Admin\DashboardController;

              Route::middleware(['web', 'auth:web', 'verified', 'role:Super Admin|Employee'])
                  ->prefix(config('modules.AppConfig.admin_prefix', 'admin')) // Use a config for admin prefix if available, otherwise default to 'admin'
                  ->name('admin.')
                  ->group(function () {
                      Route::get('/dashboard', [DashboardController::class, 'home'])->name('dashboard');
                      // Alias for /admin to redirect to /admin/dashboard
                      Route::get('/', function () {
                          return redirect()->route('admin.dashboard');
                      })->name('index'); // This makes admin.index available
                  });
              ```
    * **3. REGISTER_MODULE_ROUTES_IN_MODULE_SERVICE_PROVIDER:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Providers/RouteServiceProvider.php`
        * **DETAILED_LOGIC:**
            * "1. تأكد من أن `RouteServiceProvider.php` في موديول `Dashboard` موجود (أنشئه إذا لم يكن: `php artisan module:make-provider RouteServiceProvider Dashboard`)."
            * "2. تأكد من أنه يقوم بتحميل ملف `admin.php` بشكل صحيح ضمن `map()` method. مثال:
              ```php
              // Inside Modules/Dashboard/Providers/RouteServiceProvider.php
              public function map()
              {
                  \$this->mapAdminRoutes();
                  // \$this->mapWebRoutes(); // If you have public routes for this module
                  // \$this->mapApiRoutes(); // If you have API routes for this module
              }

              protected function mapAdminRoutes()
              {
                  Route::middleware(['web']) // Keep this simple, specifics are in the admin.php file
                       ->group(module_path('Dashboard', '/Routes/admin.php'));
              }
              ```"
            * "3. تأكد من تسجيل `Modules\Dashboard\Providers\RouteServiceProvider::class` في مصفوفة `providers` ضمن `config/app.php`."
    * **4. UPDATE_LOGIN_REDIRECT:**
        * **FILE_NAME_PATH:** `Modules/UserManagement/Http/Controllers/Admin/AuthenticatedSessionController.php` (تعديل)
        * **DETAILED_LOGIC:** "1. في دالة `store()`، تأكد من أن إعادة التوجيه عند النجاح تكون إلى `route('admin.dashboard')`."
    * **5. UPDATE_ROUTE_SERVICE_PROVIDER_HOME_CONSTANT (توضيح):**
        * **FILE_NAME_PATH:** `App/Providers/RouteServiceProvider.php`
        * **DETAILED_LOGIC:** "1. تأكد من أن الثابت `HOME` (إذا كان مستخدمًا بواسطة middleware المصادقة الافتراضي لإعادة توجيه المستخدمين المصادق عليهم الذين يحاولون الوصول إلى صفحات الضيف) لا يتعارض. المسار `admin.dashboard` له middleware خاص به (`role:Super Admin|Employee`) ويجب أن يكون الوصول إليه محكومًا بذلك."
* **REFERENCE_CODE_SNIPPETS:** (كما هو موضح أعلاه).
* **EXPECTED_OUTPUTS_BEHAVIOR:** يمكن للمدير/الموظف تسجيل الدخول والوصول إلى صفحة لوحة البيانات الأولية عبر المسار `/admin/dashboard` (أو `/admin`). المستخدمون غير المصادق عليهم يتم توجيههم إلى صفحة تسجيل الدخول الإدارية.
* **SECURITY_CONSIDERATIONS:** حماية المسارات الإدارية بوسائط المصادقة والتفويض (`auth:web`, `role`).
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** DocBlocks للـ Controller method والمسارات.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء `DashboardController.php` في `Modules/Dashboard` مع دالة `home()` تعرض `dashboard::admin.home` view."
    2. "تم تعريف المسار `admin.dashboard` (والمسار `/admin` كـ alias) في `Modules/Dashboard/Routes/admin.php` وهو محمي بوسائط المصادقة والأدوار."
    3. "يتم تحميل مسارات موديول `Dashboard` الإدارية بشكل صحيح عبر `RouteServiceProvider` الخاص به، ومسجل في `config/app.php`."
    4. "بعد تسجيل الدخول الناجح كمدير/موظف، يتم التوجيه إلى `route('admin.dashboard')`."
    5. "الوصول المباشر إلى `/admin/dashboard` أو `/admin` بدون تسجيل دخول يوجه إلى صفحة تسجيل الدخول الإدارية."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created DashboardController and routes for admin dashboard home page, including /admin redirect.' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: تأكد من أن مهمة `TODO-DASHBOARD-DYNAMIC-CONTENT-001` (لإضافة محتوى ديناميكي للوحة البيانات الرئيسية) موجودة للمرحلة القادمة."
---

### **TASK-ID:PH01-TASK-015** `DASH-ADMIN-PASSWORD-RESET-VIEWS-001`
* **LEVEL:** `Medium`
* **OBJECTIVE:** إنشاء واجهات Blade لعملية استعادة كلمة المرور للوحة تحكم الإدارة Dash (`password_request.blade.php` و `password_reset.blade.php`)، بناءً على تصميم مشابه لـ `DASH-ADMIN-LOGIN-001` من `UIUX-FR.md`. يخدم هذا اكتمال وظيفة المصادقة لـ [لوحة التحكم الاحترافية Dash].
* **TYPE:** `Dash Custom Blade View Creation (Password Reset Pages)`
* **FILE_NAME_PATH:**
    * `Modules/UserManagement/Resources/views/admin/auth/forgot-password.blade.php`
    * `Modules/UserManagement/Resources/views/admin/auth/reset-password.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (`PH-01-DEL-005`), `REQ-FR.md` (`MOD-USER-MGMT-FEAT-003` - الجزء الإداري).
    * **DESIGN_REF:** `UIUX-FR.md` (يفترض تصميمًا مشابهًا لـ `DASH-ADMIN-LOGIN-001` مع تعديل الحقول والعناوين).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `UIUX-FR.md` (`DASH-ADMIN-LOGIN-001`) واستلهم منه تصميمًا بسيطًا ومتسقًا لصفحات استعادة كلمة المرور. تأكد من أن الصفحات تحتوي على الحقول اللازمة (البريد الإلكتروني لطلب الرابط؛ والرمز، البريد، كلمة المرور، وتأكيد كلمة المرور لصفحة إعادة التعيين)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:** لا يوجد.
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-007 ADMIN-AUTH-IMPLEMENTATION-001` (لوجود وحدات التحكم والمسارات التي ستعرض هذه الواجهات).
* **LLM_ASSUMPTIONS:**
    * سيتم استخدام تصميم بسيط ومتسق مع صفحة تسجيل الدخول للوحة التحكم.
    * لن تستخدم هذه الصفحات التخطيط الكامل للوحة التحكم (`admin_layout.blade.php`).
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORGOT_PASSWORD_VIEW:**
        * **FILE_NAME_PATH:** `Modules/UserManagement/Resources/views/admin/auth/forgot-password.blade.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ الملف."
            * "2. قم ببناء هيكل HTML مشابه لصفحة تسجيل الدخول (حاوية مركزية، شعار، عنوان 'استعادة كلمة المرور')."
            * "3. أضف نموذجًا (`<form method=\"POST\" action=\"{{ route('admin.password.email') }}\">`) يحتوي على:"
                * "`@csrf`."
                * "حقل 'البريد الإلكتروني' (input `name='email'`, type='email')."
                * "زر 'إرسال رابط إعادة تعيين كلمة المرور'."
            * "4. قم بعرض رسائل النجاح (مثل `session('status')`) أو أخطاء التحقق."
    * **2. CREATE_RESET_PASSWORD_VIEW:**
        * **FILE_NAME_PATH:** `Modules/UserManagement/Resources/views/admin/auth/reset-password.blade.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ الملف."
            * "2. قم ببناء هيكل HTML مشابه."
            * "3. أضف نموذجًا (`<form method=\"POST\" action=\"{{ route('admin.password.update') }}\">`) يحتوي على:"
                * "`@csrf`."
                * "حقل مخفي `name='token'` بقيمة `{{ $request->route('token') }}`."
                * "حقل 'البريد الإلكتروني' (input `name='email'`, type='email', value `{{ $request->email ?? old('email') }}`)."
                * "حقل 'كلمة المرور الجديدة' (input `name='password'`, type='password')."
                * "حقل 'تأكيد كلمة المرور الجديدة' (input `name='password_confirmation'`, type='password')."
                * "زر 'إعادة تعيين كلمة المرور'."
            * "4. قم بعرض أخطاء التحقق من الصحة."
* **REFERENCE_CODE_SNIPPETS:** (يمكن للـ Augment الاستلهام من هياكل نماذج Laravel Breeze/Fortify مع تطبيق تصميم Dash البسيط).
* **EXPECTED_OUTPUTS_BEHAVIOR:** يتم عرض صفحات وظيفية لطلب إعادة تعيين كلمة المرور وإعادة تعيينها للوحة تحكم الإدارة Dash.
* **SECURITY_CONSIDERATIONS:** استخدام `@csrf`.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء ملف `forgot-password.blade.php` ويعرض النموذج بشكل صحيح."
    2. "تم إنشاء ملف `reset-password.blade.php` ويعرض النموذج بشكل صحيح مع الحقول المطلوبة (بما في ذلك الـ token والبريد الإلكتروني)."
    3. "التصميم يتسق مع `DASH-ADMIN-LOGIN-001` من حيث الألوان والخطوط الأساسية."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created Blade views for admin dashboard password reset functionality (forgot password and reset password forms).' في `docs/CHANGELOG.md`."

---

# END

# إليك المهام الإضافية المقترحة للمرحلة الأولى:

### **TASK-ID:PH01-TASK-016** `INITIAL-CODE-LINTING-FORMATTING-001`
* **LEVEL:** `Low`
* **OBJECTIVE:** إجراء عملية فحص وتنسيق أولية للكود (Linting & Formatting) لجميع ملفات PHP و Blade التي تم إنشاؤها أو تعديلها في المرحلة الأولى، لضمان اتساق الكود وقراءته. يخدم هذا جودة وصيانة جميع [المخرجات النهائية الأربعة] على المدى الطويل.
* **TYPE:** `Code Quality Assurance`
* **FILE_NAME_PATH:** جميع ملفات `.php` و `.blade.php` ضمن جذر المشروع ومجلد `Modules/`.
* **PRIMARY_INPUTS:**
    * **TASK_REF:** (ممارسة جيدة عامة، تخدم `NFR-MAINT-001` من `REQ-FR.md` بشكل غير مباشر).
    * **DESIGN_REF:** لا ينطبق.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: لا توجد سجلات محددة لمراجعتها لهذه المهمة، ولكن تأكد من أن أدوات التنسيق (مثل PHP CS Fixer أو Prettier إذا كانت مستخدمة) تم تكوينها بشكل أساسي أو سيتم استخدام الإعدادات الافتراضية."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: أضف هذه المهمة `TASK-ID:PH01-TASK-016` إلى `TODO.md` تحت Phase 01 وقم بتحديث حالتها إلى 'قيد التنفيذ'."
* **DEPENDENCIES:** اكتمال جميع مهام `PH-01` السابقة.
* **LLM_ASSUMPTIONS:**
    * نفترض أنه يمكن استخدام أدوات تنسيق قياسية مثل PHP CS Fixer لملفات PHP، وربما Prettier لملفات Blade (إذا تم تكوينه).
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. SETUP_LINTER_FORMATTER (إذا لم يتم إعداده بعد):**
        * **DETAILED_LOGIC:** "1. (اختياري، إذا لم يكن موجودًا) قم بتثبيت PHP CS Fixer كـ dev dependency: `composer require friendsofphp/php-cs-fixer --dev`."
        * "2. قم بإنشاء ملف تهيئة ` .php-cs-fixer.dist.php` في جذر المشروع بقواعد PSR-12 (أو أي معيار متفق عليه)."
    * **2. RUN_LINTING_AND_FORMATTING:**
        * **DETAILED_LOGIC:** "1. قم بتشغيل أداة التنسيق على جميع ملفات PHP (مثال: `vendor/bin/php-cs-fixer fix`)."
        * "2. إذا كانت هناك أداة تنسيق لملفات Blade، قم بتشغيلها أيضًا."
    * **3. REVIEW_CHANGES_AND_COMMIT:**
        * **DETAILED_LOGIC:** "1. قم بمراجعة التغييرات التي أجرتها أدوات التنسيق."
        * "2. قم بعمل commit للتغييرات مع رسالة مناسبة (e.g., `git commit -m \"Chore: Apply code formatting and linting for Phase 1\"`)."
* **REFERENCE_CODE_SNIPPETS:** لا يوجد.
* **EXPECTED_OUTPUTS_BEHAVIOR:** الكود المكتوب في المرحلة الأولى منسق بشكل متسق وفقًا للمعايير المحددة.
* **SECURITY_CONSIDERATIONS:** لا يوجد.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد تأثير مباشر على أداء التطبيق.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1. "تم تشغيل أدوات فحص وتنسيق الكود على ملفات PHP و Blade التي تم إنشاؤها/تعديلها."
    2. "تم عمل commit لأي تغييرات ناتجة عن عملية التنسيق."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Applied initial code linting and formatting for Phase 1 files.' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: ضع علامة [x] بجانب `TASK-ID:PH01-TASK-016` في `docs/TODO.md`."

---

### **TASK-ID:PH01-TASK-017** `DASH-INITIAL-LOGIN-AND-LAYOUT-TEST-001`
* **LEVEL:** `Medium`
* **OBJECTIVE:** إجراء اختبار يدوي شامل لعملية تسجيل الدخول إلى لوحة تحكم الإدارة Dash باستخدام مستخدم Super Admin الافتراضي، والتحقق من أن التخطيط الأساسي للوحة التحكم (`admin_layout.blade.php` مع الـ partials الثابتة) يتم عرضه بشكل صحيح ومتوافق مع أصول Dash الأصلية. يخدم هذا التحقق المبكر من تكامل [لوحة التحكم الاحترافية Dash].
* **TYPE:** `Manual Testing and Verification`
* **FILE_NAME_PATH:** لا يوجد إنشاء ملفات جديدة، بل تفاعل مع الواجهات الموجودة.
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `PPP-FR.md` (يخدم التحقق من اكتمال مخرجات `PH-01-DEL-005`, `PH-01-DEL-007`, `PH-01-DEL-008`, `PH-01-DEL-009`, `PH-01-DEL-011`, `PH-01-DEL-013`, `PH-01-DEL-014`).
    * **DESIGN_REF:** `UIUX-FR.md` (خاصة `DASH-ADMIN-LOGIN-001` والهيكل العام المتوقع للوحة التحكم المستوحى من `Dash/index.html`).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/DECISIONS.md` (`DECISION-DEFAULT-ADMIN-CREDENTIALS-001`) للحصول على بيانات اعتماد المدير الافتراضي. راجع `docs/UIUX-FR.md` للتأكد من أن المظهر العام الأولي للوحة التحكم يتوافق مع التوقعات الأساسية."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: أضف هذه المهمة `TASK-ID:PH01-TASK-017` إلى `docs/TODO.md` تحت Phase 01 وقم بتحديث حالتها إلى 'قيد التنفيذ'."
* **DEPENDENCIES:** اكتمال جميع مهام `PH-01` السابقة، خاصة `PH01-TASK-007`, `PH01-TASK-008A`, `PH01-TASK-009`, `PH01-TASK-010`, `PH01-TASK-011`, `PH01-TASK-014`.
* **LLM_ASSUMPTIONS:**
    * خادم التطوير (`php artisan serve`) يعمل.
    * تم تشغيل `db:seed` لإنشاء المستخدم المدير.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. PERFORM_ADMIN_LOGIN:**
        * **DETAILED_LOGIC:** "1. افتح المتصفح وانتقل إلى مسار تسجيل الدخول للوحة التحكم الإدارية (e.g., `/admin/auth/login`)."
        * "2. استخدم بيانات اعتماد Super Admin الافتراضية (من `DECISION-DEFAULT-ADMIN-CREDENTIALS-001`) لتسجيل الدخول."
        * "3. تحقق من أنه تم توجيهك بنجاح إلى لوحة البيانات الرئيسية للوحة التحكم (e.g., `/admin/dashboard`)."
    * **2. VERIFY_DASHBOARD_LAYOUT_AND_STATIC_ELEMENTS:**
        * **DETAILED_LOGIC:** "1. بمجرد تسجيل الدخول، قم بفحص المظهر العام لصفحة لوحة البيانات (`home.blade.php` الأولية)."
        * "2. **الشريط الجانبي (`_sidebar.blade.php`):** تحقق من أنه يُعرض بشكل صحيح مع العناصر الثابتة المنسوخة من `Dash/index.html`. اختبر فتح وإغلاق القوائم الفرعية الثابتة (إذا كانت موجودة وظيفيًا في `Dash/script.js` الأصلي)."
        * "3. **الشريط العلوي (`_topbar.blade.php`):** تحقق من أنه يُعرض بشكل صحيح مع العناصر الثابتة (مثل اسم المستخدم placeholder، أيقونة الإشعارات). اختبر عمل زر تبديل القائمة الجانبية."
        * "4. **منطقة المحتوى:** تأكد من أن المحتوى الثابت لـ `home.blade.php` (رسالة "قيد الإنشاء") يُعرض ضمن التخطيط."
        * "5. **التذييل (`_footer.blade.php`):** تحقق من عرضه بشكل صحيح إذا كان جزءًا من التخطيط."
        * "6. **الأنماط الأساسية:** تحقق بصريًا من أن الأنماط العامة من `Dash/style.css` (الألوان، الخطوط، التباعد الأساسي) يتم تطبيقها بشكل مشابه للملف الأصلي `Dash/index.html`."
        * "7. **وظائف JS الأساسية:** تحقق من أن الوظائف الأساسية من `Dash/script.js` (مثل تبديل القائمة الجانبية، تفاعلات القوائم المنسدلة في الشريط العلوي إن وجدت) تعمل كما هو متوقع."
    * **3. CHECK_CONSOLE_FOR_ERRORS:**
        * **DETAILED_LOGIC:** "1. افتح أدوات مطوري المتصفح (Developer Tools) وتحقق من عدم وجود أي أخطاء JavaScript أو أخطاء تحميل موارد (CSS, JS, images) في الـ console."
    * **4. PERFORM_LOGOUT:**
        * **DETAILED_LOGIC:** "1. ابحث عن رابط/زر تسجيل الخروج (قد يكون ثابتًا في `_topbar.blade.php` في هذه المرحلة)."
        * "2. قم بتسجيل الخروج وتأكد من أنه تم توجيهك مرة أخرى إلى صفحة تسجيل الدخول للوحة التحكم."
* **REFERENCE_CODE_SNIPPETS:** لا يوجد (مهمة اختبار يدوي).
* **EXPECTED_OUTPUTS_BEHAVIOR:** تأكيد أن عملية تسجيل الدخول للوحة التحكم Dash تعمل، وأن الهيكل الأساسي للوحة التحكم (المبني من أصول Dash الثابتة) يُعرض بشكل صحيح بدون أخطاء واضحة.
* **SECURITY_CONSIDERATIONS:** لا يوجد (هذه مهمة تحقق).
* **PERFORMANCE_CONSIDERATIONS:** ملاحظة أي بطء ملحوظ في التحميل الأولي (سيتم معالجته لاحقًا بـ Vite/Mix).
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن تسجيل الدخول بنجاح إلى لوحة التحكم الإدارية باستخدام مستخدم Super Admin الافتراضي."
    2. "يتم عرض التخطيط الأساسي للوحة التحكم (الشريط الجانبي، الشريط العلوي، منطقة المحتوى، التذييل) بشكل مشابه لـ `Dash/index.html`."
    3. "العناصر الثابتة في الشريط الجانبي والشريط العلوي تظهر كما هو متوقع."
    4. "لا توجد أخطاء JavaScript واضحة في الـ console تتعلق بتحميل أو تهيئة أصول Dash الأساسية."
    5. "تعمل وظيفة تسجيل الخروج بشكل صحيح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Performed initial manual testing of Dash admin login and basic layout rendering. Verified successful login and static component display from Dash assets.' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: ضع علامة [x] بجانب `TASK-ID:PH01-TASK-017` في `docs/TODO.md`. إذا تم اكتشاف أي مشاكل بسيطة أثناء هذا الاختبار لم يتم تغطيتها بمهام TODO أخرى، قم بإنشاء مهام TODO جديدة لها."
    * **DECISIONS.md:** "Augment: إذا أدى هذا الاختبار إلى أي قرارات تصميمية أو فنية (مثلاً، الحاجة لتعديل طفيف في HTML المنسوخ من Dash ليتناسب مع Blade)، قم بتوثيقها."

---

# بهاتين المهمتين الإضافيتين، أعتقد أن المرحلة الأولى ستكون أكثر صلابة وجاهزية للانتقال إلى المرحلة الثانية التي ستركز بشكل أكبر على بناء الوظائف الديناميكية داخل لوحة التحكم.

# FIXED

### **TASK-ID:PH01-TASK-018** `SPATIE-MIDDLEWARE-REGISTRATION-001`
* **LEVEL:** `Medium`
* **OBJECTIVE:** تسجيل وسائط (middleware) حزمة `spatie/laravel-permission` (`RoleMiddleware`, `PermissionMiddleware`, `RoleOrPermissionMiddleware`) في `App\Http\Kernel.php` لتمكين استخدامها في حماية المسارات بناءً على الأدوار والصلاحيات. يخدم هذا أمان [لوحة التحكم الاحترافية Dash] و [API تطبيق Flutter].
* **TYPE:** `Laravel Middleware Configuration`
* **FILE_NAME_PATH:** `app/Http/Kernel.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** ضمني لـ `TASK-ID:PH01-TASK-008 SPATIE-PERMISSION-SETUP-001` و `TASK-ID:PH01-TASK-014 ADMIN-DASHBOARD-BASIC-ROUTING-CONTROLLER-001` (لأن المسار يستخدم `role`).
    * **DESIGN_REF:** لا ينطبق.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع توثيق `spatie/laravel-permission` للتأكد من أسماء الكلاسات الصحيحة للوسائط والأسماء المختصرة المقترحة."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: أضف هذه المهمة `TASK-ID:PH01-TASK-018` إلى `docs/TODO.md` تحت Phase 01 وقم بتحديث حالتها إلى 'قيد التنفيذ'."
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-008 SPATIE-PERMISSION-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * حزمة `spatie/laravel-permission` تم تثبيتها وتكوينها بشكل صحيح.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. REGISTER_ROUTE_MIDDLEWARE:**
        * **FILE_NAME_PATH:** `app/Http/Kernel.php`
        * **DETAILED_LOGIC:**
            * "1. افتح ملف `app/Http/Kernel.php`."
            * "2. في مصفوفة `$routeMiddleware` (أو `$middlewareAliases` في Laravel 10+)، قم بإضافة التسجيلات التالية:
                ```php
                protected \$middlewareAliases = [ // or protected \$routeMiddleware = [ for older Laravel versions
                    // ... other middleware
                    'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
                    'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
                    'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
                ];
                ```"
            * "3. احفظ الملف."
* **REFERENCE_CODE_SNIPPETS:** (كما هو موضح أعلاه).
* **EXPECTED_OUTPUTS_BEHAVIOR:** يتم تسجيل وسائط Spatie Permission بشكل صحيح، ويجب أن يعمل المسار `/admin/dashboard` المحمي بوسيط `role:Super Admin|Employee` بدون الخطأ `Target class [role] does not exist.`.
* **SECURITY_CONSIDERATIONS:** هذا جزء أساسي من تطبيق التحكم في الوصول.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد تأثير كبير.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1. "تم تحديث مصفوفة `$middlewareAliases` (أو `$routeMiddleware`) في `app/Http/Kernel.php` لتشمل وسائط `role`, `permission`, و `role_or_permission` من Spatie."
    2. "بعد هذا التعديل، عند محاولة الوصول إلى مسار محمي بـ `middleware('role:...')`، لا يظهر خطأ `Target class [role] does not exist.`."
    3. "الوصول للمسار `/admin/dashboard` يعمل بشكل صحيح للمستخدمين الذين لديهم دور 'Super Admin' أو 'Employee'."
    4. "المستخدمون الذين ليس لديهم هذه الأدوار (بعد تسجيل الدخول) يجب أن يتلقوا استجابة `403 Forbidden` (أو يتم توجيههم، حسب سلوك `RoleMiddleware`)."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Registered Spatie Permission middleware (role, permission, role_or_permission) in Http Kernel.' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: ضع علامة [x] بجانب `TASK-ID:PH01-TASK-018` في `docs/TODO.md`."
    * **DECISIONS.md:** "Augment: وثق قرار استخدام الأسماء المختصرة القياسية (`role`, `permission`) لوسائط Spatie كـ `DECISION-SPATIE-MIDDLEWARE-ALIASES-001`."

