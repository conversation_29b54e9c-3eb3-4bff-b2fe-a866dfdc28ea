<?php

namespace Modules\CarCatalog\Http\Controllers\Admin;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\CarCatalog\Http\Requests\Admin\StoreCarRequest;
use Modules\CarCatalog\Http\Requests\Admin\UpdateCarRequest;
use Modules\CarCatalog\Models\BodyType;
use Modules\CarCatalog\Models\Brand;
use Modules\CarCatalog\Models\Car;
use Modules\CarCatalog\Models\CarModel;
use Modules\CarCatalog\Models\Color;
use Modules\CarCatalog\Models\FeatureCategory;
use Modules\CarCatalog\Models\FuelType;
use Modules\CarCatalog\Models\ManufacturingYear;
use Modules\CarCatalog\Models\TransmissionType;

/**
 * وحدة تحكم إدارة السيارات.
 *
 * تتعامل هذه الوحدة مع عمليات CRUD للسيارات في لوحة تحكم الإدارة
 * تدعم البحث والفلترة والترقيم مع واجهة Stepper للإضافة والتعديل
 *
 * <AUTHOR> Development Team
 *
 * @version 1.0.0
 *
 * @since 2024
 */
class CarController extends Controller
{
    public function __construct()
    {
        // إضافة logging لجميع الطلبات
        $this->middleware(function ($request, $next) {
            Log::info('=== CAR CONTROLLER REQUEST ===');
            Log::info('Route: ' . $request->route()->getName());
            Log::info('Method: ' . $request->method());
            Log::info('URL: ' . $request->fullUrl());
            Log::info('User: ' . (auth()->check() ? auth()->user()->email : 'Guest'));
            Log::info('IP: ' . $request->ip());
            Log::info('User Agent: ' . $request->userAgent());

            return $next($request);
        });
    }
    /**
     * عرض قائمة السيارات مع إمكانيات البحث والفلترة.
     *
     * يعرض هذا المتحكم قائمة بجميع السيارات مع إمكانية البحث بالعنوان أو VIN
     * أو اسم الماركة أو الموديل، بالإضافة إلى فلترة حسب الماركة والحالة
     *
     * @param Request $request طلب HTTP يحتوي على معاملات البحث والفلترة
     *
     * @return Renderable عرض صفحة قائمة السيارات
     *
     * @throws \Exception في حالة حدوث خطأ في الاستعلام
     */
    public function index(Request $request)
    {
        // بناء الاستعلام مع العلاقات المطلوبة
        $query = Car::query()->with(['brand', 'carModel', 'manufacturingYear', 'mainColor']);

        // تطبيق فلتر البحث
        if ($request->filled('search')) {
            $searchTerm = $request->input('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('vin', 'like', "%{$searchTerm}%")
                  ->orWhereHas('brand', function ($qb) use ($searchTerm) {
                      $qb->where('name', 'like', "%{$searchTerm}%");
                  })
                  ->orWhereHas('carModel', function ($qm) use ($searchTerm) {
                      $qm->where('name', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // تطبيق فلتر الماركة
        if ($request->filled('brand_id')) {
            $query->where('brand_id', $request->input('brand_id'));
        }

        // تطبيق فلتر الموديل
        if ($request->filled('model_id')) {
            $query->where('car_model_id', $request->input('model_id'));
        }

        // تطبيق فلتر الحالة
        if ($request->filled('status')) {
            $status = $request->input('status');
            switch ($status) {
                case 'active':
                    $query->where('is_active', true)->where('is_sold', false);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'sold':
                    $query->where('is_sold', true);
                    break;
                case 'featured':
                    $query->where('is_featured', true);
                    break;
            }
        }

        // الترتيب والترقيم
        $query->latest();
        $cars = $query->paginate(15);

        // جلب البيانات للفلاتر
        $brands = Brand::where('status', 1)->orderBy('name')->pluck('name', 'id');

        // جلب الموديلات للماركة المختارة (إن وجدت)
        $models = collect();
        if ($request->filled('brand_id')) {
            $models = CarModel::where('brand_id', $request->input('brand_id'))
                ->where('status', true)
                ->orderBy('name')
                ->pluck('name', 'id');
        }

        // قائمة حالات السيارات
        $carStatuses = [
            'active'   => 'نشطة',
            'inactive' => 'غير نشطة',
            'sold'     => 'مباعة',
            'featured' => 'مميزة',
        ];

        return view('car_catalog::admin.cars.index', compact('cars', 'brands', 'models', 'carStatuses'));
    }

    /**
     * عرض نموذج إضافة سيارة جديدة باستخدام واجهة Stepper.
     *
     * يجلب جميع البيانات اللازمة للقوائم المنسدلة في الخطوات الخمس
     * ويعرض واجهة إضافة السيارة المتدرجة
     *
     * @return Renderable عرض صفحة إضافة سيارة جديدة
     *
     * @throws \Exception في حالة فشل جلب البيانات المطلوبة
     */
    public function create()
    {
        // جلب البيانات اللازمة للقوائم المنسدلة
        $brands = Brand::where('status', true)->orderBy('name')->get();
        $years = ManufacturingYear::where('status', true)->orderBy('year', 'desc')->get();
        $colors = Color::where('status', true)->orderBy('name')->get();
        $transmissionTypes = TransmissionType::where('status', true)->orderBy('name')->get();
        $fuelTypes = FuelType::where('status', true)->orderBy('name')->get();
        $bodyTypes = BodyType::where('status', true)->orderBy('name')->get();

        // جلب الميزات مجمعة حسب الفئة
        $featureCategories = FeatureCategory::with(['features' => function ($query) {
            $query->where('status', true)->orderBy('name');
        }])->where('status', true)->orderBy('name')->get();

        // إنشاء مجموعة فارغة للموديلات (ستتم تعبئتها عبر AJAX)
        $models = collect();

        return view('car_catalog::admin.cars.create', compact(
            'brands',
            'models',
            'years',
            'colors',
            'transmissionTypes',
            'fuelTypes',
            'bodyTypes',
            'featureCategories'
        ));
    }

    /**
     * حفظ سيارة جديدة في قاعدة البيانات.
     *
     * يقوم بحفظ جميع بيانات السيارة من الخطوات الخمس بما في ذلك:
     * - البيانات الأساسية والمواصفات الفنية
     * - الميزات المختارة والصور المرفوعة
     * - بيانات السعر والحالة
     *
     * @param StoreCarRequest $request طلب HTTP محقق يحتوي على بيانات السيارة
     *
     * @return RedirectResponse إعادة توجيه إلى قائمة السيارات مع رسالة نجاح
     *
     * @throws \Exception في حالة فشل حفظ البيانات أو رفع الصور
     */
    public function store(StoreCarRequest $request): RedirectResponse
    {
        Log::info('=== CAR STORE METHOD CALLED ===');
        Log::info('User ID: ' . auth()->id());
        Log::info('User Email: ' . auth()->user()->email);
        Log::info('Request Method: ' . $request->method());
        Log::info('Request URL: ' . $request->fullUrl());
        Log::info('Request Headers: ', $request->headers->all());
        Log::info('Request Input: ', $request->all());

        // DEBUG: فحص الملفات المرفوعة بتفصيل
        Log::info('=== DEBUGGING FILE UPLOADS ===');
        Log::info('Request files:', $request->allFiles());

        if ($request->hasFile('car_images')) {
            Log::info('Car images are present in the request.');
            $images = $request->file('car_images');
            Log::info('Images count: ' . (is_array($images) ? count($images) : 'not array'));

            if (is_array($images)) {
                foreach ($images as $key => $file) {
                    if ($file && $file->isValid()) {
                        Log::info("File {$key} is valid.", [
                            'path' => $file->path(),
                            'original_name' => $file->getClientOriginalName(),
                            'mime_type' => $file->getMimeType(),
                            'size' => $file->getSize(),
                            'extension' => $file->getClientOriginalExtension()
                        ]);
                    } else {
                        Log::error("File {$key} is not valid.", [
                            'error' => $file ? $file->getError() : 'File is null'
                        ]);
                    }
                }
            }
        } else {
            Log::warning('No car images found in the request in controller.');
        }

        // DEBUG: فحص main_image_index
        if ($request->has('main_image_index')) {
            Log::info('Main image index: ' . $request->input('main_image_index'));
        } else {
            Log::info('No main image index provided');
        }

        Log::info('=== END FILE UPLOAD DEBUG ===');

        try {
            DB::beginTransaction();

            // إنشاء سجل السيارة
            $carData = $request->validated();
            $carData['created_by'] = auth()->id();
            $carData['updated_by'] = auth()->id();

            // تعيين القيم الافتراضية للحقول المنطقية
            $carData['is_featured'] = $request->boolean('is_featured', false);
            $carData['is_sold'] = $request->boolean('is_sold', false);
            $carData['is_active'] = $request->boolean('is_active', true);

            $car = Car::create($carData);

            // ربط الميزات المختارة
            if ($request->has('features') && is_array($request->features)) {
                $car->features()->sync($request->features);
            }

            // معالجة رفع الصور
            if ($request->hasFile('car_images')) {
                $this->handleImageUploads($car, $request);
            }

            DB::commit();

            // TODO: PH02-TASK-025 - إرسال إشعار عند إضافة سيارة جديدة
            // NotificationService::send([
            //     'type' => 'car_created',
            //     'title' => 'تم إضافة سيارة جديدة',
            //     'message' => "تم إضافة السيارة {$car->title} بنجاح",
            //     'data' => ['car_id' => $car->id, 'car_title' => $car->title],
            //     'recipients' => ['admin', 'inventory_manager'],
            //     'channels' => ['database', 'email']
            // ]);

            return redirect()->route('admin.cars.index')
                ->with('success', 'تم إضافة السيارة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating car: ' . $e->getMessage());

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إضافة السيارة. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * عرض تفاصيل سيارة محددة.
     *
     * يعرض جميع تفاصيل السيارة بما في ذلك الصور والميزات والمواصفات
     * مع تحميل جميع العلاقات المطلوبة لعرض شامل
     *
     * @param Car $car نموذج السيارة المراد عرض تفاصيلها
     *
     * @return Renderable عرض صفحة تفاصيل السيارة
     */
    public function show(Car $car)
    {
        $car->load(['brand', 'carModel', 'manufacturingYear', 'mainColor', 'interiorColor',
            'bodyType', 'transmissionType', 'fuelType', 'features.category']);

        return view('car_catalog::admin.cars.show', compact('car'));
    }

    /**
     * عرض نموذج تعديل سيارة موجودة باستخدام واجهة Stepper.
     *
     * يجلب جميع البيانات اللازمة للقوائم المنسدلة والميزات المختارة مسبقاً
     * ويعرض واجهة تعديل السيارة المتدرجة مع البيانات الحالية
     *
     * @param Car $car نموذج السيارة المراد تعديلها
     *
     * @return Renderable عرض صفحة تعديل السيارة
     *
     * @throws \Exception في حالة فشل جلب البيانات المطلوبة
     */
    public function edit(Car $car)
    {
        // جلب البيانات اللازمة للقوائم المنسدلة
        $brands = Brand::where('status', true)->orderBy('name')->get();
        $years = ManufacturingYear::where('status', true)->orderBy('year', 'desc')->get();
        $colors = Color::where('status', true)->orderBy('name')->get();
        $transmissionTypes = TransmissionType::where('status', true)->orderBy('name')->get();
        $fuelTypes = FuelType::where('status', true)->orderBy('name')->get();
        $bodyTypes = BodyType::where('status', true)->orderBy('name')->get();

        // جلب الميزات مجمعة حسب الفئة
        $featureCategories = FeatureCategory::with(['features' => function ($query) {
            $query->where('status', true)->orderBy('name');
        }])->where('status', true)->orderBy('name')->get();

        // جلب موديلات الماركة المختارة
        $models = CarModel::where('brand_id', $car->brand_id)
            ->where('status', true)
            ->orderBy('name')
            ->get();

        // جلب الميزات المختارة للسيارة
        $selectedFeatures = $car->features->pluck('id')->toArray();

        // تحميل العلاقات
        $car->load(['brand', 'carModel', 'manufacturingYear', 'mainColor', 'interiorColor',
            'bodyType', 'transmissionType', 'fuelType', 'features']);

        return view('car_catalog::admin.cars.edit', compact(
            'car',
            'brands',
            'models',
            'years',
            'colors',
            'transmissionTypes',
            'fuelTypes',
            'bodyTypes',
            'featureCategories',
            'selectedFeatures'
        ));
    }

    /**
     * تحديث بيانات سيارة موجودة في قاعدة البيانات.
     *
     * يقوم بتحديث جميع بيانات السيارة من الخطوات الخمس بما في ذلك:
     * - البيانات الأساسية والمواصفات الفنية
     * - الميزات المختارة وإدارة الصور (حذف/إضافة)
     * - بيانات السعر والحالة
     *
     * @param UpdateCarRequest $request طلب HTTP محقق يحتوي على بيانات السيارة المحدثة
     * @param Car $car نموذج السيارة المراد تحديثها
     *
     * @return RedirectResponse إعادة توجيه إلى قائمة السيارات مع رسالة نجاح
     *
     * @throws \Exception في حالة فشل تحديث البيانات أو معالجة الصور
     */
    public function update(UpdateCarRequest $request, Car $car): RedirectResponse
    {
        Log::info('=== CAR UPDATE METHOD CALLED ===');
        Log::info('Car ID: ' . $car->id);
        Log::info('User ID: ' . auth()->id());
        Log::info('User Email: ' . auth()->user()->email);
        Log::info('Request Method: ' . $request->method());
        Log::info('Request URL: ' . $request->fullUrl());
        Log::info('Request Headers: ', $request->headers->all());
        Log::info('Request Input: ', $request->all());

        // DEBUG: فحص الملفات المرفوعة بتفصيل
        Log::info('=== DEBUGGING FILE UPLOADS FOR UPDATE ===');
        Log::info('Request files:', $request->allFiles());

        if ($request->hasFile('car_images')) {
            Log::info('Car images are present in the update request.');
            $images = $request->file('car_images');
            Log::info('Images count: ' . (is_array($images) ? count($images) : 'not array'));

            if (is_array($images)) {
                foreach ($images as $key => $file) {
                    if ($file && $file->isValid()) {
                        Log::info("File {$key} is valid.", [
                            'path' => $file->path(),
                            'original_name' => $file->getClientOriginalName(),
                            'mime_type' => $file->getMimeType(),
                            'size' => $file->getSize(),
                            'extension' => $file->getClientOriginalExtension()
                        ]);
                    } else {
                        Log::error("File {$key} is not valid.", [
                            'error' => $file ? $file->getError() : 'File is null'
                        ]);
                    }
                }
            }
        } else {
            Log::warning('No car images found in the update request in controller.');
        }

        // DEBUG: فحص delete_images
        if ($request->has('delete_images')) {
            Log::info('Delete images: ', $request->input('delete_images'));
        } else {
            Log::info('No images to delete');
        }

        Log::info('=== END FILE UPLOAD DEBUG FOR UPDATE ===');

        try {
            Log::info('Car update started for car ID: ' . $car->id);
            Log::info('Request data: ', $request->all());

            DB::beginTransaction();

            // تحديث بيانات السيارة
            $carData = $request->validated();
            $carData['updated_by'] = auth()->id();

            Log::info('Validated data: ', $carData);

            // تعيين القيم الافتراضية للحقول المنطقية
            $carData['is_featured'] = $request->boolean('is_featured', false);
            $carData['is_sold'] = $request->boolean('is_sold', false);
            $carData['is_active'] = $request->boolean('is_active', true);

            $car->update($carData);
            Log::info('Car data updated successfully');

            // تحديث الميزات المختارة
            if ($request->has('features') && is_array($request->features)) {
                $car->features()->sync($request->features);
                Log::info('Features synced: ', $request->features);
            } else {
                $car->features()->detach();
                Log::info('All features detached');
            }

            // معالجة حذف الصور المحددة
            if ($request->has('delete_images') && is_array($request->delete_images)) {
                Log::info('Processing image deletions', [
                    'car_id' => $car->id,
                    'delete_images' => $request->delete_images
                ]);

                foreach ($request->delete_images as $mediaId) {
                    // البحث في مجموعة car_images
                    $media = $car->getMedia('car_images')->where('id', $mediaId)->first();
                    if ($media) {
                        Log::info('Deleting image from car_images', [
                            'media_id' => $mediaId,
                            'file_name' => $media->file_name
                        ]);
                        $media->delete();
                    }

                    // التحقق من الصورة الرئيسية أيضاً
                    $mainMedia = $car->getMedia('car_main_image')->where('id', $mediaId)->first();
                    if ($mainMedia) {
                        Log::info('Deleting main image', [
                            'media_id' => $mediaId,
                            'file_name' => $mainMedia->file_name
                        ]);
                        $mainMedia->delete();
                    }
                }
            }

            // معالجة رفع الصور الجديدة
            if ($request->hasFile('car_images')) {
                $this->handleImageUploads($car, $request);
            }

            DB::commit();
            Log::info('Car update transaction committed successfully');

            // TODO: PH02-TASK-025 - إرسال إشعار عند تحديث السيارة
            // $statusChanged = $car->wasChanged(['is_active', 'is_sold', 'is_featured']);
            // if ($statusChanged) {
            //     NotificationService::send([
            //         'type' => 'car_status_changed',
            //         'title' => 'تم تغيير حالة السيارة',
            //         'message' => "تم تحديث حالة السيارة {$car->title}",
            //         'data' => ['car_id' => $car->id, 'car_title' => $car->title, 'changes' => $car->getChanges()],
            //         'recipients' => ['admin', 'sales_manager'],
            //         'channels' => ['database', 'push']
            //     ]);
            // }

            Log::info('Car update completed successfully, redirecting to index');
            return redirect()->route('admin.cars.index')
                ->with('success', 'تم تحديث السيارة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating car: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث السيارة: ' . $e->getMessage());
        }
    }

    /**
     * حذف سيارة من النظام (حذف ناعم).
     *
     * يقوم بحذف السيارة باستخدام الحذف الناعم مع إزالة جميع الصور المرتبطة
     * ويحتفظ بالبيانات في قاعدة البيانات لأغراض التدقيق
     *
     * @param Car $car نموذج السيارة المراد حذفها
     *
     * @return RedirectResponse إعادة توجيه إلى قائمة السيارات مع رسالة نجاح
     *
     * @throws \Exception في حالة فشل عملية الحذف
     */
    public function destroy(Car $car): RedirectResponse
    {
        try {
            // حذف جميع الصور المرتبطة
            $car->clearMediaCollection('car_images');
            $car->clearMediaCollection('car_main_image');

            // حذف السيارة (soft delete)
            $car->delete();

            // TODO: PH02-TASK-025 - إرسال إشعار عند حذف السيارة
            // NotificationService::send([
            //     'type' => 'car_deleted',
            //     'title' => 'تم حذف سيارة',
            //     'message' => "تم حذف السيارة {$car->title} من النظام",
            //     'data' => ['car_id' => $car->id, 'car_title' => $car->title, 'deleted_by' => auth()->user()->name],
            //     'recipients' => ['admin', 'inventory_manager'],
            //     'channels' => ['database', 'email'],
            //     'priority' => 'high'
            // ]);

            return redirect()->route('admin.cars.index')
                ->with('success', 'تم حذف السيارة بنجاح');
        } catch (\Exception $e) {
            Log::error('Error deleting car: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف السيارة. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * جلب موديلات السيارات حسب الماركة (نقطة نهاية AJAX).
     *
     * يستخدم هذا المتحكم في واجهة إضافة/تعديل السيارة لتحديث قائمة الموديلات
     * ديناميكياً عند اختيار ماركة معينة
     *
     * @param Request $request طلب HTTP (غير مستخدم حالياً)
     * @param Brand $brand نموذج الماركة المختارة
     *
     * @return JsonResponse استجابة JSON تحتوي على قائمة الموديلات
     *
     * @throws \Exception في حالة فشل جلب البيانات
     */
    public function getModelsByBrand(Request $request, Brand $brand): JsonResponse
    {
        try {
            Log::info('getModelsByBrand called for brand ID: ' . $brand->id);

            $models = CarModel::where('brand_id', $brand->id)
                ->where('status', true)
                ->orderBy('name')
                ->get(['id', 'name'])
                ->map(function ($model) {
                    return [
                        'id'   => $model->id,
                        'name' => $model->getTranslation('name', app()->getLocale()) ?? $model->name,
                    ];
                });

            Log::info('Found ' . $models->count() . ' models for brand ID: ' . $brand->id);

            return response()->json([
                'success' => true,
                'models'  => $models,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching models by brand: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الموديلات',
            ], 500);
        }
    }

    /**
     * معالجة رفع الصور للسيارة.
     *
     * يقوم برفع الصور المختارة وتعيين الصورة الرئيسية
     * ويضيف الصور إلى المجموعات المناسبة في مكتبة الوسائط
     *
     * @param Car $car نموذج السيارة المراد إضافة الصور لها
     * @param Request $request طلب HTTP يحتوي على الصور المرفوعة
     *
     * @throws \Exception في حالة فشل رفع الصور
     */
    private function handleImageUploads(Car $car, Request $request): void
    {
        try {
            $images = $request->file('car_images');

            if (!$images || !is_array($images)) {
                Log::info('No images to upload');
                return;
            }

            $mainImageIndex = (int) $request->input('main_image_index', 0);

            Log::info('Starting image upload process', [
                'car_id' => $car->id,
                'images_count' => count($images),
                'main_image_index' => $mainImageIndex
            ]);

            $uploadedImages = [];
            $mainImageMedia = null;

            // رفع جميع الصور إلى مجموعة car_images أولاً
            foreach ($images as $index => $image) {
                if ($image && $image->isValid()) {
                    // التحقق من نوع الملف
                    if (!in_array($image->getMimeType(), ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'])) {
                        Log::warning('Invalid image type', [
                            'index' => $index,
                            'mime_type' => $image->getMimeType()
                        ]);
                        continue;
                    }

                    // التحقق من حجم الملف (5MB = 5120KB)
                    if ($image->getSize() > 5120 * 1024) {
                        Log::warning('Image too large', [
                            'index' => $index,
                            'size' => $image->getSize()
                        ]);
                        continue;
                    }

                    // رفع الصورة إلى مجموعة car_images
                    try {
                        Log::info('Attempting to add media to car', [
                            'car_id' => $car->id,
                            'image_index' => $index,
                            'image_name' => $image->getClientOriginalName(),
                            'image_size' => $image->getSize(),
                            'image_mime' => $image->getMimeType()
                        ]);

                        $mediaItem = $car->addMedia($image)
                            ->usingName("صورة السيارة " . ($index + 1))
                            ->usingFileName($this->generateUniqueFileName($image))
                            ->toMediaCollection('car_images');

                        $uploadedImages[] = $mediaItem;

                        Log::info('Media item added successfully', [
                            'index' => $index,
                            'media_id' => $mediaItem->id,
                            'file_name' => $mediaItem->file_name,
                            'disk' => $mediaItem->disk,
                            'collection_name' => $mediaItem->collection_name,
                            'model_type' => $mediaItem->model_type,
                            'model_id' => $mediaItem->model_id,
                            'size' => $mediaItem->size,
                            'mime_type' => $mediaItem->mime_type,
                            'url' => $mediaItem->getUrl()
                        ]);

                        // التحقق من وجود الملف فعلياً في التخزين
                        $filePath = $mediaItem->getPath();
                        if (file_exists($filePath)) {
                            Log::info('File exists on disk', ['path' => $filePath]);
                        } else {
                            Log::error('File does not exist on disk', ['path' => $filePath]);
                        }

                    } catch (\Spatie\MediaLibrary\MediaCollections\Exceptions\FileCannotBeAdded $e) {
                        Log::error('Spatie FileCannotBeAdded Exception', [
                            'index' => $index,
                            'file' => $image->getClientOriginalName(),
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        continue;
                    } catch (\Exception $e) {
                        Log::error('Generic Exception during media addition', [
                            'index' => $index,
                            'file' => $image->getClientOriginalName(),
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        continue;
                    }

                    // حفظ الصورة الرئيسية للمعالجة لاحقاً
                    if ($index == $mainImageIndex) {
                        $mainImageMedia = $mediaItem;
                    }
                } else {
                    Log::warning('Invalid image file', ['index' => $index]);
                }
            }

            // معالجة الصورة الرئيسية
            if ($mainImageMedia) {
                try {
                    // حذف الصورة الرئيسية السابقة إن وجدت
                    $car->clearMediaCollection('car_main_image');

                    // نسخ الصورة الرئيسية من الملف الموجود بدلاً من URL
                    $originalPath = $mainImageMedia->getPath();

                    if (file_exists($originalPath)) {
                        // إنشاء نسخة مؤقتة من الملف لتجنب حذف الأصل
                        $tempPath = storage_path('app/temp_main_image_' . time() . '_' . basename($originalPath));
                        copy($originalPath, $tempPath);

                        $car->addMedia($tempPath)
                            ->usingName("الصورة الرئيسية للسيارة")
                            ->usingFileName($this->generateUniqueFileName($mainImageMedia, 'main_'))
                            ->toMediaCollection('car_main_image');

                        // حذف الملف المؤقت
                        if (file_exists($tempPath)) {
                            unlink($tempPath);
                        }

                        Log::info('Main image set successfully', [
                            'main_image_index' => $mainImageIndex,
                            'main_image_id' => $mainImageMedia->id,
                            'original_path' => $originalPath
                        ]);
                    } else {
                        Log::warning('Main image file not found', [
                            'path' => $originalPath,
                            'main_image_id' => $mainImageMedia->id
                        ]);
                    }

                } catch (\Exception $e) {
                    Log::error('Error setting main image', [
                        'main_image_index' => $mainImageIndex,
                        'main_image_id' => $mainImageMedia->id,
                        'error' => $e->getMessage()
                    ]);
                    // لا نرمي الاستثناء هنا لأن الصور الأخرى تم رفعها بنجاح
                }
            }

            Log::info('Image upload process completed', [
                'uploaded_count' => count($uploadedImages),
                'main_image_set' => $mainImageMedia ? true : false
            ]);

        } catch (\Exception $e) {
            Log::error('Error uploading images: ' . $e->getMessage(), [
                'car_id' => $car->id,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * توليد اسم ملف فريد للصورة
     *
     * @param \Illuminate\Http\UploadedFile|\Spatie\MediaLibrary\MediaCollections\Models\Media $file
     * @param string $prefix
     * @return string
     */
    private function generateUniqueFileName($file, $prefix = ''): string
    {
        // التحقق من نوع الكائن للحصول على الامتداد
        if ($file instanceof \Illuminate\Http\UploadedFile) {
            $extension = $file->getClientOriginalExtension();
        } elseif ($file instanceof \Spatie\MediaLibrary\MediaCollections\Models\Media) {
            $extension = pathinfo($file->file_name, PATHINFO_EXTENSION);
        } else {
            $extension = 'jpg'; // امتداد افتراضي
        }

        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = \Str::random(8);

        return $prefix . "car_image_{$timestamp}_{$random}.{$extension}";
    }
}
