<?php

namespace Modules\UserManagement\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Modules\UserManagement\Notifications\ResetPasswordNotification;

/**
 * User Model
 *
 * يمثل هذا النموذج جدول المستخدمين في النظام
 *
 * @property int $id
 * @property string $first_name الاسم الأول للمستخدم
 * @property string $last_name الاسم الأخير للمستخدم
 * @property string $email البريد الإلكتروني للمستخدم
 * @property string $phone_number رقم الجوال للمستخدم
 * @property \Illuminate\Support\Carbon|null $email_verified_at تاريخ التحقق من البريد الإلكتروني
 * @property \Illuminate\Support\Carbon|null $phone_verified_at تاريخ التحقق من رقم الجوال
 * @property string $password كلمة المرور (مشفرة)
 * @property int|null $profile_photo_id معرّف الصورة الشخصية
 * @property string $status حالة الحساب
 * @property bool $can_refer_customer هل يمكن للمستخدم ترشيح عملاء جدد
 * @property \Illuminate\Support\Carbon|null $last_login_at تاريخ آخر تسجيل دخول
 * @property string|null $address_line1 العنوان
 * @property string|null $city المدينة
 * @property string|null $national_id رقم الهوية الوطنية
 * @property \Illuminate\Support\Carbon|null $date_of_birth تاريخ الميلاد
 * @property int|null $nationality_id معرّف الجنسية
 * @property string|null $otp_code رمز التحقق لمرة واحدة
 * @property \Illuminate\Support\Carbon|null $otp_expires_at تاريخ انتهاء صلاحية رمز التحقق
 * @property string|null $remember_token رمز تذكر تسجيل الدخول
 * @property \Illuminate\Support\Carbon|null $created_at تاريخ الإنشاء
 * @property \Illuminate\Support\Carbon|null $updated_at تاريخ التحديث
 * @property \Illuminate\Support\Carbon|null $deleted_at تاريخ الحذف الناعم
 * @property-read \Modules\UserManagement\Models\Nationality|null $nationality علاقة الجنسية
 */
class User extends Authenticatable
{
    use HasFactory;
    use HasRoles;
    use Notifiable;
    use SoftDeletes;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي
     *
     * @var array
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone_number',
        'email_verified_at',
        'phone_verified_at',
        'password',
        'profile_photo_id',
        'status',
        'can_refer_customer',
        'last_login_at',
        'address_line1',
        'city',
        'national_id',
        'date_of_birth',
        'nationality_id',
        'otp_code',
        'otp_expires_at',
    ];

    /**
     * الخصائص التي يجب إخفاؤها عند التسلسل
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'otp_code',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'can_refer_customer' => 'boolean',
        'date_of_birth' => 'date',
        'otp_expires_at' => 'datetime',
    ];

    /**
     * علاقة الجنسية التي ينتمي إليها المستخدم
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function nationality()
    {
        return $this->belongsTo(Nationality::class);
    }

    /**
     * الحصول على الاسم الكامل للمستخدم
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return \Modules\UserManagement\Database\Factories\UserFactory::new();
    }

    /**
     * إرسال إشعار إعادة تعيين كلمة المرور المخصص
     *
     * @param string $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPasswordNotification($token));
    }

    /**
     * علاقة السيارات المفضلة للمستخدم
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function favorites()
    {
        return $this->belongsToMany(
            \Modules\CarCatalog\Models\Car::class,
            'user_favorites',
            'user_id',
            'car_id'
        )->withTimestamps();
    }

    /**
     * علاقة طلبات المستخدم
     * علاقة واحد لمتعدد - المستخدم له العديد من الطلبات
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orders()
    {
        return $this->hasMany(\Modules\OrderManagement\Models\Order::class);
    }

    /**
     * علاقة الطلبات المعينة للموظف
     * علاقة واحد لمتعدد - الموظف له العديد من الطلبات المعينة له
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function assignedOrders()
    {
        return $this->hasMany(\Modules\OrderManagement\Models\Order::class, 'assigned_employee_id');
    }

    // سيتم إضافة دوال Media Collections لاحقًا عند تثبيت حزمة spatie/laravel-medialibrary
}
