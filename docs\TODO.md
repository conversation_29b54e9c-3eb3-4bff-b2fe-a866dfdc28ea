# TODO List

## Phase 01: Foundation and Dash Setup
- [x] TASK-ID:PH01-TASK-001 LARAVEL-PROJECT-VERIFY-AND-LOGS-SETUP-001
- [x] TASK-ID:PH01-TASK-002 LARAVEL-MODULES-SETUP-001
- [x] TASK-ID:PH01-TASK-003 CORE-MODULE-CREATION-001
- [x] TASK-ID:PH01-TASK-004 CORE-MODULE-BASE-CLASSES-001
- [x] TASK-ID:PH01-TASK-005 CORE-MODULE-GLOBAL-HELPERS-001
- [x] TASK-ID:PH01-TASK-006 USER-MANAGEMENT-MODULE-SETUP-001
- [x] TASK-ID:PH01-TASK-008 SPATIE-PERMISSION-SETUP-001
- [x] TASK-ID:PH01-TASK-009 DASH-LAYOUT-SETUP-001
- [x] TASK-ID:PH01-TASK-010 DASH-LAYOUT-PARTIALS-SETUP-001
- [x] TASK-ID:PH01-TASK-011 DASH-ADMIN-LOGIN-VIEW-001
- [x] TASK-ID:PH01-TASK-007 ADMIN-AUTH-IMPLEMENTATION-001
- [x] TASK-ID:PH01-TASK-012 CORE-MODULE-SETTINGS-DB-SERVICE-001
- [x] TASK-ID:PH01-TASK-015 DASH-ADMIN-PASSWORD-RESET-VIEWS-001
- [x] TASK-ID:PH01-TASK-008A SPATIE-PERMISSION-DEFAULT-ADMIN-USER-001
- [x] TASK-ID:PH01-TASK-013 DASH-ADMIN-INITIAL-DASHBOARD-VIEW-001
- [x] TASK-ID:PH01-TASK-014 ADMIN-DASHBOARD-BASIC-ROUTING-CONTROLLER-001
- [x] TASK-ID:PH01-TASK-016 INITIAL-CODE-LINTING-FORMATTING-001
- [x] TASK-ID:PH01-TASK-017 DASH-INITIAL-LOGIN-AND-LAYOUT-TEST-001
- [x] TASK-ID:PH01-TASK-018 SPATIE-MIDDLEWARE-REGISTRATION-001

## Phase 02: Car Catalog Module
- [x] PH-02-DEL-003: إنشاء موديول CarCatalog وقاعدة بياناته
- [x] PH-02-DEL-008: إنشاء موديول Notification وتنفيذ الإشعارات الأساسية
- [x] PH-02-DEL-004 (Part 1): تنفيذ واجهات CRUD لإدارة ماركات السيارات (Brands) في لوحة تحكم الإدارة Dash
- [x] PH-02-DEL-004 (Part 2): تنفيذ واجهات CRUD لإدارة ألوان السيارات (Colors) في لوحة تحكم الإدارة Dash
- [x] PH-02-DEL-004 (Part 3): تنفيذ واجهات CRUD لإدارة سنوات صنع السيارات (ManufacturingYear) في لوحة تحكم الإدارة Dash
- [x] PH-02-DEL-004 (Part 4): تنفيذ واجهات CRUD لإدارة أنواع ناقل الحركة (TransmissionType) في لوحة تحكم الإدارة Dash
- [x] PH-02-DEL-004 (Part 5): تنفيذ واجهات CRUD لإدارة أنواع الوقود (FuelType) في لوحة تحكم الإدارة Dash
- [x] PH-02-DEL-004 (Part 6): تنفيذ واجهات CRUD لإدارة أنواع هياكل السيارات (BodyType) في لوحة تحكم الإدارة Dash
- [x] PH-02-DEL-004 (Part 7): تنفيذ واجهات CRUD لإدارة فئات الميزات (FeatureCategory) في لوحة تحكم الإدارة Dash
- [x] PH-02-DEL-005: تنفيذ واجهة إضافة/تعديل السيارة (DASH-CAR-CRUD-001) في لوحة تحكم الإدارة Dash باستخدام مكون Stepper
- [x] PH-02-DEL-001: تنفيذ واجهة لوحة البيانات الرئيسية للإدارة (DASH-ADMIN-HOME-001) مع البطاقات الإحصائية والرسوم البيانية ببيانات وهمية
- [x] PH02-TASK-020-DASH-ADMIN-HOME-DATA-INTEGRATION-002: ربط واجهة لوحة البيانات الرئيسية ببيانات ديناميكية من Controller وقاعدة البيانات
- [x] PH02-TASK-029-DASH-SIDEBAR-CAR-CATALOG-LINKS-VERIFICATION-001: التحقق من وتحديث جميع الروابط داخل القائمة الفرعية "إدارة السيارات" في _sidebar.blade.php
- [x] PH02-TASK-023-DASH-ROUTE-PERMISSION-CONSISTENCY-CHECK-001: مراجعة جميع المسارات الإدارية والتأكد من تطبيق وسائط الحماية بشكل صحيح ومتسق
- [x] PH02-TASK-024-DASH-HELPER-FUNCTIONS-STANDARDIZATION-001: مراجعة وتوحيد الدوال المساعدة والتأكد من تنظيمها في المكان المناسب
- [x] PH02-TASK-025-DASH-NOTIFICATION-INTEGRATION-PLACEHOLDERS-001: تحديد وتوثيق أماكن الإشعارات في Controllers وإضافة placeholders للتكامل المستقبلي

## Phase 02: Car Images Upload Debugging
- [x] TASK-ID:PH02-FIX-CAR-IMAGES-003 CARCATALOG-UPLOAD-DEBUG-VALIDATION-REQUEST: التحقق بدقة من تدفق طلب رفع الصور من النموذج عبر FormRequest إلى Controller (مكتمل)
  - ✅ تم التحقق من تكوين النموذج وenctype="multipart/form-data"
  - ✅ تم التحقق من قواعد التحقق في FormRequests
  - ✅ تم التأكد من وصول الملفات إلى Controller بشكل صحيح
  - ✅ تم إنشاء ملفات اختبار شاملة للتحقق من التدفق
  - 📋 النتيجة: النظام مكون بشكل صحيح، المشكلة قد تكون في معالجة spatie/laravel-medialibrary
- [x] TASK-ID:PH02-FIX-CAR-IMAGES-004 CARCATALOG-MEDIALIB-INTEGRATION-VERIFICATION: التحقق والتصحيح الدقيق لتكامل spatie/laravel-medialibrary مع نموذج Car (مكتمل)
  - ✅ تم فحص تكوين spatie/laravel-medialibrary وfilesystems
  - ✅ تم التحقق من إعداد نموذج Car مع HasMedia و InteractsWithMedia
  - ✅ تم اكتشاف وإصلاح مشكلة في معالجة الصورة الرئيسية (addMediaFromUrl → addMedia)
  - ✅ تم إضافة كود تصحيح شامل ومعالجة الاستثناءات
  - ✅ تم اختبار العملية الكاملة وتأكيد عمل النظام بشكل صحيح
  - 📋 النتيجة: نظام رفع الصور يعمل بشكل مثالي الآن

## Phase 02: Car CRUD Database Fix
- [x] TASK-ID:PH02-FIX-CAR-CRUD-005 CARCATALOG-DATABASE-MISSING-COLUMNS: إصلاح مشكلة الأعمدة المفقودة في جدول cars (مكتمل)
  - ✅ تم تشخيص المشكلة: أعمدة video_url, offer_price, offer_start_date, offer_end_date مفقودة
  - ✅ تم إنشاء migration جديد لإضافة الأعمدة المفقودة
  - ✅ تم إضافة الفهارس المناسبة للأعمدة الجديدة
  - ✅ تم اختبار النظام برمجياً والتأكد من عمل جميع العمليات
  - ✅ تم التحقق من عمل إضافة السيارات وحفظ البيانات والصور
  - 📋 النتيجة: نظام إدارة السيارات يعمل بشكل مثالي الآن

## Phase 02: Car Images Display Fix
- [x] TASK-ID:PH02-FIX-CAR-IMAGES-006 CARCATALOG-IMAGES-DISPLAY-ISSUES: إصلاح مشكلة عرض الصور في واجهات إدارة السيارات (مكتمل)
  - ✅ تم تشخيص المشكلة: APP_URL مزدوج الشرطة المائلة + صور أصلية مفقودة + عدم استخدام التحويلات
  - ✅ تم إصلاح تكوين APP_URL في .env لإزالة الشرطة المائلة النهائية
  - ✅ تم إصلاح معالجة الصورة الرئيسية لتجنب حذف الملف الأصلي
  - ✅ تم تحديث ملفات العرض لاستخدام التحويلات مع fallback للصورة الأصلية
  - ✅ تم إنشاء صورة افتراضية SVG للسيارات بدون صور
  - ✅ تم إنشاء أداة إصلاح الصور المفقودة وإصلاح 2 صورة بنجاح
  - ✅ تم اختبار عرض الصور في جميع الواجهات (قائمة، تفاصيل، تعديل)
  - 📋 النتيجة: جميع الصور تعرض بشكل صحيح في جميع الواجهات

## Phase 03: Site Frontend Development
- [x] PH03-TASK-001 FE-BLADE-SITE-LAYOUT-MAIN-001: إنشاء ملف التخطيط الرئيسي للموقع العام (site_layout.blade.php) مع الرأس والتذييل
  - ✅ تم إنشاء ملف التخطيط الرئيسي site_layout.blade.php مع دعم RTL والهوية البصرية
  - ✅ تم إنشاء ملف الرأس _header.blade.php مع قائمة التنقل وأيقونات التفاعل ومودال البحث
  - ✅ تم إنشاء ملف التذييل _footer.blade.php مع أقسام الروابط ومعلومات الاتصال ووسائل التواصل
  - ✅ تم إنشاء ملفات CSS و JavaScript مخصصة للموقع العام (site_app.css, site_app.js)
  - ✅ تم تكامل مع Laravel Vite لإدارة الأصول
  - ✅ تم تطبيق الهوية البصرية الموحدة للمشروع
- [x] PH03-TASK-002 BE-CTRL-SITE-HOMEPAGE-DISPLAY-001: إنشاء Controller للصفحة الرئيسية للموقع العام
  - ✅ تم إنشاء HomepageController في app/Http/Controllers/Site/ مع دالة index()
  - ✅ تم جلب السيارات المميزة من موديول CarCatalog مع eager loading للعلاقات والصور
  - ✅ تم إعداد مؤقت للبنرات والعروض (سيتم تفعيلها عند إنشاء موديولات Cms و PromotionManagement)
  - ✅ تم إنشاء view للصفحة الرئيسية (home.blade.php) مع تصميم متجاوب وأقسام ديناميكية
  - ✅ تم إضافة routes للصفحة الرئيسية ومسارات مؤقتة للسيارات والعروض
  - ✅ تم تطبيق أفضل الممارسات في استخدام Eloquent مع with() للأداء الأمثل
- [x] PH03-TASK-003 FE-BLADE-SITE-HOMEPAGE-VIEW-001: إنشاء ملف Blade view للصفحة الرئيسية للموقع العام
  - ✅ تم تحديث ملف home.blade.php ليتوافق مع المواصفات المحددة في UIUX-FR.md
  - ✅ تم إنشاء قسم السلايدر الرئيسي مع البنرات الديناميكية ومؤشرات وأسهم التحكم
  - ✅ تم إنشاء قسم البحث السريع عن السيارات مع نماذج تفاعلية للماركة والموديل والسنة ونطاق السعر
  - ✅ تم تحسين قسم السيارات المميزة مع عرض تفصيلي للمواصفات والأسعار والعروض الخاصة
  - ✅ تم تحسين قسم العروض الحالية مع تصميم بطاقات جذابة وشارات العروض المحدودة
  - ✅ تم إنشاء قسم الخدمات المتكاملة مع 6 خدمات رئيسية
  - ✅ تم إنشاء قسم "لماذا تختارنا؟" مع 4 نقاط قوة رئيسية
  - ✅ تم إنشاء قسم "اطلب سيارتك المخصصة" مع دعوة للعمل واضحة
  - ✅ تم تطبيق أنماط CSS متقدمة مع تأثيرات التحويم والانتقالات السلسة
  - ✅ تم إضافة JavaScript تفاعلي للسلايدر والبحث الديناميكي ووظائف المفضلة
  - ✅ تم تحسين الاستجابة للأجهزة المختلفة مع media queries شاملة
  - ✅ تم إضافة نظام إشعارات Toast للتفاعل مع المستخدم
  - ✅ تم تطبيق مبادئ الأداء مع Lazy Loading للصور و Intersection Observer
- [x] PH03-TASK-004 BE-CTRL-SITE-AUTH-PAGES-SETUP-001: إنشاء SiteAuthController في موديول UserManagement
  - ✅ تم إنشاء SiteAuthController في Modules/UserManagement/Http/Controllers/Site/
  - ✅ تم تنفيذ جميع الدوال المطلوبة: showRegisterForm, showLoginForm, showVerifyOtpForm, showForgotPasswordForm, showResetPasswordForm
  - ✅ تم إضافة التوثيق الشامل لجميع الدوال مع شرح الغرض والمعاملات
  - ✅ تم تطبيق أفضل الممارسات في تنظيم Controllers مع امتداد BaseController
  - ✅ تم تمرير البيانات اللازمة (رقم الجوال، الرمز المميز، البريد الإلكتروني) للـ views
  - 📋 النتيجة: SiteAuthController جاهز لعرض جميع صفحات المصادقة في الموقع العام
- [x] PH03-TASK-005 FE-BLADE-SITE-AUTH-REGISTER-VIEW-001: إنشاء ملف Blade view لنموذج التسجيل
  - ✅ تم إنشاء ملف register.blade.php في resources/views/site/auth/
  - ✅ تم تنفيذ نموذج تسجيل شامل مع جميع الحقول المطلوبة وفقاً لـ UIUX-FR.md و REQ-FR.md
  - ✅ تم إضافة التحقق من صحة البيانات مع عرض أخطاء التحقق لكل حقل
  - ✅ تم استخدام old() helper للحفاظ على القيم المدخلة عند فشل التحقق
  - ✅ تم إضافة checkbox للموافقة على الشروط والأحكام مع روابط مناسبة
  - ✅ تم تطبيق تصميم متجاوب مع CSS مخصص وJavaScript تفاعلي
  - ✅ تم إضافة وظائف إظهار/إخفاء كلمة المرور وتنسيق رقم الجوال
  - ✅ تم إضافة رابط للانتقال إلى صفحة تسجيل الدخول
  - 📋 النتيجة: صفحة تسجيل كاملة وجاهزة للاستخدام مع تجربة مستخدم ممتازة
- [x] PH03-TASK-006 FE-BLADE-SITE-AUTH-LOGIN-VIEW-001: إنشاء ملف Blade view لنموذج تسجيل الدخول
  - ✅ تم إنشاء مسارات المصادقة للموقع العام في Modules/UserManagement/Routes/web.php
  - ✅ تم إنشاء ملف login.blade.php في resources/views/site/auth/
  - ✅ تم تطبيق التصميم المحدد في UIUX-FR.md (SITE-AUTH-LOGIN-001)
  - ✅ تم إضافة جميع الحقول المطلوبة: معرف الدخول، كلمة المرور، تذكرني
  - ✅ تم إضافة روابط نسيت كلمة المرور وإنشاء حساب جديد
  - ✅ تم تطبيق التحقق من صحة البيانات وعرض الأخطاء
  - ✅ تم إضافة ميزة إظهار/إخفاء كلمة المرور
  - ✅ تم تطبيق الأنماط البصرية المتسقة مع صفحة التسجيل
  - ✅ تم ربط النموذج بالمسارات المناسبة مع @csrf protection
  - 📋 النتيجة: صفحة تسجيل الدخول جاهزة للاستخدام في الموقع العام
- [x] PH03-TASK-010 BE-LOGIC-SITE-AUTH-REGISTER-PROCESS-001: تنفيذ منطق الـ Backend لمعالجة طلب تسجيل مستخدم جديد
  - ✅ تم إنشاء RegisterRequest FormRequest مع قواعد التحقق الشاملة
  - ✅ تم إنشاء UserVerificationOtpNotification لإرسال رموز OTP
  - ✅ تم تنفيذ دالة register() في SiteAuthController لمعالجة التسجيل
  - ✅ تم تنفيذ دالة verifyOtp() في SiteAuthController للتحقق من OTP
  - ✅ تم إضافة مسارات POST للتسجيل والتحقق من OTP
  - ✅ تم تطبيق معايير الأمان: تشفير OTP، إدارة الجلسات، انتهاء صلاحية OTP
  - ✅ تم إنشاء اختبارات وحدة أساسية للتحقق من صحة التنفيذ
  - 📋 النتيجة: منطق Backend للتسجيل والتحقق من OTP جاهز للاستخدام
- [x] PH03-TASK-011 BE-LOGIC-SITE-AUTH-LOGIN-PROCESS-001: تنفيذ منطق الـ Backend لمعالجة طلب تسجيل دخول مستخدم من الموقع العام
  - ✅ تم إنشاء LoginRequest FormRequest مع قواعد التحقق الشاملة
  - ✅ تم تنفيذ دالة login() في SiteAuthController لمعالجة تسجيل الدخول
  - ✅ تم تطبيق منطق تحديد نوع المعرف (بريد إلكتروني أو رقم جوال)
  - ✅ تم تطبيق التحقق من دور المستخدم (Customer) وحالة الحساب (active)
  - ✅ تم تطبيق إدارة الجلسات وتحديث last_login_at
  - ✅ تم إضافة مسار POST لتسجيل الدخول مع حماية من brute force attacks
  - ✅ تم إنشاء UserFactory واختبارات شاملة للتحقق من صحة التنفيذ
  - ✅ تم تطبيق معايير الأمان: session regeneration، rate limiting، input validation
  - 📋 النتيجة: منطق Backend لتسجيل الدخول جاهز للاستخدام في الموقع العام
- [x] PH03-TASK-012 BE-LOGIC-SITE-AUTH-PASSWORD-RESET-PROCESS-001: تنفيذ منطق الـ Backend الكامل لعملية استعادة كلمة المرور
  - ✅ تم إضافة دالة sendResetLinkEmail() في SiteAuthController لمعالجة طلب إرسال رابط إعادة التعيين
  - ✅ تم إضافة دالة resetPassword() في SiteAuthController لمعالجة إعادة تعيين كلمة المرور الجديدة
  - ✅ تم إنشاء ResetPasswordRequest FormRequest مع قواعد التحقق الشاملة لكلمة المرور الجديدة
  - ✅ تم إنشاء ResetPasswordNotification مخصص لإرسال رابط إعادة التعيين باللغة العربية
  - ✅ تم تحديث نموذج User لاستخدام notification المخصص عبر sendPasswordResetNotification()
  - ✅ تم إضافة مسارات POST لطلب إعادة التعيين وتنفيذ إعادة التعيين مع حماية throttle
  - ✅ تم تطبيق آليات Laravel المدمجة للأمان: Password facade، tokens آمنة، انتهاء صلاحية
  - ✅ تم إضافة معالجة شاملة للأخطاء مع رسائل واضحة باللغة العربية
  - ✅ تم تطبيق تسجيل دخول تلقائي للمستخدم بعد إعادة التعيين الناجحة
  - 📋 النتيجة: نظام استعادة كلمة المرور آمن وكامل جاهز للاستخدام في الموقع العام
- [ ] PH03-TASK-007 FE-BLADE-SITE-AUTH-VERIFY-OTP-VIEW-001: إنشاء ملف Blade view لنموذج التحقق من OTP
- [ ] PH03-TASK-008 FE-BLADE-SITE-AUTH-FORGOT-PASSWORD-VIEW-001: إنشاء ملف Blade view لنموذج طلب استعادة كلمة المرور
- [x] PH03-TASK-009 FE-BLADE-SITE-AUTH-RESET-PASSWORD-VIEW-001: إنشاء ملف Blade view لنموذج إعادة تعيين كلمة المرور
  - ✅ تم إنشاء ملف reset_password.blade.php في resources/views/site/auth/
  - ✅ تم تطبيق التصميم المحدد في UIUX-FR.md (SITE-AUTH-RESET-PASSWORD-001)
  - ✅ تم إضافة جميع الحقول المطلوبة: البريد الإلكتروني (للقراءة فقط)، كلمة المرور الجديدة، تأكيد كلمة المرور
  - ✅ تم إضافة حقل الرمز المميز المخفي (token) للأمان
  - ✅ تم تطبيق التحقق من صحة البيانات وعرض الأخطاء
  - ✅ تم إضافة ميزة إظهار/إخفاء كلمة المرور للحقلين
  - ✅ تم إضافة التحقق الفوري من تطابق كلمات المرور
  - ✅ تم تطبيق الأنماط البصرية المتسقة مع صفحات المصادقة الأخرى
  - ✅ تم ربط النموذج بالمسار المناسب مع @csrf protection
  - ✅ تم إضافة رابط العودة لتسجيل الدخول
  - 📋 النتيجة: صفحة إعادة تعيين كلمة المرور جاهزة للاستخدام في الموقع العام
- [x] PH03-TASK-013 BE-CTRL-SITE-CAR-CATALOG-LIST-DISPLAY-001: إنشاء Controller لقائمة السيارات مع الفلاتر والترتيب
  - ✅ تم إنشاء SiteCarController في Modules/CarCatalog/Http/Controllers/Site/
  - ✅ تم تنفيذ دالة index() مع دعم كامل للفلترة والترتيب والترقيم
  - ✅ تم دعم فلاتر: البحث النصي، الماركات، الموديلات، نطاق السعر، الألوان، أنواع الوقود، ناقل الحركة، نوع الهيكل
  - ✅ تم دعم خيارات الترتيب: الأحدث، السعر تصاعدي/تنازلي، المميزة أولاً
  - ✅ تم تطبيق eager loading للعلاقات لتحسين الأداء
  - ✅ تم إنشاء دالة getFilterOptions() لجلب خيارات الفلاتر الديناميكية
  - ✅ تم إضافة مسار AJAX لجلب الموديلات حسب الماركة
  - 📋 النتيجة: Controller كامل وجاهز لعرض قائمة السيارات مع فلترة متقدمة
- [x] PH03-TASK-014 FE-BLADE-SITE-CAR-LIST-VIEW-001: إنشاء واجهة عرض قائمة السيارات والفلاتر
  - ✅ تم إنشاء ملف index.blade.php في resources/views/site/cars/
  - ✅ تم تطبيق التصميم المحدد في UIUX-FR.md (SITE-CAR-LIST-001)
  - ✅ تم إنشاء شريط فلاتر متقدم مع جميع الخيارات المطلوبة
  - ✅ تم عرض الفلاتر المطبقة حالياً مع إمكانية إزالتها
  - ✅ تم إنشاء بطاقات السيارات مع الصور والمعلومات الأساسية والمواصفات
  - ✅ تم دعم عرض السيارات المميزة مع شارات خاصة
  - ✅ تم إضافة إحصائيات سريعة (عدد السيارات، عدد الفلاتر)
  - ✅ تم تطبيق ترقيم الصفحات مع الحفاظ على query parameters
  - ✅ تم إضافة حالة الفراغ مع رسالة مناسبة
  - ✅ تم استخدام spatie/laravel-medialibrary لعرض الصور مع fallback
  - 📋 النتيجة: واجهة قائمة السيارات كاملة وجاهزة للاستخدام
- [x] PH03-TASK-030 FE-BLADE-SITE-HOW-TO-BUY-VIEW-001: إنشاء ملف Blade view لصفحة "كيف أشتريها؟"
  - ✅ تم إنشاء ملف how_to_buy.blade.php في resources/views/site/info/
  - ✅ تم تطبيق التصميم المحدد في UIUX-FR.md مع شرح خطوات الشراء الكاش والتمويل
  - ✅ تم إضافة قسم شامل لخطوات الشراء بالكاش (5 خطوات) مع المستندات المطلوبة
  - ✅ تم إضافة قسم شامل لخطوات طلب التمويل (5 خطوات) مع المستندات الإضافية
  - ✅ تم تطبيق الهوية البصرية الموحدة مع brand-identity.css
  - ✅ تم إضافة تصميم متجاوب مع تأثيرات CSS متقدمة وانتقالات سلسة
  - ✅ تم إضافة قسم التواصل والمساعدة مع أزرار الاتصال والواتساب
  - ✅ تم إضافة JavaScript تفاعلي مع تأثيرات الحركة عند التمرير
  - ✅ تم تطبيق SEO optimization مع meta tags مناسبة
  - 📋 النتيجة: صفحة "كيف أشتريها؟" كاملة وجاهزة للاستخدام مع تجربة مستخدم ممتازة
- [ ] TODO-SITE-AUTH-ROUTES-CREATION-001: إنشاء المسارات (routes) لدوال SiteAuthController
- [ ] TODO-SITE-HOMEPAGE-JS-SLIDER-001: تفعيل السلايدر الرئيسي بمكتبة JS متقدمة (Swiper.js أو Slick Carousel)
- [ ] TODO-SITE-HOMEPAGE-CSS-DESIGN-001: تطوير تصميم CSS مخصص للصفحة الرئيسية وتحسين الهوية البصرية
- [ ] TODO-SITE-ROUTES-CREATION-001: إنشاء المسارات الأساسية للموقع العام (الرئيسية، السيارات، الخدمات، إلخ)
- [ ] TODO-SITE-CONTROLLERS-CREATION-001: إنشاء Controllers للموقع العام مع الوظائف الأساسية
- [x] TODO-SITE-STATIC-PAGES-CONTROLLER-001: إنشاء Controller لعرض الصفحات الثابتة للموقع العام

## Future Phases / General Todos
- [ ] TODO-DASH-ASSETS-VITE-INTEGRATION-001: Configure Laravel Vite/Mix for Dash assets.
- [x] TODO-SPATIE-PERMISSION-USER-INTEG-001: Fully integrate Spatie Permission with User model.
- [ ] TODO-NOTIFICATION-USER-REGISTRATION-INTEG-001: Integrate sending NewUserWelcomeNotification when a new user registers successfully (within MOD-USER-MGMT).
- [ ] TODO-SPATIE-MEDIALIB-USER-INTEG-001: Fully integrate Spatie Media Library with User model for profile photos.
- [ ] TODO-DASH-FORGOT-PASS-VIEW-001: Create Blade view for admin 'Forgot Password' page.
- [ ] TODO-DASH-RESET-PASS-VIEW-001: Create Blade view for admin 'Reset Password' page.
- [ ] TODO-DASH-SETTINGS-UI-001: Create UI in Dash admin panel for managing system settings.
- [ ] TODO-DASHBOARD-DYNAMIC-CONTENT-001: Implement dynamic content for the main admin dashboard (charts, stats).
- [x] TODO-DASH-SIDEBAR-DYNAMIC-001: Implement dynamic sidebar for Dash admin panel. (Reference: TASK-ID:PH02-ITEM-002)
- [x] TODO-DASH-TOPBAR-DYNAMIC-001: Implement dynamic topbar for Dash admin panel (user info, notifications). (Reference: TASK-ID:PH02-ITEM-002)
- [ ] TODO-CREATE-DEFAULT-ADMIN-USER-001: Create default Super Admin user if not already done, and assign 'Super Admin' role to it.
- [ ] TODO-DASH-TOPBAR-LOGOUT-FIX-001: Update the logout button in the admin dashboard topbar to use a form with POST method to properly call the logout route.
- [ ] TODO-ADMIN-ROUTES-CREATION-001: Create actual routes for all admin dashboard sections referenced in the sidebar (cars, orders, customers, employees, roles, cms, settings).
- [ ] TODO-NOTIFICATION-DYNAMIC-COUNTER-001: Make the notification counter in the topbar dynamic based on MOD-NOTIFICATION-FEAT-002.
