{"preset": "laravel", "rules": {"array_syntax": {"syntax": "short"}, "ordered_imports": {"sort_algorithm": "alpha"}, "no_unused_imports": true, "not_operator_with_successor_space": true, "trailing_comma_in_multiline": true, "phpdoc_scalar": true, "unary_operator_spaces": true, "binary_operator_spaces": {"default": "single_space"}, "blank_line_before_statement": {"statements": ["return"]}, "phpdoc_single_line_var_spacing": true, "phpdoc_var_without_name": true, "method_argument_space": {"on_multiline": "ensure_fully_multiline", "keep_multiple_spaces_after_comma": true}, "single_trait_insert_per_statement": true}, "exclude": ["bootstrap/cache", "storage", "vendor"]}