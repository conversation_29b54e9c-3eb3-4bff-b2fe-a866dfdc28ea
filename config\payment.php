<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Payment Gateway Configuration
    |--------------------------------------------------------------------------
    |
    | إعدادات بوابات الدفع المختلفة المدعومة في النظام
    | يمكن تفعيل/إلغاء تفعيل البوابات حسب الحاجة
    |
    */

    'default_gateway' => env('PAYMENT_DEFAULT_GATEWAY', 'test'),

    'gateways' => [

        /*
        |--------------------------------------------------------------------------
        | Test Gateway (للتطوير والاختبار)
        |--------------------------------------------------------------------------
        */
        'test' => [
            'name' => 'Test Gateway',
            'enabled' => env('PAYMENT_TEST_ENABLED', true),
            'base_url' => env('PAYMENT_TEST_BASE_URL', 'https://test-payment.example.com'),
            'api_key' => env('PAYMENT_TEST_API_KEY', 'test_api_key'),
            'secret_key' => env('PAYMENT_TEST_SECRET_KEY', 'test_secret_key'),
            'webhook_secret' => env('PAYMENT_TEST_WEBHOOK_SECRET', 'test_webhook_secret'),
            'supported_methods' => ['visa', 'mastercard', 'mada', 'apple_pay', 'stc_pay'],
        ],

        /*
        |--------------------------------------------------------------------------
        | Moyasar Gateway
        |--------------------------------------------------------------------------
        */
        'moyasar' => [
            'name' => 'Moyasar',
            'enabled' => env('PAYMENT_MOYASAR_ENABLED', false),
            'base_url' => env('PAYMENT_MOYASAR_BASE_URL', 'https://api.moyasar.com/v1'),
            'api_key' => env('PAYMENT_MOYASAR_API_KEY'),
            'secret_key' => env('PAYMENT_MOYASAR_SECRET_KEY'),
            'webhook_secret' => env('PAYMENT_MOYASAR_WEBHOOK_SECRET'),
            'supported_methods' => ['visa', 'mastercard', 'mada', 'apple_pay', 'stc_pay'],
        ],

        /*
        |--------------------------------------------------------------------------
        | PayTabs Gateway
        |--------------------------------------------------------------------------
        */
        'paytabs' => [
            'name' => 'PayTabs',
            'enabled' => env('PAYMENT_PAYTABS_ENABLED', false),
            'base_url' => env('PAYMENT_PAYTABS_BASE_URL', 'https://secure.paytabs.sa'),
            'api_key' => env('PAYMENT_PAYTABS_API_KEY'),
            'secret_key' => env('PAYMENT_PAYTABS_SECRET_KEY'),
            'webhook_secret' => env('PAYMENT_PAYTABS_WEBHOOK_SECRET'),
            'supported_methods' => ['visa', 'mastercard', 'mada', 'apple_pay', 'stc_pay'],
        ],

        /*
        |--------------------------------------------------------------------------
        | HyperPay Gateway
        |--------------------------------------------------------------------------
        */
        'hyperpay' => [
            'name' => 'HyperPay',
            'enabled' => env('PAYMENT_HYPERPAY_ENABLED', false),
            'base_url' => env('PAYMENT_HYPERPAY_BASE_URL', 'https://eu-test.oppwa.com'),
            'api_key' => env('PAYMENT_HYPERPAY_API_KEY'),
            'secret_key' => env('PAYMENT_HYPERPAY_SECRET_KEY'),
            'webhook_secret' => env('PAYMENT_HYPERPAY_WEBHOOK_SECRET'),
            'supported_methods' => ['visa', 'mastercard', 'mada', 'apple_pay'],
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Settings
    |--------------------------------------------------------------------------
    */
    'currency' => env('PAYMENT_CURRENCY', 'SAR'),
    'timeout' => env('PAYMENT_TIMEOUT', 30), // seconds
    'retry_attempts' => env('PAYMENT_RETRY_ATTEMPTS', 3),

    /*
    |--------------------------------------------------------------------------
    | Webhook Settings
    |--------------------------------------------------------------------------
    */
    'webhook' => [
        'timeout' => env('PAYMENT_WEBHOOK_TIMEOUT', 10), // seconds
        'verify_signature' => env('PAYMENT_WEBHOOK_VERIFY_SIGNATURE', true),
        'allowed_ips' => env('PAYMENT_WEBHOOK_ALLOWED_IPS', ''), // comma separated IPs
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => env('PAYMENT_LOGGING_ENABLED', true),
        'level' => env('PAYMENT_LOGGING_LEVEL', 'info'), // debug, info, warning, error
        'channel' => env('PAYMENT_LOGGING_CHANNEL', 'single'),
    ],

];
