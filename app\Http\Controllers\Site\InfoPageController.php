<?php

namespace App\Http\Controllers\Site;

use App\Http\Controllers\Controller;
use Illuminate\View\View;

/**
 * InfoPageController
 * 
 * Controller مسؤول عن عرض صفحات المعلومات في الموقع العام
 * مثل صفحة "كيف أشتريها؟" وصفحات المعلومات الأخرى
 * 
 * يدعم جلب المحتوى من MOD-CMS عند توفره أو عرض محتوى ثابت
 */
class InfoPageController extends Controller
{
    /**
     * عرض صفحة "كيف أشتريها؟"
     * 
     * يعرض صفحة تحتوي على معلومات حول كيفية شراء السيارة
     * مع دعم جلب المحتوى من CMS عند توفره
     * 
     * @return View
     */
    public function showHowToBuy(): View
    {
        // محاولة جلب المحتوى من MOD-CMS (عند توفره)
        $pageContent = null;
        $pageTitle = 'كيف أشتريها؟';
        
        // TODO: تفعيل هذا الكود عند إنشاء موديول CMS
        /*
        try {
            if (class_exists('\Modules\Cms\Models\CmsPage')) {
                $page = \Modules\Cms\Models\CmsPage::where('slug', 'how-to-buy')
                    ->where('status', 'published')
                    ->first();
                
                if ($page) {
                    $pageContent = $page->content;
                    $pageTitle = $page->title ?? $pageTitle;
                }
            }
        } catch (\Exception $e) {
            // في حالة عدم توفر موديول CMS أو حدوث خطأ
            // سيتم استخدام المحتوى الافتراضي من الـ view
        }
        */
        
        return view('site.info.how_to_buy', compact('pageContent', 'pageTitle'));
    }
}
