@extends('site.layouts.site_layout')

@section('title', 'إنشاء حساب جديد')

@section('content')
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="auth-card">
                    <!-- عنوان الصفحة -->
                    <div class="auth-header text-center mb-4">
                        <h2 class="auth-title">إنشاء حساب جديد</h2>
                        <p class="auth-subtitle text-muted">أدخل بياناتك لإنشاء حساب جديد والاستمتاع بخدماتنا</p>
                    </div>

                    <!-- نموذج التسجيل -->
                    <form method="POST" action="{{ route('site.auth.register.submit') }}" class="auth-form">
                        @csrf

                        <!-- الاسم الأول -->
                        <div class="form-group mb-3">
                            <label for="first_name" class="form-label required">الاسم الأول</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" 
                                       class="form-control @error('first_name') is-invalid @enderror" 
                                       id="first_name" 
                                       name="first_name" 
                                       value="{{ old('first_name') }}" 
                                       placeholder="أدخل الاسم الأول"
                                       required>
                            </div>
                            @error('first_name')
                                <span class="text-danger small">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- اسم العائلة -->
                        <div class="form-group mb-3">
                            <label for="last_name" class="form-label required">اسم العائلة</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" 
                                       class="form-control @error('last_name') is-invalid @enderror" 
                                       id="last_name" 
                                       name="last_name" 
                                       value="{{ old('last_name') }}" 
                                       placeholder="أدخل اسم العائلة"
                                       required>
                            </div>
                            @error('last_name')
                                <span class="text-danger small">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- البريد الإلكتروني -->
                        <div class="form-group mb-3">
                            <label for="email" class="form-label required">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" 
                                       class="form-control @error('email') is-invalid @enderror" 
                                       id="email" 
                                       name="email" 
                                       value="{{ old('email') }}" 
                                       placeholder="أدخل البريد الإلكتروني"
                                       required>
                            </div>
                            @error('email')
                                <span class="text-danger small">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- رقم الجوال -->
                        <div class="form-group mb-3">
                            <label for="phone_number" class="form-label required">رقم الجوال</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                <input type="tel" 
                                       class="form-control @error('phone_number') is-invalid @enderror" 
                                       id="phone_number" 
                                       name="phone_number" 
                                       value="{{ old('phone_number') }}" 
                                       placeholder="05xxxxxxxx"
                                       pattern="05[0-9]{8}"
                                       required>
                            </div>
                            <small class="form-text text-muted">يجب أن يبدأ الرقم بـ 05 ويتكون من 10 أرقام</small>
                            @error('phone_number')
                                <span class="text-danger small">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- كلمة المرور -->
                        <div class="form-group mb-3">
                            <label for="password" class="form-label required">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password" 
                                       placeholder="أدخل كلمة المرور"
                                       required>
                                <button type="button" class="btn btn-outline-secondary toggle-password" data-target="password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <small class="form-text text-muted">يجب أن تحتوي على 8 أحرف على الأقل، حروف كبيرة وصغيرة، أرقام ورموز</small>
                            @error('password')
                                <span class="text-danger small">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- تأكيد كلمة المرور -->
                        <div class="form-group mb-3">
                            <label for="password_confirmation" class="form-label required">تأكيد كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" 
                                       class="form-control @error('password_confirmation') is-invalid @enderror" 
                                       id="password_confirmation" 
                                       name="password_confirmation" 
                                       placeholder="أعد إدخال كلمة المرور"
                                       required>
                                <button type="button" class="btn btn-outline-secondary toggle-password" data-target="password_confirmation">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            @error('password_confirmation')
                                <span class="text-danger small">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- الموافقة على الشروط والأحكام -->
                        <div class="form-group mb-4">
                            <div class="form-check">
                                <input type="checkbox" 
                                       class="form-check-input @error('agree_terms') is-invalid @enderror" 
                                       id="agree_terms" 
                                       name="agree_terms" 
                                       value="1" 
                                       {{ old('agree_terms') ? 'checked' : '' }}
                                       required>
                                <label class="form-check-label" for="agree_terms">
                                    أوافق على 
                                    <a href="{{ route('site.terms') }}" target="_blank" class="text-primary">الشروط والأحكام</a>
                                    و
                                    <a href="{{ route('site.privacy') }}" target="_blank" class="text-primary">سياسة الخصوصية</a>
                                </label>
                            </div>
                            @error('agree_terms')
                                <span class="text-danger small">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- زر إنشاء الحساب -->
                        <div class="form-group mb-3">
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-user-plus me-2"></i>
                                إنشاء حساب
                            </button>
                        </div>

                        <!-- رابط تسجيل الدخول -->
                        <div class="text-center">
                            <p class="mb-0">
                                لديك حساب بالفعل؟ 
                                <a href="{{ route('site.auth.login.form') }}" class="text-primary fw-bold">تسجيل الدخول</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* أنماط خاصة بصفحة التسجيل */
.auth-container {
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    padding: 2rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.auth-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    font-size: 0.95rem;
    line-height: 1.5;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
}

.input-group-text {
    background-color: #f8f9fa;
    border-left: none;
    color: var(--secondary-color);
}

.form-control {
    border-right: none;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
}

.toggle-password {
    border-right: none;
    background-color: #f8f9fa;
}

.toggle-password:hover {
    background-color: #e9ecef;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-danger {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

@media (max-width: 768px) {
    .auth-card {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .auth-container {
        padding: 1rem 0;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // وظيفة إظهار/إخفاء كلمة المرور
    const toggleButtons = document.querySelectorAll('.toggle-password');
    
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const targetInput = document.getElementById(targetId);
            const icon = this.querySelector('i');
            
            if (targetInput.type === 'password') {
                targetInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                targetInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
    
    // التحقق من قوة كلمة المرور
    const passwordInput = document.getElementById('password');
    const passwordConfirmation = document.getElementById('password_confirmation');
    
    passwordInput.addEventListener('input', function() {
        validatePasswordStrength(this.value);
    });
    
    passwordConfirmation.addEventListener('input', function() {
        validatePasswordMatch();
    });
    
    function validatePasswordStrength(password) {
        // يمكن إضافة منطق التحقق من قوة كلمة المرور هنا
        // مثل عرض مؤشر قوة كلمة المرور
    }
    
    function validatePasswordMatch() {
        const password = passwordInput.value;
        const confirmation = passwordConfirmation.value;
        
        if (confirmation && password !== confirmation) {
            passwordConfirmation.classList.add('is-invalid');
        } else {
            passwordConfirmation.classList.remove('is-invalid');
        }
    }
    
    // تنسيق رقم الجوال
    const phoneInput = document.getElementById('phone_number');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
        
        if (value.length > 0 && !value.startsWith('05')) {
            if (value.startsWith('5')) {
                value = '0' + value;
            } else if (!value.startsWith('0')) {
                value = '05' + value;
            }
        }
        
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        
        this.value = value;
    });
});
</script>
@endpush
