{{--
    الخطوة 2: معلومات التمويل - عملية طلب التمويل

    يعرض هذا الـ view نموذج لجمع معلومات التمويل والعمل
    بناءً على UIUX-FR.md (SITE-BUY-FINANCE-STEPX-001) و REQ-FR.md (MOD-ORDER-MGMT-FEAT-004)
--}}

@extends('site.layouts.site_layout')

@section('title', 'طلب التمويل - معلومات التمويل')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">

            {{-- عنوان الصفحة ومؤشر التقدم --}}
            <div class="text-center mb-4">
                <h2 class="mb-3">طلب تمويل السيارة</h2>

                {{-- مؤشر التقدم (Stepper) --}}
                <div class="progress-stepper mb-4">
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">البيانات الشخصية</div>
                    </div>
                    <div class="step active">
                        <div class="step-number">2</div>
                        <div class="step-title">معلومات التمويل</div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">المستندات</div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-title">المراجعة والتأكيد</div>
                    </div>
                </div>
            </div>

            <div class="row">
                {{-- ملخص السيارة وحاسبة التمويل --}}
                <div class="col-lg-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                تفاصيل السيارة
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($car->getFirstMediaUrl('car_main_image') || $car->getFirstMediaUrl('car_images'))
                                <img src="{{ $car->getFirstMediaUrl('car_main_image') ?: $car->getFirstMediaUrl('car_images') }}"
                                     alt="{{ $car->title }}"
                                     class="img-fluid rounded mb-3">
                            @endif

                            <h6 class="fw-bold">{{ $car->title }}</h6>
                            <p class="text-muted mb-2">
                                {{ $car->brand->name ?? '' }} - {{ $car->carModel->name ?? '' }}
                            </p>
                            <p class="text-muted mb-3">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $car->manufacturingYear->year ?? '' }}
                                <span class="mx-2">|</span>
                                <i class="fas fa-palette me-1"></i>
                                {{ $car->mainColor->name ?? '' }}
                            </p>

                            {{-- تفاصيل الأسعار --}}
                            <div class="price-breakdown">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>سعر السيارة:</span>
                                    <span class="fw-bold" id="car-price">{{ number_format($car->price, 0) }} {{ $car->currency }}</span>
                                </div>

                                <hr class="my-2">

                                <div class="d-flex justify-content-between mb-2 text-success">
                                    <span>الدفعة الأولى:</span>
                                    <span class="fw-bold" id="down-payment-display">0 {{ $car->currency }}</span>
                                </div>

                                <div class="d-flex justify-content-between mb-3 text-primary">
                                    <span>مبلغ التمويل المطلوب:</span>
                                    <span class="fw-bold" id="finance-amount-display">{{ number_format($car->price, 0) }} {{ $car->currency }}</span>
                                </div>

                                <div class="alert alert-info small">
                                    <i class="fas fa-info-circle me-1"></i>
                                    سيتم تحديد القسط الشهري وفترة السداد بناءً على دراسة طلب التمويل
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- نموذج معلومات التمويل --}}
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-percentage me-2"></i>
                                معلومات التمويل والعمل
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('site.order.finance.step2.process') }}" method="POST" id="financeInfoForm">
                                @csrf

                                {{-- معلومات التمويل --}}
                                <div class="mb-4">
                                    <h6 class="text-success mb-3">
                                        <i class="fas fa-money-bill-wave me-2"></i>
                                        تفاصيل التمويل
                                    </h6>

                                    <div class="row">
                                        {{-- الدفعة الأولى --}}
                                        <div class="col-md-6 mb-3">
                                            <label for="down_payment_amount" class="form-label">
                                                الدفعة الأولى <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="number"
                                                       class="form-control @error('down_payment_amount') is-invalid @enderror"
                                                       id="down_payment_amount"
                                                       name="down_payment_amount"
                                                       value="{{ old('down_payment_amount', 0) }}"
                                                       min="0"
                                                       max="{{ $car->price * 0.5 }}"
                                                       step="1000"
                                                       required>
                                                <span class="input-group-text">{{ $car->currency }}</span>
                                            </div>
                                            <div class="form-text">الحد الأدنى: 0 ريال، الحد الأقصى: {{ number_format($car->price * 0.5, 0) }} ريال</div>
                                            @error('down_payment_amount')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        {{-- الدخل الشهري --}}
                                        <div class="col-md-6 mb-3">
                                            <label for="monthly_income" class="form-label">
                                                الدخل الشهري <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="number"
                                                       class="form-control @error('monthly_income') is-invalid @enderror"
                                                       id="monthly_income"
                                                       name="monthly_income"
                                                       value="{{ old('monthly_income') }}"
                                                       min="3000"
                                                       max="100000"
                                                       step="100"
                                                       required>
                                                <span class="input-group-text">ريال</span>
                                            </div>
                                            <div class="form-text">الحد الأدنى: 3,000 ريال</div>
                                            @error('monthly_income')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="row">
                                        {{-- الالتزامات الشهرية --}}
                                        <div class="col-md-6 mb-3">
                                            <label for="monthly_obligations" class="form-label">
                                                الالتزامات الشهرية <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="number"
                                                       class="form-control @error('monthly_obligations') is-invalid @enderror"
                                                       id="monthly_obligations"
                                                       name="monthly_obligations"
                                                       value="{{ old('monthly_obligations', 0) }}"
                                                       min="0"
                                                       step="100"
                                                       required>
                                                <span class="input-group-text">ريال</span>
                                            </div>
                                            <div class="form-text">أقساط أخرى، إيجار، التزامات مالية</div>
                                            @error('monthly_obligations')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        {{-- فترة التمويل المرغوبة --}}
                                        <div class="col-md-6 mb-3">
                                            <label for="preferred_finance_period" class="form-label">
                                                فترة التمويل المرغوبة
                                            </label>
                                            <select class="form-select @error('preferred_finance_period') is-invalid @enderror"
                                                    id="preferred_finance_period"
                                                    name="preferred_finance_period">
                                                <option value="">اختر الفترة (اختياري)</option>
                                                <option value="12" {{ old('preferred_finance_period') == '12' ? 'selected' : '' }}>12 شهر</option>
                                                <option value="24" {{ old('preferred_finance_period') == '24' ? 'selected' : '' }}>24 شهر</option>
                                                <option value="36" {{ old('preferred_finance_period') == '36' ? 'selected' : '' }}>36 شهر</option>
                                                <option value="48" {{ old('preferred_finance_period') == '48' ? 'selected' : '' }}>48 شهر</option>
                                                <option value="60" {{ old('preferred_finance_period') == '60' ? 'selected' : '' }}>60 شهر</option>
                                            </select>
                                            <div class="form-text">سيتم تحديد الفترة النهائية بناءً على دراسة الطلب</div>
                                            @error('preferred_finance_period')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                {{-- معلومات العمل --}}
                                <div class="mb-4">
                                    <h6 class="text-success mb-3">
                                        <i class="fas fa-briefcase me-2"></i>
                                        معلومات العمل
                                    </h6>

                                    <div class="row">
                                        {{-- نوع العمل --}}
                                        <div class="col-md-6 mb-3">
                                            <label for="employment_type" class="form-label">
                                                نوع العمل <span class="text-danger">*</span>
                                            </label>
                                            <select class="form-select @error('employment_type') is-invalid @enderror"
                                                    id="employment_type"
                                                    name="employment_type"
                                                    required>
                                                <option value="">اختر نوع العمل</option>
                                                <option value="government" {{ old('employment_type') == 'government' ? 'selected' : '' }}>حكومي</option>
                                                <option value="private" {{ old('employment_type') == 'private' ? 'selected' : '' }}>قطاع خاص</option>
                                                <option value="self_employed" {{ old('employment_type') == 'self_employed' ? 'selected' : '' }}>أعمال حرة</option>
                                                <option value="retired" {{ old('employment_type') == 'retired' ? 'selected' : '' }}>متقاعد</option>
                                            </select>
                                            @error('employment_type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        {{-- المسمى الوظيفي --}}
                                        <div class="col-md-6 mb-3">
                                            <label for="job_title" class="form-label">
                                                المسمى الوظيفي <span class="text-danger">*</span>
                                            </label>
                                            <input type="text"
                                                   class="form-control @error('job_title') is-invalid @enderror"
                                                   id="job_title"
                                                   name="job_title"
                                                   value="{{ old('job_title') }}"
                                                   required>
                                            @error('job_title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="row">
                                        {{-- جهة العمل --}}
                                        <div class="col-md-6 mb-3">
                                            <label for="employer_name" class="form-label">
                                                جهة العمل <span class="text-danger">*</span>
                                            </label>
                                            <input type="text"
                                                   class="form-control @error('employer_name') is-invalid @enderror"
                                                   id="employer_name"
                                                   name="employer_name"
                                                   value="{{ old('employer_name') }}"
                                                   required>
                                            @error('employer_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        {{-- سنوات الخبرة --}}
                                        <div class="col-md-6 mb-3">
                                            <label for="years_of_experience" class="form-label">
                                                سنوات الخبرة <span class="text-danger">*</span>
                                            </label>
                                            <select class="form-select @error('years_of_experience') is-invalid @enderror"
                                                    id="years_of_experience"
                                                    name="years_of_experience"
                                                    required>
                                                <option value="">اختر سنوات الخبرة</option>
                                                <option value="less_than_1" {{ old('years_of_experience') == 'less_than_1' ? 'selected' : '' }}>أقل من سنة</option>
                                                <option value="1_to_3" {{ old('years_of_experience') == '1_to_3' ? 'selected' : '' }}>1-3 سنوات</option>
                                                <option value="3_to_5" {{ old('years_of_experience') == '3_to_5' ? 'selected' : '' }}>3-5 سنوات</option>
                                                <option value="5_to_10" {{ old('years_of_experience') == '5_to_10' ? 'selected' : '' }}>5-10 سنوات</option>
                                                <option value="more_than_10" {{ old('years_of_experience') == 'more_than_10' ? 'selected' : '' }}>أكثر من 10 سنوات</option>
                                            </select>
                                            @error('years_of_experience')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    {{-- البنك المحول عليه الراتب --}}
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="salary_bank" class="form-label">
                                                البنك المحول عليه الراتب <span class="text-danger">*</span>
                                            </label>
                                            <select class="form-select @error('salary_bank') is-invalid @enderror"
                                                    id="salary_bank"
                                                    name="salary_bank"
                                                    required>
                                                <option value="">اختر البنك</option>
                                                <option value="الراجحي" {{ old('salary_bank') == 'الراجحي' ? 'selected' : '' }}>مصرف الراجحي</option>
                                                <option value="الأهلي" {{ old('salary_bank') == 'الأهلي' ? 'selected' : '' }}>البنك الأهلي السعودي</option>
                                                <option value="سامبا" {{ old('salary_bank') == 'سامبا' ? 'selected' : '' }}>بنك سامبا</option>
                                                <option value="الرياض" {{ old('salary_bank') == 'الرياض' ? 'selected' : '' }}>بنك الرياض</option>
                                                <option value="ساب" {{ old('salary_bank') == 'ساب' ? 'selected' : '' }}>البنك السعودي البريطاني</option>
                                                <option value="الفرنسي" {{ old('salary_bank') == 'الفرنسي' ? 'selected' : '' }}>البنك السعودي الفرنسي</option>
                                                <option value="الهولندي" {{ old('salary_bank') == 'الهولندي' ? 'selected' : '' }}>البنك السعودي الهولندي</option>
                                                <option value="الاستثمار" {{ old('salary_bank') == 'الاستثمار' ? 'selected' : '' }}>بنك الاستثمار</option>
                                                <option value="الجزيرة" {{ old('salary_bank') == 'الجزيرة' ? 'selected' : '' }}>بنك الجزيرة</option>
                                                <option value="البلاد" {{ old('salary_bank') == 'البلاد' ? 'selected' : '' }}>بنك البلاد</option>
                                                <option value="أخرى" {{ old('salary_bank') == 'أخرى' ? 'selected' : '' }}>أخرى</option>
                                            </select>
                                            @error('salary_bank')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                {{-- معلومات إضافية --}}
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        ملاحظات مهمة
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li>جميع المعلومات المدخلة سرية وآمنة</li>
                                        <li>سيتم التحقق من المعلومات مع الجهات ذات العلاقة</li>
                                        <li>قد تختلف شروط التمويل النهائية حسب دراسة الطلب</li>
                                        <li>الموافقة على التمويل تخضع لسياسات البنك أو جهة التمويل</li>
                                    </ul>
                                </div>

                                {{-- أزرار التنقل --}}
                                <div class="d-flex justify-content-between mt-4">
                                    <a href="{{ route('site.order.finance.step1', $car->id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        السابق
                                    </a>

                                    <button type="submit" class="btn btn-success">
                                        التالي: رفع المستندات
                                        <i class="fas fa-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress-stepper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.5;
}

.step.active, .step.completed {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.step.active .step-number {
    background-color: #198754;
    color: white;
}

.step.completed .step-number {
    background-color: #198754;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    text-align: center;
}

.price-breakdown {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const carPrice = {{ $car->price }};
    const downPaymentInput = document.getElementById('down_payment_amount');
    const monthlyIncomeInput = document.getElementById('monthly_income');
    const monthlyObligationsInput = document.getElementById('monthly_obligations');

    // تحديث عرض الأسعار
    function updatePriceDisplay() {
        const downPayment = parseFloat(downPaymentInput.value) || 0;
        const financeAmount = carPrice - downPayment;

        document.getElementById('down-payment-display').textContent =
            new Intl.NumberFormat('ar-SA').format(downPayment) + ' {{ $car->currency }}';
        document.getElementById('finance-amount-display').textContent =
            new Intl.NumberFormat('ar-SA').format(financeAmount) + ' {{ $car->currency }}';
    }

    // التحقق من الدفعة الأولى
    if (downPaymentInput) {
        downPaymentInput.addEventListener('input', function() {
            const value = parseFloat(this.value) || 0;
            const maxDownPayment = carPrice * 0.5;

            if (value > maxDownPayment) {
                this.setCustomValidity('الدفعة الأولى لا يمكن أن تتجاوز 50% من سعر السيارة');
            } else if (value < 0) {
                this.setCustomValidity('الدفعة الأولى لا يمكن أن تكون أقل من صفر');
            } else {
                this.setCustomValidity('');
            }

            updatePriceDisplay();
        });

        // تحديث العرض عند تحميل الصفحة
        updatePriceDisplay();
    }

    // التحقق من الدخل الشهري
    if (monthlyIncomeInput) {
        monthlyIncomeInput.addEventListener('input', function() {
            const value = parseFloat(this.value) || 0;

            if (value < 3000) {
                this.setCustomValidity('الدخل الشهري يجب أن يكون 3000 ريال على الأقل');
            } else if (value > 100000) {
                this.setCustomValidity('الدخل الشهري لا يمكن أن يتجاوز 100,000 ريال');
            } else {
                this.setCustomValidity('');
            }

            // تحديث الحد الأقصى للالتزامات
            if (monthlyObligationsInput) {
                monthlyObligationsInput.max = value * 0.8;
            }
        });
    }

    // التحقق من الالتزامات الشهرية
    if (monthlyObligationsInput) {
        monthlyObligationsInput.addEventListener('input', function() {
            const monthlyIncome = parseFloat(monthlyIncomeInput.value) || 0;
            const obligations = parseFloat(this.value) || 0;
            const maxObligations = monthlyIncome * 0.8;

            if (obligations > maxObligations) {
                this.setCustomValidity('الالتزامات الشهرية لا يمكن أن تتجاوز 80% من الدخل الشهري');
            } else if (obligations < 0) {
                this.setCustomValidity('الالتزامات الشهرية لا يمكن أن تكون أقل من صفر');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // تنسيق الأرقام في الحقول
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value) {
                const value = parseFloat(this.value);
                if (!isNaN(value)) {
                    this.value = Math.round(value);
                }
            }
        });
    });
});
</script>
@endpush
