<?php

namespace Modules\CarCatalog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * ManufacturingYear Model.
 *
 * يمثل هذا النموذج جدول سنوات الصنع في النظام
 *
 * @property int $id
 * @property int $year سنة الصنع (4 أرقام)
 * @property bool $status حالة السنة (نشطة/غير نشطة)
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\CarCatalog\Models\Car[] $cars علاقة السيارات
 */
class ManufacturingYear extends Model
{
    use HasFactory;

    /**
     * تعطيل الطوابع الزمنية (created_at, updated_at)
     * حيث أن بيانات سنوات الصنع عادة ما تكون ثابتة.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي.
     *
     * @var array
     */
    protected $fillable = [
        'year',
        'status',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة.
     *
     * @var array
     */
    protected $casts = [
        'year'   => 'integer',
        'status' => 'boolean',
    ];

    /**
     * علاقة السيارات المرتبطة بسنة الصنع هذه.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cars()
    {
        return $this->hasMany(Car::class);
    }

    /**
     * مصنع النموذج.
     *
     * @return \Modules\CarCatalog\Database\factories\ManufacturingYearFactory
     */
    protected static function newFactory()
    {
        return \Modules\CarCatalog\Database\factories\ManufacturingYearFactory::new();
    }
}
