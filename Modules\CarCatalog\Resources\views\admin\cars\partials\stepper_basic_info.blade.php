<div id="basic-info-part" class="content" role="tabpanel" aria-labelledby="basic-info-part-trigger">
    <h6 class="mb-4 text-primary">
        <i class="fas fa-info-circle me-2"></i>
        البيانات الأساسية للسيارة
    </h6>

    <div class="row g-3">
        <!-- عنوان السيارة -->
        <div class="col-md-6">
            <label for="title" class="form-label">عنوان السيارة <span class="text-danger">*</span></label>
            <input type="text" class="form-control @error('title') is-invalid @enderror"
                   id="title" name="title"
                   value="{{ old('title', isset($car) ? $car->title : '') }}"
                   placeholder="مثال: تويوتا كامري فل كامل 2025"
                   maxlength="200" required>
            <div class="form-text">اكتب عنوان واضح ومميز للسيارة</div>
            @error('title')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- الماركة -->
        <div class="col-md-3">
            <label for="brand_id" class="form-label">الماركة <span class="text-danger">*</span></label>
            <select class="form-select @error('brand_id') is-invalid @enderror" id="brand_id" name="brand_id" required>
                <option value="">اختر الماركة</option>
                @foreach($brands as $brand)
                    <option value="{{ $brand->id }}"
                            {{ old('brand_id', isset($car) ? $car->brand_id : '') == $brand->id ? 'selected' : '' }}>
                        {{ $brand->name }}
                    </option>
                @endforeach
            </select>
            <div class="form-text">اختر الماركة لتظهر قائمة الموديلات المتاحة</div>
            @error('brand_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- الموديل -->
        <div class="col-md-3">
            <label for="car_model_id" class="form-label">الموديل <span class="text-danger">*</span></label>
            <select class="form-select @error('car_model_id') is-invalid @enderror"
                    id="car_model_id" name="car_model_id" required>
                <option value="">اختر الموديل</option>
                @if(isset($models))
                    @foreach($models as $model)
                        <option value="{{ $model->id }}"
                                {{ old('car_model_id', isset($car) ? $car->car_model_id : '') == $model->id ? 'selected' : '' }}>
                            {{ $model->name }}
                        </option>
                    @endforeach
                @endif
            </select>
            <div class="form-text">سيتم تحديث قائمة الموديلات بناءً على الماركة المختارة</div>
            @error('car_model_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- سنة الصنع -->
        <div class="col-md-4">
            <label for="manufacturing_year_id" class="form-label">سنة الصنع <span class="text-danger">*</span></label>
            <select class="form-select @error('manufacturing_year_id') is-invalid @enderror"
                    id="manufacturing_year_id" name="manufacturing_year_id" required>
                <option value="">اختر السنة</option>
                @foreach($years as $year)
                    <option value="{{ $year->id }}"
                            {{ old('manufacturing_year_id', isset($car) ? $car->manufacturing_year_id : '') == $year->id ? 'selected' : '' }}>
                        {{ $year->year }}
                    </option>
                @endforeach
            </select>
            @error('manufacturing_year_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- اللون الخارجي -->
        <div class="col-md-4">
            <label for="main_color_id" class="form-label">اللون الخارجي <span class="text-danger">*</span></label>
            <select class="form-select @error('main_color_id') is-invalid @enderror"
                    id="main_color_id" name="main_color_id" required>
                <option value="">اختر اللون</option>
                @foreach($colors as $color)
                    <option value="{{ $color->id }}"
                            {{ old('main_color_id', isset($car) ? $car->main_color_id : '') == $color->id ? 'selected' : '' }}>
                        {{ $color->name }}
                        @if($color->hex_code)
                            <span style="background-color: {{ $color->hex_code }}; width: 15px; height: 15px; display: inline-block; margin-right: 5px; border: 1px solid #ccc;"></span>
                        @endif
                    </option>
                @endforeach
            </select>
            @error('main_color_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- لون المقصورة الداخلية -->
        <div class="col-md-4">
            <label for="interior_color_id" class="form-label">لون المقصورة الداخلية</label>
            <select class="form-select @error('interior_color_id') is-invalid @enderror"
                    id="interior_color_id" name="interior_color_id">
                <option value="">اختر اللون (اختياري)</option>
                @foreach($colors as $color)
                    <option value="{{ $color->id }}"
                            {{ old('interior_color_id', isset($car) ? $car->interior_color_id : '') == $color->id ? 'selected' : '' }}>
                        {{ $color->name }}
                    </option>
                @endforeach
            </select>
            @error('interior_color_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- رقم الهيكل (VIN) -->
        <div class="col-md-6">
            <label for="vin" class="form-label">رقم الهيكل (VIN)</label>
            <input type="text" class="form-control @error('vin') is-invalid @enderror"
                   id="vin" name="vin"
                   value="{{ old('vin', isset($car) ? $car->vin : '') }}"
                   placeholder="مثال: 1HGBH41JXMN109186"
                   maxlength="17">
            <div class="form-text">رقم الهيكل الفريد للسيارة (اختياري)</div>
            @error('vin')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- رقم اللوحة -->
        <div class="col-md-6">
            <label for="plate_number" class="form-label">رقم اللوحة</label>
            <input type="text" class="form-control @error('plate_number') is-invalid @enderror"
                   id="plate_number" name="plate_number"
                   value="{{ old('plate_number', isset($car) ? $car->plate_number : '') }}"
                   placeholder="مثال: أ ب ج 1234"
                   maxlength="20">
            <div class="form-text">رقم لوحة السيارة (اختياري)</div>
            @error('plate_number')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- وصف السيارة -->
        <div class="col-12">
            <label for="description" class="form-label">وصف السيارة <span class="text-danger">*</span></label>
            <textarea class="form-control @error('description') is-invalid @enderror"
                      id="description" name="description" rows="6" required
                      placeholder="اكتب وصفاً تفصيلياً للسيارة يشمل المميزات والحالة العامة...">{{ old('description', isset($car) ? $car->description : '') }}</textarea>
            <div class="form-text">اكتب وصفاً شاملاً ومفصلاً للسيارة</div>
            @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <div class="mt-4 d-flex justify-content-end">
        <button type="button" class="btn btn-primary" onclick="stepper.next()">
            التالي: المواصفات الفنية
            <i class="fas fa-arrow-left ms-1"></i>
        </button>
    </div>
</div>
