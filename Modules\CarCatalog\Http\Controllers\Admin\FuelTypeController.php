<?php

namespace Modules\CarCatalog\Http\Controllers\Admin;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\CarCatalog\Http\Requests\Admin\StoreFuelTypeRequest;
use Modules\CarCatalog\Http\Requests\Admin\UpdateFuelTypeRequest;
use Modules\CarCatalog\Models\FuelType;
use Modules\Core\Http\Controllers\BaseController;

/**
 * وحدة تحكم إدارة أنواع الوقود.
 *
 * تتعامل هذه الوحدة مع عمليات CRUD لأنواع الوقود في لوحة تحكم الإدارة
 */
class FuelTypeController extends BaseController
{
    /**
     * عرض قائمة أنواع الوقود.
     *
     * @param Request $request طلب HTTP
     *
     * @return View
     */
    public function index(Request $request): View
    {
        $query = FuelType::withCount('cars');

        // تطبيق فلتر البحث بالاسم إذا تم تقديمه
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->input('search') . '%');
        }

        // تطبيق فلتر الحالة إذا تم تقديمه
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // ترتيب وترقيم النتائج
        $fuelTypes = $query->orderBy('name')->paginate(10);

        return view('carcatalog::admin.fuel-types.index', compact('fuelTypes'));
    }

    /**
     * عرض نموذج إنشاء نوع وقود جديد.
     *
     * @return View
     */
    public function create(): View
    {
        return view('carcatalog::admin.fuel-types.create');
    }

    /**
     * تخزين نوع وقود جديد.
     *
     * @param StoreFuelTypeRequest $request طلب تخزين نوع وقود
     *
     * @return RedirectResponse
     */
    public function store(StoreFuelTypeRequest $request): RedirectResponse
    {
        // إنشاء نوع الوقود باستخدام البيانات المتحقق منها
        FuelType::create($request->validated());

        return redirect()->route('admin.fuel-types.index')->with('success', 'تمت إضافة نوع الوقود بنجاح.');
    }

    /**
     * عرض تفاصيل نوع وقود محدد.
     *
     * @param FuelType $fueltype نوع الوقود المراد عرضه
     *
     * @return View
     */
    public function show(FuelType $fueltype): View
    {
        // تحميل السيارات المرتبطة بنوع الوقود
        $fueltype->loadCount('cars');

        return view('carcatalog::admin.fuel-types.show', compact('fueltype'));
    }

    /**
     * عرض نموذج تعديل نوع وقود موجود.
     *
     * @param FuelType $fueltype نوع الوقود المراد تعديله
     *
     * @return View
     */
    public function edit(FuelType $fueltype): View
    {
        return view('carcatalog::admin.fuel-types.edit', compact('fueltype'));
    }

    /**
     * تحديث نوع وقود موجود.
     *
     * @param UpdateFuelTypeRequest $request طلب تحديث نوع وقود
     * @param FuelType $fueltype نوع الوقود المراد تحديثه
     *
     * @return RedirectResponse
     */
    public function update(UpdateFuelTypeRequest $request, FuelType $fueltype): RedirectResponse
    {
        // تحديث نوع الوقود باستخدام البيانات المتحقق منها
        $fueltype->update($request->validated());

        return redirect()->route('admin.fuel-types.index')->with('success', 'تم تعديل نوع الوقود بنجاح.');
    }

    /**
     * حذف نوع وقود موجود.
     *
     * @param FuelType $fueltype نوع الوقود المراد حذفه
     *
     * @return RedirectResponse
     */
    public function destroy(FuelType $fueltype): RedirectResponse
    {
        // التحقق من عدم وجود سيارات مرتبطة بنوع الوقود
        if ($fueltype->cars()->count() > 0) {
            return redirect()->route('admin.fuel-types.index')
                ->with('error', 'لا يمكن حذف نوع الوقود لأنه مرتبط بسيارات موجودة.');
        }

        // حذف نوع الوقود
        $fueltype->delete();

        return redirect()->route('admin.fuel-types.index')->with('success', 'تم حذف نوع الوقود بنجاح.');
    }
}
