<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('ordermanagement')->group(function() {
    Route::get('/', 'OrderManagementController@index');
});

/*
|--------------------------------------------------------------------------
| Site Order Routes - عمليات شراء السيارات (كاش وتمويل)
|--------------------------------------------------------------------------
|
| Routes لعمليات شراء السيارة متعددة المراحل في الموقع العام:
| - عملية الشراء كاش (4 خطوات)
| - عملية طلب التمويل (4 خطوات)
| يتطلب تسجيل دخول المستخدم (auth middleware)
|
*/

Route::prefix('site/order')->name('site.order.')->middleware(['web', 'auth'])->group(function() {

    // مسارات عملية الشراء كاش
    Route::prefix('cash')->name('cash.')->group(function() {

        // الخطوة 1: البيانات الشخصية
        Route::get('step1/{car}', 'Site\SiteOrderController@showCashOrderStep1PersonalDetails')
            ->name('step1');
        Route::post('step1/process', 'Site\SiteOrderController@processCashOrderStep1')
            ->name('step1.process');

        // الخطوة 2: تفاصيل الحجز والدفع
        Route::get('step2', 'Site\SiteOrderController@showCashOrderStep2BookingPayment')
            ->name('step2');
        Route::post('step2/process', 'Site\SiteOrderController@processCashOrderStep2')
            ->name('step2.process');

        // الخطوة 3: رفع المستندات
        Route::get('step3', 'Site\SiteOrderController@showCashOrderStep3Documents')
            ->name('step3');
        Route::post('step3/process', 'Site\SiteOrderController@processCashOrderStep3')
            ->name('step3.process');

        // الخطوة 4: المراجعة والتأكيد
        Route::get('step4', 'Site\SiteOrderController@showCashOrderStep4ReviewConfirm')
            ->name('step4');
        Route::post('step4/process', 'Site\SiteOrderController@processCashOrderStep4')
            ->name('step4.process');

        // إنشاء الطلب النهائي
        Route::post('store', 'Site\SiteOrderController@storeCashOrder')
            ->name('store');

        // إلغاء عملية الطلب
        Route::post('cancel', 'Site\SiteOrderController@cancelOrder')
            ->name('cancel');
    });

    // مسارات عملية طلب التمويل
    Route::prefix('finance')->name('finance.')->group(function() {

        // الخطوة 1: البيانات الشخصية
        Route::get('step1/{car}', 'Site\SiteOrderController@showFinanceOrderStep1PersonalDetails')
            ->name('step1');
        Route::post('step1/process', 'Site\SiteOrderController@processFinanceOrderStep1')
            ->name('step1.process');

        // الخطوة 2: معلومات التمويل
        Route::get('step2', 'Site\SiteOrderController@showFinanceOrderStep2FinanceInfo')
            ->name('step2');
        Route::post('step2/process', 'Site\SiteOrderController@processFinanceOrderStep2')
            ->name('step2.process');

        // الخطوة 3: رفع المستندات
        Route::get('step3', 'Site\SiteOrderController@showFinanceOrderStep3Documents')
            ->name('step3');
        Route::post('step3/process', 'Site\SiteOrderController@processFinanceOrderStep3')
            ->name('step3.process');

        // الخطوة 4: المراجعة والتأكيد
        Route::get('step4', 'Site\SiteOrderController@showFinanceOrderStep4ReviewConfirm')
            ->name('step4');
        Route::post('step4/process', 'Site\SiteOrderController@processFinanceOrderStep4')
            ->name('step4.process');

        // إنشاء طلب التمويل النهائي
        Route::post('store', 'Site\SiteOrderController@storeFinanceOrder')
            ->name('store');

        // إلغاء عملية طلب التمويل
        Route::post('cancel', 'Site\SiteOrderController@cancelFinanceOrder')
            ->name('cancel');
    });

    // مسارات الدفع والنتائج
    Route::prefix('payment')->name('payment.')->group(function() {
        Route::get('success/{order}', 'Site\SiteOrderController@paymentSuccess')
            ->name('success');
        Route::get('cancel/{order}', 'Site\SiteOrderController@paymentCancel')
            ->name('cancel');
        Route::get('error/{order}', 'Site\SiteOrderController@paymentError')
            ->name('error');
        Route::get('test/{order}', 'Site\SiteOrderController@paymentTest')
            ->name('test');
    });

    // صفحات النتائج
    Route::get('success/{order}', 'Site\SiteOrderController@orderSuccess')
        ->name('success');
    Route::get('details/{order}', 'Site\SiteOrderController@orderDetails')
        ->name('details');

    // مسارات إدارة المستندات عبر AJAX
    Route::prefix('document')->name('document.')->group(function() {

        // رفع مستند واحد عبر AJAX
        Route::post('upload', 'Site\SiteOrderController@uploadDocumentAjax')
            ->name('upload');

        // حذف مستند عبر AJAX
        Route::delete('delete', 'Site\SiteOrderController@deleteDocumentAjax')
            ->name('delete');

        // معاينة مستند
        Route::get('preview/{order}/{mediaId}', 'Site\SiteOrderController@previewDocument')
            ->name('preview')
            ->where(['order' => '[0-9]+', 'mediaId' => '[0-9]+']);

        // تحميل مستند
        Route::get('download/{order}/{mediaId}', 'Site\SiteOrderController@downloadDocument')
            ->name('download')
            ->where(['order' => '[0-9]+', 'mediaId' => '[0-9]+']);

        // الحصول على قائمة مستندات الطلب عبر AJAX
        Route::get('list/{order}', 'Site\SiteOrderController@getOrderDocuments')
            ->name('list')
            ->where('order', '[0-9]+');
    });
});
