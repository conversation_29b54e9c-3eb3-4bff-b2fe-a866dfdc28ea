<?php

namespace Modules\UserManagement\Http\Controllers\Admin;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\Core\Http\Controllers\BaseController;
use Modules\UserManagement\Http\Requests\Admin\StoreRoleRequest;
use Modules\UserManagement\Http\Requests\Admin\UpdateRoleRequest;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

/**
 * متحكم إدارة الأدوار
 *
 * يتعامل هذا المتحكم مع عمليات CRUD للأدوار وتعيين الصلاحيات لها
 */
class RoleController extends BaseController
{
    /**
     * عرض قائمة الأدوار
     *
     * @param Request $request
     * @return View
     */
    public function index(Request $request): View
    {
        $query = Role::query();

        // البحث بالاسم
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // جلب الأدوار مع عدد المستخدمين والصلاحيات
        $roles = $query->withCount(['users', 'permissions'])
                      ->orderBy('name')
                      ->paginate(10);

        return view('usermanagement::admin.roles.index', compact('roles'));
    }

    /**
     * عرض نموذج إنشاء دور جديد
     *
     * @return View
     */
    public function create(): View
    {
        // جلب جميع الصلاحيات المتاحة مجمعة حسب النوع
        $permissions = Permission::all()->groupBy(function($item) {
            return explode('_', $item->name)[0] ?? 'general';
        });

        return view('usermanagement::admin.roles.create', compact('permissions'));
    }

    /**
     * تخزين دور جديد
     *
     * @param StoreRoleRequest $request
     * @return RedirectResponse
     */
    public function store(StoreRoleRequest $request): RedirectResponse
    {
        // إنشاء الدور
        $role = Role::create([
            'name' => $request->name,
            'guard_name' => 'web',
        ]);

        // تعيين الصلاحيات المختارة
        $role->syncPermissions($request->input('permissions', []));

        // TODO: PH02-TASK-025 - إرسال إشعار عند إنشاء دور جديد
        // NotificationService::send([
        //     'type' => 'role_created',
        //     'title' => 'تم إنشاء دور جديد',
        //     'message' => "تم إنشاء الدور '{$role->name}' بنجاح",
        //     'data' => ['role_id' => $role->id, 'role_name' => $role->name, 'permissions_count' => count($request->input('permissions', []))],
        //     'recipients' => ['super_admin'],
        //     'channels' => ['database', 'email'],
        //     'priority' => 'medium'
        // ]);

        return redirect()->route('admin.roles.index')
                        ->with('success', 'تم إنشاء الدور بنجاح.');
    }

    /**
     * عرض تفاصيل دور محدد
     *
     * @param Role $role
     * @return View
     */
    public function show(Role $role): View
    {
        $role->load(['permissions', 'users']);

        return view('usermanagement::admin.roles.show', compact('role'));
    }

    /**
     * عرض نموذج تعديل دور
     *
     * @param Role $role
     * @return View
     */
    public function edit(Role $role): View
    {
        // جلب جميع الصلاحيات المتاحة مجمعة حسب النوع
        $permissions = Permission::all()->groupBy(function($item) {
            return explode('_', $item->name)[0] ?? 'general';
        });

        // جلب الصلاحيات الحالية للدور
        $rolePermissions = $role->permissions->pluck('name')->toArray();

        return view('usermanagement::admin.roles.edit', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * تحديث دور موجود
     *
     * @param UpdateRoleRequest $request
     * @param Role $role
     * @return RedirectResponse
     */
    public function update(UpdateRoleRequest $request, Role $role): RedirectResponse
    {
        // تحديث اسم الدور
        $role->update([
            'name' => $request->name,
        ]);

        // تحديث الصلاحيات
        $oldPermissions = $role->permissions->pluck('name')->toArray();
        $newPermissions = $request->input('permissions', []);
        $role->syncPermissions($newPermissions);

        // TODO: PH02-TASK-025 - إرسال إشعار عند تحديث دور
        // $permissionsChanged = array_diff($oldPermissions, $newPermissions) || array_diff($newPermissions, $oldPermissions);
        // if ($permissionsChanged || $role->wasChanged('name')) {
        //     NotificationService::send([
        //         'type' => 'role_updated',
        //         'title' => 'تم تحديث دور',
        //         'message' => "تم تحديث الدور '{$role->name}' وصلاحياته",
        //         'data' => [
        //             'role_id' => $role->id,
        //             'role_name' => $role->name,
        //             'old_permissions' => $oldPermissions,
        //             'new_permissions' => $newPermissions,
        //             'updated_by' => auth()->user()->name
        //         ],
        //         'recipients' => ['super_admin'],
        //         'channels' => ['database', 'email'],
        //         'priority' => 'high'
        //     ]);
        // }

        return redirect()->route('admin.roles.index')
                        ->with('success', 'تم تحديث الدور بنجاح.');
    }

    /**
     * حذف دور
     *
     * @param Role $role
     * @return RedirectResponse
     */
    public function destroy(Role $role): RedirectResponse
    {
        // منع حذف الأدوار الأساسية
        $protectedRoles = ['Super Admin', 'Employee', 'Customer'];

        if (in_array($role->name, $protectedRoles)) {
            return redirect()->route('admin.roles.index')
                           ->with('error', 'لا يمكن حذف هذا الدور لأنه من الأدوار الأساسية في النظام.');
        }

        // التحقق من وجود مستخدمين مرتبطين بالدور
        if ($role->users()->count() > 0) {
            return redirect()->route('admin.roles.index')
                           ->with('error', 'لا يمكن حذف هذا الدور لأن هناك مستخدمين مرتبطين به.');
        }

        // حذف الدور
        $roleName = $role->name;
        $role->delete();

        // TODO: PH02-TASK-025 - إرسال إشعار عند حذف دور
        // NotificationService::send([
        //     'type' => 'role_deleted',
        //     'title' => 'تم حذف دور',
        //     'message' => "تم حذف الدور '{$roleName}' من النظام",
        //     'data' => ['role_name' => $roleName, 'deleted_by' => auth()->user()->name],
        //     'recipients' => ['super_admin'],
        //     'channels' => ['database', 'email'],
        //     'priority' => 'high'
        // ]);

        return redirect()->route('admin.roles.index')
                        ->with('success', 'تم حذف الدور بنجاح.');
    }
}
