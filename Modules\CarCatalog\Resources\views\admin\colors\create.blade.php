@extends('dashboard::layouts.admin_layout')

@section('title', 'إضافة لون جديد')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إضافة لون جديد',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'الألوان', 'url' => route('admin.colors.index')],
            ['name' => 'إضافة جديد', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.colors.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>'
    ])

    {{-- نموذج إضافة لون جديد --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">بيانات اللون</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.colors.store') }}" method="POST">
                @csrf

                @include('car_catalog::admin.colors._form')

                <div class="mt-4 d-flex justify-content-end">
                    <button type="submit" class="btn btn-success me-2">حفظ اللون</button>
                    <a href="{{ route('admin.colors.index') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
