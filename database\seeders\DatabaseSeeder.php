<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);

        $this->call(\Modules\UserManagement\Database\Seeders\UserManagementDatabaseSeeder::class);
        $this->call(\Modules\Core\Database\Seeders\CoreDatabaseSeeder::class);
        $this->call(\Modules\CarCatalog\Database\Seeders\CarCatalogTestDataSeeder::class);
    }
}
