@extends('dashboard::layouts.admin_layout')

@section('title', 'تعديل الماركة: ' . $brand->name)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'تعديل الماركة: ' . $brand->name,
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'الماركات', 'url' => route('admin.brands.index')],
            ['name' => $brand->name, 'url' => null],
            ['name' => 'تعديل', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.brands.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>'
    ])

    {{-- نموذج تعديل ماركة --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">بيانات الماركة</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.brands.update', $brand->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                @include('car_catalog::admin.brands._form', ['brand' => $brand])

                <div class="mt-4 d-flex justify-content-end">
                    <button type="submit" class="btn btn-success me-2">تحديث الماركة</button>
                    <a href="{{ route('admin.brands.index') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
