@extends('site.layouts.site_layout')

@section('title', 'استعادة كلمة المرور - موتور لاين')

@section('meta_description', 'استعد كلمة المرور الخاصة بك في موتور لاين بسهولة وأمان')

@section('content')
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="auth-card">
                    <!-- شعار الموقع -->
                    <div class="text-center mb-4">
                        <img src="{{ asset('images/logo.png') }}" alt="موتور لاين" class="auth-logo">
                        <h2 class="auth-title mt-3">استعادة كلمة المرور</h2>
                        <p class="auth-subtitle text-muted">أدخل بريدك الإلكتروني المسجل لإرسال رابط إعادة تعيين كلمة المرور</p>
                    </div>

                    <!-- عرض رسالة النجاح -->
                    @if(session('status'))
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('status') }}
                        </div>
                    @endif

                    <!-- عرض رسائل الخطأ العامة -->
                    @if(session('error'))
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ session('error') }}
                        </div>
                    @endif

                    <!-- نموذج طلب استعادة كلمة المرور -->
                    <form action="{{ route('site.auth.password.email.submit') }}" method="POST" class="auth-form">
                        @csrf

                        <!-- حقل البريد الإلكتروني -->
                        <div class="form-group mb-4">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>
                                البريد الإلكتروني المسجل
                            </label>
                            <input type="email" 
                                   class="form-control @error('email') is-invalid @enderror" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email') }}" 
                                   placeholder="أدخل بريدك الإلكتروني المسجل"
                                   required
                                   autofocus>
                            @error('email')
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- زر إرسال رابط إعادة التعيين -->
                        <div class="form-group mb-4">
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-paper-plane me-2"></i>
                                إرسال رابط إعادة التعيين
                            </button>
                        </div>

                        <!-- رابط العودة لتسجيل الدخول -->
                        <div class="text-center">
                            <p class="mb-0">
                                تذكرت كلمة المرور؟ 
                                <a href="{{ route('site.auth.login.form') }}" class="text-primary fw-bold">العودة لتسجيل الدخول</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* أنماط خاصة بصفحة استعادة كلمة المرور */
.auth-container {
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    padding: 2rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.auth-logo {
    max-height: 60px;
    width: auto;
}

.auth-title {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    font-size: 1rem;
    margin-bottom: 0;
    line-height: 1.5;
}

.auth-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.auth-form .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.auth-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 576px) {
    .auth-container {
        padding: 1rem 0;
    }
    
    .auth-card {
        padding: 1.5rem;
        margin: 0 1rem;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
    
    .auth-subtitle {
        font-size: 0.9rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// التركيز على حقل البريد الإلكتروني عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.focus();
    }
});

// إضافة تأثير تحميل على الزر عند الإرسال
document.querySelector('.auth-form').addEventListener('submit', function() {
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    
    // إعادة تفعيل الزر بعد 10 ثوانٍ في حالة عدم الاستجابة
    setTimeout(function() {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }, 10000);
});
</script>
@endpush
