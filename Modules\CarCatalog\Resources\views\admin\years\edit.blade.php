@extends('dashboard::layouts.admin_layout')

@section('title', 'تعديل سنة الصنع: ' . $year->year)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'تعديل سنة الصنع: ' . $year->year,
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'سنوات الصنع', 'url' => route('admin.years.index')],
            ['name' => $year->year, 'url' => null],
            ['name' => 'تعديل', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.years.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>'
    ])

    {{-- نموذج تعديل سنة صنع --}}
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">بيانات سنة الصنع</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.years.update', $year) }}" method="POST">
                        @csrf
                        @method('PUT')

                        @include('carcatalog::admin.years._form')

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.years.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> تحديث
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        {{-- معلومات إضافية --}}
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">معلومات السنة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>رقم السنة:</strong>
                        </div>
                        <div class="col-6">
                            {{ $year->id }}
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <strong>عدد السيارات:</strong>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-info">{{ $year->cars_count ?? 0 }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">ملاحظات مهمة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تنبيه:</h6>
                        <ul class="mb-0">
                            <li>تعديل السنة قد يؤثر على السيارات المرتبطة</li>
                            <li>تأكد من صحة البيانات قبل الحفظ</li>
                            @if($year->cars_count > 0)
                                <li class="text-danger">هذه السنة مرتبطة بـ {{ $year->cars_count }} سيارة</li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
