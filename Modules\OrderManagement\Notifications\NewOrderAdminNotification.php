<?php

namespace Modules\OrderManagement\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\OrderManagement\Models\Order;

/**
 * إشعار طلب جديد للإدارة
 * 
 * يتم إرساله للإدارة عند تقديم طلب شراء سيارة جديد
 */
class NewOrderAdminNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $order;

    /**
     * إنشاء instance جديد من الإشعار
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * تحديد قنوات الإرسال
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * تكوين رسالة البريد الإلكتروني
     */
    public function toMail($notifiable): MailMessage
    {
        $orderUrl = route('admin.orders.show', $this->order->id);
        
        return (new MailMessage)
                    ->subject('طلب حجز جديد - ' . $this->order->order_number)
                    ->greeting('مرحباً')
                    ->line('تم استلام طلب حجز سيارة جديد يتطلب المراجعة.')
                    ->line('**تفاصيل الطلب:**')
                    ->line('رقم الطلب: ' . $this->order->order_number)
                    ->line('العميل: ' . $this->order->user->full_name)
                    ->line('رقم الجوال: ' . $this->order->user->phone_number)
                    ->line('البريد الإلكتروني: ' . $this->order->user->email)
                    ->line('السيارة: ' . $this->order->car->title)
                    ->line('سعر السيارة: ' . number_format($this->order->car_price_at_order, 2) . ' ريال')
                    ->line('مبلغ الحجز: ' . number_format($this->order->reservation_amount, 2) . ' ريال')
                    ->line('طريقة الدفع: ' . $this->getPaymentMethodInArabic())
                    ->line('حالة الدفع: ' . $this->getPaymentStatusInArabic())
                    ->line('تاريخ الطلب: ' . $this->order->created_at->format('Y-m-d H:i'))
                    ->when($this->order->payment_method === 'online_payment', function ($message) {
                        if ($this->order->payment_status === 'completed') {
                            return $message->line('✅ تم دفع مبلغ الحجز - الطلب جاهز للمراجعة');
                        } else {
                            return $message->line('⏳ في انتظار دفع مبلغ الحجز من العميل');
                        }
                    })
                    ->when($this->order->payment_method === 'showroom_payment', function ($message) {
                        return $message->line('💰 سيتم دفع مبلغ الحجز في المعرض - يرجى التواصل مع العميل');
                    })
                    ->action('مراجعة الطلب', $orderUrl)
                    ->line('يرجى مراجعة الطلب واتخاذ الإجراء المناسب.')
                    ->salutation('نظام إدارة الطلبات');
    }

    /**
     * تكوين الإشعار في قاعدة البيانات
     */
    public function toDatabase($notifiable): array
    {
        return [
            'title' => 'طلب حجز جديد',
            'message' => "طلب حجز جديد من {$this->order->user->full_name} للسيارة {$this->order->car->title}",
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'customer_name' => $this->order->user->full_name,
            'customer_phone' => $this->order->user->phone_number,
            'car_title' => $this->order->car->title,
            'reservation_amount' => $this->order->reservation_amount,
            'payment_method' => $this->order->payment_method,
            'payment_status' => $this->order->payment_status,
            'status' => $this->order->status,
            'created_at' => $this->order->created_at,
            'action_url' => route('admin.orders.show', $this->order->id),
            'icon' => 'fas fa-shopping-cart',
            'type' => 'new_order',
            'priority' => $this->order->payment_status === 'completed' ? 'high' : 'normal'
        ];
    }

    /**
     * الحصول على طريقة الدفع بالعربية
     */
    private function getPaymentMethodInArabic(): string
    {
        return match($this->order->payment_method) {
            'online_payment' => 'دفع إلكتروني',
            'showroom_payment' => 'دفع في المعرض',
            default => $this->order->payment_method
        };
    }

    /**
     * الحصول على حالة الدفع بالعربية
     */
    private function getPaymentStatusInArabic(): string
    {
        return match($this->order->payment_status) {
            'pending' => 'في الانتظار',
            'completed' => 'مكتمل',
            'failed' => 'فشل',
            'cancelled' => 'ملغي',
            default => $this->order->payment_status
        };
    }
}
