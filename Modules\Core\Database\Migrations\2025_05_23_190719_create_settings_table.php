<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول إعدادات النظام
 *
 * يستخدم هذا الجدول لتخزين إعدادات النظام العامة مثل اسم الموقع، البريد الإلكتروني للإدارة،
 * مفاتيح API للخدمات الخارجية، إعدادات العملة، نسبة الضريبة، وغيرها
 */
class CreateSettingsTable extends Migration
{
    /**
     * تنفيذ عملية الترحيل وإنشاء الجدول
     *
     * @return void
     */
    public function up()
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key', 255)->unique()->comment('مفتاح الإعداد (فريد)');
            $table->text('value')->nullable()->comment('قيمة الإعداد');
            $table->string('group_name', 100)->nullable()->index()->comment('مجموعة الإعداد (للتصنيف في واجهة الإدارة)');
            $table->string('display_name', 255)->nullable()->comment('الاسم المعروض في واجهة الإدارة');
            $table->string('type', 50)->default('text')->comment('نوع الإعداد (text, number, boolean, textarea, select)');
            $table->json('options')->nullable()->comment('خيارات إضافية للنوع (مثل خيارات قائمة select)');
            $table->boolean('is_translatable')->default(false)->comment('هل قيمة الإعداد قابلة للترجمة؟');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * التراجع عن عملية الترحيل وحذف الجدول
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('settings');
    }
}
