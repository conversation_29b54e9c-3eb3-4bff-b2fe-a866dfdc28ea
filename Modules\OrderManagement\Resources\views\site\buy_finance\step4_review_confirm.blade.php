{{--
    الخطوة 4: المراجعة والتأكيد - عملية طلب التمويل

    يعرض هذا الـ view ملخص شامل لطلب التمويل والتأكيد النهائي
    بناءً على UIUX-FR.md (SITE-BUY-FINANCE-STEPX-001) و REQ-FR.md (MOD-ORDER-MGMT-FEAT-004)
--}}

@extends('site.layouts.site_layout')

@section('title', 'طلب التمويل - المراجعة والتأكيد')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">

            {{-- عنوان الصفحة ومؤشر التقدم --}}
            <div class="text-center mb-4">
                <h2 class="mb-3">طلب تمويل السيارة</h2>

                {{-- مؤشر التقدم (Stepper) --}}
                <div class="progress-stepper mb-4">
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">البيانات الشخصية</div>
                    </div>
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">معلومات التمويل</div>
                    </div>
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">المستندات</div>
                    </div>
                    <div class="step active">
                        <div class="step-number">4</div>
                        <div class="step-title">المراجعة والتأكيد</div>
                    </div>
                </div>
            </div>

            <div class="row">
                {{-- ملخص السيارة والتمويل --}}
                <div class="col-lg-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                ملخص طلب التمويل
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($car->getFirstMediaUrl('car_main_image') || $car->getFirstMediaUrl('car_images'))
                                <img src="{{ $car->getFirstMediaUrl('car_main_image') ?: $car->getFirstMediaUrl('car_images') }}"
                                     alt="{{ $car->title }}"
                                     class="img-fluid rounded mb-3">
                            @endif

                            <h6 class="fw-bold">{{ $car->title }}</h6>
                            <p class="text-muted mb-2">
                                {{ $car->brand->name ?? '' }} - {{ $car->carModel->name ?? '' }}
                            </p>
                            <p class="text-muted mb-3">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $car->manufacturingYear->year ?? '' }}
                                <span class="mx-2">|</span>
                                <i class="fas fa-palette me-1"></i>
                                {{ $car->mainColor->name ?? '' }}
                            </p>

                            {{-- تفاصيل التمويل --}}
                            <div class="finance-summary">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>سعر السيارة:</span>
                                    <span class="fw-bold">{{ number_format($car->price, 0) }} {{ $car->currency }}</span>
                                </div>

                                <div class="d-flex justify-content-between mb-2 text-success">
                                    <span>الدفعة الأولى:</span>
                                    <span class="fw-bold">{{ number_format($financeData['down_payment_amount'] ?? 0, 0) }} {{ $car->currency }}</span>
                                </div>

                                <hr class="my-2">

                                <div class="d-flex justify-content-between mb-3 text-primary">
                                    <span>مبلغ التمويل المطلوب:</span>
                                    <span class="fw-bold">{{ number_format($car->price - ($financeData['down_payment_amount'] ?? 0), 0) }} {{ $car->currency }}</span>
                                </div>

                                <div class="alert alert-info small">
                                    <i class="fas fa-info-circle me-1"></i>
                                    سيتم تحديد القسط الشهري وفترة السداد بعد دراسة الطلب
                                </div>
                            </div>

                            {{-- حالة الطلب --}}
                            <div class="mt-3 p-3 bg-light rounded">
                                <h6 class="text-success mb-2">
                                    <i class="fas fa-hourglass-half me-1"></i>
                                    حالة الطلب
                                </h6>
                                <span class="badge bg-warning text-dark">في انتظار التأكيد</span>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- مراجعة البيانات والتأكيد --}}
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-check me-2"></i>
                                مراجعة البيانات والتأكيد النهائي
                            </h5>
                        </div>
                        <div class="card-body">

                            {{-- البيانات الشخصية --}}
                            <div class="review-section mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-success mb-0">
                                        <i class="fas fa-user me-2"></i>
                                        البيانات الشخصية
                                    </h6>
                                    <a href="{{ route('site.order.finance.step1', $car->id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل
                                    </a>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>الاسم الكامل:</strong><br>
                                        <span class="text-muted">{{ $personalData['full_name'] ?? 'غير محدد' }}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>رقم الهوية:</strong><br>
                                        <span class="text-muted">{{ $personalData['national_id'] ?? 'غير محدد' }}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>رقم الجوال:</strong><br>
                                        <span class="text-muted">{{ $personalData['phone'] ?? 'غير محدد' }}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>البريد الإلكتروني:</strong><br>
                                        <span class="text-muted">{{ $personalData['email'] ?? 'غير محدد' }}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>المدينة:</strong><br>
                                        <span class="text-muted">{{ $personalData['city'] ?? 'غير محدد' }}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>الحالة الاجتماعية:</strong><br>
                                        <span class="text-muted">
                                            @switch($personalData['marital_status'] ?? '')
                                                @case('single') أعزب @break
                                                @case('married') متزوج @break
                                                @case('divorced') مطلق @break
                                                @case('widowed') أرمل @break
                                                @default غير محدد
                                            @endswitch
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {{-- معلومات التمويل --}}
                            <div class="review-section mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-success mb-0">
                                        <i class="fas fa-percentage me-2"></i>
                                        معلومات التمويل والعمل
                                    </h6>
                                    <a href="{{ route('site.order.finance.step2') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل
                                    </a>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>الدخل الشهري:</strong><br>
                                        <span class="text-muted">{{ number_format($financeData['monthly_income'] ?? 0, 0) }} ريال</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>الالتزامات الشهرية:</strong><br>
                                        <span class="text-muted">{{ number_format($financeData['monthly_obligations'] ?? 0, 0) }} ريال</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>نوع العمل:</strong><br>
                                        <span class="text-muted">
                                            @switch($financeData['employment_type'] ?? '')
                                                @case('government') حكومي @break
                                                @case('private') قطاع خاص @break
                                                @case('self_employed') أعمال حرة @break
                                                @case('retired') متقاعد @break
                                                @default غير محدد
                                            @endswitch
                                        </span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>المسمى الوظيفي:</strong><br>
                                        <span class="text-muted">{{ $financeData['job_title'] ?? 'غير محدد' }}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>جهة العمل:</strong><br>
                                        <span class="text-muted">{{ $financeData['employer_name'] ?? 'غير محدد' }}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>البنك المحول عليه الراتب:</strong><br>
                                        <span class="text-muted">{{ $financeData['salary_bank'] ?? 'غير محدد' }}</span>
                                    </div>
                                </div>
                            </div>

                            {{-- المستندات المرفوعة --}}
                            <div class="review-section mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-success mb-0">
                                        <i class="fas fa-file-alt me-2"></i>
                                        المستندات المرفوعة
                                    </h6>
                                    <a href="{{ route('site.order.finance.step3') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل
                                    </a>
                                </div>

                                <div class="documents-list">
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <span>الهوية الوطنية (الوجه الأمامي)</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <span>الهوية الوطنية (الوجه الخلفي)</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <span>رخصة القيادة</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <span>تعريف بالراتب</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <span>كشف حساب بنكي</span>
                                            </div>
                                        </div>
                                        @if(isset($documentsData['additional_documents']))
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <span>مستندات إضافية</span>
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            {{-- نموذج التأكيد النهائي --}}
                            <form action="{{ route('site.order.finance.store') }}" method="POST" id="confirmationForm">
                                @csrf

                                {{-- الشروط والأحكام --}}
                                <div class="mb-4">
                                    <h6 class="text-success mb-3">
                                        <i class="fas fa-file-contract me-2"></i>
                                        الشروط والأحكام
                                    </h6>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input @error('terms_agreement') is-invalid @enderror"
                                               type="checkbox"
                                               id="terms_agreement"
                                               name="terms_agreement"
                                               required>
                                        <label class="form-check-label" for="terms_agreement">
                                            أوافق على
                                            <a href="#" class="text-primary" data-bs-toggle="modal" data-bs-target="#termsModal">شروط وأحكام طلب التمويل</a>
                                            <span class="text-danger">*</span>
                                        </label>
                                        @error('terms_agreement')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input @error('data_accuracy') is-invalid @enderror"
                                               type="checkbox"
                                               id="data_accuracy"
                                               name="data_accuracy"
                                               required>
                                        <label class="form-check-label" for="data_accuracy">
                                            أقر بصحة جميع البيانات والمعلومات المقدمة
                                            <span class="text-danger">*</span>
                                        </label>
                                        @error('data_accuracy')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input @error('privacy_consent') is-invalid @enderror"
                                               type="checkbox"
                                               id="privacy_consent"
                                               name="privacy_consent"
                                               required>
                                        <label class="form-check-label" for="privacy_consent">
                                            أوافق على
                                            <a href="#" class="text-primary" data-bs-toggle="modal" data-bs-target="#privacyModal">سياسة الخصوصية</a>
                                            ومعالجة بياناتي الشخصية
                                            <span class="text-danger">*</span>
                                        </label>
                                        @error('privacy_consent')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                {{-- معلومات مهمة --}}
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>
                                        ماذا يحدث بعد تقديم الطلب؟
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li>سيتم مراجعة طلبك خلال 3-5 أيام عمل</li>
                                        <li>سيتم التواصل معك لتأكيد البيانات أو طلب مستندات إضافية</li>
                                        <li>ستحصل على رد نهائي بالموافقة أو الرفض مع الأسباب</li>
                                        <li>في حالة الموافقة، سيتم تحديد شروط التمويل النهائية</li>
                                        <li>يمكنك متابعة حالة طلبك من خلال حسابك في الموقع</li>
                                    </ul>
                                </div>

                                {{-- أزرار التنقل --}}
                                <div class="d-flex justify-content-between mt-4">
                                    <a href="{{ route('site.order.finance.step3') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        السابق
                                    </a>

                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        إرسال طلب التمويل
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Modal للشروط والأحكام --}}
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">شروط وأحكام طلب التمويل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هنا نص الشروط والأحكام الخاصة بطلب التمويل...</p>
                {{-- يمكن إضافة المحتوى الفعلي للشروط والأحكام هنا --}}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

{{-- Modal لسياسة الخصوصية --}}
<div class="modal fade" id="privacyModal" tabindex="-1" aria-labelledby="privacyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="privacyModalLabel">سياسة الخصوصية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هنا نص سياسة الخصوصية...</p>
                {{-- يمكن إضافة المحتوى الفعلي لسياسة الخصوصية هنا --}}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress-stepper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.5;
}

.step.active, .step.completed {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.step.active .step-number {
    background-color: #198754;
    color: white;
}

.step.completed .step-number {
    background-color: #198754;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    text-align: center;
}

.review-section {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    background-color: #fafafa;
}

.finance-summary {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}

.documents-list {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('confirmationForm');
    const submitBtn = form.querySelector('button[type="submit"]');

    // التحقق من الموافقة على جميع الشروط
    const checkboxes = form.querySelectorAll('input[type="checkbox"][required]');

    function updateSubmitButton() {
        let allChecked = true;
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                allChecked = false;
            }
        });

        submitBtn.disabled = !allChecked;

        if (allChecked) {
            submitBtn.classList.remove('btn-secondary');
            submitBtn.classList.add('btn-success');
        } else {
            submitBtn.classList.remove('btn-success');
            submitBtn.classList.add('btn-secondary');
        }
    }

    // مراقبة تغيير حالة الـ checkboxes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSubmitButton);
    });

    // تحديث حالة الزر عند تحميل الصفحة
    updateSubmitButton();

    // تأكيد الإرسال
    form.addEventListener('submit', function(e) {
        // التحقق النهائي من جميع الشروط
        let allChecked = true;
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                allChecked = false;
            }
        });

        if (!allChecked) {
            e.preventDefault();
            alert('يرجى الموافقة على جميع الشروط والأحكام');
            return false;
        }

        // تأكيد الإرسال
        const confirmed = confirm('هل أنت متأكد من إرسال طلب التمويل؟ لن تتمكن من تعديل البيانات بعد الإرسال.');

        if (!confirmed) {
            e.preventDefault();
            return false;
        }

        // تعطيل الزر لمنع الإرسال المتكرر
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    });

    // تحسين تجربة المستخدم عند النقر على روابط التعديل
    const editLinks = document.querySelectorAll('a[href*="step"]');
    editLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const confirmed = confirm('سيتم حفظ البيانات الحالية والانتقال لتعديل هذه الخطوة. هل تريد المتابعة؟');
            if (!confirmed) {
                e.preventDefault();
            }
        });
    });
});
</script>
@endpush
