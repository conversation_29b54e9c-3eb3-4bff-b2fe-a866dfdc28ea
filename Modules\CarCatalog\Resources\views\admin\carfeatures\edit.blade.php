@extends('dashboard::layouts.admin_layout')

@section('title', 'تعديل ميزة السيارة: ' . $carfeature->name)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid">
    {{-- عنوان الصفحة --}}
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">تعديل ميزة السيارة: {{ $carfeature->name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item">إدارة السيارات</li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.car-features.index') }}">ميزات السيارات</a></li>
                        <li class="breadcrumb-item active">تعديل</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    {{-- نموذج تعديل ميزة السيارة --}}
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit text-warning"></i>
                        تعديل ميزة السيارة
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.car-features.update', $carfeature) }}" method="POST">
                        @csrf
                        @method('PUT')

                        @include('carcatalog::admin.carfeatures._form')

                        {{-- أزرار الإجراءات --}}
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.car-features.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة للقائمة
                            </a>
                            <div>
                                <a href="{{ route('admin.car-features.show', $carfeature) }}" class="btn btn-info me-2">
                                    <i class="fas fa-eye"></i> عرض التفاصيل
                                </a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save"></i> حفظ التعديلات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
