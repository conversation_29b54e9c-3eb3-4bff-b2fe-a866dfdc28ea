{{--
    الخطوة 3: رفع المستندات - عملية طلب التمويل

    يعرض هذا الـ view نموذج لرفع المستندات المطلوبة لطلب التمويل
    بناءً على UIUX-FR.md (SITE-BUY-FINANCE-STEPX-001) و REQ-FR.md (MOD-ORDER-MGMT-FEAT-004)
--}}

@extends('site.layouts.site_layout')

@section('title', 'طلب التمويل - رفع المستندات')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">

            {{-- عنوان الصفحة ومؤشر التقدم --}}
            <div class="text-center mb-4">
                <h2 class="mb-3">طلب تمويل السيارة</h2>

                {{-- مؤشر التقدم (Stepper) --}}
                <div class="progress-stepper mb-4">
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">البيانات الشخصية</div>
                    </div>
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">معلومات التمويل</div>
                    </div>
                    <div class="step active">
                        <div class="step-number">3</div>
                        <div class="step-title">المستندات</div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-title">المراجعة والتأكيد</div>
                    </div>
                </div>
            </div>

            <div class="row">
                {{-- ملخص السيارة --}}
                <div class="col-lg-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                السيارة المختارة
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($car->getFirstMediaUrl('car_main_image') || $car->getFirstMediaUrl('car_images'))
                                <img src="{{ $car->getFirstMediaUrl('car_main_image') ?: $car->getFirstMediaUrl('car_images') }}"
                                     alt="{{ $car->title }}"
                                     class="img-fluid rounded mb-3">
                            @endif

                            <h6 class="fw-bold">{{ $car->title }}</h6>
                            <p class="text-muted mb-2">
                                {{ $car->brand->name ?? '' }} - {{ $car->carModel->name ?? '' }}
                            </p>
                            <p class="text-muted mb-3">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $car->manufacturingYear->year ?? '' }}
                                <span class="mx-2">|</span>
                                <i class="fas fa-palette me-1"></i>
                                {{ $car->mainColor->name ?? '' }}
                            </p>

                            <div class="price-section">
                                <h4 class="text-success fw-bold">
                                    {{ number_format($car->price, 0) }} {{ $car->currency }}
                                </h4>
                            </div>

                            {{-- تقدم العملية --}}
                            <div class="mt-3 p-3 bg-light rounded">
                                <h6 class="text-success mb-2">
                                    <i class="fas fa-tasks me-1"></i>
                                    تقدم العملية
                                </h6>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%"></div>
                                </div>
                                <small class="text-muted">الخطوة 3 من 4</small>
                            </div>
                        </div>
                    </div>

                    {{-- نصائح مهمة --}}
                    <div class="card shadow-sm mt-3">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>
                                نصائح لرفع المستندات
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="small mb-0">
                                <li>تأكد من وضوح النص في المستندات</li>
                                <li>استخدم صور عالية الجودة</li>
                                <li>تأكد من صحة تاريخ انتهاء المستندات</li>
                                <li>الحد الأقصى لحجم الملف: 5 ميجابايت</li>
                                <li>الصيغ المدعومة: PDF, JPG, PNG, WEBP</li>
                            </ul>
                        </div>
                    </div>
                </div>

                {{-- نموذج رفع المستندات --}}
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-upload me-2"></i>
                                المستندات المطلوبة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('site.order.finance.step3.process') }}" method="POST" enctype="multipart/form-data" id="documentsForm">
                                @csrf

                                {{-- المستندات الأساسية --}}
                                <div class="mb-4">
                                    <h6 class="text-success mb-3">
                                        <i class="fas fa-id-card me-2"></i>
                                        المستندات الأساسية
                                    </h6>

                                    {{-- الهوية الوطنية (الوجه الأمامي) --}}
                                    <div class="document-upload-section mb-4">
                                        <label class="form-label fw-bold">
                                            الهوية الوطنية/الإقامة (الوجه الأمامي) <span class="text-danger">*</span>
                                        </label>
                                        <div class="upload-area" data-target="national_id_front">
                                            <input type="file"
                                                   class="form-control d-none @error('national_id_front') is-invalid @enderror"
                                                   id="national_id_front"
                                                   name="national_id_front"
                                                   accept=".pdf,.jpg,.jpeg,.png,.webp"
                                                   required>
                                            <div class="upload-placeholder">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">اضغط هنا لرفع الملف أو اسحب الملف إلى هنا</p>
                                                <small class="text-muted">PDF, JPG, PNG, WEBP (حد أقصى 5 ميجابايت)</small>
                                            </div>
                                            <div class="upload-preview d-none">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-alt fa-2x text-success me-3"></i>
                                                    <div class="flex-grow-1">
                                                        <div class="file-name fw-bold"></div>
                                                        <div class="file-size text-muted small"></div>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        @error('national_id_front')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    {{-- الهوية الوطنية (الوجه الخلفي) --}}
                                    <div class="document-upload-section mb-4">
                                        <label class="form-label fw-bold">
                                            الهوية الوطنية/الإقامة (الوجه الخلفي) <span class="text-danger">*</span>
                                        </label>
                                        <div class="upload-area" data-target="national_id_back">
                                            <input type="file"
                                                   class="form-control d-none @error('national_id_back') is-invalid @enderror"
                                                   id="national_id_back"
                                                   name="national_id_back"
                                                   accept=".pdf,.jpg,.jpeg,.png,.webp"
                                                   required>
                                            <div class="upload-placeholder">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">اضغط هنا لرفع الملف أو اسحب الملف إلى هنا</p>
                                                <small class="text-muted">PDF, JPG, PNG, WEBP (حد أقصى 5 ميجابايت)</small>
                                            </div>
                                            <div class="upload-preview d-none">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-alt fa-2x text-success me-3"></i>
                                                    <div class="flex-grow-1">
                                                        <div class="file-name fw-bold"></div>
                                                        <div class="file-size text-muted small"></div>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        @error('national_id_back')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    {{-- رخصة القيادة --}}
                                    <div class="document-upload-section mb-4">
                                        <label class="form-label fw-bold">
                                            رخصة القيادة (سارية المفعول) <span class="text-danger">*</span>
                                        </label>
                                        <div class="upload-area" data-target="driving_license">
                                            <input type="file"
                                                   class="form-control d-none @error('driving_license') is-invalid @enderror"
                                                   id="driving_license"
                                                   name="driving_license"
                                                   accept=".pdf,.jpg,.jpeg,.png,.webp"
                                                   required>
                                            <div class="upload-placeholder">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">اضغط هنا لرفع الملف أو اسحب الملف إلى هنا</p>
                                                <small class="text-muted">PDF, JPG, PNG, WEBP (حد أقصى 5 ميجابايت)</small>
                                            </div>
                                            <div class="upload-preview d-none">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-alt fa-2x text-success me-3"></i>
                                                    <div class="flex-grow-1">
                                                        <div class="file-name fw-bold"></div>
                                                        <div class="file-size text-muted small"></div>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        @error('driving_license')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                {{-- مستندات التمويل --}}
                                <div class="mb-4">
                                    <h6 class="text-success mb-3">
                                        <i class="fas fa-file-invoice-dollar me-2"></i>
                                        مستندات التمويل
                                    </h6>

                                    {{-- تعريف بالراتب --}}
                                    <div class="document-upload-section mb-4">
                                        <label class="form-label fw-bold">
                                            تعريف بالراتب (لا يتجاوز 3 أشهر) <span class="text-danger">*</span>
                                        </label>
                                        <div class="upload-area" data-target="salary_certificate">
                                            <input type="file"
                                                   class="form-control d-none @error('salary_certificate') is-invalid @enderror"
                                                   id="salary_certificate"
                                                   name="salary_certificate"
                                                   accept=".pdf,.jpg,.jpeg,.png,.webp"
                                                   required>
                                            <div class="upload-placeholder">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">اضغط هنا لرفع الملف أو اسحب الملف إلى هنا</p>
                                                <small class="text-muted">PDF, JPG, PNG, WEBP (حد أقصى 5 ميجابايت)</small>
                                            </div>
                                            <div class="upload-preview d-none">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-alt fa-2x text-success me-3"></i>
                                                    <div class="flex-grow-1">
                                                        <div class="file-name fw-bold"></div>
                                                        <div class="file-size text-muted small"></div>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        @error('salary_certificate')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    {{-- كشف حساب بنكي --}}
                                    <div class="document-upload-section mb-4">
                                        <label class="form-label fw-bold">
                                            كشف حساب بنكي (آخر 3 أشهر) <span class="text-danger">*</span>
                                        </label>
                                        <div class="upload-area" data-target="bank_statement">
                                            <input type="file"
                                                   class="form-control d-none @error('bank_statement') is-invalid @enderror"
                                                   id="bank_statement"
                                                   name="bank_statement"
                                                   accept=".pdf,.jpg,.jpeg,.png,.webp"
                                                   required>
                                            <div class="upload-placeholder">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">اضغط هنا لرفع الملف أو اسحب الملف إلى هنا</p>
                                                <small class="text-muted">PDF, JPG, PNG, WEBP (حد أقصى 5 ميجابايت)</small>
                                            </div>
                                            <div class="upload-preview d-none">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-alt fa-2x text-success me-3"></i>
                                                    <div class="flex-grow-1">
                                                        <div class="file-name fw-bold"></div>
                                                        <div class="file-size text-muted small"></div>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        @error('bank_statement')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    {{-- مستندات إضافية (اختيارية) --}}
                                    <div class="document-upload-section mb-4">
                                        <label class="form-label fw-bold">
                                            مستندات إضافية (اختيارية)
                                        </label>
                                        <div class="upload-area" data-target="additional_documents">
                                            <input type="file"
                                                   class="form-control d-none @error('additional_documents') is-invalid @enderror"
                                                   id="additional_documents"
                                                   name="additional_documents"
                                                   accept=".pdf,.jpg,.jpeg,.png,.webp">
                                            <div class="upload-placeholder">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">اضغط هنا لرفع الملف أو اسحب الملف إلى هنا</p>
                                                <small class="text-muted">عقد عمل، شهادات أخرى، إلخ</small>
                                            </div>
                                            <div class="upload-preview d-none">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-alt fa-2x text-success me-3"></i>
                                                    <div class="flex-grow-1">
                                                        <div class="file-name fw-bold"></div>
                                                        <div class="file-size text-muted small"></div>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        @error('additional_documents')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                {{-- معلومات مهمة --}}
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        معلومات مهمة
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li>تأكد من صحة ووضوح جميع المستندات المرفوعة</li>
                                        <li>يجب أن تكون المستندات سارية المفعول</li>
                                        <li>سيتم مراجعة المستندات من قبل فريق التمويل</li>
                                        <li>قد يتم طلب مستندات إضافية حسب الحاجة</li>
                                        <li>جميع المستندات محفوظة بشكل آمن ومشفر</li>
                                    </ul>
                                </div>

                                {{-- أزرار التنقل --}}
                                <div class="d-flex justify-content-between mt-4">
                                    <a href="{{ route('site.order.finance.step2') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        السابق
                                    </a>

                                    <button type="submit" class="btn btn-success">
                                        التالي: المراجعة والتأكيد
                                        <i class="fas fa-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress-stepper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.5;
}

.step.active, .step.completed {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.step.active .step-number {
    background-color: #198754;
    color: white;
}

.step.completed .step-number {
    background-color: #198754;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    text-align: center;
}

.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.upload-area:hover {
    border-color: #198754;
    background-color: #f0f8f0;
}

.upload-area.dragover {
    border-color: #198754;
    background-color: #e8f5e8;
}

.upload-area.has-file {
    border-color: #198754;
    background-color: #e8f5e8;
}

.document-upload-section {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    background-color: #fafafa;
}

.price-section {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إعداد رفع الملفات
    const uploadAreas = document.querySelectorAll('.upload-area');

    uploadAreas.forEach(area => {
        const input = area.querySelector('input[type="file"]');
        const placeholder = area.querySelector('.upload-placeholder');
        const preview = area.querySelector('.upload-preview');
        const removeBtn = area.querySelector('.remove-file');

        // النقر على المنطقة لفتح مربع اختيار الملف
        area.addEventListener('click', () => {
            if (!area.classList.contains('has-file')) {
                input.click();
            }
        });

        // منع النقر على المعاينة من فتح مربع الاختيار
        if (preview) {
            preview.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // التعامل مع اختيار الملف
        input.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                showFilePreview(area, this.files[0]);
            }
        });

        // التعامل مع السحب والإفلات
        area.addEventListener('dragover', (e) => {
            e.preventDefault();
            area.classList.add('dragover');
        });

        area.addEventListener('dragleave', () => {
            area.classList.remove('dragover');
        });

        area.addEventListener('drop', (e) => {
            e.preventDefault();
            area.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                input.files = files;
                showFilePreview(area, files[0]);
            }
        });

        // إزالة الملف
        if (removeBtn) {
            removeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                removeFile(area);
            });
        }
    });

    function showFilePreview(area, file) {
        const placeholder = area.querySelector('.upload-placeholder');
        const preview = area.querySelector('.upload-preview');
        const fileName = preview.querySelector('.file-name');
        const fileSize = preview.querySelector('.file-size');

        // التحقق من نوع الملف
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('نوع الملف غير مدعوم. يرجى اختيار ملف PDF أو صورة (JPG, PNG, WEBP)');
            return;
        }

        // التحقق من حجم الملف (5 ميجابايت)
        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
            return;
        }

        // عرض معاينة الملف
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);

        placeholder.classList.add('d-none');
        preview.classList.remove('d-none');
        area.classList.add('has-file');
    }

    function removeFile(area) {
        const input = area.querySelector('input[type="file"]');
        const placeholder = area.querySelector('.upload-placeholder');
        const preview = area.querySelector('.upload-preview');

        input.value = '';
        placeholder.classList.remove('d-none');
        preview.classList.add('d-none');
        area.classList.remove('has-file');
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';

        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // التحقق من رفع الملفات المطلوبة قبل الإرسال
    const form = document.getElementById('documentsForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredInputs = form.querySelectorAll('input[type="file"][required]');
            let allFilesUploaded = true;

            requiredInputs.forEach(input => {
                if (!input.files || input.files.length === 0) {
                    allFilesUploaded = false;
                    const label = form.querySelector(`label[for="${input.id}"]`);
                    if (label) {
                        alert(`يرجى رفع: ${label.textContent.replace('*', '').trim()}`);
                    }
                }
            });

            if (!allFilesUploaded) {
                e.preventDefault();
                return false;
            }
        });
    }
});
</script>
@endpush
