@extends('site.layouts.site_layout')

@section('title', 'اختبار الدفع - ' . $order->order_number)

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            
            <!-- Header -->
            <div class="text-center mb-4">
                <h2 class="text-primary">صفحة اختبار الدفع</h2>
                <p class="text-muted">هذه صفحة اختبار لمحاكاة عملية الدفع</p>
            </div>

            <!-- Order Info Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        تفاصيل الطلب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>رقم الطلب:</strong> {{ $order->order_number }}</p>
                            <p><strong>السيارة:</strong> {{ $order->car->title }}</p>
                            <p><strong>العميل:</strong> {{ $order->user->full_name }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>مبلغ الحجز:</strong> {{ number_format($order->reservation_amount, 2) }} ريال</p>
                            <p><strong>طريقة الدفع:</strong> {{ request('gateway', 'فيزا/ماستركارد') }}</p>
                            <p><strong>حالة الطلب:</strong> 
                                <span class="badge bg-warning">{{ $order->getStatusDisplayAttribute() }}</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Simulation Card -->
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        محاكاة عملية الدفع
                    </h5>
                </div>
                <div class="card-body">
                    
                    <!-- Alert -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> هذه صفحة اختبار فقط. لن يتم خصم أي مبلغ فعلي.
                    </div>

                    <!-- Payment Form -->
                    <form id="testPaymentForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">رقم البطاقة (وهمي)</label>
                                <input type="text" class="form-control" value="4111 1111 1111 1111" readonly>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">تاريخ الانتهاء</label>
                                <input type="text" class="form-control" value="12/25" readonly>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">CVV</label>
                                <input type="text" class="form-control" value="123" readonly>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">اسم حامل البطاقة</label>
                            <input type="text" class="form-control" value="{{ $order->user->full_name }}" readonly>
                        </div>

                        <!-- Payment Result Selection -->
                        <div class="mb-4">
                            <label class="form-label">اختر نتيجة الدفع للاختبار:</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="payment_result" id="success" value="success" checked>
                                        <label class="form-check-label text-success" for="success">
                                            <i class="fas fa-check-circle me-1"></i>
                                            نجح الدفع
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="payment_result" id="failed" value="failed">
                                        <label class="form-check-label text-danger" for="failed">
                                            <i class="fas fa-times-circle me-1"></i>
                                            فشل الدفع
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="payment_result" id="pending" value="pending">
                                        <label class="form-check-label text-warning" for="pending">
                                            <i class="fas fa-clock me-1"></i>
                                            معلق
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('site.order.details', $order->id) }}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة للطلب
                            </a>
                            <button type="submit" class="btn btn-primary" id="processPaymentBtn">
                                <i class="fas fa-credit-card me-1"></i>
                                معالجة الدفع
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Loading Overlay -->
            <div id="loadingOverlay" class="d-none">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري المعالجة...</span>
                    </div>
                    <p class="mt-2">جاري معالجة الدفع...</p>
                </div>
            </div>

        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
#loadingOverlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}

.form-check-label {
    font-weight: 500;
}

.form-check-input:checked + .form-check-label {
    font-weight: 600;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('testPaymentForm');
    const loadingOverlay = document.getElementById('loadingOverlay');
    const processBtn = document.getElementById('processPaymentBtn');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const result = document.querySelector('input[name="payment_result"]:checked').value;
        
        // Show loading
        loadingOverlay.classList.remove('d-none');
        processBtn.disabled = true;
        
        // Simulate processing delay
        setTimeout(function() {
            processTestPayment(result);
        }, 2000);
    });

    function processTestPayment(result) {
        const orderId = {{ $order->id }};
        const transactionId = 'TEST_' + Date.now();
        
        let redirectUrl;
        let message;
        
        switch(result) {
            case 'success':
                redirectUrl = `{{ route('site.order.payment.success', $order->id) }}?transaction_id=${transactionId}&payment_method=test_card&amount={{ $order->reservation_amount }}`;
                break;
            case 'failed':
                redirectUrl = `{{ route('site.order.payment.cancel', $order->id) }}?error=payment_failed&reason=insufficient_funds`;
                break;
            case 'pending':
                redirectUrl = `{{ route('site.order.details', $order->id) }}?status=pending&message=payment_pending`;
                break;
        }
        
        // Redirect to appropriate page
        window.location.href = redirectUrl;
    }
});
</script>
@endpush
