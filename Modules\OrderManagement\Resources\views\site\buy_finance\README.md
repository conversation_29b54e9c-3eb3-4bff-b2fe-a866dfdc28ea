# Finance Order Blade Views - OrderManagement Module

## نظرة عامة

تم إنشاء مجموعة ملفات Blade views لخطوات عملية طلب التمويل في موديول OrderManagement بناءً على المتطلبات المحددة في:
- `UIUX-FR.md` (SITE-BUY-FINANCE-STEPX-001)
- `REQ-FR.md` (MOD-ORDER-MGMT-FEAT-004)

## الملفات المنشأة

### 1. step1_personal.blade.php
**الغرض:** جمع البيانات الشخصية للعميل
**المسار:** `Modules/OrderManagement/Resources/views/site/buy_finance/step1_personal.blade.php`

**الحقول المتضمنة:**
- الاسم الكامل
- رقم الهوية/الإقامة
- تاريخ الميلاد
- رقم الجوال
- البريد الإلكتروني
- الجنسية
- المدينة
- الحالة الاجتماعية
- العنوان التفصيلي

**الميزات:**
- التحقق من صحة البيانات في الوقت الفعلي
- ملء تلقائي للبيانات من ملف المستخدم
- تصميم متجاوب
- مؤشر تقدم (Stepper)

### 2. step2_finance_info.blade.php
**الغرض:** جمع معلومات التمويل والعمل
**المسار:** `Modules/OrderManagement/Resources/views/site/buy_finance/step2_finance_info.blade.php`

**الحقول المتضمنة:**
- الدفعة الأولى
- الدخل الشهري
- الالتزامات الشهرية
- فترة التمويل المرغوبة
- نوع العمل
- المسمى الوظيفي
- جهة العمل
- سنوات الخبرة
- البنك المحول عليه الراتب

**الميزات:**
- حاسبة تمويل تفاعلية
- تحديث فوري لمبلغ التمويل المطلوب
- التحقق من صحة النسب المالية
- قوائم منسدلة للبنوك السعودية

### 3. step3_documents.blade.php
**الغرض:** رفع المستندات المطلوبة للتمويل
**المسار:** `Modules/OrderManagement/Resources/views/site/buy_finance/step3_documents.blade.php`

**المستندات المطلوبة:**
- الهوية الوطنية (الوجه الأمامي والخلفي)
- رخصة القيادة
- تعريف بالراتب
- كشف حساب بنكي
- مستندات إضافية (اختيارية)

**الميزات:**
- رفع بالسحب والإفلات (Drag & Drop)
- معاينة الملفات المرفوعة
- التحقق من نوع وحجم الملفات
- دعم PDF, JPG, PNG, WEBP
- حد أقصى 5 ميجابايت لكل ملف

### 4. step4_review_confirm.blade.php
**الغرض:** مراجعة البيانات والتأكيد النهائي
**المسار:** `Modules/OrderManagement/Resources/views/site/buy_finance/step4_review_confirm.blade.php`

**المحتويات:**
- ملخص شامل لجميع البيانات المدخلة
- عرض تفاصيل التمويل
- قائمة المستندات المرفوعة
- الشروط والأحكام
- التأكيد النهائي

**الميزات:**
- روابط تعديل لكل خطوة
- نوافذ منبثقة للشروط والأحكام
- تأكيد الإرسال
- منع الإرسال المتكرر

## الميزات العامة

### 1. التصميم
- تصميم متجاوب يعمل على جميع الأجهزة
- استخدام Bootstrap 5
- ألوان متسقة مع هوية الموقع (الأخضر للتمويل)
- أيقونات Font Awesome

### 2. تجربة المستخدم
- مؤشر تقدم واضح (Stepper)
- رسائل مساعدة ونصائح
- التحقق من صحة البيانات
- حفظ تلقائي للتقدم

### 3. الأمان
- التحقق من صحة البيانات في الخادم والعميل
- حماية CSRF
- تشفير رفع الملفات
- التحقق من أنواع الملفات

### 4. إمكانية الوصول
- تسميات واضحة للحقول
- دعم قارئات الشاشة
- تباين ألوان مناسب
- تنقل بلوحة المفاتيح

## Routes المطلوبة

يجب التأكد من وجود هذه المسارات في `web.php`:

```php
Route::prefix('finance')->name('finance.')->group(function() {
    Route::get('step1/{car}', 'Site\SiteOrderController@showFinanceOrderStep1PersonalDetails')->name('step1');
    Route::post('step1/process', 'Site\SiteOrderController@processFinanceOrderStep1')->name('step1.process');
    
    Route::get('step2', 'Site\SiteOrderController@showFinanceOrderStep2FinanceInfo')->name('step2');
    Route::post('step2/process', 'Site\SiteOrderController@processFinanceOrderStep2')->name('step2.process');
    
    Route::get('step3', 'Site\SiteOrderController@showFinanceOrderStep3Documents')->name('step3');
    Route::post('step3/process', 'Site\SiteOrderController@processFinanceOrderStep3')->name('step3.process');
    
    Route::get('step4', 'Site\SiteOrderController@showFinanceOrderStep4ReviewConfirm')->name('step4');
    Route::post('step4/process', 'Site\SiteOrderController@processFinanceOrderStep4')->name('step4.process');
    
    Route::post('store', 'Site\SiteOrderController@storeFinanceOrder')->name('store');
});
```

## Controller Methods المطلوبة

يجب التأكد من وجود هذه الدوال في `SiteOrderController`:

- `showFinanceOrderStep1PersonalDetails($request, $carId)`
- `processFinanceOrderStep1(FinanceOrderStep1Request $request)`
- `showFinanceOrderStep2FinanceInfo()`
- `processFinanceOrderStep2(FinanceOrderStep2Request $request)`
- `showFinanceOrderStep3Documents()`
- `processFinanceOrderStep3(FinanceOrderStep3Request $request)`
- `showFinanceOrderStep4ReviewConfirm()`
- `processFinanceOrderStep4(FinanceOrderStep4Request $request)`
- `storeFinanceOrder()`

## Form Requests المطلوبة

- `FinanceOrderStep1Request`
- `FinanceOrderStep2Request`
- `FinanceOrderStep3Request`
- `FinanceOrderStep4Request`

## Session Variables

يتم استخدام هذه المتغيرات في الجلسة:
- `finance_order_car_id`
- `finance_order_step`
- `finance_order_personal_data`
- `finance_order_finance_data`
- `finance_order_documents_data`

## التبعيات

- Laravel 10
- Bootstrap 5
- Font Awesome
- spatie/laravel-medialibrary (لرفع الملفات)

## ملاحظات التطوير

1. تم تصميم الواجهات لتكون متسقة مع واجهات الشراء النقدي
2. يمكن تخصيص الألوان والتصميم حسب الحاجة
3. تم إضافة تعليقات باللغة العربية لسهولة الصيانة
4. الكود قابل للتوسع والتطوير

## الخطوات التالية

1. التأكد من وجود Controller methods
2. إنشاء Form Requests
3. تحديث Routes
4. اختبار الواجهات
5. إضافة المحتوى الفعلي للشروط والأحكام

---

**تاريخ الإنشاء:** تم إنشاء هذه الملفات بناءً على TASK-ID: PH03-TASK-026
**المطور:** Augment Agent
**الحالة:** مكتمل - جاهز للاختبار والتطوير
