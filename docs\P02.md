### 1. **`PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`**
* **LEVEL:** `High`
* **OBJECTIVE:** إنشاء موديول `CarCatalog` (`STRU-MOD-CARCATALOG-001`) مع جميع مخططات قاعدة البيانات اللازمة (جداول الماركات `DB-TBL-002`, الموديلات `DB-TBL-003`, سنوات الصنع `DB-TBL-004`, الألوان `DB-TBL-005`, أنواع ناقل الحركة `DB-TBL-006`, أنواع الوقود `DB-TBL-007`, أنواع هياكل السيارات `DB-TBL-021`, الميزات `DB-TBL-008`, فئات الميزات `DB-TBL-022`, جدول السيارات الرئيسي `DB-TBL-009`, وجدول ربط السيارات بالميزات `DB-TBL-010`). يخدم هذا بشكل مباشر بناء [لوحة تحكم احترافية Dash] لإدارة السيارات و [الموقع الإلكتروني الفعال] و [تطبيق Flutter] لعرضها. (يغطي `PH-02-DEL-003` من `PPP-FR.md`).
* **TYPE:** `Module Creation, Eloquent Model Implementation, Database Migration Generation & Execution`
* **FILE_NAME_PATH:**
    * `Modules/CarCatalog/`
    * `Modules/CarCatalog/Database/Migrations/` (ملفات migrations متعددة)
    * `Modules/CarCatalog/Models/` (ملفات النماذج: `Brand.php`, `CarModel.php`, `ManufacturingYear.php`, `Color.php`, `TransmissionType.php`, `FuelType.php`, `BodyType.php`, `FeatureCategory.php`, `CarFeature.php`, `Car.php`)
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `docs/PPP-FR.md` (`PH-02-DEL-003`)
        * `docs/STRU-FR.md` (`STRU-MOD-CARCATALOG-001`)
        * `docs/TS-FR.md` (الجداول: `DB-TBL-002` إلى `DB-TBL-010`, `DB-TBL-021`, `DB-TBL-022`)
        * `docs/REQ-FR.md` (الوظائف المتعلقة بإدارة هذه الكيانات)
    * **DESIGN_REF:** لا ينطبق مباشرة على هذه المهمة (هيكلة بيانات).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: قبل البدء، راجع `docs/TS-FR.md` بدقة فائقة للتأكد من أن جميع الأعمدة، أنواع البيانات، القيود (NOT NULL, UNIQUE, DEFAULT)، المفاتيح الخارجية (مع `onDelete` و `onUpdate` الصحيحين)، والفهارس للجداول المذكورة أعلاه مطابقة تمامًا للمواصفات. تحقق من `docs/DECISIONS.md` لأي قرارات تتعلق باستخدام `SoftDeletes` أو `UUIDs` لهذه النماذج، أو إذا كان سيتم استخدام `spatie/laravel-translatable` لبعض الحقول النصية في هذه النماذج (مثل `name` في `Brand`, `CarModel`, `Color` إلخ). بناءً على `TS-FR.md`, `SoftDeletes` مطلوب لـ `Brand`, `CarModel`, و `Car`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-003` (إنشاء موديول CarCatalog وقاعدة بياناته) إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-002 LARAVEL-MODULES-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * جميع الجداول المذكورة سيتم إنشاؤها ضمن موديول `CarCatalog`.
    * سيتم استخدام `bigIncrements` كـ PK ما لم يُذكر خلاف ذلك في `TS-FR.md`.
    * النماذج سترث من `Modules\Core\Models\BaseModel`.
    * سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا كان محددًا في `TS-FR.md` لحقول معينة (مثل الأسماء والأوصاف).
    * سيتم استخدام `SoftDeletes` للنماذج المحددة (`Brand`, `CarModel`, `Car`).
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_CAR_CATALOG_MODULE:**
        * **DETAILED_LOGIC:** "1. استخدم أمر Artisan لإنشاء موديول `CarCatalog`: `php artisan module:make CarCatalog`."
    * **2. CREATE_MODELS_AND_MIGRATIONS (لكل جدول من الجداول المحددة في `TS-FR.md` ضمن `PH-02-DEL-003`):**
        * **DETAILED_LOGIC:** "لكل جدول (Brand `DB-TBL-002`, FeatureCategory `DB-TBL-022`, CarFeature `DB-TBL-008`, CarModel `DB-TBL-003`, ManufacturingYear `DB-TBL-004`, Color `DB-TBL-005`, TransmissionType `DB-TBL-006`, FuelType `DB-TBL-007`, BodyType `DB-TBL-021`, Car `DB-TBL-009`, CarCarFeature Pivot `DB-TBL-010`):"
            * "أ. أنشئ نموذج Eloquent المقابل في `Modules/CarCatalog/Models/` يرث من `Modules\Core\Models\BaseModel` (أو `Illuminate\Database\Eloquent\Model` للنماذج البسيطة جدًا)."
                * "طبق الـ traits المطلوبة (مثل `SoftDeletes`, `HasFactory`, `Spatie\Translatable\HasTranslations`, `Spatie\MediaLibrary\HasMedia`, `Spatie\MediaLibrary\InteractsWithMedia`) كما هو محدد أو مستنتج من `TS-FR.md` و `REQ-FR.md`."
                * "عرّف خاصية `$fillable` لتشمل جميع الأعمدة القابلة للتعبئة."
                * "عرّف خاصية `$casts` لتحويل أنواع البيانات (مثل `boolean`, `date`, `datetime`, `decimal:X`, `array` لـ JSON)."
                * "إذا كان النموذج يستخدم `HasTranslations`, عرّف خاصية `$translatable` بالحقول القابلة للترجمة."
                * "عرّف جميع العلاقات (Relationships) (`belongsTo`, `hasMany`, `belongsToMany`, etc.) مع النماذج الأخرى كما هو موضح في `TS-FR.md` (القسم 1.30)."
                * "إذا كان النموذج يستخدم `HasMedia`, قم بتنفيذ `registerMediaCollections()` و `registerMediaConversions()` إذا لزم الأمر."
            * "ب. أنشئ ملف migration باستخدام `php artisan module:make-migration create_[table_name]_table CarCatalog`."
            * "ج. في ملف migration، قم بتعريف بنية الجدول (`Schema::create`) بجميع الأعمدة، أنواع البيانات الدقيقة، القيود (NOT NULL, UNIQUE, DEFAULT)، المفاتيح الخارجية (مع `onDelete` و `onUpdate` المناسبين)، والفهارس (indexes) كما هو محدد تمامًا في `TS-FR.md` لكل جدول."
    * **3. RUN_ALL_CAR_CATALOG_MIGRATIONS:**
        * **DETAILED_LOGIC:** "1. بعد إنشاء جميع ملفات migration، قم بتشغيل `php artisan migrate` للتأكد من أن جميع جداول موديول `CarCatalog` تم إنشاؤها بنجاح."
* **REFERENCE_CODE_SNIPPETS:** (مقتطفات النماذج والعلاقات الأساسية كما هو موضح في `TS-FR.md`).
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم إنشاء موديول `CarCatalog` وجميع جداوله ونماذجه الأولية بنجAUC، مع العلاقات الأساسية المعرفة.
* **SECURITY_CONSIDERATIONS:** لا يوجد في هذه المرحلة.
* **PERFORMANCE_CONSIDERATIONS:** التأكد من إنشاء جميع الفهارس المحددة في `TS-FR.md`.
* **REQUIRED_CODE_COMMENTS:** DocBlocks للنماذج، العلاقات، والخصائص الهامة.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء موديول `CarCatalog`."
    2. "تم إنشاء جميع النماذج المطلوبة (`Brand`, `CarModel`, `Car`, إلخ) ضمن `Modules/CarCatalog/Models/` مع الـ traits والعلاقات والخصائص الأساسية كما هو محدد في `TS-FR.md` وخطوات التنفيذ."
    3. "تم إنشاء ملفات migration لجميع الجداول المطلوبة ضمن `Modules/CarCatalog/Database/Migrations/` بدقة وفقًا لـ `TS-FR.md`."
    4. "تم تشغيل جميع الـ migrations الخاصة بـ `CarCatalog` بنجAUC دون أخطاء."
    5. "تم تطبيق `SoftDeletes` على النماذج المحددة (`Brand`, `CarModel`, `Car`)."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created `CarCatalog` module structure, Eloquent models (Brand, CarModel, ManufacturingYear, Color, TransmissionType, FuelType, BodyType, FeatureCategory, CarFeature, Car), and all related database migrations as per TS-FR.md (PH-02-DEL-003).' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-003` إلى `Completed` في `docs/TODO.md`."
    * **DECISIONS.md:** "Augment: إذا تم اتخاذ أي قرار أثناء هذه المهمة (مثل تأكيد استخدام `spatie/laravel-translatable` لحقل معين لم يكن واضحًا تمامًا من قبل، أو قرار بشأن `onDelete` cascade/restrict)، قم بتوثيقه."

---

### 2. **`PH02-TASK-002-NOTIFICATION-MODULE-SETUP-001`**
* **LEVEL:** `Medium`
* **OBJECTIVE:** إنشاء موديول `Notification` (`STRU-MOD-NOTIFICATION-001`) وتنفيذ الهيكل الأساسي لإرسال الإشعارات عبر البريد وقاعدة البيانات (`MOD-NOTIFICATION-FEAT-001`). سيشمل ذلك إنشاء فئة Notification ترحيبية أساسية (مثل `NewUserWelcomeNotification`) وفئة Mailable مرتبطة بها، وقالب Blade للبريد. يخدم هذا بناء نظام إشعارات يدعم [الموقع الإلكتروني الفعال]، [لوحة التحكم الاحترافية Dash]، و [تطبيق Flutter]. (يغطي جزء من `PH-02-DEL-008` من `PPP-FR.md`).
* **TYPE:** `Module Creation, Laravel Notification & Mailable Implementation, Blade View Creation`
* **FILE_NAME_PATH:**
    * `Modules/Notification/`
    * `Modules/Notification/Notifications/NewUserWelcomeNotification.php`
    * `Modules/Notification/Mail/WelcomeEmail.php`
    * `Modules/Notification/Resources/views/emails/welcome.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `docs/PPP-FR.md` (`PH-02-DEL-008`)
        * `docs/STRU-FR.md` (`STRU-MOD-NOTIFICATION-001`)
        * `docs/REQ-FR.md` (`MOD-NOTIFICATION-FEAT-001`)
    * **DESIGN_REF:** لا يوجد تصميم UI مباشر لهذه المهمة، ولكن قالب البريد الترحيبي يجب أن يكون بسيطًا واحترافيًا.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-NOTIFICATION-FEAT-001`) لفهم القنوات المطلوبة (بريد، قاعدة بيانات، SMS اختياري). في هذه المهمة، ركز على البريد وقاعدة البيانات. تحقق من `docs/DECISIONS.md` إذا كان هناك قرار بشأن محتوى أو تصميم البريد الترحيبي الأولي."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-008` (الجزء الخاص بإنشاء موديول Notification والإشعارات الأساسية) إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-002 LARAVEL-MODULES-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * سيتم استخدام نظام Laravel Notifications المدمج.
    * سيتم إنشاء قالب Blade بسيط للبريد الترحيبي باستخدام مكونات Markdown.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_NOTIFICATION_MODULE:**
        * **DETAILED_LOGIC:** "1. استخدم أمر Artisan لإنشاء موديول `Notification`: `php artisan module:make Notification`."
    * **2. CREATE_WELCOME_EMAIL_Mailable:**
        * **FILE_NAME_PATH:** `Modules/Notification/Mail/WelcomeEmail.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ Mailable: `php artisan module:make-mail WelcomeEmail Notification`."
            * "2. في `WelcomeEmail.php`، اجعله يرث من `Illuminate\Mail\Mailable` ويستخدم `Queueable, SerializesModels` traits."
            * "3. أضف خاصية عامة (public property) `\$user` من نوع `\Modules\UserManagement\Models\User`."
            * "4. في الـ `__construct(\Modules\UserManagement\Models\User \$user)`, قم بتعيين `\$this->user = \$user;`."
            * "5. في دالة `build()`, قم بإرجاع `\$this->subject('مرحباً بك في ' . config('app.name') . '!') ->markdown('notification::emails.welcome');`."
    * **3. CREATE_WELCOME_EMAIL_BLADE_VIEW:**
        * **FILE_NAME_PATH:** `Modules/Notification/Resources/views/emails/welcome.blade.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ المجلدات `Modules/Notification/Resources/views/emails/`."
            * "2. أنشئ ملف Blade هذا."
            * "3. صمم رسالة ترحيب بسيطة باستخدام مكونات Markdown للبريد:
            ```markdown
            @component('mail::message')
            # مرحباً بك، {{ $user->first_name }}!

            نشكرك على تسجيلك في {{ config('app.name') }}. نحن متحمسون لانضمامك إلينا.

            يمكنك الآن تصفح أحدث السيارات والعروض من خلال موقعنا أو تطبيقنا.

            @component('mail::button', ['url' => route('home')]) {{-- Adjust route('home') to the main site route --}}
            تفضل بزيارة الموقع
            @endcomponent

            {{-- Placeholder for app links, to be implemented if app exists and routes are defined --}}
            {{-- 
            @if(route_exists('app.android_link'))
            @component('mail::button', ['url' => route('app.android_link'), 'color' => 'success'])
            حمّل تطبيقنا للأندرويد
            @endcomponent
            @endif
            @if(route_exists('app.ios_link'))
            @component('mail::button', ['url' => route('app.ios_link'), 'color' => 'primary'])
            حمّل تطبيقنا للآيفون
            @endcomponent
            @endif
            --}}

            شكراً،<br>
            فريق عمل {{ config('app.name') }}
            @endcomponent
            ```"
    * **4. CREATE_NEW_USER_WELCOME_NOTIFICATION_CLASS:**
        * **FILE_NAME_PATH:** `Modules/Notification/Notifications/NewUserWelcomeNotification.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ Notification class: `php artisan module:make-notification NewUserWelcomeNotification Notification`."
            * "2. في `NewUserWelcomeNotification.php`، اجعله يرث من `Illuminate\Notifications\Notification` ويستخدم `Illuminate\Contracts\Queue\ShouldQueue`."
            * "3. أضف خاصية عامة (public property) `\$user` من نوع `\Modules\UserManagement\Models\User`."
            * "4. في الـ `__construct(\Modules\UserManagement\Models\User \$user)`, قم بتعيين `\$this->user = \$user;`."
            * "5. في دالة `via(\$notifiable)`, أرجع `['mail', 'database']`."
            * "6. قم بتنفيذ دالة `toMail(\$notifiable)` لترجع `(new \Modules\Notification\Mail\WelcomeEmail(\$this->user))->to(\$notifiable->email);`."
            * "7. قم بتنفيذ دالة `toArray(\$notifiable)` (أو `toDatabase`) لترجع مصفوفة بالبيانات التي سيتم تخزينها في جدول `notifications`:
              ```php
              public function toArray($notifiable)
              {
                  return [
                      'title' => 'ترحيب مستخدم جديد', // Arabic
                      'message' => 'مرحباً بك، ' . $this->user->first_name . '! شكراً لتسجيلك في ' . config('app.name') . '.', // Arabic
                      'action_url' => route('customer.dashboard'), // Placeholder for customer dashboard route name
                      'user_id' => $this->user->id,
                      'icon' => 'fas fa-user-plus', // Example Font Awesome icon for Dash display
                  ];
              }
              ```"
    * **5. ENSURE_NOTIFIABLE_TRAIT_ON_USER_MODEL:**
        * **FILE_NAME_PATH:** `Modules/UserManagement/Models/User.php`
        * **DETAILED_LOGIC:** "1. تأكد من أن نموذج `User` (في `Modules/UserManagement/Models/`) يستخدم `Illuminate\Notifications\Notifiable` trait."
* **REFERENCE_CODE_SNIPPETS:** (كما هو موضح أعلاه).
* **EXPECTED_OUTPUTS_BEHAVIOR:** تم إنشاء موديول `Notification` مع فئة إشعار ترحيبي يمكنها إرسال بريد إلكتروني وتخزين إشعار في قاعدة البيانات.
* **SECURITY_CONSIDERATIONS:** لا يوجد مباشرة، ولكن تأمين إعدادات البريد مهم.
* **PERFORMANCE_CONSIDERATIONS:** استخدام `ShouldQueue` للإشعارات.
* **REQUIRED_CODE_COMMENTS:** DocBlocks للفئات والدوال الهامة.
* **ACCEPTANCE_CRITERIA:**
    1. "تم إنشاء موديول `Notification`."
    2. "تم إنشاء `WelcomeEmail.php` (Mailable) وقالب `notification::emails.welcome` الخاص به."
    3. "تم إنشاء `NewUserWelcomeNotification.php` وهو مهيأ للإرسال عبر قناتي `mail` و `database`، ويستخدم `ShouldQueue`."
    4. "نموذج `User` يستخدم `Notifiable` trait."
    5. "يمكن استدعاء `\$user->notify(new NewUserWelcomeNotification(\$user));` برمجيًا لإرسال الإشعار (مع التحقق من سجلات البريد وقاعدة بيانات الإشعارات بعد تشغيل queue worker)."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Created `Notification` module and implemented basic `NewUserWelcomeNotification` (email & database channels, queued) (PH-02-DEL-008 Part 1).' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-008` (الجزء الأول) إلى `Completed` في `docs/TODO.md`. أضف مهمة TODO جديدة لتكامل إرسال هذا الإشعار عند تسجيل مستخدم جديد بنجAUC (ضمن `MOD-USER-MGMT`)."

---

### 3. **`PH02-TASK-003-DASH-DYNAMIC-SIDEBAR-001`**
* **LEVEL:** `High`
* **OBJECTIVE:** تطوير القائمة الجانبية (`_sidebar.blade.php`) للوحة تحكم الإدارة Dash لتصبح ديناميكية، بحيث تعرض عناصر القائمة بناءً على أدوار وصلاحيات المستخدم المسجل دخوله، باستخدام `spatie/laravel-permission`. يخدم هذا بشكل مباشر تخصيص وفعالية [لوحة التحكم الاحترافية Dash]. (يغطي جزء القائمة الجانبية من `PH-02-DEL-002` من `PPP-FR.md`).
* **TYPE:** `Dash Custom Blade View Modification (Dynamic Sidebar Logic)`
* **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/layouts/partials/_sidebar.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `docs/PPP-FR.md` (`PH-02-DEL-002`)
        * `docs/UIUX-FR.md` (القسم 4.1.2 لوصف الهيكل العام للقائمة الجانبية للوحة تحكم الإدارة، مع الإشارة إلى أنها ستكون ديناميكية بناءً على الأدوار، وكذلك أسماء عناصر القائمة العربية والأيقونات المقترحة)
        * `docs/REQ-FR.md` (لتحديد قائمة عناصر القائمة المتوقعة للوحة تحكم الإدارة، والصلاحيات المرتبطة بها إذا كانت محددة مبدئيًا).
        * `TASK-ID:PH01-TASK-010` (للحصول على الهيكل الثابت الأولي للقائمة الجانبية).
    * **DESIGN_REF:** `Dash/index.html` (للهيكل الأساسي لعناصر القائمة (`li.nav-item`, `a.nav-link`, `div.collapse`, `ul.submenu`) والقوائم الفرعية). `docs/UIUX-FR.md` (لأسماء عناصر القائمة باللغة العربية وأيقوناتها المتوقعة).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `Dash/index.html` (خاصة قسم `div.sidebar` الذي يحمل `id=\"sidebar\"`) لتحديد هيكل HTML لعناصر القائمة والقوائم الفرعية. راجع `docs/UIUX-FR.md` و `docs/REQ-FR.md` لتحديد قائمة عناصر القائمة الرئيسية والفرعية المطلوبة للوحة تحكم الإدارة وأي صلاحيات مرتبطة بها إذا تم تحديدها (استخدم الصلاحيات الأولية من `DECISION-INITIAL-ROLES-PERMS-001` كنقطة انطلاق). تحقق من `docs/DECISIONS.md` لأي قرارات بشأن هيكل القائمة أو الصلاحيات."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-002` (الجزء الخاص بالقائمة الجانبية) إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:**
    * `TASK-ID:PH01-TASK-008 SPATIE-PERMISSION-SETUP-001` (لتوفر الأدوار والصلاحيات).
    * `TASK-ID:PH01-TASK-010 DASH-LAYOUT-PARTIALS-SETUP-001` (لوجود `_sidebar.blade.php` الأولي).
    * تعريف مسارات (routes) لواجهات لوحة التحكم الإدارية التي سترتبط بها عناصر القائمة (سيتم إنشاؤها في مهام لاحقة، ولكن يمكن استخدام `#` أو `javascript:void(0);` كـ `href` مؤقتًا، مع استخدام `name()` للمسارات المستقبلية).
* **LLM_ASSUMPTIONS:**
    * سيتم استخدام شروط `@can('permission_name')` أو `@hasrole('Role Name')` أو `@canany(['perm1', 'perm2'])` من `spatie/laravel-permission` لعرض/إخفاء عناصر القائمة.
    * القائمة الجانبية ستحتوي على عناصر رئيسية وقوائم فرعية متداخلة كما في `Dash/index.html`.
    * سيتم الحفاظ على تصميم أيقونات Font Awesome من `Dash/index.html` مع تعديلها إذا لزم الأمر بناءً على `UIUX-FR.md`.
    * سيتم تحديد حالة "نشط" (active) لعنصر القائمة بناءً على المسار الحالي باستخدام `request()->routeIs()`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. MODIFY_SIDEBAR_BLADE_FILE:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/layouts/partials/_sidebar.blade.php`
        * **DETAILED_LOGIC:**
            * "1. افتح ملف `_sidebar.blade.php` الذي يحتوي على HTML الثابت من `Dash/index.html`."
            * "2. **رأس القائمة الجانبية (`sidebar-header`):** احتفظ به كما هو من `Dash/index.html` مبدئيًا (شعار ونص ثابت). سيتم جعله ديناميكيًا (مثل اسم المعرض من الإعدادات) في مهمة لاحقة إذا لزم الأمر."
            * "3. **هيكل القائمة الرئيسي (`ul.nav.flex-column.mt-3`):**"
            * "4. قم بتعديل عناصر القائمة (`li.nav-item` و `a.nav-link`) لتصبح ديناميكية. هيكل القائمة المبدئي المقترح (سيتم إنشاء المسارات لاحقًا، استخدم `#` كرابط مؤقت):"
                *   **(الرئيسية):**
                    ```html+php
                    @can('access_admin_dashboard')
                    <li class="nav-item">
                        <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>
                    @endcan
                    ```
                *   **(إدارة السيارات - قائمة فرعية):**
                    ```html+php
                    @php 
                        $carSubmenuActive = request()->routeIs('admin.cars.*') || 
                                          request()->routeIs('admin.brands.*') || 
                                          request()->routeIs('admin.models.*') || 
                                          request()->routeIs('admin.colors.*') || 
                                          request()->routeIs('admin.years.*') || 
                                          request()->routeIs('admin.transmissiontypes.*') || 
                                          request()->routeIs('admin.fueltypes.*') || 
                                          request()->routeIs('admin.bodytypes.*') || 
                                          request()->routeIs('admin.featurecategories.*') || 
                                          request()->routeIs('admin.carfeatures.*');
                    @endphp
                    @canany(['view_cars_admin', 'manage_cars_admin', 'manage_car_metadata'])
                    <li class="nav-item">
                        <a href="#cars-submenu" class="nav-link {{ $carSubmenuActive ? '' : 'collapsed' }}" 
                           data-bs-toggle="collapse" 
                           aria-expanded="{{ $carSubmenuActive ? 'true' : 'false' }}">
                            <i class="fas fa-car-alt"></i>
                            <span>إدارة السيارات</span>
                            <i class="fas fa-chevron-down chevron"></i>
                        </a>
                        <div class="collapse {{ $carSubmenuActive ? 'show' : '' }}" id="cars-submenu">
                            <ul class="nav flex-column submenu">
                                @can('view_cars_admin')
                                <li class="nav-item"><a href="{{-- route('admin.cars.index') --}}#" class="nav-link {{ request()->routeIs('admin.cars.index') ? 'active' : '' }}"><i class="fas fa-list"></i><span>عرض جميع السيارات</span></a></li>
                                @endcan
                                @can('manage_cars_admin')
                                <li class="nav-item"><a href="{{-- route('admin.cars.create') --}}#" class="nav-link {{ request()->routeIs('admin.cars.create') ? 'active' : '' }}"><i class="fas fa-plus"></i><span>إضافة سيارة جديدة</span></a></li>
                                @endcan
                                @can('manage_car_metadata')
                                <li class="nav-item"><a href="{{ route('admin.brands.index') }}" class="nav-link {{ request()->routeIs('admin.brands.*') ? 'active' : '' }}"><i class="fas fa-copyright"></i><span>الماركات</span></a></li>
                                {{-- Add more metadata links here as they are implemented --}}
                                {{-- Example: <li class="nav-item"><a href="{{ route('admin.models.index') }}" class="nav-link {{ request()->routeIs('admin.models.*') ? 'active' : '' }}"><i class="fas fa-car-side"></i><span>الموديلات</span></a></li> --}}
                                @endcan
                            </ul>
                        </div>
                    </li>
                    @endcanany
                    ```
                *   **(إدارة الطلبات - قائمة فرعية `orders-submenu`):** (أيقونة `fas fa-shopping-cart`, صلاحية رئيسية `view_orders_admin` أو مشابه)
                    *   "جميع الطلبات" (رابط: `{{-- route('admin.orders.index') --}}#`, حالة نشطة: `request()->routeIs('admin.orders.index') || request()->routeIs('admin.orders.show')`)
                *   **(إدارة العملاء):** (أيقونة `fas fa-users`, صلاحية: `view_customers_admin`)
                    *   "قائمة العملاء" (رابط: `{{-- route('admin.customers.index') --}}#`, حالة نشطة: `request()->routeIs('admin.customers.*')`)
                *   **(إدارة الموظفين - قائمة فرعية `employees-submenu`):** (أيقونة `fas fa-user-tie`, صلاحية رئيسية `@canany(['manage_employees_admin', 'manage_roles_permissions'])`)
                    *   @can('manage_employees_admin') "قائمة الموظفين" (رابط: `{{-- route('admin.employees.index') --}}#`, حالة نشطة: `request()->routeIs('admin.employees.*')`) @endcan
                    *   @can('manage_roles_permissions') "الأدوار والصلاحيات" (رابط: `{{-- route('admin.roles.index') --}}#`, حالة نشطة: `request()->routeIs('admin.roles.*')`) @endcan
                *   **(إدارة المحتوى - قائمة فرعية `cms-submenu`):** (أيقونة `fas fa-file-alt`, صلاحية رئيسية: `manage_cms`)
                    *   "الصفحات الثابتة" (رابط: `{{-- route('admin.cms.pages.index') --}}#`, حالة نشطة: `request()->routeIs('admin.cms.pages.*')`)
                    *   "بنرات الصفحة الرئيسية" (رابط: `{{-- route('admin.cms.banners.index') --}}#`, حالة نشطة: `request()->routeIs('admin.cms.banners.*')`)
                *   **(الإعدادات العامة - قائمة فرعية `settings-submenu`):** (أيقونة `fas fa-cog`, صلاحية رئيسية: `manage_system_settings`)
                    *   "إعدادات النظام" (رابط: `{{-- route('admin.settings.system.index') --}}#`, حالة نشطة: `request()->routeIs('admin.settings.system.*')`)
            * "5. تأكد من أن منطق `active`, `show`, `aria-expanded`, و `collapsed` يتم تطبيقه بشكل صحيح بناءً على المسار الحالي ومسارات القوائم الفرعية. استخدم `request()->routeIs('admin.prefix.*')` أو متغير Blade مجمع (مثل `$carSubmenuActive`) للتحقق من نشاط القائمة الفرعية."
* **REFERENCE_CODE_SNIPPETS:** (كما هو موضح أعلاه).
* **EXPECTED_OUTPUTS_BEHAVIOR:** القائمة الجانبية للوحة تحكم الإدارة تعرض العناصر بشكل ديناميكي بناءً على صلاحيات المستخدم المسجل دخوله، مع الحفاظ على تصميم وأيقونات وتفاعلات Dash الأصلية. يجب أن تعمل القوائم الفرعية بشكل صحيح وأن يتم تمييز العنصر النشط.
* **SECURITY_CONSIDERATIONS:** الاعتماد على `spatie/laravel-permission` للتحكم في العرض. يجب أن يكون التحقق من الصلاحيات في الـ Backend (Controllers/Middleware) هو المصدر الأساسي للحماية.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد تأثير كبير، الاستعلامات عن الصلاحيات تكون محسنة.
* **REQUIRED_CODE_COMMENTS:** تعليقات توضح شروط الصلاحيات لكل عنصر قائمة رئيسي أو قائمة فرعية.
* **ACCEPTANCE_CRITERIA:**
    1. "القائمة الجانبية تعرض فقط العناصر والقوائم الفرعية المسموح بها للمستخدم المسجل دخوله بناءً على صلاحياته."
    2. "الروابط في القائمة الجانبية (حتى لو كانت `#` مؤقتًا لبعضها) صحيحة وتتبع هيكل `route('admin.routeName')` أو `#`."
    3. "حالة 'active' للعنصر الحالي أو القائمة الفرعية الحالية (عبر `class='active'` و `class='show'` لـ `div.collapse` و `aria-expanded='true'` للرابط) تعمل بشكل صحيح."
    4. "القوائم الفرعية تفتح وتغلق بشكل صحيح عند النقر على العنصر الرئيسي لها، مع دوران أيقونة chevron."
    5. "وضع 'mini-mode' و 'hover-expand' (الموروث من `Dash/script.js`) يستمران في العمل بشكل صحيح مع القائمة الديناميكية (اختبار يدوي)."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented dynamic sidebar for Dash admin panel based on user roles/permissions, maintaining original Dash interactions (PH-02-DEL-002 Part 1).' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-002` (الجزء الخاص بالقائمة الجانبية) إلى `Completed` في `docs/TODO.md`. أضف مهام TODO لإنشاء المسارات (routes) الفعلية التي تشير إليها عناصر القائمة إذا لم تكن موجودة بعد."

---

### 4. **`PH02-TASK-004-DASH-DYNAMIC-TOPBAR-001`**
* **LEVEL:** `Medium`
* **OBJECTIVE:** تطوير الشريط العلوي (`_topbar.blade.php`) للوحة تحكم الإدارة Dash لعرض بيانات المستخدم المسجل دخوله (الاسم، الدور) بشكل ديناميكي، وعداد مبدئي للإشعارات غير المقروءة. يخدم هذا تخصيص [لوحة التحكم الاحترافية Dash]. (يغطي جزء الشريط العلوي من `PH-02-DEL-002` من `PPP-FR.md`).
* **TYPE:** `Dash Custom Blade View Modification (Dynamic Topbar Content)`
* **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/layouts/partials/_topbar.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-002`), `docs/UIUX-FR.md` (القسم 4.1.2 لوصف الشريط العلوي للوحة تحكم الإدارة).
    * **DESIGN_REF:** `Dash/index.html` (للهيكل الأساسي للشريط العلوي وقائمة المستخدم المنسدلة).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `Dash/index.html` (قسم `nav.top-navbar`) لتحديد أماكن عرض اسم المستخدم، القائمة المنسدلة للمستخدم، وأيقونة الإشعارات. تحقق من `docs/UIUX-FR.md` لتفاصيل ما يجب عرضه (مثل اسم المستخدم ودوره الأول). تأكد من أن نموذج تسجيل الخروج يعمل بشكل صحيح (POST)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-002` (الجزء الخاص بالشريط العلوي) إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:**
    * `TASK-ID:PH01-TASK-010 DASH-LAYOUT-PARTIALS-SETUP-001` (لوجود `_topbar.blade.php` الأولي).
    * `TASK-ID:PH01-TASK-007 ADMIN-AUTH-IMPLEMENTATION-001` (لتوفر بيانات المستخدم المسجل ودالة تسجيل الخروج).
* **LLM_ASSUMPTIONS:**
    * `Auth::user()` سيعيد كائن المستخدم المسجل.
    * سيتم عرض اسم المستخدم ودوره (الأول).
    * عداد الإشعارات سيكون ثابتًا (e.g., '5' كما في `dashboard.html`) في هذه المرحلة.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. MODIFY_TOPBAR_BLADE_FILE:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/layouts/partials/_topbar.blade.php`
        * **DETAILED_LOGIC:**
            * "1. افتح ملف `_topbar.blade.php` الذي يحتوي على HTML الثابت."
            * "2. **معلومات المستخدم (User Info in Dropdown Toggle):**"
                * "ابحث عن قسم عرض اسم المستخدم والصورة الرمزية (placeholder) في `div.user-dropdown > a.dropdown-toggle`."
                * "استبدل اسم المستخدم الثابت الأول بـ `@auth {{ Auth::user()->first_name }} {{ Auth::user()->last_name }} @else Guest @endauth`."
                * "أسفل الاسم (أو بجانبه إذا كان التصميم يسمح)، اعرض دور المستخدم الأول: `@auth <div class=\"small text-muted\">{{ Auth::user()->getRoleNames()->first() ?? 'User' }}</div> @endauth`."
                * "بالنسبة للصورة الرمزية (Avatar)، استخدم placeholder مؤقتًا (`/api/placeholder/40/40` كما في `dashboard.html`) أو أيقونة Font Awesome (`fas fa-user-circle fa-lg`) إذا لم تكن هناك صورة، مع ترك مكان لـ `@if(Auth::user()->profile_photo_path) <img src=\"{{ asset('storage/' . Auth::user()->profile_photo_path) }}\" alt=\"profile image\"> @else <img src=\"/api/placeholder/40/40\" alt=\"profile image\"> @endif` لاحقًا (بافتراض أن `profile_photo_path` هو حقل في `User` وأن الصور مخزنة في `storage/app/public`)."
            * "3. **قائمة المستخدم المنسدلة (User Dropdown Menu `ul.dropdown-menu`):**"
                * "تأكد من أن رابط 'الملف الشخصي' (`dropdown-item`) يشير إلى مسار مستقبلي (e.g., `{{-- route('admin.profile.edit') --}}#`)."
                * "تأكد من أن رابط 'الإعدادات' (`dropdown-item`) يشير إلى مسار مستقبلي (e.g., `{{-- route('admin.settings.index') --}}#`)."
                * "تأكد من أن رابط 'تسجيل الخروج' (`dropdown-item`) يستخدم النموذج الصحيح لإرسال طلب `POST` إلى `route('admin.logout')`:
                  ```html+php
                  <li>
                      <form method="POST" action="{{ route('admin.logout') }}" id="admin-topbar-logout-form" class="d-none">@csrf</form>
                      <a class="dropdown-item" href="#" 
                         onclick="event.preventDefault(); if(document.getElementById('admin-topbar-logout-form')) { document.getElementById('admin-topbar-logout-form').submit(); }">
                          <i class="fas fa-sign-out-alt me-2 text-danger"></i> تسجيل الخروج
                      </a>
                  </li>
                  ```"
            * "4. **أيقونة الإشعارات (`div.notification-bell`):**"
                * "ابحث عن أيقونة الجرس (`fas fa-bell`)."
                * "بجانبها، أو كشارة (`span.notification-badge`) عليها، اعرض الرقم الثابت `5` لعداد الإشعارات غير المقروءة كما هو في `dashboard.html` (سيتم جعله ديناميكيًا لاحقًا)."
            * "5. **زر تبديل القائمة الجانبية (`button#sidebar-toggler`):**"
                * "تأكد من أن الزر (`fas fa-bars`) موجود ومعرفه (ID) صحيح (`sidebar-toggler`) ليتفاعل مع `Dash/script.js`."
            * "6. **شريط البحث العام (`form.search-bar`):**"
                * "اتركه ثابتًا في هذه المرحلة. سيتم تحديد وظيفته لاحقًا."
* **REFERENCE_CODE_SNIPPETS:** (مقتطف لرابط تسجيل الخروج).
* **EXPECTED_OUTPUTS_BEHAVIOR:** الشريط العلوي يعرض اسم المستخدم المسجل ودوره (الأول). عداد الإشعارات يظهر بالقيمة الثابتة `5`. زر تسجيل الخروج يعمل بشكل صحيح. زر تبديل القائمة يعمل.
* **SECURITY_CONSIDERATIONS:** التأكد من صحة عمل تسجيل الخروج.
* **PERFORMANCE_CONSIDERATIONS:** لا يوجد.
* **REQUIRED_CODE_COMMENTS:** لا يوجد.
* **ACCEPTANCE_CRITERIA:**
    1. "يتم عرض اسم ودور المستخدم المسجل دخوله بشكل صحيح في الشريط العلوي."
    2. "أيقونة الإشعارات تظهر مع العداد الثابت `5`."
    3. "نموذج ورابط تسجيل الخروج في القائمة المنسدلة للمستخدم يعمل ويسجل خروج المستخدم بنجاح."
    4. "زر تبديل القائمة الجانبية يعمل كما هو متوقع."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented dynamic user information (name, role) and static notification counter (5) in Dash admin topbar. Ensured logout functionality from topbar. (PH-02-DEL-002 Part 2).' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-002` (الجزء الخاص بالشريط العلوي) إلى `Completed` في `docs/TODO.md`. أضف مهمة TODO لجعل عداد الإشعارات ديناميكيًا بناءً على `MOD-NOTIFICATION-FEAT-002` في مرحلة لاحقة."

---

### 5. **`PH02-TASK-005-METADATA-BRANDS-CRUD-001`**
* **LEVEL:** `High`
* **OBJECTIVE:** تنفيذ واجهات CRUD كاملة (قائمة عرض، نموذج إضافة، نموذج تعديل، وحذف) لإدارة ماركات السيارات (`Brands`) في لوحة تحكم الإدارة Dash، بما في ذلك وحدات التحكم (Controllers) اللازمة في موديول `CarCatalog` وطرق عرض Blade المناسبة. يخدم هذا مباشرة [لوحة التحكم الاحترافية Dash] ويمكن فريق الإدارة من تهيئة البيانات الأساسية لكتالوج السيارات. (يغطي جزء الماركات من `PH-02-DEL-004` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation (Controller, Routes, Blade Views)`
* **FILE_NAME_PATH:**
    * `Modules/CarCatalog/Http/Controllers/Admin/BrandController.php`
    * `Modules/CarCatalog/Http/Requests/Admin/StoreBrandRequest.php`
    * `Modules/CarCatalog/Http/Requests/Admin/UpdateBrandRequest.php`
    * `Modules/CarCatalog/Resources/views/admin/brands/index.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/brands/create.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/brands/edit.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/brands/_form.blade.php` (Partial)
    * `Modules/CarCatalog/Routes/admin.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `docs/PPP-FR.md` (`PH-02-DEL-004`)
        * `docs/REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-008`)
        * `docs/TS-FR.md` (`DB-TBL-002` - Brand)
        * `docs/UIUX-FR.md` (القسم 4.1.5 مثال `DASH-BRANDS-MGMT-001` لتصميم الواجهات، مع تكييف الحقول لتناسب الماركات)
        * `TASK-ID:PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001` (لوجود نموذج `Brand` و migration)
    * **DESIGN_REF:** `docs/UIUX-FR.md` (القسم 4.1.5) لتصميم جداول العرض والنماذج. `Dash/index.html` للاستلهام من هياكل الجداول والنماذج المتوافقة مع Dash (مثل استخدام classes `.card`, `.table`, `.form-control`, `.btn`).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (4.1.5) لتصميم صفحة إدارة الماركات (الجدول والنموذج). تحقق من `docs/TS-FR.md` لحقول جدول `brands` (خاصة `name`, `logo_id`, `description`, `status`). تأكد من أن `Brand` model يدعم `SoftDeletes` و `InteractsWithMedia` (إذا كان الشعار سيُدار عبر `spatie/laravel-medialibrary`). نموذج `Brand` يجب أن يكون قد تم إنشاؤه بالفعل."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-004` (الجزء الخاص بإدارة الماركات) إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:** `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`. (يفترض تثبيت `spatie/laravel-medialibrary` وتكوينه مبدئيًا).
* **LLM_ASSUMPTIONS:**
    * نموذج `Brand` موجود ويدعم `SoftDeletes`.
    * سيتم استخدام `spatie/laravel-medialibrary` لإدارة شعارات الماركات (logo) مع collection name `brand_logos`.
    * سيتم استخدام Resource Controller.
    * الصلاحية المطلوبة للوصول ستكون مبدئيًا `manage_car_metadata` (أو ما يعادلها).
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_BRAND:**
        * **`StoreBrandRequest.php`**: "أنشئ `StoreBrandRequest.php` في `Modules/CarCatalog/Http/Requests/Admin/`. Rules: `name` (required, string, max:100, unique:brands,name), `logo` (nullable, image, mimes:jpg,jpeg,png,svg,webp, max:1024), `description` (nullable, string, max:500), `status` (required, boolean)."
        * **`UpdateBrandRequest.php`**: "أنشئ `UpdateBrandRequest.php` في `Modules/CarCatalog/Http/Requests/Admin/`. Rules: `name` (required, string, max:100, Rule::unique('brands')->ignore(\$this->brand->id)), `logo` (nullable, image, mimes:jpg,jpeg,png,svg,webp, max:1024), `description` (nullable, string, max:500), `status` (required, boolean)." (تذكر إضافة `use Illuminate\Validation\Rule;`)
    * **2. CREATE_BRAND_CONTROLLER_METHODS:**
        * **FILE_NAME_PATH:** `Modules/CarCatalog/Http/Controllers/Admin/BrandController.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ `BrandController.php` يرث من `Modules\Core\Http\Controllers\BaseController`."
            * "2. أضف `use Modules\CarCatalog\Models\Brand; use Modules\CarCatalog\Http\Requests\Admin\StoreBrandRequest; use Modules\CarCatalog\Http\Requests\Admin\UpdateBrandRequest; use Illuminate\Http\Request; use Illuminate\View\View; use Illuminate\Http\RedirectResponse;`."
            * "3. **`index(Request $request): View`**:
                *   `\$query = Brand::query()->withCount('carModels');`
                *   `if (\$request->filled('search')) { \$query->where('name', 'like', '%' . \$request->input('search') . '%'); }`
                *   `if (\$request->has('status') && \$request->input('status') !== '') { \$query->where('status', \$request->input('status')); }`
                *   `\$brands = \$query->orderBy('name')->paginate(10);`
                *   `return view('car_catalog::admin.brands.index', compact('brands'));`"
            * "4. **`create(): View`**: `return view('car_catalog::admin.brands.create');`."
            * "5. **`store(StoreBrandRequest $request): RedirectResponse`**:
                *   `\$brand = Brand::create(\$request->validated());`
                *   `if (\$request->hasFile('logo') && \$request->file('logo')->isValid()) { \$brand->addMediaFromRequest('logo')->toMediaCollection('brand_logos'); }`
                *   `return redirect()->route('admin.brands.index')->with('success', 'تمت إضافة الماركة بنجAUC.');`"
            * "6. **`edit(Brand $brand): View`**: `return view('car_catalog::admin.brands.edit', compact('brand'));`."
            * "7. **`update(UpdateBrandRequest $request, Brand $brand): RedirectResponse`**:
                *   `\$brand->update(\$request->validated());`
                *   `if (\$request->hasFile('logo') && \$request->file('logo')->isValid()) { \$brand->clearMediaCollection('brand_logos'); \$brand->addMediaFromRequest('logo')->toMediaCollection('brand_logos'); }`
                *   `return redirect()->route('admin.brands.index')->with('success', 'تم تعديل الماركة بنجAUC.');`"
            * "8. **`destroy(Brand $brand): RedirectResponse`**:
                *   `if (\$brand->carModels()->exists()) { return back()->with('error', 'لا يمكن حذف الماركة لوجود موديلات مرتبطة بها. يمكنك تعطيلها بدلاً من ذلك.'); }`
                *   `\$brand->delete(); // Soft delete`
                *   `return redirect()->route('admin.brands.index')->with('success', 'تم حذف الماركة بنجAUC.');`"
    * **3. CREATE_BRAND_BLADE_VIEWS:**
        * **`Modules/CarCatalog/Resources/views/admin/brands/_form.blade.php` (Partial):**
            * **DETAILED_LOGIC:** "قم بإنشاء هذا الملف. استخدم `div.form-group.mb-3` أو `div.mb-3` لكل حقل.
                *   حقل لـ `name`: `<label for="name" class="form-label">اسم الماركة <span class="text-danger">*</span></label><input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', \$brand->name ?? '') }}" required>@error('name')<div class="invalid-feedback">{{ \$message }}</div>@enderror`
                *   حقل لـ `description`: `<label for="description" class="form-label">الوصف</label><textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description', \$brand->description ?? '') }}</textarea>@error('description')<div class="invalid-feedback">{{ \$message }}</div>@enderror`
                *   حقل لـ `status`: `<label for="status" class="form-label">الحالة <span class="text-danger">*</span></label><select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required><option value="1" {{ (old('status', isset(\$brand) ? \$brand->status : 1) == 1) ? 'selected' : '' }}>نشط</option><option value="0" {{ (old('status', isset(\$brand) ? \$brand->status : '') == '0') ? 'selected' : '' }}>غير نشط</option></select>@error('status')<div class="invalid-feedback">{{ \$message }}</div>@enderror`
                *   حقل لـ `logo`: `<label for="logo" class="form-label">شعار الماركة</label><input type="file" class="form-control @error('logo') is-invalid @enderror" id="logo" name="logo" accept="image/*">@error('logo')<div class="invalid-feedback">{{ \$message }}</div>@enderror`
                *   `@if(isset(\$brand) && \$brand->hasMedia('brand_logos'))<div class="mt-2"><img src="{{ \$brand->getFirstMediaUrl('brand_logos', 'thumb') }}" alt="{{ \$brand->name }}" width="100" class="img-thumbnail rounded"> <small>الشعار الحالي</small></div>@endif` (افترض أن لديك تحويل `thumb` بحجم 100px وأن `img-thumbnail` و `rounded` هي كلاسات Dash/Bootstrap).
                *   أزرار 'حفظ'/'تحديث' و 'إلغاء' ستكون في `create.blade.php` و `edit.blade.php`."
        * **`Modules/CarCatalog/Resources/views/admin/brands/index.blade.php`:**
            * **DETAILED_LOGIC:** "اجعله يمتد من `dashboard::layouts.admin_layout`.
                *   أضف `@section('title', 'إدارة الماركات')`.
                *   أضف مسار التنقل: لوحة التحكم > إدارة السيارات > الماركات.
                *   عنوان الصفحة: `<h4 class="fw-bold">إدارة الماركات</h4>`.
                *   زر 'إضافة ماركة جديدة': `<a href="{{ route('admin.brands.create') }}" class="btn btn-primary mb-3"><i class="fas fa-plus me-1"></i> إضافة ماركة جديدة</a>`.
                *   **نموذج الفلترة:** (باستخدام `div.card > div.card-body > form` وهيكل Dash) `<form method="GET" action="{{ route('admin.brands.index') }}" class="mb-4 p-3 border rounded"> <div class="row gx-2 gy-2 align-items-center"> <div class="col-md-4"><input type="text" name="search" class="form-control" placeholder="بحث بالاسم..." value="{{ request('search') }}"></div> <div class="col-md-3"><select name="status" class="form-select"><option value="">كل الحالات</option><option value="1" {{ request('status') == '1' ? 'selected' : '' }}>نشط</option><option value="0" {{ request('status') === '0' ? 'selected' : '' }}>غير نشط</option></select></div> <div class="col-md-2"><button type="submit" class="btn btn-info w-100">بحث</button></div> <div class="col-md-2"><a href="{{ route('admin.brands.index') }}" class="btn btn-secondary w-100">إعادة تعيين</a></div> </div></form>`.
                *   `@if (session('success')) <div class="alert alert-success alert-dismissible fade show" role="alert">{{ session('success') }}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div> @endif`
                *   `@if (session('error')) <div class="alert alert-danger alert-dismissible fade show" role="alert">{{ session('error') }}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div> @endif`
                *   اعرض جدولاً (`<div class="card"><div class="card-header"><h5 class="card-title mb-0">قائمة الماركات</h5></div><div class="card-body table-responsive p-0"><table class="table table-hover recent-activity-table">`): الأعمدة: '#', 'الشعار', 'اسم الماركة', 'الحالة', 'عدد الموديلات', 'تاريخ الإنشاء', 'الإجراءات'.
                *   **لـ 'الشعار'**: `<img src="{{ \$brand->getFirstMediaUrl('brand_logos', 'thumb') ?: '/api/placeholder/50/50?text=No+Logo' }}" alt="{{ \$brand->name }}" width="50" class="img-thumbnail rounded">`.
                *   **لـ 'الحالة'**: `<span class="badge rounded-pill bg-{{ \$brand->status ? 'success' : 'danger' }}">{{ \$brand->status ? 'نشط' : 'غير نشط' }}</span>`.
                *   **لـ 'عدد الموديلات'**: `{{ \$brand->car_models_count }}`.
                *   **لـ 'تاريخ الإنشاء'**: `{{ format_datetime_for_display(\$brand->created_at, 'Y-m-d') }}`.
                *   **لـ 'الإجراءات'**: `<div class="d-flex"><a href="{{ route('admin.brands.edit', \$brand->id) }}" class="btn btn-sm btn-warning me-1 action-btn edit" title="تعديل"><i class="fas fa-edit"></i></a><form action="{{ route('admin.brands.destroy', \$brand->id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذه الماركة؟ سيتم حذف الماركة بشكل ناعم.');">@csrf @method('DELETE')<button type="submit" class="btn btn-sm btn-danger action-btn delete" title="حذف"><i class="fas fa-trash"></i></button></form></div>`.
                *   `</tbody></table></div></div>` (أغلق الجدول والبطاقة).
                *   في حالة عدم وجود بيانات: `<tr><td colspan="7" class="text-center">لا توجد ماركات لعرضها حاليًا.</td></tr>`.
                *   أضف `<div class="mt-3">{{ \$brands->appends(request()->query())->links('pagination::bootstrap-5') }}</div>` للترقيم."
        * **`Modules/CarCatalog/Resources/views/admin/brands/create.blade.php`:**
            * **DETAILED_LOGIC:** "اجعله يمتد من `dashboard::layouts.admin_layout`. `@section('title', 'إضافة ماركة جديدة')`. مسار التنقل: لوحة التحكم > إدارة السيارات > الماركات > إضافة جديدة. عنوان الصفحة: `<h4 class="fw-bold">إضافة ماركة جديدة</h4>`. `<div class="card"><div class="card-header"><h5 class="card-title mb-0">بيانات الماركة</h5></div><div class="card-body"><form action="{{ route('admin.brands.store') }}" method="POST" enctype="multipart/form-data">@csrf @include('car_catalog::admin.brands._form') <div class="mt-4 d-flex justify-content-end"><button type="submit" class="btn btn-success me-2">حفظ الماركة</button> <a href="{{ route('admin.brands.index') }}" class="btn btn-secondary">إلغاء</a></div></form></div></div>`."
        * **`Modules/CarCatalog/Resources/views/admin/brands/edit.blade.php`:**
            * **DETAILED_LOGIC:** "اجعله يمتد من `dashboard::layouts.admin_layout`. `@section('title', 'تعديل الماركة: ' . \$brand->name)`. مسار التنقل. عنوان الصفحة: `<h4 class="fw-bold">تعديل الماركة: {{ \$brand->name }}</h4>`. `<div class="card"><div class="card-header"><h5 class="card-title mb-0">بيانات الماركة</h5></div><div class="card-body"><form action="{{ route('admin.brands.update', \$brand->id) }}" method="POST" enctype="multipart/form-data">@csrf @method('PUT') @include('car_catalog::admin.brands._form', ['brand' => \$brand]) <div class="mt-4 d-flex justify-content-end"><button type="submit" class="btn btn-success me-2">تحديث الماركة</button> <a href="{{ route('admin.brands.index') }}" class="btn btn-secondary">إلغاء</a></div></form></div></div>`."
    * **4. DEFINE_BRAND_ROUTES:**
        * **FILE_NAME_PATH:** `Modules/CarCatalog/Routes/admin.php`
        * **DETAILED_LOGIC:** "1. أنشئ المجلد والملف إذا لم يكونا موجودين."
        * "2. ضمن مجموعة مسارات محمية بـ `prefix(config('modules.AppConfig.admin_prefix', 'admin'))` و `name('admin.')` و middleware `['web', 'auth:web', 'verified', 'role:Super Admin|Employee', 'permission:manage_car_metadata']` (يجب إنشاء صلاحية `manage_car_metadata` إذا لم تكن موجودة وتعيينها للأدوار المناسبة)، قم بتعريف: `Route::resource('brands', \Modules\CarCatalog\Http\Controllers\Admin\BrandController::class);`."
        * "3. تأكد من أن `Modules\CarCatalog\Providers\RouteServiceProvider` يقوم بتحميل هذا الملف (ضمن `mapAdminRoutes()` method)."
* **REFERENCE_CODE_SNIPPETS:** (يمكن للـ Augment الاستعانة بـ Laravel best practices لإنشاء الـ Blade views والـ Controllers).
* **EXPECTED_OUTPUTS_BEHAVIOR:** يمكن للمسؤول إدارة ماركات السيارات (إضافة، عرض، تعديل، حذف) من خلال واجهات متوافقة مع تصميم Dash. يتم تحميل وعرض الشعارات بشكل صحيح.
* **SECURITY_CONSIDERATIONS:** تطبيق صلاحيات (e.g., `manage_car_metadata`) على المسارات. التحقق من صحة المدخلات.
* **PERFORMANCE_CONSIDERATIONS:** تحسين عرض الصور المصغرة للشعارات في الجدول (استخدام `thumb` conversion). استخدام الترقيم.
* **REQUIRED_CODE_COMMENTS:** DocBlocks لوحدات التحكم والدوال الهامة.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض قائمة الماركات في لوحة التحكم مع الفلترة والبحث والترقيم."
    2. "يمكن إضافة ماركة جديدة بنجAUC مع شعارها (إن وجد) وحالتها ووصفها."
    3. "يمكن تعديل بيانات ماركة موجودة (بما في ذلك تغيير الشعار، الاسم، الوصف، الحالة)."
    4. "يمكن حذف ماركة (Soft Delete)، مع منع الحذف إذا كانت مرتبطة بموديلات."
    5. "يتم تطبيق الصلاحيات بشكل صحيح على عمليات CRUD للماركات."
    6. "يتم عرض رسائل النجاح والخطأ بشكل مناسب."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented CRUD functionality for Car Brands (Controller, FormRequests, Routes, Blade Views with Spatie MediaLibrary for logos) in Dash admin panel (PH-02-DEL-004 Part 1).' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-004` (الجزء الخاص بإدارة الماركات) إلى `Completed` في `docs/TODO.md`. أنشئ مهام TODO مشابهة لباقي البيانات الوصفية للسيارات (موديلات، ألوان، إلخ)."
    * **DECISIONS.md:** "Augment: إذا تم اتخاذ قرار بشأن كيفية التعامل مع حذف ماركة مرتبطة بموديلات (مثل المنع أو التعطيل)، قم بتوثيقه. وثق استخدام collection name `brand_logos` لشعارات الماركات، وتحويل `thumb` مقترح بحجم 100px."

---

### 6. **`PH02-TASK-006-METADATA-MODELS-CRUD-002`**
* **LEVEL:** `High`
* **OBJECTIVE:** تنفيذ واجهات CRUD كاملة لإدارة موديلات السيارات (`CarModel`) في لوحة تحكم الإدارة Dash، بما في ذلك وحدات التحكم، FormRequests، طرق عرض Blade، والمسارات اللازمة ضمن موديول `CarCatalog`. يجب أن يتيح النظام ربط كل موديل بماركة (`Brand`) موجودة. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء الموديلات من `PH-02-DEL-004` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation (Controller, Routes, Blade Views)`
* **FILE_NAME_PATH:**
    * `Modules/CarCatalog/Http/Controllers/Admin/CarModelController.php`
    * `Modules/CarCatalog/Http/Requests/Admin/StoreCarModelRequest.php`
    * `Modules/CarCatalog/Http/Requests/Admin/UpdateCarModelRequest.php`
    * `Modules/CarCatalog/Resources/views/admin/models/index.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/models/create.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/models/edit.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/models/_form.blade.php` (Partial)
    * `Modules/CarCatalog/Routes/admin.php` (تحديث)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-004`), `docs/REQ-FR.md` (`FEAT-CAR-009`), `docs/TS-FR.md` (`DB-TBL-003` - CarModel).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (مبدأ تصميم مشابه لـ `DASH-BRANDS-MGMT-001` ولكن لحقول الموديلات).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TS-FR.md` لحقول جدول `car_models` (`brand_id`, `name`, `status`). نموذج `CarModel` يجب أن يدعم `SoftDeletes`. تأكد من توفير قائمة منسدلة بالماركات النشطة في نموذج الإضافة/التعديل."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-004` (الجزء الخاص بإدارة الموديلات) إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:** `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * نموذج `CarModel` موجود ويدعم `SoftDeletes` والعلاقة `belongsTo` مع `Brand`.
    * الصلاحية المطلوبة: `manage_car_metadata`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_CAR_MODEL:**
        * **`StoreCarModelRequest.php`**: Rules: `brand_id` (required, exists:brands,id), `name` (required, string, max:100, unique:car_models,name,NULL,id,brand_id,{request()->brand_id}), `status` (required, boolean).
        * **`UpdateCarModelRequest.php`**: Rules: `brand_id` (required, exists:brands,id), `name` (required, string, max:100, Rule::unique('car_models')->where('brand_id', \$this->input('brand_id'))->ignore(\$this->model->id)), `status` (required, boolean).
    * **2. CREATE_CAR_MODEL_CONTROLLER_METHODS:**
        * **FILE_NAME_PATH:** `Modules/CarCatalog/Http/Controllers/Admin/CarModelController.php`
        * **DETAILED_LOGIC:**
            * "1. أنشئ `CarModelController.php` يرث من `BaseController`."
            * "2. أضف `use Modules\CarCatalog\Models\CarModel; use Modules\CarCatalog\Models\Brand; ...`."
            * "3. **`index(Request $request): View`**: جلب الموديلات مع اسم الماركة (e.g., `CarModel::with('brand')->...`). دعم الفلترة بالاسم، الماركة، الحالة. الترقيم. عرض `car_catalog::admin.models.index`."
            * "4. **`create(): View`**: جلب الماركات النشطة (`Brand::where('status', 1)->pluck('name', 'id')`) وتمريرها إلى `car_catalog::admin.models.create`."
            * "5. **`store(StoreCarModelRequest $request): RedirectResponse`**: إنشاء `CarModel`. إعادة توجيه."
            * "6. **`edit(CarModel $model): View`**: جلب الماركات النشطة وتمريرها مع `\$model` إلى `car_catalog::admin.models.edit`."
            * "7. **`update(UpdateCarModelRequest $request, CarModel $model): RedirectResponse`**: تحديث `CarModel`. إعادة توجيه."
            * "8. **`destroy(CarModel $model): RedirectResponse`**: تحقق من عدم وجود سيارات مرتبطة بالموديل. حذف (Soft Delete). إعادة توجيه."
    * **3. CREATE_CAR_MODEL_BLADE_VIEWS:**
        * **`_form.blade.php`**: حقول: `brand_id` (select, populated with `\$brands`), `name` (text), `status` (select).
        * **`index.blade.php`**: جدول: #, اسم الموديل, اسم الماركة, الحالة, عدد السيارات, الإجراءات. فلاتر (اسم، ماركة، حالة).
        * **`create.blade.php`**: نموذج إضافة.
        * **`edit.blade.php`**: نموذج تعديل.
    * **4. DEFINE_CAR_MODEL_ROUTES:**
        * **FILE_NAME_PATH:** `Modules/CarCatalog/Routes/admin.php` (تحديث)
        * **DETAILED_LOGIC:** "ضمن نفس مجموعة المسارات المحمية، عرّف: `Route::resource('models', \Modules\CarCatalog\Http\Controllers\Admin\CarModelController::class);`."
* **EXPECTED_OUTPUTS_BEHAVIOR:** إدارة كاملة لموديلات السيارات من لوحة التحكم.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض، إضافة، تعديل، وحذف موديلات السيارات."
    2. "يتم ربط كل موديل بماركة بشكل صحيح."
    3. "الفلاتر والبحث في قائمة الموديلات تعمل."
    4. "يتم تطبيق الصلاحيات."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented CRUD functionality for Car Models in Dash admin panel.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء الموديلات) إلى `Completed`."

---

### 7. **`PH02-TASK-007-METADATA-COLORS-CRUD-003`**
* **LEVEL:** `Medium`
* **OBJECTIVE:** تنفيذ واجهات CRUD لإدارة ألوان السيارات (`Color`) في لوحة تحكم الإدارة Dash. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء الألوان من `PH-02-DEL-004` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation`
* **FILE_NAME_PATH:** (مشابه لـ Brands: Controller, FormRequests, Blade Views, Routes لـ `Color`)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-004`), `docs/REQ-FR.md` (`FEAT-CAR-011`), `docs/TS-FR.md` (`DB-TBL-005` - Color).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (مبدأ تصميم مشابه للماركات ولكن لحقول الألوان).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TS-FR.md` لحقول جدول `colors` (`name`, `hex_code`, `status`)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء الألوان) إلى `In Progress`."
* **DEPENDENCIES:** `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`.
* **LLM_ASSUMPTIONS:** نموذج `Color` موجود. الصلاحية: `manage_car_metadata`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_COLOR:**
        * **`StoreColorRequest.php`**: Rules: `name` (required, string, max:50, unique:colors,name), `hex_code` (nullable, string, max:7, regex:/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/, unique:colors,hex_code), `status` (required, boolean).
        * **`UpdateColorRequest.php`**: Rules: `name` (required, string, max:50, Rule::unique('colors')->ignore(\$this->color->id)), `hex_code` (nullable, string, max:7, regex:/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/, Rule::unique('colors')->ignore(\$this->color->id)), `status` (required, boolean).
    * **2. CREATE_COLOR_CONTROLLER_METHODS:** (إنشاء `ColorController.php` مع دوال CRUD مشابهة لـ `BrandController`).
    * **3. CREATE_COLOR_BLADE_VIEWS:**
        * **`_form.blade.php`**: حقول: `name` (text), `hex_code` (text, يمكن استخدام color picker JS بسيط إذا كان متاحًا في Dash أو يتم إضافته), `status` (select).
        * **`index.blade.php`**: جدول: #, اسم اللون, كود Hex (مع عينة لونية صغيرة `div` بخلفية `hex_code`), الحالة, الإجراءات.
        * **`create.blade.php`, `edit.blade.php`**.
    * **4. DEFINE_COLOR_ROUTES:** (تحديث `admin.php` لـ `CarCatalog` بـ `Route::resource('colors', ColorController::class);`).
* **EXPECTED_OUTPUTS_BEHAVIOR:** إدارة كاملة لألوان السيارات.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض، إضافة، تعديل، وحذف ألوان السيارات."
    2. "يتم التحقق من صحة كود Hex."
    3. "يتم تطبيق الصلاحيات."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented CRUD functionality for Car Colors in Dash admin panel.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء الألوان) إلى `Completed`."

---

### 8. **`PH02-TASK-008-METADATA-YEARS-CRUD-004`**
* **LEVEL:** `Medium`
* **OBJECTIVE:** تنفيذ واجهات CRUD لإدارة سنوات صنع السيارات (`ManufacturingYear`) في لوحة تحكم الإدارة Dash. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء سنوات الصنع من `PH-02-DEL-004` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation`
* **FILE_NAME_PATH:** (مشابه لـ Brands: Controller, FormRequests, Blade Views, Routes لـ `ManufacturingYear`)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-004`), `docs/REQ-FR.md` (`FEAT-CAR-012`), `docs/TS-FR.md` (`DB-TBL-004` - ManufacturingYear).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (مبدأ تصميم مشابه).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TS-FR.md` لحقول جدول `manufacturing_years` (`year`, `status`)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء سنوات الصنع) إلى `In Progress`."
* **DEPENDENCIES:** `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`.
* **LLM_ASSUMPTIONS:** نموذج `ManufacturingYear` موجود. الصلاحية: `manage_car_metadata`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_YEAR:**
        * **`StoreManufacturingYearRequest.php`**: Rules: `year` (required, integer, digits:4, unique:manufacturing_years,year, min:1900, max:{{date('Y') + 2}}), `status` (required, boolean).
        * **`UpdateManufacturingYearRequest.php`**: Rules: `year` (required, integer, digits:4, Rule::unique('manufacturing_years')->ignore(\$this->year->id), min:1900, max:{{date('Y') + 2}}), `status` (required, boolean).
    * **2. CREATE_MANUFACTURING_YEAR_CONTROLLER_METHODS:** (إنشاء `ManufacturingYearController.php`).
    * **3. CREATE_MANUFACTURING_YEAR_BLADE_VIEWS:**
        * **`_form.blade.php`**: حقول: `year` (number), `status` (select).
        * **`index.blade.php`**: جدول: #, السنة, الحالة, الإجراءات.
        * **`create.blade.php`, `edit.blade.php`**.
    * **4. DEFINE_MANUFACTURING_YEAR_ROUTES:** (`Route::resource('years', ManufacturingYearController::class);`).
* **EXPECTED_OUTPUTS_BEHAVIOR:** إدارة كاملة لسنوات الصنع.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض، إضافة، تعديل، وحذف سنوات الصنع."
    2. "يتم التحقق من صحة إدخال السنة."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented CRUD functionality for Manufacturing Years in Dash admin panel.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء سنوات الصنع) إلى `Completed`."

---

### 9. **`PH02-TASK-009-METADATA-TRANSMISSIONS-CRUD-005`**
* **LEVEL:** `Medium`
* **OBJECTIVE:** تنفيذ واجهات CRUD لإدارة أنواع ناقل حركة السيارات (`TransmissionType`) في لوحة تحكم الإدارة Dash. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء أنواع ناقل الحركة من `PH-02-DEL-004` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation`
* **FILE_NAME_PATH:** (مشابه لـ Brands: Controller, FormRequests, Blade Views, Routes لـ `TransmissionType`)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-004`), `docs/REQ-FR.md` (`FEAT-CAR-013`), `docs/TS-FR.md` (`DB-TBL-006` - TransmissionType).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (مبدأ تصميم مشابه).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TS-FR.md` لحقول جدول `transmission_types` (`name`, `status`)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء أنواع ناقل الحركة) إلى `In Progress`."
* **DEPENDENCIES:** `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`.
* **LLM_ASSUMPTIONS:** نموذج `TransmissionType` موجود. الصلاحية: `manage_car_metadata`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_TRANSMISSION_TYPE:**
        * **`StoreTransmissionTypeRequest.php`**: Rules: `name` (required, string, max:50, unique:transmission_types,name), `status` (required, boolean).
        * **`UpdateTransmissionTypeRequest.php`**: Rules: `name` (required, string, max:50, Rule::unique('transmission_types')->ignore(\$this->transmissiontype->id)), `status` (required, boolean).
    * **2. CREATE_TRANSMISSION_TYPE_CONTROLLER_METHODS:** (إنشاء `TransmissionTypeController.php`).
    * **3. CREATE_TRANSMISSION_TYPE_BLADE_VIEWS:**
        * **`_form.blade.php`**: حقول: `name` (text), `status` (select).
        * **`index.blade.php`**: جدول: #, اسم نوع ناقل الحركة, الحالة, الإجراءات.
        * **`create.blade.php`, `edit.blade.php`**.
    * **4. DEFINE_TRANSMISSION_TYPE_ROUTES:** (`Route::resource('transmission-types', TransmissionTypeController::class)->parameters(['transmission-types' => 'transmissiontype']);`).
* **EXPECTED_OUTPUTS_BEHAVIOR:** إدارة كاملة لأنواع ناقل الحركة.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض، إضافة، تعديل، وحذف أنواع ناقل الحركة."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented CRUD functionality for Transmission Types in Dash admin panel.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء أنواع ناقل الحركة) إلى `Completed`."

---

### 10. **`PH02-TASK-010-METADATA-FUELS-CRUD-006`**
* **LEVEL:** `Medium`
* **OBJECTIVE:** تنفيذ واجهات CRUD لإدارة أنواع وقود السيارات (`FuelType`) في لوحة تحكم الإدارة Dash. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء أنواع الوقود من `PH-02-DEL-004` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation`
* **FILE_NAME_PATH:** (مشابه لـ Brands: Controller, FormRequests, Blade Views, Routes لـ `FuelType`)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-004`), `docs/REQ-FR.md` (`FEAT-CAR-014`), `docs/TS-FR.md` (`DB-TBL-007` - FuelType).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (مبدأ تصميم مشابه).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TS-FR.md` لحقول جدول `fuel_types` (`name`, `status`)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء أنواع الوقود) إلى `In Progress`."
* **DEPENDENCIES:** `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`.
* **LLM_ASSUMPTIONS:** نموذج `FuelType` موجود. الصلاحية: `manage_car_metadata`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_FUEL_TYPE:**
        * **`StoreFuelTypeRequest.php`**: Rules: `name` (required, string, max:50, unique:fuel_types,name), `status` (required, boolean).
        * **`UpdateFuelTypeRequest.php`**: Rules: `name` (required, string, max:50, Rule::unique('fuel_types')->ignore(\$this->fueltype->id)), `status` (required, boolean).
    * **2. CREATE_FUEL_TYPE_CONTROLLER_METHODS:** (إنشاء `FuelTypeController.php`).
    * **3. CREATE_FUEL_TYPE_BLADE_VIEWS:**
        * **`_form.blade.php`**: حقول: `name` (text), `status` (select).
        * **`index.blade.php`**: جدول: #, اسم نوع الوقود, الحالة, الإجراءات.
        * **`create.blade.php`, `edit.blade.php`**.
    * **4. DEFINE_FUEL_TYPE_ROUTES:** (`Route::resource('fuel-types', FuelTypeController::class)->parameters(['fuel-types' => 'fueltype']);`).
* **EXPECTED_OUTPUTS_BEHAVIOR:** إدارة كاملة لأنواع الوقود.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض، إضافة، تعديل، وحذف أنواع الوقود."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented CRUD functionality for Fuel Types in Dash admin panel.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء أنواع الوقود) إلى `Completed`."

---

### 11. **`PH02-TASK-011-METADATA-BODYTYPES-CRUD-007`**
* **LEVEL:** `Medium`
* **OBJECTIVE:** تنفيذ واجهات CRUD لإدارة أنواع هياكل السيارات (`BodyType`) في لوحة تحكم الإدارة Dash. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء أنواع هياكل السيارات من `PH-02-DEL-004` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation (Controller, Routes, Blade Views)`
* **FILE_NAME_PATH:** (مشابه للمهام السابقة: Controller, FormRequests, Blade Views, Routes لـ `BodyType`)
    * `Modules/CarCatalog/Http/Controllers/Admin/BodyTypeController.php`
    * `Modules/CarCatalog/Http/Requests/Admin/StoreBodyTypeRequest.php`
    * `Modules/CarCatalog/Http/Requests/Admin/UpdateBodyTypeRequest.php`
    * `Modules/CarCatalog/Resources/views/admin/bodytypes/index.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/bodytypes/create.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/bodytypes/edit.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/bodytypes/_form.blade.php`
    * `Modules/CarCatalog/Routes/admin.php` (تحديث)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-004`), `docs/TS-FR.md` (`DB-TBL-021` - BodyType).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (مبدأ تصميم مشابه للماركات ولكن لحقول أنواع الهياكل).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TS-FR.md` لحقول جدول `body_types` (`name`, `status`)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء أنواع هياكل السيارات) إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:** `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`.
* **LLM_ASSUMPTIONS:** نموذج `BodyType` موجود. الصلاحية: `manage_car_metadata`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_BODY_TYPE:**
        * **`StoreBodyTypeRequest.php`**: Rules: `name` (required, string, max:50, unique:body_types,name), `status` (required, boolean).
        * **`UpdateBodyTypeRequest.php`**: Rules: `name` (required, string, max:50, Rule::unique('body_types')->ignore(\$this->bodytype->id)), `status` (required, boolean).
    * **2. CREATE_BODY_TYPE_CONTROLLER_METHODS:** (إنشاء `BodyTypeController.php` مع دوال CRUD مشابهة لـ `BrandController`).
    * **3. CREATE_BODY_TYPE_BLADE_VIEWS:**
        * **`_form.blade.php`**: حقول: `name` (text), `status` (select).
        * **`index.blade.php`**: جدول: #, اسم نوع الهيكل, الحالة, الإجراءات.
        * **`create.blade.php`, `edit.blade.php`**.
    * **4. DEFINE_BODY_TYPE_ROUTES:** (`Route::resource('body-types', \Modules\CarCatalog\Http\Controllers\Admin\BodyTypeController::class)->parameters(['body-types' => 'bodytype']);`).
* **EXPECTED_OUTPUTS_BEHAVIOR:** إدارة كاملة لأنواع هياكل السيارات.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض، إضافة، تعديل، وحذف أنواع هياكل السيارات."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented CRUD functionality for Car Body Types in Dash admin panel.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء أنواع هياكل السيارات) إلى `Completed`."

---

### 12. **`PH02-TASK-012-METADATA-FEATURECATS-CRUD-008`**
* **LEVEL:** `Medium`
* **OBJECTIVE:** تنفيذ واجهات CRUD لإدارة فئات ميزات السيارات (`FeatureCategory`) في لوحة تحكم الإدارة Dash. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء فئات الميزات من `PH-02-DEL-004` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation`
* **FILE_NAME_PATH:** (مشابه: Controller, FormRequests, Blade Views, Routes لـ `FeatureCategory`)
    * `Modules/CarCatalog/Http/Controllers/Admin/FeatureCategoryController.php`
    * `Modules/CarCatalog/Http/Requests/Admin/StoreFeatureCategoryRequest.php`
    * `Modules/CarCatalog/Http/Requests/Admin/UpdateFeatureCategoryRequest.php`
    * `Modules/CarCatalog/Resources/views/admin/featurecategories/index.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/featurecategories/create.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/featurecategories/edit.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/featurecategories/_form.blade.php`
    * `Modules/CarCatalog/Routes/admin.php` (تحديث)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-004`), `docs/TS-FR.md` (`DB-TBL-022` - FeatureCategory).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (مبدأ تصميم مشابه).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TS-FR.md` لحقول جدول `feature_categories` (`name`, `status`)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء فئات الميزات) إلى `In Progress`."
* **DEPENDENCIES:** `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`.
* **LLM_ASSUMPTIONS:** نموذج `FeatureCategory` موجود. الصلاحية: `manage_car_metadata`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_FEATURE_CATEGORY:**
        * **`StoreFeatureCategoryRequest.php`**: Rules: `name` (required, string, max:100, unique:feature_categories,name), `status` (required, boolean).
        * **`UpdateFeatureCategoryRequest.php`**: Rules: `name` (required, string, max:100, Rule::unique('feature_categories')->ignore(\$this->featurecategory->id)), `status` (required, boolean).
    * **2. CREATE_FEATURE_CATEGORY_CONTROLLER_METHODS:** (إنشاء `FeatureCategoryController.php`).
    * **3. CREATE_FEATURE_CATEGORY_BLADE_VIEWS:**
        * **`_form.blade.php`**: حقول: `name` (text), `status` (select).
        * **`index.blade.php`**: جدول: #, اسم فئة الميزة, الحالة, عدد الميزات المرتبطة, الإجراءات.
        * **`create.blade.php`, `edit.blade.php`**.
    * **4. DEFINE_FEATURE_CATEGORY_ROUTES:** (`Route::resource('feature-categories', \Modules\CarCatalog\Http\Controllers\Admin\FeatureCategoryController::class)->parameters(['feature-categories' => 'featurecategory']);`).
* **EXPECTED_OUTPUTS_BEHAVIOR:** إدارة كاملة لفئات ميزات السيارات.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض، إضافة، تعديل، وحذف فئات ميزات السيارات."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented CRUD functionality for Car Feature Categories in Dash admin panel.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء فئات الميزات) إلى `Completed`."

---

### 13. **`PH02-TASK-013-METADATA-CARFEATURES-CRUD-009`**
* **LEVEL:** `High`
* **OBJECTIVE:** تنفيذ واجهات CRUD لإدارة ميزات السيارات (`CarFeature`) في لوحة تحكم الإدارة Dash، مع ربط كل ميزة بفئة ميزات (`FeatureCategory`) موجودة. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء ميزات السيارات من `PH-02-DEL-004` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation`
* **FILE_NAME_PATH:** (مشابه: Controller, FormRequests, Blade Views, Routes لـ `CarFeature`)
    * `Modules/CarCatalog/Http/Controllers/Admin/CarFeatureController.php`
    * `Modules/CarCatalog/Http/Requests/Admin/StoreCarFeatureRequest.php`
    * `Modules/CarCatalog/Http/Requests/Admin/UpdateCarFeatureRequest.php`
    * `Modules/CarCatalog/Resources/views/admin/carfeatures/index.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/carfeatures/create.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/carfeatures/edit.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/carfeatures/_form.blade.php`
    * `Modules/CarCatalog/Routes/admin.php` (تحديث)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-004`), `docs/REQ-FR.md` (`FEAT-CAR-010`), `docs/TS-FR.md` (`DB-TBL-008` - CarFeature).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (مبدأ تصميم مشابه).
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/TS-FR.md` لحقول جدول `car_features` (`name`, `category_id`, `icon`, `status`). تأكد من توفير قائمة منسدلة بفئات الميزات النشطة في نموذج الإضافة/التعديل."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء ميزات السيارات) إلى `In Progress`."
* **DEPENDENCIES:** `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`, `PH02-TASK-012-METADATA-FEATURECATS-CRUD-008`.
* **LLM_ASSUMPTIONS:** نموذج `CarFeature` و `FeatureCategory` موجودان. الصلاحية: `manage_car_metadata`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_CAR_FEATURE:**
        * **`StoreCarFeatureRequest.php`**: Rules: `name` (required, string, max:100, unique:car_features,name), `category_id` (nullable, exists:feature_categories,id), `icon` (nullable, string, max:50), `status` (required, boolean).
        * **`UpdateCarFeatureRequest.php`**: Rules: `name` (required, string, max:100, Rule::unique('car_features')->ignore(\$this->carfeature->id)), `category_id` (nullable, exists:feature_categories,id), `icon` (nullable, string, max:50), `status` (required, boolean).
    * **2. CREATE_CAR_FEATURE_CONTROLLER_METHODS:** (إنشاء `CarFeatureController.php`). في `create()` و `edit()`، قم بجلب `FeatureCategory::where('status', 1)->pluck('name', 'id')` وتمريرها كـ `\$categories`.
    * **3. CREATE_CAR_FEATURE_BLADE_VIEWS:**
        * **`_form.blade.php`**: حقول: `name` (text), `category_id` (select, populated with `\$categories`, allow null), `icon` (text, placeholder: e.g., "fas fa-sun"), `status` (select).
        * **`index.blade.php`**: جدول: #, اسم الميزة, الفئة, الأيقونة (عرض الأيقونة نفسها إذا أمكن ` <i class="{{ \$feature->icon }}"></i>`), الحالة, الإجراءات.
        * **`create.blade.php`, `edit.blade.php`**.
    * **4. DEFINE_CAR_FEATURE_ROUTES:** (`Route::resource('car-features', \Modules\CarCatalog\Http\Controllers\Admin\CarFeatureController::class)->parameters(['car-features' => 'carfeature']);`).
* **EXPECTED_OUTPUTS_BEHAVIOR:** إدارة كاملة لميزات السيارات.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض، إضافة، تعديل، وحذف ميزات السيارات."
    2. "يتم ربط كل ميزة بفئة (اختياري) بشكل صحيح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented CRUD functionality for Car Features (with category linking) in Dash admin panel.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-004` (جزء ميزات السيارات) إلى `Completed`."

---

### 14. **`PH02-TASK-014-DASH-CAR-CRUD-STEPPER-SETUP-001`**
* **LEVEL:** `Very High`
* **OBJECTIVE:** تنفيذ واجهة إضافة/تعديل السيارة (`DASH-CAR-CRUD-001`) في لوحة تحكم الإدارة Dash، باستخدام مكون Stepper المكون من 5 خطوات (المبني على `bs-stepper` من `dashboard.html` وملفات Dash JS)، مع ربط البيانات بالقوائم المنسدلة من المهام السابقة، والتحقق من الصحة، وإدارة رفع الصور باستخدام `spatie/laravel-medialibrary`. يخدم هذا [لوحة التحكم الاحترافية Dash] بشكل محوري. (يغطي `PH-02-DEL-005` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation (Stepper UI, Controller, Routes, Blade Views)`
* **FILE_NAME_PATH:**
    * `Modules/CarCatalog/Http/Controllers/Admin/CarController.php`
    * `Modules/CarCatalog/Http/Requests/Admin/StoreCarRequest.php`
    * `Modules/CarCatalog/Http/Requests/Admin/UpdateCarRequest.php`
    * `Modules/CarCatalog/Resources/views/admin/cars/create.blade.php` (يحتوي على Stepper)
    * `Modules/CarCatalog/Resources/views/admin/cars/edit.blade.php` (يحتوي على Stepper)
    * `Modules/CarCatalog/Resources/views/admin/cars/partials/stepper_basic_info.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/cars/partials/stepper_tech_specs.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/cars/partials/stepper_features.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/cars/partials/stepper_images.blade.php`
    * `Modules/CarCatalog/Resources/views/admin/cars/partials/stepper_price_status.blade.php`
    * `Modules/CarCatalog/Routes/admin.php` (تحديث)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-005`), `docs/REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-018`), `docs/TS-FR.md` (`DB-TBL-009` - Car), `docs/UIUX-FR.md` (`DASH-CAR-CRUD-001`, القسم 4.1.3).
    * **DESIGN_REF:** `Dash/dashboard.html` (لهيكل Stepper `div.bs-stepper`), `docs/UIUX-FR.md` (4.1.3) لتفاصيل الحقول في كل خطوة.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (4.1.3) و `docs/REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-018`) بدقة لتحديد جميع الحقول المطلوبة لكل خطوة من خطوات Stepper الخمس، وأنواعها، وقواعد التحقق. تأكد من فهم كيفية ربط القوائم المنسدلة (ماركات، موديلات، ألوان، إلخ) بالبيانات التي تم إعدادها في مهام CRUD السابقة. كيفية تهيئة `bs-stepper.min.js` موجودة في `Dash/script.js` أو `dashboard.html`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-005` إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:** جميع مهام CRUD للبيانات الوصفية (`PH02-TASK-005` إلى `PH02-TASK-013`), `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`. (يفترض تثبيت `spatie/laravel-medialibrary`).
* **LLM_ASSUMPTIONS:**
    * نموذج `Car` موجود ويدعم `SoftDeletes` و `InteractsWithMedia`.
    * جميع نماذج البيانات الوصفية (Brand, CarModel, Color, etc.) موجودة.
    * الصلاحية المطلوبة: `manage_cars_admin`.
    * سيتم استخدام collection name `car_images` للصور الرئيسية وصور المعرض، و `car_main_image` للصورة الرئيسية إذا تم فصلها.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_CAR:**
        * **`StoreCarRequest.php`**: Rules لجميع الحقول عبر الخطوات الخمس، مع التحقق من تفرد `vin` (nullable, unique).
        * **`UpdateCarRequest.php`**: Rules مشابهة مع `Rule::unique('cars')->ignore(\$this->car->id)` لـ `vin`.
    * **2. CREATE_CAR_CONTROLLER_METHODS (`CarController.php`):**
        * **`create(): View`**: جلب البيانات اللازمة لجميع القوائم المنسدلة والميزات (Brands, Models (سيتم تحميلها بـ AJAX بناءً على الماركة), Years, Colors, TransmissionTypes, FuelTypes, BodyTypes, CarFeatures مجمعة حسب FeatureCategory) وتمريرها إلى `car_catalog::admin.cars.create`.
        * **`store(StoreCarRequest $request): RedirectResponse`**:
            *   "إنشاء سجل `Car` بالبيانات من `\$request->validated()`."
            *   "ربط العلاقات `BelongsTo` (brand_id, model_id, etc.)."
            *   "ربط الميزات المختارة (علاقة `BelongsToMany` مع `car_features`)."
            *   "معالجة رفع الصور (الرئيسية وصور المعرض) باستخدام `spatie/laravel-medialibrary`. إذا تم رفع صورة رئيسية منفصلة، استخدم collection name مختلف. لصور المعرض، ارفعها كمجموعة."
            *   "إعادة توجيه إلى قائمة السيارات أو صفحة عرض السيارة مع رسالة نجاح."
        * **`edit(Car $car): View`**: جلب بيانات السيارة والبيانات اللازمة للقوائم المنسدلة، وتمريرها إلى `car_catalog::admin.cars.edit`.
        * **`update(UpdateCarRequest $request, Car $car): RedirectResponse`**: تحديث سجل `Car`، معالجة الصور (حذف القديمة إذا تم رفع جديد، إضافة صور جديدة للمعرض)، وتحديث العلاقات.
        * **(AJAX Endpoint - اختياري لكن موصى به) `getModelsByBrand(Request $request, Brand $brand): JsonResponse`**: "يرجع قائمة بالموديلات (id, name) بتنسيق JSON للماركة المحددة، ليتم استخدامها لتعبئة قائمة الموديلات ديناميكيًا في Stepper."
    * **3. CREATE_CAR_STEPPER_BLADE_VIEWS:**
        * **`create.blade.php` و `edit.blade.php` (الصفحات الرئيسية للـ Stepper):**
            * **DETAILED_LOGIC:** "اجعلها تمتد من `dashboard::layouts.admin_layout`. أضف عنوان الصفحة ومسار التنقل."
            * "قم بتضمين هيكل HTML لـ `bs-stepper` كما في `dashboard.html` (`<div class=\"bs-stepper\"> <div class=\"bs-stepper-header\">...</div> <div class=\"bs-stepper-content\">...</div> </div>`)."
            * "في `bs-stepper-header`، قم بتعريف الخطوات الخمس مع أيقوناتها وأسمائها."
            * "في `bs-stepper-content`، لكل خطوة (`<div id=\"step-name-part\" class=\"content\" role=\"tabpanel\">`)، قم بتضمين (`@include`) الـ partial الخاص بها (e.g., `@include('car_catalog::admin.cars.partials.stepper_basic_info')`)."
            * "أضف أزرار التنقل بين الخطوات وأزرار الحفظ/التحديث في المكان المناسب (عادةً داخل كل content part أو كأزرار عامة للـ stepper)."
            * "في نهاية الصفحة (`@push('scripts')`)، قم بتهيئة `bs-stepper` JavaScript: `var stepper = new Stepper(document.querySelector('.bs-stepper'));` وأضف أي JS مطلوب للتفاعلات (مثل تحميل الموديلات بـ AJAX)."
        * **`partials/stepper_basic_info.blade.php`**: حقول الخطوة 1 (الماركة (select)، الموديل (select - يُعبأ بـ AJAX)، السنة، اللون، VIN، الفئة، الوصف (Rich Text Editor)).
        * **`partials/stepper_tech_specs.blade.php`**: حقول الخطوة 2.
        * **`partials/stepper_features.blade.php`**: حقول الخطوة 3 (checkboxes للميزات مجمعة حسب الفئة).
        * **`partials/stepper_images.blade.php`**: حقول الخطوة 4 (مكون رفع صور متعددة، حقل لرابط الفيديو).
        * **`partials/stepper_price_status.blade.php`**: حقول الخطوة 5.
    * **4. DEFINE_CAR_ROUTES (تحديث `admin.php` لـ `CarCatalog`):**
        * "أضف `Route::resource('cars', \Modules\CarCatalog\Http\Controllers\Admin\CarController::class);`."
        * "أضف المسار لنقطة نهاية AJAX للموديلات: `Route::get('brands/{brand}/models', [\Modules\CarCatalog\Http\Controllers\Admin\CarController::class, 'getModelsByBrand'])->name('brands.models.get');`."
* **EXPECTED_OUTPUTS_BEHAVIOR:** واجهة Stepper وظيفية بالكامل لإضافة وتعديل السيارات، مع تحميل ديناميكي للبيانات (الموديلات) ورفع الصور.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن التنقل بين جميع خطوات Stepper الخمس."
    2. "يتم ملء القوائم المنسدلة (الماركات، الألوان، إلخ) بشكل صحيح."
    3. "قائمة الموديلات تتحدث ديناميكيًا عند اختيار ماركة."
    4. "يمكن إضافة سيارة جديدة بنجاح مع جميع تفاصيلها (بيانات، ميزات، صور)."
    5. "يمكن تعديل سيارة موجودة بنجاح."
    6. "يتم تطبيق التحقق من صحة المدخلات بشكل صحيح في الـ Backend."
    7. "يتم التعامل مع رفع الصور وتخزينها بشكل صحيح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented Car CRUD functionality using a 5-step Stepper UI in Dash admin panel (Controller, FormRequests, Stepper Blade Views, Routes, AJAX for models, Spatie MediaLibrary for images) (PH-02-DEL-005).'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-005` إلى `Completed`."
    * **DECISIONS.md:** "Augment: وثق قرار استخدام AJAX لجلب الموديلات، واستخدام collection name `car_images` و `car_main_image` للصور."

---

### 15. **`PH02-TASK-015-DASH-CAR-LIST-SETUP-001`**
* **LEVEL:** `High`
* **OBJECTIVE:** تنفيذ واجهة قائمة السيارات (`DASH-CAR-LIST-001`) في لوحة تحكم الإدارة Dash، مع دعم البحث الأساسي، الفلترة البسيطة، والترقيم، وعرض إجراءات الإدارة. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي `PH-02-DEL-006` من `PPP-FR.md`).
* **TYPE:** `Dash Custom Blade View Implementation (Car List & Filtering)`
* **FILE_NAME_PATH:**
    * `Modules/CarCatalog/Http/Controllers/Admin/CarController.php` (تحديث `index` method)
    * `Modules/CarCatalog/Resources/views/admin/cars/index.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-006`), `docs/UIUX-FR.md` (`DASH-CAR-LIST-001`).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (4.1.4) لتصميم الواجهة والفلاتر.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (4.1.4) لتفاصيل تصميم قائمة السيارات، بما في ذلك الأعمدة المطلوبة في الجدول، الفلاتر الأساسية (بحث بالاسم/الموديل/VIN، فلتر بالماركة، الموديل، الحالة)، وأزرار الإجراءات."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-006` إلى `In Progress`."
* **DEPENDENCIES:** `PH02-TASK-014-DASH-CAR-CRUD-STEPPER-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * نموذج `Car` وعلاقاته مع البيانات الوصفية موجودة.
    * الصلاحية المطلوبة: `view_cars_admin`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. UPDATE_CAR_CONTROLLER_INDEX_METHOD:**
        * **FILE_NAME_PATH:** `Modules/CarCatalog/Http/Controllers/Admin/CarController.php`
        * **DETAILED_LOGIC:**
            * "1. قم بتحديث دالة `index(Request $request): View`."
            * "2. ابدأ بالاستعلام: `\$query = Car::query()->with(['brand', 'model', 'mainColor', 'manufacturingYear']);` (أو أي علاقات أخرى تحتاج لعرضها في الجدول)."
            * "3. **تطبيق الفلاتر:**"
                *   `if (\$request->filled('search')) { \$searchTerm = \$request->input('search'); \$query->where(function(\$q) use (\$searchTerm) { \$q->where('trim_name', 'like', "%{\$searchTerm}%") ->orWhere('vin', 'like', "%{\$searchTerm}%") ->orWhereHas('brand', function(\$qb) use (\$searchTerm){ \$qb->where('name', 'like', "%{\$searchTerm}%"); }) ->orWhereHas('model', function(\$qm) use (\$searchTerm){ \$qm->where('name', 'like', "%{\$searchTerm}%"); }); }); }`
                *   `if (\$request->filled('brand_id')) { \$query->where('brand_id', \$request->input('brand_id')); }`
                *   `if (\$request->filled('model_id')) { \$query->where('model_id', \$request->input('model_id')); }`
                *   `if (\$request->filled('status')) { \$query->where('status', \$request->input('status')); }`
            * "4. **الترتيب (Sort):** (مبدئيًا حسب الأحدث) `\$query->latest();` (يمكن إضافة خيارات ترتيب متقدمة لاحقًا)."
            * "5. **الترقيم (Pagination):** `\$cars = \$query->paginate(15);`."
            * "6. جلب البيانات اللازمة للفلاتر (e.g., `\$brands = Brand::where('status',1)->pluck('name','id');`, `\$carStatuses = [...]` - قائمة ثابتة أو من enum)."
            * "7. قم بتمرير `\$cars`, `\$brands`, `\$carStatuses` إلى `car_catalog::admin.cars.index`."
    * **2. CREATE_CAR_LIST_BLADE_VIEW (`index.blade.php`):**
        * **FILE_NAME_PATH:** `Modules/CarCatalog/Resources/views/admin/cars/index.blade.php`
        * **DETAILED_LOGIC:**
            * "1. اجعله يمتد من `dashboard::layouts.admin_layout`."
            * "2. أضف عنوان الصفحة ومسار التنقل."
            * "3. زر 'إضافة سيارة جديدة' (`route('admin.cars.create')`)."
            * "4. **نموذج الفلترة والبحث (مشابه لـ Brands index):** حقل بحث نصي (`search`), select لـ `brand_id` (مع `\$brands`), select لـ `model_id` (يُعبأ بـ AJAX بناءً على الماركة - TODO: يمكن إضافة هذه الميزة هنا أو في تحسين لاحق), select لـ `status` (مع `\$carStatuses`), زر 'بحث', زر 'إعادة تعيين'."
            * "5. **جدول السيارات:** الأعمدة: '#', 'صورة مصغرة', 'اسم السيارة (ماركة-موديل-فئة-سنة)', 'رقم الهيكل (VIN)', 'السعر', 'الحالة', 'مميزة؟', 'تاريخ الإضافة', 'الإجراءات'."
                *   "**لـ 'صورة مصغرة'**: `<img src=\"{{ \$car->getFirstMediaUrl('car_images', 'thumb') ?: (\$car->getFirstMediaUrl('car_main_image', 'thumb') ?: '/api/placeholder/60/40?text=No+Image') }}\" alt=\"{{ \$car->trim_name }}\" width=\"60\" class=\"img-thumbnail rounded\">` (بافتراض تحويل `thumb`)."
                *   "**لـ 'اسم السيارة'**: `{{ \$car->brand->name ?? '' }} {{ \$car->model->name ?? '' }} - {{ \$car->trim_name }} ({{ \$car->manufacturingYear->year ?? '' }})`."
                *   "**لـ 'السعر'**: `{{ format_currency(\$car->total_price) }}` (استخدم دالة المساعدة)."
                *   "**لـ 'الحالة'**: `<span class=\"badge rounded-pill bg-{{ car_status_badge_class(\$car->status) }}\">{{ car_status_label(\$car->status) }}</span>` (يتطلب دوال مساعدة `car_status_badge_class` و `car_status_label` لترجمة الحالات إلى كلاسات وأسماء عرض)."
                *   "**لـ 'مميزة؟'**: `<i class=\"fas fa-{{ \$car->is_featured ? 'star text-warning' : 'star-o text-muted' }}\"></i>`."
                *   **لـ 'الإجراءات'**: رابط 'تعديل' (`route('admin.cars.edit', \$car)`), ونموذج 'حذف' (`route('admin.cars.destroy', \$car)`). (اختياري: زر 'عرض' لمعاينة في لوحة التحكم أو الموقع العام).
            * "6. الترقيم (`{{ \$cars->appends(request()->query())->links('pagination::bootstrap-5') }}`)."
    * **3. (OPTIONAL) CREATE_HELPER_FUNCTIONS_FOR_CAR_STATUS:**
        * **FILE_NAME_PATH:** `Modules/CarCatalog/Helpers/helpers.php` (أو في `Core` Helpers)
        * **DETAILED_LOGIC:** "أنشئ دالتين:
            *   `function car_status_label(string \$status): string` (ترجع اسم عرض عربي للحالة).
            *   `function car_status_badge_class(string \$status): string` (ترجع كلاس Bootstrap badge color (e.g., 'success', 'warning', 'danger') بناءً على الحالة)."
        * "قم بتحميل هذا الملف عبر `composer.json` الخاص بموديول `CarCatalog`."
* **EXPECTED_OUTPUTS_BEHAVIOR:** قائمة سيارات وظيفية في لوحة التحكم مع إمكانية البحث والفلترة البسيطة والترقيم.
* **ACCEPTANCE_CRITERIA:**
    1. "يتم عرض قائمة السيارات مع الأعمدة المحددة والترقيم."
    2. "يعمل البحث بالاسم/الموديل/VIN بشكل صحيح."
    3. "تعمل فلاتر الماركة والموديل (مبدئيًا للموديل إذا لم يتم تنفيذ AJAX) والحالة بشكل صحيح."
    4. "أزرار الإجراءات (تعديل، حذف) موجودة وتشير للمسارات الصحيحة."
    5. "يتم عرض حالة السيارة وشارتها المميزة بشكل صحيح."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented Car List view (DASH-CAR-LIST-001) in Dash admin panel with basic search, filtering, and pagination (PH-02-DEL-006).'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-006` إلى `Completed`. أضف مهمة TODO لتحسين فلتر الموديلات ليعمل بـ AJAX في قائمة السيارات."

---



### 16. **`PH02-TASK-016-USER-MGMT-EMPLOYEES-CRUD-001`**
* **LEVEL:** `High`
* **OBJECTIVE:** تنفيذ واجهات CRUD كاملة (قائمة، إضافة، تعديل، تغيير الحالة) لإدارة الموظفين (`User` model مع دور 'Employee' أو 'Super Admin') في لوحة تحكم الإدارة Dash. يشمل ذلك وحدات التحكم، FormRequests، طرق عرض Blade، والمسارات ضمن موديول `UserManagement`. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء إدارة الموظفين من `PH-02-DEL-007` من `PPP-FR.md`).
* **TYPE:** `Dash Custom CRUD Implementation (Controller, Routes, Blade Views for Employees)`
* **FILE_NAME_PATH:**
    * `Modules/UserManagement/Http/Controllers/Admin/EmployeeController.php`
    * `Modules/UserManagement/Http/Requests/Admin/StoreEmployeeRequest.php`
    * `Modules/UserManagement/Http/Requests/Admin/UpdateEmployeeRequest.php`
    * `Modules/UserManagement/Resources/views/admin/employees/index.blade.php`
    * `Modules/UserManagement/Resources/views/admin/employees/create.blade.php`
    * `Modules/UserManagement/Resources/views/admin/employees/edit.blade.php`
    * `Modules/UserManagement/Resources/views/admin/employees/_form.blade.php`
    * `Modules/UserManagement/Routes/admin.php` (تحديث)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-007`), `docs/REQ-FR.md` (`MOD-USER-MGMT-FEAT-010`), `docs/TS-FR.md` (`DB-TBL-001` - User), `docs/UIUX-FR.md` (`DASH-EMPLOYEES-LIST-001`).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (`DASH-EMPLOYEES-LIST-001`) لتصميم جداول ونماذج إدارة الموظفين.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` و `docs/REQ-FR.md` لتحديد الحقول المطلوبة لإضافة/تعديل موظف (الاسم، البريد، الجوال (اختياري)، كلمة المرور (توليد/إعادة تعيين)، الأدوار، الحالة). تأكد من أن نموذج `User` يدعم `HasRoles`."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-007` (جزء إدارة الموظفين) إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-006 USER-MANAGEMENT-MODULE-SETUP-001`, `TASK-ID:PH01-TASK-008 SPATIE-PERMISSION-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * نموذج `User` موجود ويدعم `HasRoles`.
    * الأدوار ('Super Admin', 'Employee') موجودة.
    * الصلاحية المطلوبة: `manage_employees_admin`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_EMPLOYEE:**
        * **`StoreEmployeeRequest.php`**: Rules: `first_name` (required), `last_name` (required), `email` (required, email, unique:users,email), `phone_number` (nullable, unique:users,phone_number, regex for saudi), `password` (required, min:8, confirmed), `roles` (required, array, exists:roles,name), `status` (required, in:active,inactive).
        * **`UpdateEmployeeRequest.php`**: Rules: `first_name` (required), `last_name` (required), `email` (required, email, Rule::unique('users')->ignore(\$this->employee->id)), `phone_number` (nullable, Rule::unique('users')->ignore(\$this->employee->id), regex for saudi), `password` (nullable, min:8, confirmed - if provided), `roles` (required, array, exists:roles,name), `status` (required, in:active,inactive).
    * **2. CREATE_EMPLOYEE_CONTROLLER_METHODS (`EmployeeController.php`):**
        * **`index(Request $request): View`**: جلب الموظفين (المستخدمون الذين لديهم دور 'Employee' أو 'Super Admin') مع أدوارهم. دعم الفلترة بالاسم، البريد، الحالة، الدور. الترقيم. عرض `usermanagement::admin.employees.index`.
        * **`create(): View`**: جلب الأدوار المتاحة للموظفين (e.g., `Role::whereIn('name', ['Super Admin', 'Employee'])->pluck('name', 'name')`) وتمريرها إلى `usermanagement::admin.employees.create`.
        * **`store(StoreEmployeeRequest $request): RedirectResponse`**:
            *   "إنشاء `User` جديد بالبيانات المدخلة (مع تشفير كلمة المرور)."
            *   "تعيين الأدوار المختارة للموظف باستخدام `\$employee->syncRoles(\$request->input('roles'));`."
            *   "(اختياري) إرسال بريد ترحيبي مع كلمة مرور مؤقتة أو رابط لتعيين كلمة المرور."
            *   "إعادة توجيه."
        * **`edit(User $employee): View`**: جلب الأدوار المتاحة وتمريرها مع `\$employee` إلى `usermanagement::admin.employees.edit`.
        * **`update(UpdateEmployeeRequest $request, User $employee): RedirectResponse`**: تحديث بيانات الموظف، الأدوار، وكلمة المرور (إذا تم توفيرها). إعادة توجيه.
        * **`destroy(User $employee): RedirectResponse`**: (عادة لا يتم حذف الموظفين، بل يتم تعطيلهم (`status='inactive'`). إذا كان الحذف مطلوبًا، يجب أن يكون Soft Delete مع تحذيرات.) "قم بتغيير حالة الموظف إلى 'inactive' أو قم بحذفه (Soft Delete) إذا كان ذلك مسموحًا."
    * **3. CREATE_EMPLOYEE_BLADE_VIEWS:**
        * **`_form.blade.php`**: حقول: `first_name`, `last_name`, `email`, `phone_number`, `password`, `password_confirmation`, `roles` (multiselect, populated with `\$availableRoles`), `status` (select: active/inactive).
        * **`index.blade.php`**: جدول: #, الاسم الكامل, البريد, الأدوار (عرض أسماء الأدوار), الحالة, تاريخ الإنشاء, الإجراءات (تعديل, تغيير الحالة/حذف).
        * **`create.blade.php`, `edit.blade.php`**.
    * **4. DEFINE_EMPLOYEE_ROUTES (تحديث `admin.php` لـ `UserManagement`):**
        * `Route::resource('employees', \Modules\UserManagement\Http\Controllers\Admin\EmployeeController::class)->middleware('permission:manage_employees_admin');` (افترض أن صلاحية `manage_employees_admin` موجودة ومناسبة).
* **EXPECTED_OUTPUTS_BEHAVIOR:** إدارة كاملة لحسابات الموظفين وأدوارهم.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض، إضافة، تعديل، وتغيير حالة الموظفين."
    2. "يتم تعيين الأدوار للموظفين بشكل صحيح."
    3. "يتم تطبيق التحقق من صحة المدخلات."
    4. "يتم تطبيق الصلاحيات على عمليات CRUD."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented CRUD functionality for Employees (User model with Employee/Super Admin roles) in Dash admin panel, including role assignment.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-007` (جزء إدارة الموظفين) إلى `Completed`."

---

### 17. **`PH02-TASK-017-USER-MGMT-ROLES-PERMISSIONS-UI-001`**
* **LEVEL:** `High`
* **OBJECTIVE:** تنفيذ واجهة المستخدم (Blade Views و Controller Actions) لإدارة الأدوار (`Roles`) والصلاحيات (`Permissions`) في لوحة تحكم الإدارة Dash، مما يسمح لمدير النظام بإنشاء أدوار جديدة وتعيين صلاحيات محددة لها. يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء إدارة الأدوار والصلاحيات من `PH-02-DEL-007` من `PPP-FR.md`).
* **TYPE:** `Dash Custom UI Implementation (Roles & Permissions Management)`
* **FILE_NAME_PATH:**
    * `Modules/UserManagement/Http/Controllers/Admin/RoleController.php`
    * `Modules/UserManagement/Http/Requests/Admin/StoreRoleRequest.php`
    * `Modules/UserManagement/Http/Requests/Admin/UpdateRoleRequest.php`
    * `Modules/UserManagement/Resources/views/admin/roles/index.blade.php`
    * `Modules/UserManagement/Resources/views/admin/roles/create.blade.php`
    * `Modules/UserManagement/Resources/views/admin/roles/edit.blade.php`
    * `Modules/UserManagement/Resources/views/admin/roles/_form.blade.php`
    * `Modules/UserManagement/Routes/admin.php` (تحديث)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-007`), `docs/REQ-FR.md` (`MOD-USER-MGMT-FEAT-010`), `docs/UIUX-FR.md` (`DASH-ROLES-PERMISSIONS-MGMT-001`).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (`DASH-ROLES-PERMISSIONS-MGMT-001`) لتصميم الواجهة.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` لتصميم واجهة إدارة الأدوار، والتي تتضمن عادة قائمة بالأدوار، ونموذجًا لإنشاء/تعديل دور مع قائمة checkbox لجميع الصلاحيات المتاحة في النظام (مجمعة حسب الموديول/الفئة)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-007` (جزء إدارة الأدوار والصلاحيات) إلى `In Progress`."
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-008 SPATIE-PERMISSION-SETUP-001`.
* **LLM_ASSUMPTIONS:**
    * سيتم استخدام `Spatie\Permission\Models\Role` و `Spatie\Permission\Models\Permission`.
    * الصلاحية المطلوبة: `manage_roles_permissions` (يجب إنشاؤها إذا لم تكن موجودة وتعيينها لـ Super Admin).
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_FORM_REQUESTS_FOR_ROLE:**
        * **`StoreRoleRequest.php`**: Rules: `name` (required, string, max:50, unique:roles,name), `permissions` (nullable, array), `permissions.*` (exists:permissions,name).
        * **`UpdateRoleRequest.php`**: Rules: `name` (required, string, max:50, Rule::unique('roles')->ignore(\$this->role->id)), `permissions` (nullable, array), `permissions.*` (exists:permissions,name).
    * **2. CREATE_ROLE_CONTROLLER_METHODS (`RoleController.php`):**
        * **`index(Request $request): View`**: جلب جميع الأدوار مع عدد المستخدمين/الصلاحيات. عرض `usermanagement::admin.roles.index`.
        * **`create(): View`**: جلب جميع الصلاحيات المتاحة (`Permission::all()->groupBy(function(\$item) { return explode('.', \$item->name)[0] ?? 'general'; })` - للتجميع حسب اسم الموديول أو الجزء الأول من اسم الصلاحية). تمريرها إلى `usermanagement::admin.roles.create`.
        * **`store(StoreRoleRequest $request): RedirectResponse`**: إنشاء `Role`. تعيين الصلاحيات المختارة باستخدام `\$role->syncPermissions(\$request->input('permissions', []));`. إعادة توجيه.
        * **`edit(Role $role): View`**: جلب الدور، صلاحياته الحالية، وجميع الصلاحيات المتاحة (مجمعة). تمريرها إلى `usermanagement::admin.roles.edit`.
        * **`update(UpdateRoleRequest $request, Role $role): RedirectResponse`**: تحديث اسم الدور. تحديث الصلاحيات باستخدام `syncPermissions`. إعادة توجيه.
        * **`destroy(Role $role): RedirectResponse`**: تحقق من عدم وجود مستخدمين مرتبطين بالدور (أو قم بإلغاء تعيين الدور منهم). لا تسمح بحذف الأدوار الأساسية (Super Admin, Employee, Customer). حذف الدور. إعادة توجيه.
    * **3. CREATE_ROLE_BLADE_VIEWS:**
        * **`_form.blade.php`**: حقل `name` (text). قسم لعرض الصلاحيات كـ checkboxes مجمعة (e.g., لكل مجموعة صلاحيات، عنوان ثم checkboxes للصلاحيات ضمنها). عند التعديل، يجب تحديد الـ checkboxes للصلاحيات المعينة حاليًا للدور.
        * **`index.blade.php`**: جدول: #, اسم الدور, عدد المستخدمين, الإجراءات (تعديل, حذف - مع تحذيرات).
        * **`create.blade.php`, `edit.blade.php`**.
    * **4. DEFINE_ROLE_ROUTES (تحديث `admin.php` لـ `UserManagement`):**
        * `Route::resource('roles', \Modules\UserManagement\Http\Controllers\Admin\RoleController::class)->middleware('permission:manage_roles_permissions');`.
* **EXPECTED_OUTPUTS_BEHAVIOR:** واجهة كاملة لإدارة الأدوار وتعيين الصلاحيات لها.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض، إضافة، تعديل، وحذف الأدوار (مع قيود على الأدوار الأساسية)."
    2. "يمكن تعيين وإلغاء تعيين الصلاحيات للأدوار."
    3. "يتم عرض الصلاحيات بشكل مجمع وواضح في نموذج التعديل."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented UI for Roles and Permissions management in Dash admin panel.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-007` (جزء إدارة الأدوار والصلاحيات) إلى `Completed`."
    * **DECISIONS.md:** "Augment: وثق قرار عدم السماح بحذف الأدوار الأساسية."

---

### 18. **`PH02-TASK-018-DASH-SYSTEM-SETTINGS-UI-BASIC-001`**
* **LEVEL:** `High`
* **OBJECTIVE:** تنفيذ واجهة مستخدم أساسية لإعدادات النظام (`DASH-SYSTEM-SETTINGS-001` - الجزء الخاص بالإعدادات العامة و SEO) في لوحة تحكم الإدارة Dash. يجب أن تسمح هذه الواجهة للمسؤول بتعديل الإعدادات المخزنة في جدول `settings` (من `MOD-CORE-FEAT-003`). يخدم هذا [لوحة التحكم الاحترافية Dash]. (يغطي جزء من `PH-02-DEL-009` من `PPP-FR.md`).
* **TYPE:** `Dash Custom UI Implementation (System Settings Form)`
* **FILE_NAME_PATH:**
    * `Modules/Dashboard/Http/Controllers/Admin/SystemSettingsController.php` (أو يمكن دمجه في `DashboardController` إذا كان بسيطًا)
    * `Modules/Dashboard/Resources/views/admin/settings/system.blade.php`
    * `Modules/Dashboard/Routes/admin.php` (تحديث)
* **PRIMARY_INPUTS:**
    * **TASK_REF:** `docs/PPP-FR.md` (`PH-02-DEL-009`), `docs/REQ-FR.md` (`MOD-CORE-FEAT-003` و `FEAT-ADMIN-003`), `docs/UIUX-FR.md` (`DASH-SYSTEM-SETTINGS-001` - تبويب الإعدادات العامة و SEO).
    * **DESIGN_REF:** `docs/UIUX-FR.md` (`DASH-SYSTEM-SETTINGS-001`) لتصميم الواجهة وتبويباتها.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` لتحديد الحقول المطلوبة في تبويبي 'الإعدادات العامة' و 'إعدادات SEO'. استخدم جدول `settings` من `TS-FR.md` (`DB-TBL-CORE-001`) لتحديد مفاتيح الإعدادات المقابلة."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-009` (جزء إعدادات النظام) إلى `In Progress`."
* **DEPENDENCIES:** `TASK-ID:PH01-TASK-012 CORE-MODULE-SETTINGS-DB-SERVICE-001`.
* **LLM_ASSUMPTIONS:**
    * `SettingsService` ودالة `setting()` متاحة.
    * الصلاحية المطلوبة: `manage_system_settings`.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_SYSTEM_SETTINGS_CONTROLLER (أو تحديث `DashboardController`):**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Http/Controllers/Admin/SystemSettingsController.php`
        * **`index(): View`**:
            *   "جلب جميع الإعدادات التي تنتمي لمجموعة `general` و `seo` باستخدام `Setting::whereIn('group_name', ['general', 'seo'])->get()->keyBy('key');`."
            *   "تمرير هذه الإعدادات (كمصفوفة `key => value_object`) إلى `dashboard::admin.settings.system` view."
        * **`update(Request $request): RedirectResponse`**:
            *   "التحقق من صحة المدخلات (تعتمد على أنواع الإعدادات)."
            *   "التكرار على `\$request->except('_token', '_method')` وحفظ كل إعداد باستخدام `SettingsService->set(\$key, \$value, \$group)` (أو مباشرة `Setting::updateOrCreate(['key' => \$key], ['value' => \$value, 'group_name' => \$group_based_on_key_prefix_or_hidden_field])`)."
            *   "مسح cache الإعدادات: `Cache::forget('setting.some_key')` لكل مفتاح تم تعديله، أو طريقة لمسح جميع إعدادات cache."
            *   "إعادة توجيه مع رسالة نجاح."
    * **2. CREATE_SYSTEM_SETTINGS_BLADE_VIEW (`system.blade.php`):**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/admin/settings/system.blade.php`
        * **DETAILED_LOGIC:**
            * "اجعله يمتد من `dashboard::layouts.admin_layout`."
            * "استخدم نظام تبويبات Dash (أو Bootstrap Tabs) لـ 'الإعدادات العامة' و 'إعدادات SEO'."
            * "ضمن كل تبويب، قم بإنشاء نموذج (`<form method="POST" action="{{ route('admin.settings.system.update') }}" enctype="multipart/form-data"> @csrf @method('PUT')`)."
            * "لكل إعداد ضمن المجموعة (عامة أو SEO)، قم بعرض حقل مناسب (text, textarea, file - للشعار/الأيقونة) مع `label` من `\$setting->display_name` وقيمة `\$setting->value` الحالية."
            *   "**مثال لتبويب الإعدادات العامة:** حقول لـ: `site_name`, `admin_email`, `main_phone`, `address`, `site_logo_id` (file input), `favicon_id` (file input), `default_currency`, `vat_percentage`, `default_booking_fee`, `after_sales_service_text`."
            *   "**مثال لتبويب إعدادات SEO:** حقول لـ: `seo_default_keywords`, `seo_default_description`, `ga_tracking_id`."
            * "أضف زر 'حفظ الإعدادات' لكل تبويب أو زر حفظ عام."
    * **3. DEFINE_SYSTEM_SETTINGS_ROUTES (تحديث `admin.php` لـ `Dashboard`):**
        * `Route::get('settings/system', [SystemSettingsController::class, 'index'])->name('settings.system.index')->middleware('permission:manage_system_settings');`
        * `Route::put('settings/system', [SystemSettingsController::class, 'update'])->name('settings.system.update')->middleware('permission:manage_system_settings');`
* **EXPECTED_OUTPUTS_BEHAVIOR:** واجهة لإدارة الإعدادات العامة و SEO.
* **ACCEPTANCE_CRITERIA:**
    1. "يمكن عرض وتعديل الإعدادات العامة (اسم الموقع، شعار، إلخ)."
    2. "يمكن عرض وتعديل إعدادات SEO الأساسية."
    3. "يتم حفظ التغييرات بشكل صحيح ومسح الـ cache."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented UI for basic System Settings (General & SEO tabs) in Dash admin panel.'."
    * **TODO.md:** "Augment: حدّث حالة `PH-02-DEL-009` (جزء إعدادات النظام) إلى `Completed`."

---

### 19. **`PH02-TASK-019-DASH-ADMIN-HOME-UI-IMPLEMENTATION-001`**
* **LEVEL:** `Very High`
* **OBJECTIVE:** تنفيذ واجهة لوحة البيانات الرئيسية للإدارة (`DASH-ADMIN-HOME-001`) في `Modules/Dashboard/Resources/views/admin/home.blade.php`، بما في ذلك بناء جميع البطاقات الإحصائية والرسوم البيانية المحددة في `UIUX-FR.md` (القسم 4.1.2) و `REQ-FR.md` (`MOD-DASHBOARD-FEAT-002`). يجب أن تكون الرسوم البيانية مهيأة مبدئيًا ببيانات وهمية (placeholder data) إذا لم تكن البيانات الديناميكية جاهزة بعد، مع ترك تعليقات واضحة حول كيفية ربطها بالبيانات الحقيقية لاحقًا. (يغطي `PH-02-DEL-001` و جزء من `PH-02-DEL-010` من `PPP-FR.md`).
* **TYPE:** `Dash Custom Blade View Implementation (Dashboard Home UI)`
* **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/admin/home.blade.php`
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `docs/PPP-FR.md` (`PH-02-DEL-001`, `PH-02-DEL-010`)
        * `docs/UIUX-FR.md` (القسم 4.1.2 `DASH-ADMIN-HOME-001` لتصميم ومحتوى لوحة البيانات)
        * `docs/REQ-FR.md` (`MOD-DASHBOARD-FEAT-002` لمصادر بيانات الرسوم البيانية)
        * `Dash/index.html` (كمرجع هيكلي للبطاقات والرسوم البيانية)
        * `Dash/script.js` (كمرجع لكيفية تهيئة الرسوم البيانية ومكتباتها)
    * **DESIGN_REF:** `docs/UIUX-FR.md` (4.1.2) هو المرجع الأساسي لتحديد البطاقات، الرسوم البيانية، وترتيبها. `Dash/index.html` و `Dash/script.js` للمكونات الهيكلية والتهيئات الأولية.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/UIUX-FR.md` (4.1.2) و `Dash/index.html` بدقة لتحديد جميع البطاقات الإحصائية والرسوم البيانية المطلوبة وترتيبها. انتبه بشكل خاص إلى أنواع الرسوم البيانية (خطي، دائري، شريطي) والمقاييس والفترات الزمنية المقترحة لكل منها في `docs/REQ-FR.md` (`MOD-DASHBOARD-FEAT-002`). حدد ما إذا كان `Dash/script.js` يحتوي بالفعل على تهيئة لجميع هذه الرسوم البيانية أو إذا كنت ستحتاج إلى تهيئتها يدويًا باستخدام Chart.js في Blade view أو ملف JS مخصص (`dash_app.js`)."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-001` إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:**
    * `TASK-ID:PH01-TASK-013 DASH-ADMIN-INITIAL-DASHBOARD-VIEW-001` (لوجود `home.blade.php` الأولي).
    * `TASK-ID:PH01-TASK-009 DASH-LAYOUT-SETUP-001` (للتأكد من ربط مكتبات Chart.js وأصول Dash).
* **LLM_ASSUMPTIONS:**
    * سيتم استخدام مكتبة Chart.js (المُشار إليها في `Dash/script.js` و `dashboard.html`).
    * سيتم استخدام هيكل HTML للبطاقات والرسوم البيانية مشابه لما هو موجود في `Dash/index.html`.
    * البيانات الديناميكية للرسوم البيانية سيتم تمريرها من `DashboardController` في المهمة `PH02-TASK-020-DASH-ADMIN-HOME-DATA-INTEGRATION-002`. هذه المهمة تركز على بناء الواجهة وتهيئتها ببيانات وهمية إذا لزم الأمر.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. MODIFY_ADMIN_HOME_BLADE_FILE:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/admin/home.blade.php`
        * **DETAILED_LOGIC:**
            * "1. افتح ملف `home.blade.php` (الذي يحتوي حاليًا على محتوى placeholder)."
            * "2. قم بإزالة المحتوى المؤقت، واحتفظ بـ `@extends('dashboard::layouts.admin_layout')` و `@section('title', 'لوحة التحكم الرئيسية')` و `@section('content')`."
            * "3. **بناء البطاقات الإحصائية (Stat Cards):**"
                * "قم بإنشاء صفين من البطاقات الإحصائية كما هو موصوف في `UIUX-FR.md` (4.1.2) وباستخدام هيكل HTML مشابه لـ `div.dashboard-card.stat-card` من `Dash/index.html`. يجب أن يكون هناك 8 بطاقات إجمالاً."
                * "لكل بطاقة: الأيقونة المناسبة (Font Awesome من `Dash/dashboard.html` أو `UIUX-FR.md`)، العنوان (بالعربية)، رقم placeholder (e.g., '0' أو رقم ثابت من `dashboard.html` مثل 28، 42، 142، 865 للصف الأول، و 156,500 ر.س، إلخ للصف الثاني)، والنص الصغير (e.g., 'من الأسبوع السابق'). استخدم classes مثل `bg-success-subtle text-success` للأيقونات كما في `dashboard.html`."
            * "4. **بناء الرسوم البيانية (Charts):**"
                * "لكل رسم بياني مطلوب في `UIUX-FR.md` (4.1.2) و `REQ-FR.md` (`MOD-DASHBOARD-FEAT-002`):"
                    *   "أنشئ `div.dashboard-card.p-3` لاحتواء الرسم البياني."
                    *   "أضف `<div class=\"d-flex justify-content-between align-items-center mb-3\"> <h5 class=\"card-title fw-bold mb-0\">[عنوان الرسم البياني بالعربية]</h5> <div class=\"dropdown\"> <button class=\"btn btn-sm btn-outline-secondary dropdown-toggle\" ...>الفترة: [الفترة الافتراضية]</button> <ul class=\"dropdown-menu\">...</ul></div></div>`."
                    *   "أضف `<div class=\"chart-container\"><canvas id=\"[chartId]\"></canvas></div>` بالمعرف الفريد لكل رسم بياني (e.g., `salesChart`, `brandsPieChart`, `topCarsChart`, `categoriesChart`, `financingStatusChart`, `newCustomersChart`, `paymentMethodsChart`)."
                * "5. **تهيئة الرسوم البيانية (JavaScript):**"
                    *   "ضمن قسم `@push('scripts')` في نهاية ملف Blade:"
                    *   "قم بتضمين أو استدعاء الدوال من `Dash/script.js` (الموجود في `public/vendor/dash/script.js`) لتهيئة الرسوم البيانية. إذا كانت الدوال في `Dash/script.js` عامة بما يكفي وتعتمد على وجود `canvas` IDs، يجب أن تعمل مع البيانات الوهمية الموجودة فيها. إذا كانت تتطلب تعديلًا لتناسب الهيكل الحالي أو إذا كانت بعض الرسوم البيانية جديدة، قم بتهيئة تلك الرسوم البيانية باستخدام Chart.js مباشرة."
                    *   "مثال: إذا كان `Dash/script.js` يحتوي على `initSalesChart()`، تأكد من أن `canvas#salesChart` موجود."
                    *   "للرسوم البيانية الجديدة التي لا يغطيها `Dash/script.js` (مثل `newCustomersChart`, `financingStatusChart`, `paymentMethodsChart` إذا كانت جديدة)، قم بتهيئتها ببيانات وهمية كما في المثال السابق (مهمة 19، الخطوة 5 من المسودة السابقة)."
                    *   "أضف تعليقات JavaScript واضحة تشير إلى أن هذه تهيئات أولية، وأن البيانات الديناميكية ستُربط في `PH02-TASK-020-DASH-ADMIN-HOME-DATA-INTEGRATION-002`."
            * "6. **بناء الأقسام الإضافية (قائمة السيارات، آخر النشاطات، التنبيهات، أحدث السيارات المضافة، مؤشرات الأداء):**"
                * "قم ببناء هيكل HTML لهذه الأقسام كما هو موصوف في `UIUX-FR.md` (4.1.2) و `Dash/dashboard.html` (مثل `div.dashboard-card.p-3`، `table.table-hover.recent-activity-table`، إلخ.)."
                * "استخدم بيانات نصية ثابتة أو placeholders من `Dash/dashboard.html` لهذه الأقسام مبدئيًا."
* **REFERENCE_CODE_SNIPPETS:** (لا يوجد مقتطفات جديدة هنا، بل التركيز على استخدام الهياكل من `Dash/index.html` و `Dash/script.js`).
* **EXPECTED_OUTPUTS_BEHAVIOR:** صفحة لوحة البيانات الرئيسية تعرض الهيكل الكامل للبطاقات الإحصائية وجميع الرسوم البيانية المطلوبة (مع البيانات الوهمية الأولية من `Dash/script.js` أو تهيئات JS مباشرة)، بالإضافة إلى الأقسام الأخرى، مع الحفاظ على تصميم وأسلوب Dash.
* **SECURITY_CONSIDERATIONS:** لا يوجد في هذه المرحلة.
* **PERFORMANCE_CONSIDERATIONS:** التأكد من عدم تحميل مكتبات JS غير ضرورية.
* **REQUIRED_CODE_COMMENTS:** تعليقات في JavaScript لتوضيح أن البيانات سيتم جعلها ديناميكية لاحقًا.
* **ACCEPTANCE_CRITERIA:**
    1. "يتم عرض جميع البطاقات الإحصائية الثمانية المطلوبة في `home.blade.php` مع أيقونات وعناوين وأرقام placeholder كما في `Dash/dashboard.html`."
    2. "يتم إنشاء عناصر `<canvas>` لجميع الرسوم البيانية السبعة المطلوبة بمعرفات فريدة كما في `Dash/dashboard.html`."
    3. "يتم تهيئة كل رسم بياني بنجاح (من خلال `Dash/script.js` أو تهيئة JS مباشرة) ويعرض بيانات وهمية بشكل صحيح."
    4. "يتم تضمين العناوين وأزرار الفترات الزمنية للرسوم البيانية كما هو مطلوب."
    5. "يتم بناء الهيكل الأساسي لأقسام 'قائمة السيارات'، 'آخر النشاطات'، 'التنبيهات المهمة'، 'أحدث السيارات المضافة'، و 'مؤشرات الأداء' ببيانات placeholder كما في `Dash/dashboard.html`."
    6. "الصفحة متجاوبة وتحافظ على نمط Dash العام."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Implemented UI structure for Dash admin homepage (DASH-ADMIN-HOME-001) including all stat cards and chart canvases with placeholder data/JS initialization from Dash assets (PH-02-DEL-001).' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-001` إلى `Completed` في `docs/TODO.md`. أنشئ مهمة `PH02-TASK-020-DASH-ADMIN-HOME-DATA-INTEGRATION-002` لربط هذه الواجهة ببيانات ديناميكية من Controller."

---

### 20. **`PH02-TASK-020-DASH-ADMIN-HOME-DATA-INTEGRATION-002`**
* **LEVEL:** `Very High`
* **OBJECTIVE:** تعديل `Modules/Dashboard/Http/Controllers/Admin/DashboardController.php` ليقوم بجلب البيانات الديناميكية اللازمة لجميع البطاقات الإحصائية والرسوم البيانية والأقسام الأخرى في لوحة البيانات الرئيسية للإدارة، وتمرير هذه البيانات إلى `dashboard::admin.home` view. تعديل `home.blade.php` لاستقبال هذه البيانات وتمريرها إلى تهيئات JavaScript للرسوم البيانية، واستبدال البيانات الوهمية. (يغطي `PH-02-DEL-010` وأجزاء من `MOD-DASHBOARD-FEAT-002`).
* **TYPE:** `Laravel Controller Logic, Dash Blade View Data Binding, JavaScript Data Integration`
* **FILE_NAME_PATH:**
    * `Modules/Dashboard/Http/Controllers/Admin/DashboardController.php`
    * `Modules/Dashboard/Resources/views/admin/home.blade.php`
    * (قد يتطلب إنشاء Services في موديولات `CarCatalog`, `OrderManagement`, `UserManagement` لتجميع البيانات الإحصائية)
* **PRIMARY_INPUTS:**
    * **TASK_REF:**
        * `docs/PPP-FR.md` (`PH-02-DEL-010`)
        * `docs/REQ-FR.md` (`MOD-DASHBOARD-FEAT-002` - يحدد مصادر البيانات والمقاييس للرسوم البيانية والبطاقات)
        * `TASK-ID:PH02-TASK-019-DASH-ADMIN-HOME-UI-IMPLEMENTATION-001` (الواجهة جاهزة لاستقبال البيانات)
    * **DESIGN_REF:** `docs/UIUX-FR.md` (4.1.2) لمعرفة ما هي البيانات المطلوبة لكل عنصر.
    * **LOG_REVIEW_INSTRUCTION:** "Augment: راجع `docs/REQ-FR.md` (`MOD-DASHBOARD-FEAT-002`) بدقة لتحديد جميع مصادر البيانات والمقاييس والفترات الزمنية المطلوبة لكل بطاقة إحصائية وكل رسم بياني. هذه هي المهمة الأساسية لجعل لوحة البيانات حية. تأكد من أن Controller سيقوم بتجميع هذه البيانات بشكل فعال. انتبه لكيفية تحويل البيانات لتناسب هيكل `datasets` و `labels` الذي يتوقعه Chart.js."
* **LOG_UPDATE_GUIDANCE_PRE_TASK:**
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-010` (جعل لوحة البيانات ديناميكية) إلى `In Progress` في `docs/TODO.md`."
* **DEPENDENCIES:**
    * `PH02-TASK-019-DASH-ADMIN-HOME-UI-IMPLEMENTATION-001`
    * `PH02-TASK-001-CARCATALOG-MODULE-DB-SETUP-001`
    * (نماذج وبيانات من `OrderManagement` و `UserManagement` - يفترض أن تكون هذه الموديولات قد تم إنشاؤها بمستوى أساسي على الأقل، مع وجود بعض البيانات الوهمية لاختبار العرض)
* **LLM_ASSUMPTIONS:**
    * سيتم تجميع البيانات المعقدة (مثل إحصائيات المبيعات الشهرية) باستخدام استعلامات Eloquent في Controller أو Services مخصصة.
    * سيتم تمرير البيانات إلى Blade view كمتغيرات PHP، ثم يتم تحويلها إلى JSON ليتم استهلاكها بواسطة JavaScript.
    * دوال تهيئة الرسوم البيانية في `Dash/script.js` (أو التهيئة المباشرة في Blade) يمكنها قبول بيانات ديناميكية أو يمكن تحديثها.
* **DETAILED_IMPLEMENTATION_STEPS:**
    * **1. CREATE_DATA_AGGREGATION_LOGIC (في Controller أو Services):**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Http/Controllers/Admin/DashboardController.php` (و Services جديدة إذا لزم الأمر، مثل `Modules\Dashboard\Services\DashboardDataService.php`)
        * **DETAILED_LOGIC:**
            * "1. في `DashboardController@home` method (أو استدع Service method):"
            * "2. **للبطاقات الإحصائية (Stat Cards):**"
                *   "اكتب استعلامات Eloquent لجلب الأرقام المطلوبة كما هو محدد في `UIUX-FR.md` و `REQ-FR.md` (مثال: `\$newOrdersTodayCount = Order::whereDate('created_at', today())->count();`, `\$pendingFinanceRequestsCount = Order::where('order_type', 'finance_application')->where('status', 'pending_admin_review')->count();`, إلخ). قم بتمرير هذه المتغيرات إلى الـ view."
            * "3. **للرسوم البيانية (Charts):**"
                *   "لكل رسم بياني (Sales, Brands Pie, Top Cars, Categories, Financing Status, New Customers, Payment Methods)، قم بإنشاء دالة (خاصة في Controller أو في Service) تقوم بتجميع البيانات بالتنسيق الذي يتوقعه Chart.js (عادة مصفوفة `labels` ومصفوفة `datasets` تحتوي على `label`, `data`, `backgroundColor`, `borderColor` إلخ)."
                *   "**مثال لـ Sales Chart Data (بيانات شهرية لآخر 6 أشهر):**"
                    *   "استخدم `DB::table('orders')->select(DB::raw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(total_price) as total_sales, COUNT(*) as total_orders'))->where('status', 'completed')->where('created_at', '>=', now()->subMonths(6)->startOfMonth())->groupBy('year', 'month')->orderBy('year')->orderBy('month')->get();`"
                    *   "قم بمعالجة النتائج لتشكيل مصفوفات `labels` (e.g., 'يناير 2024') و `datasets` (واحدة للمبيعات وأخرى لعدد الطلبات)."
                *   "قم بتطبيق منطق مشابه للرسوم البيانية الأخرى، مع مراعاة المقاييس والفترات الزمنية المحددة في `REQ-FR.md`."
                *   "مرر بيانات كل رسم بياني إلى الـ view (e.g., `compact('salesChartData', 'brandsPieData', ...)`)."
            * "4. **للأقسام الأخرى (آخر النشاطات، أحدث السيارات، إلخ.):**"
                *   "اكتب استعلامات لجلب أحدث 5-10 سجلات (e.g., `Order::latest()->take(5)->get()`, `Car::latest()->take(3)->get()`)."
                *   "مرر هذه البيانات إلى الـ view."
    * **2. MODIFY_ADMIN_HOME_BLADE_TO_USE_DYNAMIC_DATA:**
        * **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/admin/home.blade.php`
        * **DETAILED_LOGIC:**
            * "1. **للبطاقات الإحصائية:** استبدل الأرقام الثابتة بالمتغيرات التي تم تمريرها من Controller (e.g., `<h3 class=\"fw-bold mb-0\">{{ \$newOrdersTodayCount ?? 0 }}</h3>`)."
            * "2. **للرسوم البيانية (JavaScript):**"
                *   "ضمن قسم `@push('scripts')`، قبل تهيئة كل رسم بياني:"
                    *   "قم بتمرير البيانات من متغيرات PHP إلى متغيرات JavaScript باستخدام `@json()` directive:
                        ```javascript
                        const salesChartData = @json($salesChartData ?? ['labels' => [], 'datasets' => []]);
                        const brandsPieData = @json($brandsPieData ?? ['labels' => [], 'datasets' => [['data' => []]]]);
                        // ... and so on for all 7 charts
                        ```"
                    *   "إذا كان `Dash/script.js` الأصلي يقوم بتهيئة الرسوم البيانية، ستحتاج إلى إيجاد طريقة لتحديث كائنات Chart.js الموجودة ببياناتك الجديدة، أو تعطيل التهيئة الأصلية وإعادة تهيئتها بالكامل هنا."
                    *   "**الطريقة المفضلة (إذا كان `Dash/script.js` مهيأ بشكل جيد):** ابحث عن دوال التهيئة في `Dash/script.js`. إذا كانت تقبل بيانات كمعامل، قم بتعديل استدعائها هنا. إذا كانت تهيئ الرسوم مباشرة، فقد تحتاج إلى إزالة ذلك الجزء من `Dash/script.js` (أو التعليق عليه) وإعادة كتابة تهيئة Chart.js بالكامل هنا باستخدام بياناتك الديناميكية وخيارات التنسيق من `Dash/script.js`."
                    *   "**مثال لإعادة تهيئة Sales Chart (إذا تم تعطيل تهيئته من `Dash/script.js`):**
                        ```javascript
                        const salesChartElement = document.getElementById('salesChart');
                        if (salesChartElement) {
                            const salesCtx = salesChartElement.getContext('2d');
                            // ... (salesGradient definition if used, taken from Dash/script.js)
                            new Chart(salesCtx, {
                                type: 'line',
                                data: {
                                    labels: salesChartData.labels,
                                    datasets: salesChartData.datasets.map(dataset => ({
                                        ...dataset,
                                        // Ensure colors are applied if not part of salesChartData.datasets
                                        // borderColor: dataset.label === 'المبيعات' ? colors.secondary : colors.accent, 
                                        // backgroundColor: dataset.label === 'المبيعات' ? salesGradient : 'transparent',
                                        // ... other dataset specific options from Dash/script.js
                                    }))
                                },
                                options: { // Copy relevant options from Dash/script.js for salesChart
                                    responsive: true, maintainAspectRatio: false,
                                    // ... other scales, plugins, tooltips, legend options
                                }
                            });
                        }
                        // Repeat for brandsPieChart, topCarsChart, etc.
                        ```"
                    *   "تأكد من استخدام نفس معرفات `<canvas>` والألوان وخيارات التنسيق الأساسية الموجودة في `Dash/script.js` للحفاظ على المظهر."
            * "3. **للأقسام الأخرى (آخر النشاطات، أحدث السيارات، إلخ.):**"
                *   "استخدم `@foreach` لعرض البيانات الديناميكية في الجداول والبطاقات. مثال لآخر النشاطات:
                    ```html+php
                    @forelse($recentActivities as $activity)
                    <tr>
                        <td>{{ $activity->order_number ?? $activity->id }}</td>
                        <td>{{ $activity->description ?? 'نشاط غير محدد' }}</td>
                        <td>{{ $activity->user->full_name ?? 'N/A' }}</td>
                        <td>{{ format_datetime_for_display($activity->created_at) }}</td>
                        <td><span class="status-badge status-{{ $activity->status_class ?? 'default' }}">{{ $activity->status_label ?? $activity->status }}</span></td>
                        <td>{{-- Actions --}}</td>
                    </tr>
                    @empty
                    <tr><td colspan="6" class="text-center">لا توجد نشاطات حديثة.</td></tr>
                    @endforelse
                    ```"
* **REFERENCE_CODE_SNIPPETS:** (مقتطفات JavaScript و PHP لتمرير البيانات وتهيئة الرسوم).
* **EXPECTED_OUTPUTS_BEHAVIOR:** لوحة البيانات الرئيسية تعرض الآن بيانات حقيقية وديناميكية في جميع البطاقات الإحصائية والرسوم البيانية والأقسام الأخرى.
* **SECURITY_CONSIDERATIONS:** التأكد من أن الاستعلامات آمنة ولا تعرض بيانات حساسة غير ضرورية.
* **PERFORMANCE_CONSIDERATIONS:** تحسين استعلامات تجميع البيانات. استخدام التخزين المؤقت (Caching) للبيانات التي لا تتغير بشكل متكرر (e.g., إحصائيات الشهر الماضي).
* **REQUIRED_CODE_COMMENTS:** تعليقات في Controller و JavaScript تشرح منطق تجميع وتمرير البيانات، وأي تعديلات على تهيئة الرسوم البيانية الأصلية.
* **ACCEPTANCE_CRITERIA:**
    1. "`DashboardController@home` يقوم بجلب وتمرير البيانات الديناميكية لجميع عناصر لوحة البيانات كما هو محدد في `REQ-FR.md` (`MOD-DASHBOARD-FEAT-002`)."
    2. "البطاقات الإحصائية في `home.blade.php` تعرض أرقامًا ديناميكية صحيحة."
    3. "جميع الرسوم البيانية السبعة يتم تهيئتها وتعرض بيانات ديناميكية صحيحة بناءً على الفترة الزمنية الافتراضية."
    4. "أقسام 'آخر النشاطات'، 'أحدث السيارات المضافة' وغيرها تعرض بيانات ديناميكية."
    5. "لا توجد أخطاء JavaScript عند عرض الصفحة بالبيانات الديناميكية."
    6. "خيارات الفترات الزمنية للرسوم البيانية (إذا كانت ستعمل بإعادة تحميل الصفحة مبدئيًا) يجب أن تمرر الفترة الصحيحة للـ Controller ليقوم بتصفية البيانات."
* **LOG_UPDATE_GUIDANCE_POST_TASK:**
    * **CHANGELOG.md:** "Augment: وثق 'Integrated dynamic data from Controller into Dash admin homepage (stat cards, all 7 charts, and other sections). Placeholder data replaced. (PH-02-DEL-010 & MOD-DASHBOARD-FEAT-002).' في `docs/CHANGELOG.md`."
    * **TODO.md:** "Augment: حدّث حالة المهمة `PH-02-DEL-010` إلى `Completed` في `docs/TODO.md`. إذا لم يتم تنفيذ فلترة الرسوم البيانية بالـ AJAX، أضف مهمة TODO لذلك كميزة متقدمة أو لمرحلة لاحقة."

---

# **الخطوات التصحيحية/التأكيدية المطلوبة الآن (ضمن `PH-02`):**

1.  ### **`PH02-TASK-029-DASH-SIDEBAR-CAR-CATALOG-LINKS-VERIFICATION-001`**
    *   **LEVEL:** `Medium`
    *   **OBJECTIVE:** التحقق من وتحديث جميع الروابط داخل القائمة الفرعية "إدارة السيارات" في `_sidebar.blade.php` لتشير إلى المسارات (routes) الصحيحة التي تم إنشاؤها في المهام السابقة (`PH02-TASK-005` إلى `PH02-TASK-015`).
    *   **TYPE:** `Dash Blade View Link Update`
    *   **FILE_NAME_PATH:** `Modules/Dashboard/Resources/views/layouts/partials/_sidebar.blade.php`
    *   **PRIMARY_INPUTS:**
        *   **TASK_REF:** ناتج المهام من `PH02-TASK-005` إلى `PH02-TASK-015` (لأسماء المسارات الصحيحة).
        *   **DESIGN_REF:** `docs/UIUX-FR.md` (للتأكد من أسماء عناصر القائمة).
        *   **LOG_REVIEW_INSTRUCTION:** "Augment: راجع ملف `_sidebar.blade.php`. ابحث عن جميع الروابط (`href`) داخل القائمة الفرعية `cars-submenu` التي لا تزال `#`. قم بتحديثها لتستخدم `route('admin.routeName')` الصحيح لكل صفحة (مثل `admin.brands.index`, `admin.cars.index`, `admin.cars.create`, إلخ)."
    *   **LOG_UPDATE_GUIDANCE_PRE_TASK:**
        *   **TODO.md:** "Augment: أضف المهمة `PH02-TASK-029` إلى `docs/TODO.md` كجزء من `PH-02` وحددها كـ `In Progress`."
    *   **DEPENDENCIES:** اكتمال مهام CRUD للبيانات الوصفية والسيارات (خاصة تعريف المسارات).
    *   **LLM_ASSUMPTIONS:** المسارات المسماة (named routes) تم تعريفها بشكل صحيح في ملفات `admin.php` للموديولات المعنية.
    *   **DETAILED_IMPLEMENTATION_STEPS:**
        *   **1. UPDATE_SIDEBAR_LINKS_FOR_CAR_CATALOG:**
            *   **DETAILED_LOGIC:**
                *   "1. افتح `Modules/Dashboard/Resources/views/layouts/partials/_sidebar.blade.php`."
                *   "2. داخل `div#cars-submenu > ul.submenu`، قم بتحديث `href` لكل `a.nav-link`:"
                    *   "لـ 'عرض جميع السيارات': استبدل `#` بـ `{{ route('admin.cars.index') }}`."
                    *   "لـ 'إضافة سيارة جديدة': استبدل `#` بـ `{{ route('admin.cars.create') }}`."
                    *   "لـ 'الماركات': تأكد من أنها `{{ route('admin.brands.index') }}`."
                    *   "لـ 'الموديلات': استبدل `#` بـ `{{ route('admin.models.index') }}`."
                    *   "لـ 'الألوان': استبدل `#` بـ `{{ route('admin.colors.index') }}`."
                    *   "لـ 'سنوات الصنع': استبدل `#` بـ `{{ route('admin.years.index') }}`."
                    *   "لـ 'أنواع ناقل الحركة': استبدل `#` بـ `{{ route('admin.transmission-types.index') }}`."
                    *   "لـ 'أنواع الوقود': استبدل `#` بـ `{{ route('admin.fuel-types.index') }}`."
                    *   "لـ 'أنواع هياكل السيارات': استبدل `#` بـ `{{ route('admin.body-types.index') }}`."
                    *   "لـ 'فئات الميزات': استبدل `#` بـ `{{ route('admin.feature-categories.index') }}`."
                    *   "لـ 'ميزات السيارات': استبدل `#` بـ `{{ route('admin.car-features.index') }}`."
                *   "3. تأكد من تحديث شروط `request()->routeIs()` لتحديد الكلاس `active` بشكل صحيح لكل رابط."
    *   **EXPECTED_OUTPUTS_BEHAVIOR:** جميع الروابط في القائمة الفرعية "إدارة السيارات" تعمل وتوجه المستخدم إلى الصفحات الصحيحة.
    *   **ACCEPTANCE_CRITERIA:**
        1.  "النقر على 'عرض جميع السيارات' يفتح صفحة قائمة السيارات."
        2.  "النقر على 'إضافة سيارة جديدة' يفتح صفحة Stepper لإضافة سيارة."
        3.  "النقر على 'الماركات' يفتح صفحة إدارة الماركات، وهكذا لباقي البيانات الوصفية."
        4.  يتم تمييز الرابط النشط بشكل صحيح في القائمة الجانبية.
*   **LOG_UPDATE_GUIDANCE_POST_TASK:**
    *   **CHANGELOG.md:** "Augment: وثق 'Updated sidebar links for Car Catalog management section to point to their respective CRUD pages.'."
    *   **TODO.md:** "Augment: حدّث حالة `PH02-TASK-029` إلى `Completed`."

2.  ### **`PH02-TASK-030-DASH-CAR-CATALOG-PAGES-MANUAL-TESTING-001`**
    *   **LEVEL:** `Medium`
    *   **OBJECTIVE:** إجراء اختبار يدوي شامل لجميع صفحات CRUD الخاصة بكتالوج السيارات (البيانات الوصفية والسيارات نفسها) للتأكد من أنها تعمل كما هو متوقع، وأن التنقل بينها سلس، وأن العرض متوافق مع تصميم Dash.
    *   **TYPE:** `Manual Testing and UI Verification`
    *   **PRIMARY_INPUTS:**
        *   **TASK_REF:** مخرجات المهام من `PH02-TASK-005` إلى `PH02-TASK-015`.
        *   **DESIGN_REF:** `docs/UIUX-FR.md` (الأقسام ذات الصلة بإدارة كتالوج السيارات).
        *   **LOG_REVIEW_INSTRUCTION:** "Augment: جهز قائمة بجميع صفحات CRUD التي تم إنشاؤها (ماركات، موديلات، ...، سيارات). خطط لاختبار كل عملية (عرض القائمة، الإضافة، التعديل، الحذف، البحث، الفلترة، الترقيم) لكل كيان."
    *   **LOG_UPDATE_GUIDANCE_PRE_TASK:**
        *   **TODO.md:** "Augment: أضف المهمة `PH02-TASK-030` إلى `docs/TODO.md` كجزء من `PH-02` وحددها كـ `In Progress`."
    *   **DEPENDENCIES:** `PH02-TASK-029-DASH-SIDEBAR-CAR-CATALOG-LINKS-VERIFICATION-001`.
    *   **LLM_ASSUMPTIONS:** المستخدم لديه صلاحيات Super Admin للوصول لجميع الوظائف.
    *   **DETAILED_IMPLEMENTATION_STEPS:**
        *   **1. TEST_METADATA_CRUD_PAGES (Brands, Models, Colors, etc.):**
            *   **DETAILED_LOGIC:** "لكل نوع من البيانات الوصفية (ماركات، موديلات، إلخ.):"
                *   "1. انتقل إلى صفحة القائمة (`index`) من القائمة الجانبية."
                *   "2. تحقق من عرض الجدول بشكل صحيح، وعمل الترقيم والبحث والفلترة (إذا تم تنفيذها)."
                *   "3. انقر على 'إضافة جديد'. تحقق من عرض نموذج الإضافة (`create`) بشكل صحيح."
                *   "4. قم بإدخال بيانات صالحة وحاول الحفظ. تحقق من رسالة النجاح والعودة للقائمة."
                *   "5. قم بإدخال بيانات غير صالحة وحاول الحفظ. تحقق من عرض رسائل الخطأ بشكل صحيح بجانب الحقول."
                *   "6. اختر سجلًا من القائمة وانقر 'تعديل'. تحقق من عرض نموذج التعديل (`edit`) مع ملء البيانات الحالية."
                *   "7. قم بتعديل البيانات وحاول الحفظ. تحقق من رسالة النجاح وتحديث البيانات في القائمة."
                *   "8. اختر سجلًا (يفضل أن يكون غير مرتبط بكيانات أخرى إذا كان هناك قيود على الحذف) وانقر 'حذف'. تحقق من مودال التأكيد وعملية الحذف (Soft Delete)."
        *   **2. TEST_CAR_CRUD_STEPPER_INTERFACE:**
            *   **DETAILED_LOGIC:**
                *   "1. انتقل إلى 'إضافة سيارة جديدة'."
                *   "2. تحقق من عمل Stepper والتنقل بين الخطوات الخمس."
                *   "3. تحقق من ملء القوائم المنسدلة بشكل صحيح (خاصة الموديلات بناءً على الماركة)."
                *   "4. قم بإكمال جميع الخطوات ببيانات صالحة وحاول حفظ السيارة. تحقق من رسالة النجاح."
                *   "5. حاول إدخال بيانات غير صالحة في كل خطوة وتحقق من رسائل الخطأ."
                *   "6. انتقل إلى قائمة السيارات، اختر سيارة للتعديل. تحقق من أن Stepper يمتلئ ببيانات السيارة الحالية."
                *   "7. قم بتعديل بعض البيانات والصور وحاول الحفظ. تحقق من التحديث."
        *   **3. TEST_CAR_LIST_PAGE:**
            *   **DETAILED_LOGIC:**
                *   "1. انتقل إلى 'عرض جميع السيارات'."
                *   "2. تحقق من عرض الجدول، الترقيم، الصور المصغرة، الأسعار، الحالات."
                *   "3. اختبر وظائف البحث والفلترة بالماركة والموديل والحالة."
                *   "4. تحقق من عمل أزرار الإجراءات (تعديل، حذف)."
        *   **4. GENERAL_UI_UX_CHECKS_FOR_CAR_CATALOG_SECTION:**
            *   **DETAILED_LOGIC:** "1. تحقق من اتساق التصميم عبر جميع صفحات كتالوج السيارات."
            *   "2. تحقق من وضوح الرسائل (نجاح، خطأ، تأكيد)."
            *   "3. تحقق من عمل مسارات التنقل (breadcrumbs)."
            *   "4. تحقق من عدم وجود أخطاء JavaScript في الـ console."
    *   **EXPECTED_OUTPUTS_BEHAVIOR:** جميع وظائف إدارة كتالوج السيارات في لوحة التحكم تعمل كما هو متوقع، والواجهات متسقة وسهلة الاستخدام.
    *   **ACCEPTANCE_CRITERIA:**
        1.  "يمكن إضافة وتعديل وحذف جميع أنواع البيانات الوصفية للسيارات بنجاح."
        2.  "يمكن إضافة وتعديل سيارة جديدة باستخدام واجهة Stepper بنجاح، بما في ذلك رفع الصور."
        3.  "تعمل قائمة السيارات مع البحث والفلترة والترقيم بشكل صحيح."
        4.  "يتم عرض رسائل التأكيد والأخطاء بشكل واضح ومناسب."
        5.  "لا توجد أخطاء وظيفية أو أخطاء JavaScript واضحة."
*   **LOG_UPDATE_GUIDANCE_POST_TASK:**
    *   **CHANGELOG.md:** "Augment: وثق 'Performed comprehensive manual testing of Car Catalog management (metadata and cars CRUD) in Dash admin panel. Verified functionality and UI consistency.'."
    *   **TODO.md:** "Augment: حدّث حالة `PH02-TASK-030` إلى `Completed`. إذا تم اكتشاف أي أخطاء، قم بإنشاء مهام TODO جديدة لإصلاحها قبل الانتقال لـ `PH-03`."

نعم، يجب بالتأكيد أن تكون قادرًا على فتح جميع صفحات إدارة السيارات والبيانات الوصفية المتعلقة بها، والتفاعل معها بشكل كامل (إضافة، تعديل، حذف، عرض) كجزء من اكتمال المرحلة الثانية. الروابط التي كانت `#` يجب أن تعمل الآن. المهمتان أعلاه (`PH02-TASK-029` و `PH02-TASK-030`) تهدفان إلى التأكد من ذلك.





### ملاحظات ومهام إضافية/تأكيدية مقترحة (ضمن نطاق `PH-02` أو كتحضير لـ `PH-03`):

1.  **`PH02-TASK-021-DASH-SEEDERS-REVIEW-AND-ENHANCEMENT-001`**
    *   **LEVEL:** `Medium`
    *   **OBJECTIVE:** مراجعة وتحسين الـ Seeders الحالية (خاصة `CarCatalogTestDataSeeder` وأي seeders للبيانات الوصفية) للتأكد من أنها توفر مجموعة بيانات اختبار شاملة وواقعية تغطي مختلف السيناريوهات للوحة التحكم، وتجهيز بيانات كافية لاختبار الواجهة العامة في `PH-03`.
    *   **TYPE:** `Database Seeding Enhancement`
    *   **REASONING:** وجود بيانات اختبار جيدة ومتنوعة سيسهل تطوير واختبار الواجهة العامة بشكل كبير في المرحلة التالية.

2.  **`PH02-TASK-022-DASH-UI-CONSISTENCY-AND-UX-REVIEW-001`**
    *   **LEVEL:** `Medium`
    *   **OBJECTIVE:** إجراء مراجعة شاملة لواجهات CRUD التي تم إنشاؤها في `PH-02` (ماركات، موديلات، ألوان، سنوات، نواقل حركة، وقود، هياكل، فئات ميزات، ميزات، سيارات) للتأكد من الاتساق في التصميم، تجربة المستخدم، رسائل الخطأ والنجاح، سلوك النماذج، والترقيم، والبحث، والفلترة.
    *   **TYPE:** `UI/UX Review and Refinement`
    *   **REASONING:** ضمان تجربة مستخدم متسقة وسلسة داخل لوحة التحكم الإدارية قبل الانتقال لبناء واجهات جديدة. هذا يشمل التأكد من أن جميع الجداول والنماذج تستخدم نفس الأنماط البصرية والتفاعلية المستوحاة من Dash.

3.  **`PH02-TASK-023-DASH-ROUTE-PERMISSION-CONSISTENCY-CHECK-001`**
    *   **LEVEL:** `Medium`
    *   **OBJECTIVE:** مراجعة جميع المسارات (routes) الإدارية التي تم إنشاؤها في `PH-02` والتأكد من تطبيق وسائط الحماية (middleware) بشكل صحيح ومتسق (المصادقة، الأدوار، والصلاحيات المناسبة مثل `manage_car_metadata`, `manage_cars_admin`, `manage_employees_admin`, `manage_roles_permissions`, `manage_system_settings`, `access_admin_dashboard`).
    *   **TYPE:** `Security and Routing Review`
    *   **REASONING:** ضمان أمان جميع أجزاء لوحة التحكم الإدارية قبل إضافة المزيد من الوظائف أو فتح النظام لجمهور أوسع.

4.  **`PH02-TASK-024-DASH-HELPER-FUNCTIONS-STANDARDIZATION-001`**
    *   **LEVEL:** `Low`
    *   **OBJECTIVE:** مراجعة وتوحيد أي دوال مساعدة (helper functions) تم إنشاؤها (مثل `car_status_label`, `car_status_badge_class`, `format_datetime_for_display`, `format_currency`) والتأكد من أنها موجودة في المكان المناسب (يفضل ضمن موديول `Core` إذا كانت عامة) ويتم تحميلها بشكل صحيح.
    *   **TYPE:** `Code Refactoring and Standardization`
    *   **REASONING:** الحفاظ على تنظيم الكود وتجنب التكرار.

5.  **`PH02-TASK-025-DASH-NOTIFICATION-INTEGRATION-PLACEHOLDERS-001`**
    *   **LEVEL:** `Low`
    *   **OBJECTIVE:** تحديد (وتوثيق كـ TODOs إذا لزم الأمر) الأماكن في وحدات التحكم التي تم إنشاؤها في `PH-02` (مثل عند إنشاء موظف جديد، أو ربما تحديث حالة سيارة بشكل حرج) حيث سيتم إرسال إشعارات إدارية أو للمستخدمين في المستقبل، وربما إضافة استدعاءات إشعارات معلقة (commented out) أو placeholders.
    *   **TYPE:** `Future Feature Preparation`
    *   **REASONING:** تسهيل تكامل نظام الإشعارات بشكل كامل في المراحل اللاحقة.

6.  **`PH02-TASK-026-SPATIE-MEDIALIBRARY-CONFIG-REVIEW-001`**
    *   **LEVEL:** `Medium`
    *   **OBJECTIVE:** مراجعة تكوينات `spatie/laravel-medialibrary` (ملف `config/media-library.php`). التأكد من تعريف تحويلات الصور (image conversions) المستخدمة (مثل `thumb` لشعارات الماركات وصور السيارات في الجداول) بشكل مركزي وصحيح، وتحديد مسارات التخزين والـ disk المستخدم.
    *   **TYPE:** `Package Configuration Review`
    *   **REASONING:** ضمان إدارة فعالة ومتسقة للملفات والصور.

7.  **`PH02-TASK-027-DASH-BREADCRUMBS-CONSISTENCY-001`**
    *   **LEVEL:** `Medium`
    *   **OBJECTIVE:** التأكد من أن جميع صفحات لوحة التحكم الإدارية (خاصة صفحات CRUD) تحتوي على مسارات تنقل (breadcrumbs) واضحة ومتسقة تساعد المستخدم على معرفة مكانه في النظام والتنقل بسهولة.
    *   **TYPE:** `UI/UX Enhancement`
    *   **REASONING:** تحسين قابلية الاستخدام والتنقل داخل لوحة التحكم.

8.  **`PH02-TASK-028-CODE-DOCUMENTATION-AND-LINTING-PH02-001`**
    *   **LEVEL:** `Medium`
    *   **OBJECTIVE:** التأكد من أن جميع الكلاسات والدوال الهامة التي تم إنشاؤها في `PH-02` موثقة بشكل جيد (DocBlocks)، وأن الكود يتبع معايير التنسيق المعتمدة (تشغيل Linter/Formatter).
    *   **TYPE:** `Code Quality and Documentation`
    *   **REASONING:** الحفاظ على جودة الكود وقابليته للصيانة.


