## REQ-FR.md - المتطلبات التفصيلية للمشروع (نسخة نهائية معتمدة ذاتيًا)

**تاريخ الإنشاء:** 2024-07-27
**إصدار المستند:** 1.0 (نهائي)

---

### مقدمة

الغرض من هذا المستند هو تقديم المتطلبات التفصيلية لمنصة معرض السيارات الإلكترونية المتكاملة. يستند هذا المستند بشكل مباشر إلى التحليل الأولي ومقترح الموديولات المعتمد في `00-FR.md` (النسخة النهائية المعتمدة ذاتيًا، إصدار 1.0، بتاريخ 2024-07-26) وإلى مستند نظرة عامة على المشروع المعتمد `PO-FR.md` (النسخة النهائية المعتمدة ذاتيًا، إصدار 1.0، بتاريخ 2024-07-26).
يفترض هذا المستند استخدام إطار عمل Laravel 10، وبناء لوحة تحكم مخصصة (Dash) من الصفر باستخدام أصول HTML/CSS/JS المتوفرة ودمجها كواجهات Blade ديناميكية، واستخدام حزمة `nwidart/laravel-modules` لتنظيم الموديولات، وتطوير تطبيق Flutter للعملاء، وواجهة موقع عامة باستخدام Blade.

يهدف هذا المستند إلى أن يكون بمثابة المخطط الهندسي الأساسي الذي سيُبنى عليه توليد المواصفات التقنية (`TS-FR.md`), هيكل المشروع (`STRU-FR.md`), وتصميم واجهات المستخدم (`UIUX-FR.md`). يجب أن تكون المتطلبات هنا مفصلة للغاية، دقيقة، قابلة للاستهلاك الآلي من قبل أنظمة الذكاء الاصطناعي الأخرى (LLMs)، وموجهة نحو تحقيق المخرجات النهائية المتوقعة للمشروع.

**المخرجات النهائية المتوقعة للمشروع (المرجع الأساسي):**
1.  **موقع إلكتروني فعال:** مرفوع على خادم عبر الإنترنت، جاهز لاستقبال الزوار بدون مشاكل.
2.  **لوحة تحكم احترافية (مخصصة من الصفر باستخدام أصول Dash HTML/CSS/JS):** (للإدارة و/أو العملاء) جاهزة للاستخدام، توفر تجربة مستخدم ممتازة، وتدعم اللغة العربية (RTL) بشكل كامل.
3.  **واجهة موقع (Frontend) جذابة وفعالة:** (باستخدام Blade إذا كانت مختلفة عن لوحة التحكم) جاهزة لاستقبال الزوار وتوفير تجربة مستخدم سلسة.
4.  **تطبيق موبايل (Flutter) كامل:** جاهز للرفع على متاجر التطبيقات ويعمل بشكل صحيح ومتكامل.

---

### 1. المتطلبات الوظيفية (Functional Requirements)

---
#### **1.1. موديول: `Core` (النواة)**
*   **معرف الموديول:** `MOD-CORE`
*   **اسم الموديول:** النواة (Core)
*   **وصف موجز للموديول:** يحتوي هذا الموديول على الوظائف الأساسية المشتركة عبر النظام بأكمله، مثل السمات (Traits)، الدوال المساعدة (Helpers)، الأصناف الأساسية (Base Classes مثل Controllers, Models, Requests)، وإعدادات النظام العامة التي تستخدمها الموديولات الأخرى. يهدف إلى توفير أساس متين وقابل لإعادة الاستخدام، مما يساهم في صيانة النظام وتطويره بكفاءة، ويدعم بشكل غير مباشر جميع المخرجات النهائية من خلال توفير البنية التحتية البرمجية. لا يتفاعل هذا الموديول مباشرة مع واجهات المستخدم ولكنه يخدمها جميعًا.
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-CORE-FEAT-001`
    *   **اسم الميزة/الوظيفة:** توفير أصناف أساسية (Base Classes)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** توفير مجموعة من الأصناف الأساسية (مثل BaseController, BaseModel, BaseRepository, BaseRequest) التي تتضمن وظائف مشتركة لتقليل التكرار وتوحيد طريقة العمل في الموديولات الأخرى.
        *   **أنواع المستخدمين المتفاعلين:** مطورو النظام (بشكل غير مباشر).
        *   **المدخلات المطلوبة للميزة:** لا يوجد مدخلات مباشرة من المستخدم، بل هي أدوات تطوير.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  تعريف `BaseController` مع معالجة موحدة للاستجابات (JSON للـ API، Views للـ Web)، بما في ذلك التعامل مع الأخطاء الشائعة.
            2.  تعريف `BaseModel` مع Traits مشتركة (مثل SoftDeletes، استخدام UUIDs للمعرفات إذا تقرر ذلك، التعامل مع `created_by`/`updated_by` تلقائيًا).
            3.  تعريف `BaseRepository` (إذا تم اعتماد نمط Repository) لعمليات CRUD الأساسية (create, read, update, delete, findById, getAll with pagination and filters).
            4.  تعريف `BaseRequest` (لطلبات FormRequest في Laravel) لقواعد التحقق المشتركة أو طرق معالجة أخطاء التحقق الموحدة.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** توفر أصناف أساسية موثقة وقابلة للتوسع والاستخدام في جميع الموديولات.
        *   **قواعد العمل:**
            *   يجب أن تكون الأصناف الأساسية عامة بما يكفي لتناسب احتياجات مختلف الموديولات.
            *   يجب أن تتبع مبادئ SOLID للتصميم.
            *   يجب أن تكون قابلة للتخصيص عند الحاجة في الموديولات المشتقة.
    *   **التبعيات:** لا يوجد.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-004` (تأسيس بنية خلفية قوية).
    *   **ربط بالمخرجات النهائية المتوقعة:** يدعم جميع المخرجات النهائية من خلال توفير بنية تحتية برمجية قوية وموثوقة.

    *   **معرف فريد للميزة:** `MOD-CORE-FEAT-002`
    *   **اسم الميزة/الوظيفة:** دوال مساعدة عامة (Global Helpers)
    *   **الأولوية:** عالية
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** توفير مجموعة من الدوال المساعدة العامة التي يمكن استخدامها في أي مكان في التطبيق لتبسيط المهام المتكررة.
        *   **أنواع المستخدمين المتفاعلين:** مطورو النظام (بشكل غير مباشر).
        *   **المدخلات المطلوبة للميزة:** تختلف حسب الدالة المساعدة (مثال: `format_currency(amount, currency_code)`, `generate_otp(length)`).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  تحديد الدوال المساعدة الشائعة المطلوبة (مثل: تنسيق التواريخ، تنسيق العملات، توليد رموز OTP، التعامل مع النصوص، عمليات حسابية بسيطة متكررة).
            2.  إنشاء ملف `helpers.php` يتم تحميله تلقائيًا بواسطة Composer.
            3.  تنفيذ الدوال المساعدة مع توثيق واضح لكل دالة (المدخلات، المخرجات، الغرض).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** توفر دوال مساعدة موثوقة، سهلة الاستخدام، ومختبرة.
        *   **قواعد العمل:**
            *   يجب ألا تعتمد الدوال المساعدة على حالة تطبيق محددة بشكل كبير لتكون عامة قدر الإمكان.
            *   يجب تجنب تسمية الدوال بأسماء قد تتعارض مع دوال PHP أو Laravel المدمجة.
    *   **التبعيات:** لا يوجد.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** يدعم جميع المخرجات النهائية من خلال تسهيل عملية التطوير وزيادة كفاءتها.

    *   **معرف فريد للميزة:** `MOD-CORE-FEAT-003`
    *   **اسم الميزة/الوظيفة:** إدارة الإعدادات العامة للنظام
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** توفير آلية مركزية لتخزين واسترجاع إعدادات النظام العامة (مثل اسم الموقع، البريد الإلكتروني للإدارة، مفاتيح API للخدمات الخارجية، إعدادات العملة، نسبة الضريبة، مبلغ الحجز الافتراضي، إلخ) التي يمكن إدارتها من لوحة تحكم الإدارة Dash.
        *   **أنواع المستخدمين المتفاعلين:** مديرو النظام (لتعديل الإعدادات عبر `MOD-DASHBOARD`)، النظام (لاستهلاك الإعدادات).
        *   **المدخلات المطلوبة للميزة (من خلال واجهة الإدارة في `MOD-DASHBOARD`):**
            *   مفتاح الإعداد (نص، فريد، مثال: `site_name`, `admin_email`, `default_currency_symbol`, `vat_percentage`).
            *   قيمة الإعداد (نص، يمكن أن يكون رقمًا أو JSON أو قيمة منطقية يتم تحويلها لنص عند التخزين).
            *   نوع الإعداد (نص، يستخدم لتحديد نوع حقل الإدخال في الواجهة، مثال: `text`, `number`, `boolean`, `textarea`, `select`).
            *   مجموعة الإعداد (نص، لتجميع الإعدادات في واجهة Dash، مثال: `general`, `payment`, `seo`).
            *   (اختياري) وصف للإعداد (لتوضيح الغرض منه في واجهة Dash).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  إنشاء جدول في قاعدة البيانات (`settings`) لتخزين الإعدادات (columns: `key` (PK), `value` (TEXT), `type` (VARCHAR), `group` (VARCHAR), `description` (TEXT), `is_translatable` (BOOLEAN, default false)).
            2.  توفير واجهة (Service/Helper) للوصول إلى الإعدادات بسهولة في الكود (e.g., `setting('site_name', 'default_value')`). يجب أن تدعم هذه الواجهة القيم الافتراضية والـ Caching لتحسين الأداء.
            3.  توفير واجهة مستخدم ضمن لوحة تحكم الإدارة Dash (في موديول `MOD-DASHBOARD`، ضمن قسم "إعدادات النظام" - `FEAT-ADMIN-003`) لعرض وتعديل هذه الإعدادات بشكل مُجمّع وسهل الاستخدام (مثال: تجميع إعدادات الموقع، إعدادات البريد، إعدادات الدفع). يجب أن تدعم الواجهة أنواع حقول مختلفة (نص، رقم، قائمة منسدلة، checkbox) بناءً على نوع الإعداد.
            4.  إمكانية تحديد بعض الإعدادات كـ "غير قابلة للتعديل من الواجهة" (تُدار من الكود فقط).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** نظام إعدادات مركزي، قابل للإدارة، وفعال.
        *   **قواعد العمل:**
            *   يجب توفير قيم افتراضية لبعض الإعدادات الأساسية في ملفات التهيئة أو seeders.
            *   يجب أن تكون هناك إمكانية لتجميع الإعدادات لتسهيل عرضها في لوحة التحكم.
            *   يجب مسح الـ cache الخاص بالإعدادات عند تعديل أي إعداد.
    *   **التبعيات:** `MOD-DASHBOARD` (لواجهة الإدارة).
    *   **ربト بأهداف `PO-FR.md`:** `OBJ-002`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية (لإدارة الإعدادات)، موقع إلكتروني فعال (يستخدم الإعدادات)، تطبيق موبايل كامل (يستخدم الإعدادات عبر API).

---
#### **1.2. موديول: `UserManagement` (إدارة المستخدمين)**
*   **معرف الموديول:** `MOD-USER-MGMT`
*   **اسم الموديول:** إدارة المستخدمين (UserManagement)
*   **وصف موجز للموديول:** مسؤول عن جميع جوانب إدارة المستخدمين بما في ذلك العملاء الأفراد (USER-TYPE-001)، ممثلي الشركات (USER-TYPE-002)، مديري النظام (USER-TYPE-003)، والموظفين (USER-TYPE-004). يشمل ذلك عمليات التسجيل، تسجيل الدخول، استعادة كلمة المرور، إدارة الملفات الشخصية، وتحديد الأدوار والصلاحيات. يخدم هذا الموديول بشكل مباشر المخرجات النهائية المتعلقة بتفاعل المستخدمين مع المنصة عبر الموقع العام، تطبيق Flutter، ولوحات التحكم المخصصة (Dash).
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-USER-MGMT-FEAT-001` (مستمد من `FEAT-USER-001`)
    *   **اسم الميزة/الوظيفة:** تسجيل حساب جديد للعملاء الأفراد
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين الأفراد الجدد من إنشاء حسابات شخصية على المنصة.
        *   **أنواع المستخدمين المتفاعلين:** المستخدمون العاديون (العملاء الأفراد) (USER-TYPE-001).
        *   **المدخلات المطلوبة للميزة (عبر واجهة الموقع العامة وتطبيق Flutter):**
            *   الاسم الأول (نص، مطلوب، الحد الأدنى 2 حروف، الحد الأقصى 50 حرفًا، regex: `^[\\p{L}\\s'-]+$`).
            *   اسم العائلة (نص، مطلوب، الحد الأدنى 2 حروف، الحد الأقصى 50 حرفًا, regex: `^[\\p{L}\\s'-]+$`).
            *   البريد الإلكتروني (بريد إلكتروني صالح، مطلوب، فريد في جدول المستخدمين، تحويل إلى أحرف صغيرة قبل الحفظ).
            *   رقم الجوال (نص، مطلوب، يجب أن يكون رقم جوال سعودي صالح يبدأ بـ '05' ويتبعه 8 أرقام، فريد في جدول المستخدمين، سيتم إرسال رمز OTP للتحقق من ملكيته).
            *   كلمة المرور (نص، مطلوب، الحد الأدنى 8 حروف، يجب أن تحتوي على حروف كبيرة وصغيرة وأرقام ورمز واحد على الأقل من `!@#$%^&*()_+-=[]{};':"\|,.<>/?~`).
            *   تأكيد كلمة المرور (نص، مطلوب، يجب أن يطابق كلمة المرور).
            *   الموافقة على الشروط والأحكام (منطقي، مطلوب، يجب أن تكون قيمته true، مع رابط لصفحة الشروط والأحكام).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  التحقق من صحة المدخلات (validation) بناءً على القيود المذكورة أعلاه.
            2.  التحقق من أن البريد الإلكتروني ورقم الجوال غير مسجلين مسبقًا.
            3.  إنشاء سجل مستخدم جديد في قاعدة البيانات (`users` table) مع حالة "غير مفعل" (pending_verification) أو "نشط" مباشرة إذا تم التحقق من الجوال.
            4.  تشفير كلمة المرور باستخدام خوارزمية قوية (e.g., bcrypt).
            5.  تعيين دور "عميل" (customer) للمستخدم الجديد بشكل افتراضي باستخدام `spatie/laravel-permission`.
            6.  إرسال رمز OTP (One-Time Password) إلى رقم الجوال المدخل للتحقق من الملكية (باستخدام `MOD-NOTIFICATION`).
            7.  (اختياري بعد التحقق) إرسال بريد إلكتروني ترحيبي.
            8.  بعد التحقق الناجح من OTP، يتم تفعيل الحساب وتسجيل دخول المستخدم تلقائيًا.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** إنشاء حساب مستخدم جديد مفعل، وإرجاع بيانات المستخدم (أو token مصادقة لـ API).
        *   **قواعد العمل:**
            *   يجب أن يكون البريد الإلكتروني ورقم الجوال فريدين.
            *   يجب تطبيق سياسات كلمة مرور قوية.
            *   يجب أن يوافق المستخدم على الشروط والأحكام.
            *   يجب التحقق من ملكية رقم الجوال عبر OTP قبل تفعيل الحساب بالكامل.
    *   **التبعيات:** `MOD-NOTIFICATION` (لإرسال OTP والبريد الترحيبي)، `spatie/laravel-permission`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-USER-MGMT-FEAT-001B`
    *   **اسم الميزة/الوظيفة:** التحقق من رقم الجوال عبر OTP (أثناء التسجيل وبعد تغيير الرقم)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** التحقق من ملكية رقم الجوال المقدم من المستخدم أثناء التسجيل أو عند تغييره من الملف الشخصي.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001.
        *   **المدخلات المطلوبة للميزة:** رقم الجوال، رمز OTP المدخل من المستخدم.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  **إرسال OTP:**
                *   عند طلب التسجيل أو تغيير رقم الجوال، يتم توليد رمز OTP (رقمي، 4-6 خانات) مع تاريخ انتهاء صلاحية قصير (5-10 دقائق).
                *   يتم تخزين الرمز (مشفرًا أو مرتبطًا بالجلسة/المستخدم) مع رقم الجوال وتاريخ انتهاء الصلاحية.
                *   يتم إرسال الرمز عبر SMS إلى رقم الجوال (باستخدام `MOD-NOTIFICATION` وبوابة SMS).
            2.  **التحقق من OTP:**
                *   يعرض النظام نموذجًا للمستخدم لإدخال رمز OTP.
                *   يتحقق النظام من تطابق الرمز المدخل مع المخزن، ومن صلاحيته (لم تنتهِ صلاحيته ولم يُستخدم من قبل).
                *   في حال النجاح: يتم تفعيل الحساب (إذا كان تسجيلًا جديدًا) أو تحديث رقم الجوال في الملف الشخصي. يتم إبطال الرمز.
                *   في حال الفشل: عرض رسالة خطأ، والسماح بإعادة إرسال OTP بعد فترة (مع حد لعدد المحاولات).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** التحقق من ملكية رقم الجوال بنجاح.
        *   **قواعد العمل:**
            *   يجب أن يكون رمز OTP صالحًا للاستخدام مرة واحدة فقط.
            *   يجب تطبيق حد أقصى لعدد محاولات إدخال OTP الفاشلة وإعادة إرسال OTP.
    *   **التبعيات:** `MOD-NOTIFICATION` (وبوابة SMS).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-USER-MGMT-FEAT-002` (مستمد من `FEAT-USER-002`)
    *   **اسم الميزة/الوظيفة:** تسجيل الدخول للعملاء والموظفين/المديرين
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين المسجلين (عملاء، موظفين، مديرين) من الوصول إلى حساباتهم والمناطق المخصصة لهم.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001, USER-TYPE-003, USER-TYPE-004.
        *   **المدخلات المطلوبة للميزة (عبر واجهة الموقع العامة، تطبيق Flutter، ولوحة تحكم Dash - صفحة تسجيل الدخول):**
            *   معرف تسجيل الدخول (نص، مطلوب، يمكن أن يكون البريد الإلكتروني أو رقم الجوال).
            *   كلمة المرور (نص، مطلوب).
            *   (اختياري لواجهات الويب) تذكرني (منطقي).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  التحقق من صحة المدخلات (تنسيق البريد/الجوال، وجود كلمة المرور).
            2.  التحقق من تنسيق "معرف تسجيل الدخول" المدخل. إذا كان يشبه البريد الإلكتروني، يتم البحث عن المستخدم بواسطة البريد الإلكتروني. إذا كان يشبه رقم الجوال (بناءً على نفس تنسيق التحقق من التسجيل)، يتم البحث بواسطة رقم الجوال. إذا لم يتطابق مع أي منهما، يتم عرض رسالة خطأ ("يرجى إدخال بريد إلكتروني أو رقم جوال صالح").
            3.  إذا تم العثور على المستخدم، يتم التحقق من تطابق كلمة المرور المدخلة مع كلمة المرور المشفرة المخزنة.
            4.  التحقق من أن حساب المستخدم "نشط" وليس "محظورًا" أو "بانتظار التحقق".
            5.  في حالة النجاح:
                *   إنشاء جلسة مستخدم (Session-based authentication للواب ولوحات تحكم Dash).
                *   توليد token مصادقة (API token باستخدام Laravel Sanctum لتطبيق Flutter).
                *   تحديث حقل `last_login_at` للمستخدم.
                *   إعادة توجيه المستخدم:
                    *   USER-TYPE-001: إلى لوحة تحكم العميل Dash أو الصفحة الرئيسية بعد تسجيل الدخول في الموقع العام/التطبيق.
                    *   USER-TYPE-003, USER-TYPE-004: إلى لوحة تحكم الإدارة Dash.
            6.  في حالة الفشل (مستخدم غير موجود، كلمة مرور خاطئة، حساب غير نشط): عرض رسالة خطأ عامة (مثل "بيانات الاعتماد غير صحيحة أو الحساب غير نشط").
            7.  تطبيق آلية للحماية من محاولات تسجيل الدخول الفاشلة المتكررة (Rate Limiting per IP/User، أو CAPTCHA بعد عدد معين من المحاولات).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** مصادقة المستخدم بنجاح، وإنشاء جلسة أو إرجاع token، وتوجيه المستخدم.
        *   **قواعد العمل:**
            *   يجب أن يكون الحساب نشطًا ومفعلاً ليتمكن المستخدم من تسجيل الدخول.
            *   يجب التعامل مع حالة "تذكرني" بشكل آمن (long-lived secure cookie).
    *   **التبعيات:** Laravel Sanctum (لتطبيق Flutter).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-002`, `OBJ-003`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، لوحة تحكم احترافية، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-USER-MGMT-FEAT-003` (مستمد من `FEAT-USER-003`)
    *   **اسم الميزة/الوظيفة:** استعادة كلمة المرور للعملاء والموظفين/المديرين
    *   **الأولوية:** عالية
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين الذين نسوا كلمة المرور الخاصة بهم من إعادة تعيينها بشكل آمن.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001, USER-TYPE-003, USER-TYPE-004.
        *   **المدخلات المطلوبة للميزة (عبر واجهة الموقع العامة، تطبيق Flutter، صفحة تسجيل الدخول للوحة تحكم Dash):**
            *   **المرحلة 1 (طلب إعادة التعيين):** البريد الإلكتروني المسجل (مطلوب، يجب أن يكون بتنسيق بريد إلكتروني صالح).
            *   **المرحلة 2 (إعادة التعيين - عبر الرابط المرسل):** كلمة المرور الجديدة (نص، مطلوب، بنفس قيود كلمة المرور عند التسجيل)، تأكيد كلمة المرور الجديدة (مطلوب، يجب أن يطابق).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  **طلب إعادة التعيين:**
                *   المستخدم يدخل بريده الإلكتروني في نموذج "نسيت كلمة المرور".
                *   النظام يتحقق من وجود البريد الإلكتروني في قاعدة البيانات وأن الحساب المرتبط به نشط.
                *   إذا وجد وكان نشطًا، يتم توليد رمز (token) فريد وآمن لإعادة تعيين كلمة المرور مع تاريخ انتهاء صلاحية قصير (مثل 60 دقيقة).
                *   يتم تخزين الرمز (عادة hash للرمز) مع البريد الإلكتروني ووقت الإنشاء في جدول `password_resets` (أو ما يعادله في Laravel).
                *   يتم إرسال بريد إلكتروني إلى المستخدم يحتوي على رابط فريد لصفحة إعادة تعيين كلمة المرور (يتضمن الرمز كجزء من الرابط).
            2.  **إعادة التعيين:**
                *   المستخدم ينقر على الرابط في البريد الإلكتروني.
                *   النظام يستقبل الرمز من الرابط ويتحقق من صلاحيته (موجود في `password_resets`، لم تنتهِ صلاحيته، ويتطابق مع البريد الإلكتروني من الرابط).
                *   إذا كان الرمز صالحًا، يتم عرض نموذج لإدخال كلمة المرور الجديدة وتأكيدها.
                *   يتم التحقق من صحة كلمة المرور الجديدة (مطابقة، قيود القوة).
                *   تحديث كلمة مرور المستخدم في جدول `users` (بعد تشفيرها).
                *   حذف/إبطال صلاحية الرمز المستخدم من جدول `password_resets`.
                *   (اختياري ولكن مستحسن) إرسال بريد إلكتروني للمستخدم لتأكيد أن كلمة المرور قد تم تغييرها بنجاح.
                *   توجيه المستخدم إلى صفحة تسجيل الدخول.
            3.  إذا كان الرمز غير صالح أو منتهي الصلاحية، يتم عرض رسالة خطأ مناسبة مع إمكانية طلب رابط جديد.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** إعادة تعيين كلمة مرور المستخدم بنجاح.
        *   **قواعد العمل:**
            *   يجب أن يكون رمز إعادة التعيين صالحًا للاستخدام مرة واحدة فقط.
            *   يجب أن يكون للرمز تاريخ انتهاء صلاحية واضح.
            *   يجب أن يكون الرابط المرسل فريدًا وآمنًا.
    *   **التبعيات:** `MOD-NOTIFICATION` (لإرسال رسائل البريد الإلكتروني).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-002`, `OBJ-003`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، لوحة تحكم احترافية، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-USER-MGMT-FEAT-004` (مستمد من `FEAT-USER-004`)
    *   **اسم الميزة/الوظيفة:** لوحة تحكم العميل (Dash Custom - بيانات ووظائف)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** توفير واجهة مخصصة للعملاء المسجلين (USER-TYPE-001) لإدارة حساباتهم، طلباتهم، وتفاعلاتهم الأخرى مع المنصة. سيتم بناؤها باستخدام أصول Dash المخصصة كواجهات Blade ضمن موديول `MOD-DASHBOARD`.
        *   **أنواع المستخدمين المتفاعلين:** المستخدمون العاديون (العملاء الأفراد) (USER-TYPE-001).
        *   **المدخلات المطلوبة للميزة:** لا يوجد مدخلات مباشرة لهذه الميزة ككل، ولكن الأقسام الفرعية ستتطلب تفاعلات.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (الأقسام الرئيسية المتوقعة كما في `00-FR.md`، مع الإشارة إلى كيفية ظهورها في واجهة Dash للعميل):**
            1.  **الرئيسية (ملخص الحساب):** (واجهة Dash تعرض بطاقات ملخص)
                *   عرض رسالة ترحيب باسم العميل.
                *   عرض عدد الطلبات الحالية (قيد المعالجة، مكتملة). (من `MOD-ORDER-MGMT`)
                *   عرض عدد السيارات في قائمة المفضلة. (من `MOD-CAR-CATALOG`)
                *   (اختياري) رابط سريع لآخر طلب أو لأهم الإجراءات.
            2.  **طلباتي:** (واجهة Dash تعرض جدولاً بالطلبات مع فلاتر وترقيم صفحات)
                *   عرض قائمة بجميع طلبات العميل (شراء، تمويل، خدمات). (راجع `MOD-ORDER-MGMT-FEAT-006C` - C for Customer View).
                *   لكل طلب: رقم الطلب، نوع الطلب، تاريخ الطلب، حالة الطلب، المبلغ الإجمالي (أو مبلغ الحجز).
                *   إمكانية النقر على طلب لعرض تفاصيله الكاملة في صفحة منفصلة داخل لوحة التحكم.
            3.  **طلبات التمويل:** (قسم مشابه لـ "طلباتي" ولكن مفلتر لطلبات التمويل فقط، أو تبويب ضمن "طلباتي").
                *   عرض طلبات التمويل الحالية وحالتها. (راجع `MOD-ORDER-MGMT-FEAT-007C`).
            4.  **رشح عميل:** (واجهة Dash تعرض نموذجًا بسيطًا إذا كان العميل مؤهلاً).
                *   (راجع `MOD-USER-MGMT-FEAT-008`).
            5.  **المفضلة:** (واجهة Dash تعرض قائمة بالسيارات المفضلة كبطاقات أو جدول).
                *   عرض السيارات المحفوظة في المفضلة. (راجع `MOD-CAR-CATALOG-FEAT-005B` - B for Customer View).
                *   إمكانية إزالة سيارة من المفضلة.
                *   رابط لعرض تفاصيل السيارة أو الانتقال للمقارنة.
            6.  **المعاملات المالية:** (واجهة Dash تعرض جدولاً بالمعاملات).
                *   عرض سجل المعاملات المالية المتعلقة بالحجوزات المدفوعة أونلاين.
                *   (اختياري) عرض الفواتير التي تم إنشاؤها من النظام للعميل (إذا كانت هذه الميزة مطبقة).
            7.  **الإشعارات:** (واجهة Dash تعرض قائمة بالإشعارات).
                *   مركز الإشعارات الخاص بالعميل. (راجع `MOD-NOTIFICATION-FEAT-002`).
            8.  **الملف الشخصي:** (واجهة Dash تعرض نموذجًا لتعديل البيانات).
                *   تعديل المعلومات الشخصية وتغيير كلمة المرور. (راجع `MOD-USER-MGMT-FEAT-005`).
            9.  **تسجيل الخروج:** زر لتسجيل الخروج من لوحة التحكم.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** توفير واجهة Dash مخصصة للعميل، متجاوبة، سهلة الاستخدام، وآمنة، تعرض البيانات الخاصة بالعميل المسجل دخوله فقط.
        *   **قواعد العمل:**
            *   يجب أن تكون جميع البيانات المعروضة خاصة بالمستخدم المسجل دخوله فقط.
            *   يجب أن تكون الواجهة متجاوبة وتدعم اللغة العربية (RTL) بشكل كامل.
            *   التصميم يجب أن يكون مشابهًا لتصميم لوحة تحكم الإدارة Dash ولكن بميزات وألوان قد تكون مختلفة قليلاً لتمييزها.
    *   **التبعيات:** `MOD-DASHBOARD` (لتوفير هيكل الواجهة والمكونات الأساسية)، `MOD-ORDER-MGMT`, `MOD-CAR-CATALOG`, `MOD-NOTIFICATION`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية (للعملاء).

    *   **معرف فريد للميزة:** `MOD-USER-MGMT-FEAT-005` (مستمد من `FEAT-USER-005`)
    *   **اسم الميزة/الوظيفة:** إدارة الملف الشخصي للعميل
    *   **الأولوية:** عالية
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين العملاء من تحديث معلوماتهم الشخصية وتغيير كلمة المرور من خلال لوحة تحكم العميل Dash وتطبيق Flutter.
        *   **أنواع المستخدمين المتفاعلين:** المستخدمون العاديون (العملاء الأفراد) (USER-TYPE-001).
        *   **المدخلات المطلوبة للميزة (عبر لوحة تحكم العميل Dash وتطبيق Flutter - نماذج تعديل):**
            *   **تعديل المعلومات الشخصية:**
                *   الاسم الأول (نص، مطلوب، نفس قيود التسجيل).
                *   اسم العائلة (نص، مطلوب، نفس قيود التسجيل).
                *   (عرض فقط، غير قابل للتعديل) البريد الإلكتروني.
                *   رقم الجوال (نص، مطلوب، يجب أن يكون رقم جوال سعودي صالح، يتطلب تحقق OTP جديد إذا تم تغييره - `MOD-USER-MGMT-FEAT-001B`).
                *   (اختياري) صورة شخصية (ملف صورة: jpg, png, webp، الحد الأقصى للحجم: 2MB).
                *   (اختياري) العنوان (نص، مثال: المدينة، الحي).
            *   **تغيير كلمة المرور:**
                *   كلمة المرور الحالية (نص، مطلوب).
                *   كلمة المرور الجديدة (نص، مطلوب، نفس قيود التسجيل).
                *   تأكيد كلمة المرور الجديدة (نص، مطلوب، يجب أن يطابق).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  **عرض المعلومات الحالية:** استرجاع وعرض بيانات المستخدم الحالية في نموذج التعديل.
            2.  **تحديث المعلومات الشخصية:**
                *   التحقق من صحة المدخلات الجديدة.
                *   إذا تم تغيير رقم الجوال، يتم تفعيل عملية التحقق عبر OTP (`MOD-USER-MGMT-FEAT-001B`). لا يتم تحديث الرقم إلا بعد التحقق الناجح.
                *   إذا تم تحميل صورة شخصية، يتم معالجتها (تغيير الحجم، التحسين إذا لزم الأمر) وتخزينها (باستخدام `spatie/laravel-medialibrary` وربطها بنموذج User).
                *   تحديث سجل المستخدم في قاعدة البيانات.
                *   عرض رسالة نجاح.
            3.  **تغيير كلمة المرور:**
                *   التحقق من أن كلمة المرور الحالية المدخلة تطابق كلمة المرور المخزنة للمستخدم.
                *   التحقق من تطابق كلمتي المرور الجديدتين وتطبيق سياسات كلمة المرور.
                *   تحديث كلمة المرور المشفرة في قاعدة البيانات.
                *   (مستحسن) إرسال إشعار للمستخدم (بريد أو إشعار داخلي) بتغيير كلمة المرور.
                *   (مستحسن) إبطال صلاحية جميع الجلسات الأخرى للمستخدم (باستثناء الحالية) بعد تغيير كلمة المرور.
                *   عرض رسالة نجاح.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** تحديث بيانات المستخدم أو كلمة المرور بنجاح.
        *   **قواعد العمل:**
            *   لا يمكن تغيير البريد الإلكتروني مباشرة من قبل المستخدم (قد يتطلب عملية دعم فني إذا كان ضروريًا).
            *   يجب توفير خيار لحذف الصورة الشخصية.
    *   **التبعيات:** `MOD-NOTIFICATION` (لإشعار تغيير كلمة المرور، وإرسال OTP)، `spatie/laravel-medialibrary` (لإدارة الصور)، `MOD-USER-MGMT-FEAT-001B`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002`, `OBJ-003`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية (للعملاء)، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-USER-MGMT-FEAT-008` (مستمد من `FEAT-USER-008`)
    *   **اسم الميزة/الوظيفة:** قسم "رشح عميل" في لوحة تحكم العميل
    *   **الأولوية:** متوسطة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين بعض العملاء (حسب معايير تحددها الإدارة) من ترشيح عملاء جدد للمعرض، مع إمكانية تتبع حالة الترشيح.
        *   **أنواع المستخدمين المتفاعلين:** المستخدمون العاديون (العملاء الأفراد) (USER-TYPE-001) (المؤهلون فقط).
        *   **المدخلات المطلوبة للميزة (عبر نموذج في لوحة تحكم العميل Dash):**
            *   اسم العميل المرشح (نص، مطلوب، الحد الأدنى 3 حروف).
            *   رقم جوال العميل المرشح (نص، مطلوب، رقم سعودي صالح).
            *   (اختياري) البريد الإلكتروني للعميل المرشح (بريد إلكتروني صالح).
            *   (اختياري) ملاحظات (نص، الحد الأقصى 255 حرفًا).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  **عرض قسم "رشح عميل":** لا يتم عرض هذا القسم في لوحة تحكم العميل إلا إذا كان الحقل `can_refer_customer` (أو ما يعادله) في سجل العميل الحالي هو `true`.
            2.  **تقديم الترشيح:**
                *   التحقق من صحة المدخلات.
                *   تخزين بيانات الترشيح في جدول مخصص (`customer_referrals`) مع ربطها بالعميل الذي قام بالترشيح (referrer_user_id)، بيانات العميل المرشح، تاريخ الترشيح، وحالة الترشيح الافتراضية ("جديد").
                *   إرسال إشعار إلى الإدارة (مجموعة مستخدمين محددة أو بريد إلكتروني محدد) بوجود ترشيح جديد، يتضمن تفاصيل الترشيح والعميل المرشِح.
                *   عرض رسالة نجاح للعميل.
            3.  **عرض قائمة الترشيحات السابقة للعميل:** في نفس القسم، يتم عرض جدول بالعملاء الذين رشحهم العميل سابقًا مع حالتهم (جديد، تم التواصل، مهتم، غير مهتم، تم الشراء). هذه الحالة يتم تحديثها من قبل الإدارة.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** تسجيل طلب الترشيح بنجاح، إخطار الإدارة، وعرض سجل الترشيحات للعميل.
        *   **قواعد العمل:**
            *   يتم تفعيل ميزة "رشح عميل" لكل عميل على حدة من قبل الإدارة عبر لوحة التحكم (`MOD-USER-MGMT-FEAT-009`).
            *   يجب ألا يتمكن العميل من ترشيح نفس رقم الجوال أو البريد الإلكتروني أكثر من مرة إذا كان الترشيح السابق لا يزال "جديدًا" أو "تم التواصل معه".
            *   **إجابة لسؤال 2 من `00-FR.md`:** آلية تفعيل الميزة ستكون عبر حقل منطقي في ملف العميل يتم التحكم فيه يدويًا من قبل الإدارة.
    *   **التبعيات:** `MOD-NOTIFICATION` (لإشعار الإدارة)، `MOD-USER-MGMT-FEAT-009` (لإدارة أهلية العملاء).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية (للعملاء والإدارة).

    *   **معرف فريد للميزة:** `MOD-USER-MGMT-FEAT-009` (مستمد من `FEAT-USER-009`)
    *   **اسم الميزة/الوظيفة:** إدارة العملاء (قائمة، إضافة/تعديل، عرض تفاصيل) (مدير)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين مديري النظام والموظفين المصرح لهم من عرض وإدارة بيانات العملاء المسجلين عبر لوحة تحكم الإدارة Dash.
        *   **أنواع المستخدمين المتفاعلين:** مديرو النظام (USER-TYPE-003)، الموظفون (USER-TYPE-004) (بصلاحيات محددة).
        *   **المدخلات المطلوبة للميزة (عبر واجهات لوحة تحكم الإدارة Dash):**
            *   **لعرض قائمة العملاء:**
                *   فلاتر: الاسم (بحث جزئي)، البريد الإلكتروني (بحث جزئي)، رقم الجوال (بحث جزئي)، تاريخ التسجيل (نطاق)، الحالة (نشط/غير نشط/محظور/بانتظار التحقق).
                *   ترتيب: حسب الاسم، تاريخ التسجيل، آخر تسجيل دخول (تصاعدي/تنازلي).
                *   ترقيم الصفحات.
            *   **لإضافة عميل (إذا سمح به من الإدارة):** نفس حقول تسجيل العميل (`MOD-USER-MGMT-FEAT-001`) بالإضافة إلى تحديد حالة الحساب مباشرة.
            *   **لتعديل عميل:**
                *   جميع حقول العميل القابلة للتعديل (الاسم، رقم الجوال - مع التحقق من التفرد إذا تغير).
                *   تغيير حالة الحساب (نشط، غير نشط، محظور).
                *   إعادة إرسال OTP التحقق لرقم الجوال.
                *   تفعيل/تعطيل ميزة "رشح عميل" (`can_refer_customer` boolean).
                *   (اختياري) إضافة ملاحظات إدارية على ملف العميل.
                *   (اختياري) إعادة تعيين كلمة مرور العميل (توليد كلمة مرور مؤقتة وإرسالها أو إجبار المستخدم على تغييرها عند تسجيل الدخول التالي).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (في لوحة تحكم الإدارة Dash):**
            1.  **عرض قائمة العملاء:**
                *   جدول يعرض العملاء مع البيانات الرئيسية (معرف، الاسم، البريد، الجوال، تاريخ التسجيل، آخر تسجيل دخول، الحالة، عدد الطلبات).
                *   تطبيق الفلاتر والترتيب والترقيم.
                *   أزرار إجراءات لكل صف: عرض التفاصيل، تعديل، (تغيير الحالة السريع: تفعيل/تعطيل/حظر).
            2.  **عرض صفحة تفاصيل العميل:**
                *   عرض جميع معلومات العميل (الشخصية، الحساب).
                *   تبويب لعرض سجل طلبات العميل (من `MOD-ORDER-MGMT`).
                *   تبويب لعرض سجل ترشيحات العميل (إذا استخدم الميزة).
                *   (اختياري) تبويب لسجل التواصل مع العميل (إذا تم تطبيق نظام CRM بسيط).
            3.  **إضافة عميل جديد (إذا كان مسموحًا به):** نموذج لإدخال بيانات العميل.
            4.  **تعديل بيانات العميل:** نموذج يعرض بيانات العميل الحالية للسماح بالتعديل.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** إدارة فعالة لبيانات وحسابات العملاء من لوحة تحكم الإدارة Dash.
        *   **قواعد العمل:**
            *   يجب تسجيل جميع التغييرات الهامة على حسابات العملاء (مثل تغيير الحالة، تعديل البيانات الأساسية) في سجل تدقيق النظام (إذا تم تطبيق `MOD-CORE-AUDIT-LOGGING`).
            *   لا يمكن حذف حساب عميل بشكل دائم إذا كان لديه طلبات مرتبطة (يفضل التعطيل أو الحذف الناعم).
    *   **التبعيات:** `MOD-ORDER-MGMT` (لعرض طلبات العميل)، `MOD-DASHBOARD`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية (للإدارة).

    *   **معرف فريد للميزة:** `MOD-USER-MGMT-FEAT-010` (مستمد من `FEAT-USER-010`)
    *   **اسم الميزة/الوظيفة:** إدارة الموظفين (قائمة، إضافة/تعديل، أدوار وصلاحيات)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين مديري النظام من إدارة حسابات الموظفين وتعيين الأدوار والصلاحيات لهم للوصول إلى وظائف لوحة تحكم الإدارة Dash.
        *   **أنواع المستخدمين المتفاعلين:** مديرو النظام (USER-TYPE-003).
        *   **المدخلات المطلوبة للميزة (عبر واجهات لوحة تحكم الإدارة Dash - قسم "إدارة الموظفين"):**
            *   **لإضافة/تعديل موظف:**
                *   الاسم الكامل (نص، مطلوب).
                *   البريد الإلكتروني (بريد إلكتروني صالح، مطلوب، فريد بين الموظفين).
                *   (اختياري) رقم الجوال (رقم سعودي صالح، فريد بين الموظفين).
                *   كلمة المرور (عند الإضافة: النظام يولد كلمة مرور مؤقتة قوية أو يرسل رابط تعيين كلمة مرور. عند التعديل: خيار لإعادة تعيين كلمة المرور).
                *   الدور/الأدوار المعينة (قائمة متعددة الاختيار من الأدوار المعرفة، مطلوب دور واحد على الأقل).
                *   الحالة (قائمة اختيار: نشط/غير نشط، مطلوب).
            *   **لإدارة الأدوار (`spatie/laravel-permission`):**
                *   اسم الدور (نص، مطلوب، فريد، مثال: "مدير مبيعات", "مسؤول محتوى").
                *   (اختياري) اسم عرض للدور (لتسمية أوضح في الواجهة).
                *   قائمة الصلاحيات المرتبطة بالدور (قائمة checkbox من جميع الصلاحيات المعرفة في النظام).
            *   **لإدارة الصلاحيات (Permissions - عادة تُعرف في الكود، ولكن تُعرض هنا لربطها بالأدوار):**
                *   عرض قائمة بجميع الصلاحيات المعرفة في النظام (grouped by module or category).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (في لوحة تحكم الإدارة Dash):**
            1.  **إدارة الموظفين:**
                *   واجهة لعرض قائمة الموظفين (الاسم، البريد، الأدوار، الحالة) مع فلاتر وبحث.
                *   نموذج لإضافة موظف جديد. عند الحفظ، يتم إرسال بريد إلكتروني للموظف بالبيانات أو برابط لتعيين كلمة المرور.
                *   نموذج لتعديل بيانات الموظف، أدواره، وحالته.
            2.  **إدارة الأدوار (Roles):**
                *   واجهة لعرض قائمة الأدوار الموجودة.
                *   نموذج لإضافة دور جديد.
                *   نموذج لتعديل دور موجود وتحديد الصلاحيات الممنوحة لهذا الدور من قائمة جميع الصلاحيات المتاحة (التي يتم جلبها من النظام).
            3.  تطبيق الصلاحيات على مستوى الواجهات في لوحة التحكم Dash (إخفاء/تعطيل عناصر التحكم، القوائم الجانبية الديناميكية) وعلى مستوى الـ Backend (باستخدام Middleware و Gates/Policies في Laravel).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** نظام إدارة موظفين وأدوار وصلاحيات فعال وآمن.
        *   **قواعد العمل:**
            *   يجب أن يكون هناك دور "مدير نظام" (Super Admin) بصلاحيات كاملة لا يمكن تعديل صلاحياته أو حذفه.
            *   يجب أن تكون أسماء الصلاحيات واضحة ومرتبطة بالوظائف والموديولات (مثال: `cars.view`, `cars.create`, `cars.edit`, `cars.delete`, `orders.view_all`, `orders.edit_status`).
            *   **معالجة الفجوة `GAP-USER-ROLES-DETAIL-001`:** سيتم تحديد قائمة أولية من الأدوار المقترحة والصلاحيات المرتبطة بها كجزء من هذه الميزة. أمثلة للأدوار:
                *   **مدير عام (Super Admin):** كل الصلاحيات.
                *   **مدير مبيعات:** صلاحيات على موديولات السيارات، الطلبات، العملاء، العروض، تقارير المبيعات.
                *   **موظف مبيعات:** صلاحيات محدودة على السيارات (عرض)، الطلبات (عرض طلباته، تحديث حالات معينة)، العملاء (عرض، إضافة).
                *   **مسؤول محتوى:** صلاحيات على موديول CMS، إدارة بعض جوانب كتالوج السيارات (الأوصاف، الصور).
                *   **موظف خدمة عملاء:** صلاحيات على عرض الطلبات، التواصل مع العملاء (إذا تم تطبيق CRM)، إدارة طلبات الخدمات.
    *   **التبعيات:** `spatie/laravel-permission` (أو حزمة مشابهة)، `MOD-DASHBOARD`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية (للإدارة).

---
#### **1.3. موديول: `CarCatalog` (كتالوج السيارات)**
*   **معرف الموديول:** `MOD-CAR-CATALOG`
*   **اسم الموديول:** كتالوج السيارات (CarCatalog)
*   **وصف موجز للموديول:** مسؤول عن إدارة جميع البيانات المتعلقة بالسيارات الجديدة، بما في ذلك الماركات، الموديلات، السنوات، الألوان، الميزات، المواصفات، الصور، وإدارة السيارات نفسها وعرضها. يخدم هذا الموديول بشكل مباشر المخرجات النهائية المتمثلة في الموقع الإلكتروني الفعال، تطبيق Flutter، ولوحة التحكم الاحترافية (لعرض وإدارة السيارات).
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-001` (مستمد من `FEAT-CAR-001`)
    *   **اسم الميزة/الوظيفة:** عرض قائمة السيارات الجديدة (مع صور ومواصفات وأسعار)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين من تصفح قائمة بالسيارات الجديدة المتاحة في المعرض عبر الموقع العام وتطبيق Flutter، وعرضها للمراجعة في لوحة تحكم الإدارة Dash.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (عبر الموقع العام وتطبيق Flutter)، USER-TYPE-003, USER-TYPE-004 (عبر لوحة تحكم الإدارة Dash).
        *   **المدخلات المطلوبة للميزة (كفلاتر/خيارات عرض يختارها المستخدم أو تطبقها الإدارة):**
            *   معايير الفلترة (راجع `MOD-CAR-CATALOG-FEAT-002`).
            *   خيارات الترتيب (مثل: السعر من الأقل للأعلى/الأعلى للأقل، الأحدث إضافة، الأكثر مشاهدة - إذا تم تتبعها).
            *   ترقيم الصفحات (رقم الصفحة، عدد العناصر لكل صفحة - configurable).
            *   (للإدارة) فلتر حسب حالة السيارة (متوفرة، محجوزة، مباعة، إلخ).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  استرجاع قائمة السيارات من قاعدة البيانات (`cars` table) بناءً على معايير الفلترة والترتيب والترقيم وحالة النشاط (للعرض العام).
            2.  لكل سيارة في القائمة، يتم عرض المعلومات التالية (كبطاقة سيارة أو صف في جدول):
                *   الصورة الرئيسية للسيارة (محسنة للويب).
                *   اسم السيارة الكامل (ماركة - موديل - سنة الصنع - (اختياري) اسم الفئة/الطراز).
                *   السعر (واضح، مع توضيح ما إذا كان شاملاً الضريبة أو لا، أو عرض السعر قبل وبعد الضريبة).
                *   **أبرز المواصفات (Brief Specs):** عرض 3-4 مواصفات رئيسية (مثل: نوع ناقل الحركة، نوع الوقود، عدد الأبواب، قوة المحرك (حصان)). **آلية التحديد:** يتم تحديد مجموعة مواصفات رئيسية (حتى 4) من قبل الإدارة بشكل عام للنظام، أو يتم تحديدها لكل فئة سيارة (إذا تم تطبيق فئات). الافتراضي هو عرض: ناقل الحركة، نوع الوقود، سنة الصنع، قوة المحرك.
                *   زر "عرض التفاصيل" أو رابط للانتقال إلى صفحة تفاصيل السيارة (`MOD-CAR-CATALOG-FEAT-003`).
                *   (للمستخدم المسجل) زر "أضف للمفضلة" / "إزالة من المفضلة" (`MOD-CAR-CATALOG-FEAT-005`).
                *   (اختياري) زر "قارن" (`MOD-CAR-CATALOG-FEAT-006`).
                *   (اختياري) شارات مثل "مميزة" (`FEAT-CAR-017`), "عرض خاص" (إذا كانت السيارة ضمن عرض نشط من `MOD-PROMO-MGMT`), "جديدة".
            3.  عرض العدد الإجمالي للنتائج وأدوات التحكم في ترقيم الصفحات.
            4.  **في لوحة تحكم الإدارة Dash:** يعرض الجدول أعمدة إضافية مثل (رقم الهيكل VIN، حالة السيارة، تاريخ الإضافة، زر "تعديل"، زر "حذف/تعطيل").
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** عرض قائمة سيارات متجاوبة، مرتبة، قابلة للفلترة، وغنية بالمعلومات الأساسية.
        *   **قواعد العمل:**
            *   في الواجهات العامة (الموقع والتطبيق)، يتم عرض السيارات التي حالتها "متوفرة" و "نشطة" فقط.
            *   يجب أن تكون الصور محسنة للويب لضمان سرعة التحميل.
            *   الأسعار يجب أن تكون دقيقة ومحدثة.
    *   **التبعيات:** `MOD-CAR-CATALOG-FEAT-002` (نظام الفلترة)، `MOD-CAR-CATALOG-FEAT-003` (صفحة التفاصيل)، `MOD-CAR-CATALOG-FEAT-005` (المفضلة)، `MOD-PROMO-MGMT` (لشارات العروض).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-002`, `OBJ-003`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، لوحة تحكم احترافية، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-002` (مستمد من `FEAT-CAR-002`)
    *   **اسم الميزة/الوظيفة:** نظام فلترة متقدم للسيارات
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين من تضييق نطاق قائمة السيارات المعروضة بناءً على معايير متعددة في الموقع العام وتطبيق Flutter.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001.
        *   **المدخلات المطلوبة للميزة (كخيارات فلترة يختارها المستخدم من واجهة الفلاتر):**
            *   الماركة (قائمة منسدلة أو checkboxes، اختيار متعدد أو واحد).
            *   الموديل (قائمة منسدلة أو checkboxes، تعتمد ديناميكيًا على الماركة/الماركات المختارة، اختيار متعدد أو واحد).
            *   سنة الصنع (نطاق سنوات باستخدام slider، أو قائمة سنوات محددة checkboxes).
            *   السعر (نطاق سعري min-max باستخدام sliders أو حقول إدخال).
            *   اللون الخارجي (قائمة ألوان checkboxes مع عرض عينة اللون).
            *   نوع ناقل الحركة (قائمة checkboxes: أوتوماتيك، يدوي، CVT، إلخ).
            *   نوع الوقود (قائمة checkboxes: بنزين، ديزل، هايبرد، كهرباء).
            *   (اختياري) عدد الأبواب (قائمة checkboxes: 2، 4، 5).
            *   (اختياري) فئات السيارات (إذا تم تعريفها ككيان مستقل، مثل: سيدان، SUV، شاحنة صغيرة - قائمة checkboxes).
            *   (اختياري) ميزات محددة (قائمة checkboxes من أبرز الميزات القابلة للفلترة مثل: فتحة سقف، نظام ملاحة، كاميرا خلفية).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  عرض واجهة الفلاتر (عادة في شريط جانبي أو قائمة منسدلة) مع الخيارات المتاحة.
            2.  عند تطبيق أي فلتر من قبل المستخدم (سواء بالاختيار أو بإلغاء الاختيار أو بتغيير نطاق):
                *   يتم إرسال القيم المختارة إلى الـ Backend (عادة عبر AJAX للويب، أو كـ query parameters لـ API).
                *   يجب أن يكون هناك زر "تطبيق الفلاتر" واضح، أو يتم التحديث تلقائيًا بعد كل تغيير (debounced).
            3.  الـ Backend يقوم ببناء استعلام ديناميكي لقاعدة البيانات (`cars` table والفهارس المرتبطة) بناءً على جميع الفلاتر المطبقة.
            4.  يتم تحديث قائمة السيارات المعروضة (`MOD-CAR-CATALOG-FEAT-001`) لتعكس النتائج المفلترة، بما في ذلك تحديث عدد النتائج الإجمالي والترقيم.
            5.  **Dynamic Facet Counts:** يجب تحديث عدد الخيارات المتاحة داخل كل فلتر ليعكس عدد السيارات التي ستظهر إذا تم اختيار هذا الخيار مع الفلاتر الأخرى المطبقة حاليًا (مثال: إذا اختار المستخدم "تويوتا"، يعرض فلتر الموديلات موديلات تويوتا فقط مع عدد السيارات المتاحة لكل موديل).
            6.  توفير زر "إعادة تعيين الفلاتر" أو "مسح الكل" للعودة إلى العرض الافتراضي غير المفلتر.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** قائمة سيارات مفلترة بدقة وفورية حسب اختيار المستخدم، مع تحديث ديناميكي لخيارات الفلترة.
        *   **قواعد العمل:**
            *   عند اختيار ماركة، يجب تحديث قائمة الموديلات لتعرض موديلات تلك الماركة فقط.
            *   الفلاتر يجب أن تعمل بشكل تراكمي (AND logic بين مجموعات الفلاتر المختلفة، و OR logic داخل نفس مجموعة الفلتر إذا كانت checkboxes).
            *   يجب أن تكون واجهة الفلاتر سهلة الاستخدام ومتجاوبة.
    *   **التبعيات:** `MOD-CAR-CATALOG-FEAT-001`, وجداول البيانات الوصفية للسيارات (ماركات، موديلات، ألوان، إلخ).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-003` (مستمد من `FEAT-CAR-003`)
    *   **اسم الميزة/الوظيفة:** صفحة تفاصيل السيارة (صور، مواصفات، أزرار إجراءات)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** عرض معلومات شاملة ومفصلة عن سيارة محددة لتمكين المستخدم من اتخاذ قرار الشراء.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (عبر الموقع العام وتطبيق Flutter)، USER-TYPE-003, USER-TYPE-004 (عبر لوحة تحكم الإدارة Dash كمعاينة أو للعرض).
        *   **المدخلات المطلوبة للميزة:** معرف السيارة (Car ID أو slug فريد).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (محتويات الصفحة وتصميمها المقترح):**
            1.  استرجاع بيانات السيارة المحددة وكافة بياناتها المرتبطة من قاعدة البيانات.
            2.  **العنوان الرئيسي:** اسم السيارة الكامل (ماركة - موديل - سنة - فئة).
            3.  **معرض الصور والفيديو (Image/Video Gallery):**
                *   عرض صورة رئيسية كبيرة وواضحة.
                *   شريط صور مصغرة (thumbnails) لباقي صور السيارة (خارجية، داخلية، تفاصيل).
                *   عند النقر على صورة مصغرة، يتم تحديث الصورة الرئيسية.
                *   إمكانية تكبير الصور (lightbox/modal view) مع التنقل بين الصور المكبرة.
                *   (اختياري) دعم عرض فيديو ترويجي للسيارة (من رابط YouTube/Vimeo أو ملف مرفوع).
            4.  **قسم المعلومات الأساسية والسعر:**
                *   السعر (واضح، شامل الضريبة أو مع تفصيل، وإذا كان هناك سعر عرض خاص يتم عرضه بشكل بارز مع السعر الأصلي مشطوبًا).
                *   (اختياري) إمكانية حساب الدفعة الشهرية التقديرية للتمويل (يتطلب مدخلات بسيطة من المستخدم مثل الدفعة الأولى وعدد السنوات).
                *   أزرار الإجراءات الرئيسية:
                    *   "اطلبها الآن" / "احجزها الآن" (ينقل إلى عملية الشراء `MOD-ORDER-MGMT-FEAT-001`).
                    *   "أضف للمفضلة" / "إزالة من المفضلة" (`MOD-CAR-CATALOG-FEAT-005`).
                    *   "أضف للمقارنة" / "إزالة من المقارنة" (`MOD-CAR-CATALOG-FEAT-006`).
                *   أزرار المشاركة والتحميل:
                    *   "شارك" (يفتح خيارات المشاركة على الشبكات الاجتماعية، واتساب، نسخ الرابط - `MOD-CAR-CATALOG-FEAT-007`).
                    *   "تحميل المواصفات (PDF)" (`MOD-CAR-CATALOG-FEAT-004`).
                *   أزرار التواصل: "اتصل بنا"، "تواصل عبر واتساب" (تفتح التطبيقات المعنية بأرقام محددة مسبقًا).
            5.  **قسم الوصف التفصيلي (Description):** عرض الوصف العام للسيارة المدخل من الإدارة (من `MOD-CAR-CATALOG-FEAT-018`).
            6.  **قسم المواصفات الفنية (Technical Specifications):**
                *   عرض المواصفات في تبويبات أو أقسام منطقية وواضحة (مثل: المحرك والأداء، الأبعاد والوزن، ناقل الحركة، استهلاك الوقود، نظام التعليق والمكابح، الإطارات والعجلات).
                *   لكل مواصفة: اسم المواصفة، القيمة.
            7.  **قسم الميزات (الكماليات) (Features):**
                *   عرض الميزات المتوفرة في السيارة، مقسمة إلى فئات (مثل: الأمان والسلامة، الراحة والرفاهية الداخلية، الميزات الخارجية، الأنظمة الترفيهية والمعلوماتية).
                *   لكل ميزة: اسم الميزة (مع أيقونة مميزة إن أمكن).
            8.  **تفاصيل العرض (إذا كانت السيارة ضمن عرض ترويجي):**
                *   عرض اسم العرض، مدته، وأي شروط خاصة به (من `MOD-PROMO-MGMT`).
            9.  **تفاصيل "خدمات ما بعد البيع المجانية" (بقيمة 500 ريال):**
                *   إذا كانت السيارة تشملها، يتم عرض تفاصيل هذه الخدمات. **إجابة لسؤال 1 من `00-FR.md`:** الخدمات ستكون قائمة ثابتة من 3-5 خدمات (مثال: "غسيل مجاني أول شهر"، "تلميع أساسي عند الاستلام"، "خصم 10% على أول صيانة") يتم تحديدها من إعدادات النظام (`MOD-CORE-FEAT-003`) وتُعرض هنا إذا كانت السيارة مؤهلة.
            10. **(اختياري) قسم السيارات المشابهة/البديلة:** عرض قائمة بسيطة (صور مصغرة وأسماء) لسيارات أخرى قد تهم المستخدم (بناءً على الماركة، الفئة السعرية، أو نوع السيارة).
            11. **(اختياري) قسم تقييمات المستخدمين (إذا تم تطبيقه مستقبلًا).**
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** صفحة تفاصيل سيارة شاملة، جذابة، سهلة التصفح، ومحسنة للتحويل (Conversion).
        *   **قواعد العمل:**
            *   يجب أن تكون جميع المعلومات المعروضة دقيقة ومحدثة.
            *   يجب أن تكون الصفحة متجاوبة مع جميع أحجام الشاشات.
            *   الروابط وأزرار الإجراءات يجب أن تعمل بشكل صحيح.
            *   تحميل الصفحة يجب أن يكون سريعًا، مع تحميل الصور الكبيرة بشكل تدريجي (lazy loading) إن أمكن.
    *   **التبعيات:** `MOD-ORDER-MGMT-FEAT-001`, `MOD-CAR-CATALOG-FEAT-004`, `MOD-CAR-CATALOG-FEAT-005`, `MOD-CAR-CATALOG-FEAT-006`, `MOD-CAR-CATALOG-FEAT-007`, `MOD-PROMO-MGMT`, `MOD-CORE-FEAT-003`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-004` (مستمد من `FEAT-CAR-004`)
    *   **اسم الميزة/الوظيفة:** تحميل مواصفات السيارة (PDF)
    *   **الأولوية:** متوسطة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين من تحميل ملف PDF يحتوي على المواصفات الكاملة والتفصيلية للسيارة المعروضة.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (عبر الموقع العام وتطبيق Flutter).
        *   **المدخلات المطلوبة للميزة:** معرف السيارة (Car ID).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  عندما ينقر المستخدم على زر "تحميل المواصفات (PDF)" في صفحة تفاصيل السيارة.
            2.  يقوم النظام باسترجاع جميع بيانات السيارة (المعلومات الأساسية، الوصف، المواصفات الفنية المفصلة، قائمة الميزات، الصورة الرئيسية، السعر).
            3.  يتم توليد ملف PDF ديناميكيًا باستخدام حزمة Laravel مناسبة (مثل `barryvdh/laravel-dompdf` أو `spatie/browsershot` إذا كان التعقيد عاليًا ويتطلب HTML rendering دقيق).
            4.  يتم استخدام قالب Blade مصمم خصيصًا لملف PDF، يعرض المعلومات بشكل منظم وجذاب، ويتضمن:
                *   شعار المعرض وبيانات الاتصال.
                *   اسم السيارة الكامل وصورتها الرئيسية.
                *   السعر.
                *   جداول منظمة للمواصفات الفنية والميزات.
                *   الوصف النصي.
                *   تاريخ إنشاء الملف.
            5.  يتم إتاحة ملف PDF للتنزيل المباشر للمستخدم باسم ملف واضح (مثال: `Toyota-Camry-2023-Specs.pdf`).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** ملف PDF بمواصفات السيارة، قابل للتنزيل والطباعة.
        *   **قواعد العمل:**
            *   يجب أن يكون تصميم PDF احترافيًا ومتسقًا مع هوية المعرض البصرية.
            *   يجب أن يشمل الملف جميع المعلومات الهامة عن السيارة.
            *   يجب أن يدعم النص العربي بشكل كامل في PDF.
    *   **التبعيات:** حزمة توليد PDF (مثل `barryvdh/laravel-dompdf`).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-005` (مستمد من `FEAT-CAR-005`)
    *   **اسم الميزة/الوظيفة:** إضافة/إزالة السيارة للمفضلة وإدارة المفضلة
    *   **الأولوية:** عالية
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين المسجلين من حفظ السيارات التي تهمهم في قائمة مفضلة شخصية للرجوع إليها لاحقًا.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (عبر الموقع العام، تطبيق Flutter، ولوحة تحكم العميل Dash).
        *   **المدخلات المطلوبة للميزة:** معرف السيارة (Car ID)، معرف المستخدم (User ID - من الجلسة/token).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  **إضافة/إزالة من المفضلة (عبر زر في قائمة السيارات وصفحة التفاصيل):**
                *   عندما ينقر المستخدم على زر "أضف للمفضلة" (أو أيقونة قلب).
                *   يتحقق النظام إذا كان المستخدم مسجلاً دخوله. إذا لم يكن، يتم توجيهه لتسجيل الدخول أو عرض رسالة.
                *   إذا كان مسجلاً، يتحقق النظام إذا كانت السيارة مضافة مسبقًا لمفضلة هذا المستخدم.
                *   إذا لم تكن مضافة: يتم إنشاء سجل في جدول `user_favorites` (user_id, car_id, created_at). يتغير شكل الزر/الأيقونة ليعكس أنها في المفضلة (مثال: قلب ممتلئ).
                *   إذا كانت مضافة: يتم حذف السجل من `user_favorites`. يتغير شكل الزر/الأيقونة ليعكس أنها ليست في المفضلة.
                *   يتم إرجاع استجابة نجاح (مع تحديث لحالة الزر في الواجهة الأمامية).
            2.  **عرض قائمة المفضلة (في لوحة تحكم العميل Dash وتطبيق Flutter):**
                *   يتم استرجاع جميع السيارات من `user_favorites` للمستخدم الحالي.
                *   تُعرض السيارات كقائمة (بطاقات مشابهة لقائمة السيارات الرئيسية) مع الصورة، الاسم، السعر، وزر "إزالة من المفضلة"، وزر "عرض التفاصيل".
                *   إمكانية تحديد عدة سيارات من المفضلة للانتقال إلى صفحة المقارنة (`MOD-CAR-CATALOG-FEAT-006`).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** إدارة فعالة لقائمة مفضلة السيارات لكل مستخدم.
        *   **قواعد العمل:**
            *   يجب أن تكون الميزة متاحة فقط للمستخدمين المسجلين.
            *   يجب أن يكون هناك حد أقصى لعدد السيارات في المفضلة (مثلاً 50 سيارة) لتجنب مشاكل الأداء (يُحدد في إعدادات النظام).
    *   **التبعيات:** `MOD-USER-MGMT` (لمعرفة المستخدم المسجل).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-002`, `OBJ-003`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، لوحة تحكم احترافية (للعميل)، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-006` (مستمد من `FEAT-CAR-006`)
    *   **اسم الميزة/الوظيفة:** مقارنة السيارات
    *   **الأولوية:** متوسطة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين من اختيار عدة سيارات (2 إلى 4 سيارات) وعرض مواصفاتها وميزاتها جنبًا إلى جنب في جدول مقارنة واضح.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (عبر الموقع العام وتطبيق Flutter).
        *   **المدخلات المطلوبة للميزة:** قائمة بمعرفات السيارات المختارة للمقارنة (يتم تجميعها من خلال نقرات المستخدم على زر "قارن" في بطاقات السيارات أو من قائمة المفضلة).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  **آلية اختيار السيارات للمقارنة:**
                *   زر "قارن" بجانب كل سيارة في القوائم وصفحات التفاصيل.
                *   عند النقر، تتم إضافة السيارة إلى "سلة مقارنة" مؤقتة (مخزنة في session للزوار، أو في DB للمسجلين إذا كانت دائمة).
                *   عرض عدد السيارات في سلة المقارنة بشكل واضح (مثال: أيقونة مقارنة مع عداد).
                *   عندما يكون هناك سيارتان على الأقل في السلة، يظهر زر "قارن الآن".
            2.  **عرض صفحة المقارنة:**
                *   يتم استرجاع بيانات السيارات المختارة.
                *   يتم عرض جدول مقارنة:
                    *   الأعمدة: كل عمود يمثل سيارة (مع صورتها واسمها وسعرها في الأعلى).
                    *   الصفوف: تمثل المواصفات والميزات (مثل: المحرك، القوة، ناقل الحركة، الأبعاد، أبرز ميزات الأمان، أبرز ميزات الراحة).
                    *   يتم تمييز القيم المتفوقة أو المختلفة بشكل واضح (إن أمكن).
                *   إمكانية إزالة سيارة من المقارنة مباشرة من صفحة المقارنة.
                *   زر "اطلبها الآن" تحت كل سيارة في جدول المقارنة.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** جدول مقارنة واضح وسهل القراءة لمساعدة المستخدم على اتخاذ قرار.
        *   **قواعد العمل:**
            *   الحد الأدنى للمقارنة سيارتان، والحد الأقصى 4 سيارات (لتجنب ازدحام الواجهة).
            *   يجب اختيار مجموعة موحدة من المواصفات والميزات لعرضها في جدول المقارنة لضمان الاتساق.
    *   **التبعيات:** لا يوجد تبعيات مباشرة على ميزات أخرى، ولكنها تستخدم بيانات السيارات.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-007` (مستمد من `FEAT-CAR-007`)
    *   **اسم الميزة/الوظيفة:** مشاركة إعلان السيارة
    *   **الأولوية:** متوسطة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين من مشاركة رابط صفحة تفاصيل سيارة معينة عبر وسائل التواصل الاجتماعي أو تطبيقات المراسلة.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (عبر الموقع العام وتطبيق Flutter).
        *   **المدخلات المطلوبة للميزة:** رابط صفحة تفاصيل السيارة.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  في صفحة تفاصيل السيارة، يوجد زر "شارك" أو أيقونة مشاركة.
            2.  عند النقر، تظهر قائمة بخيارات المشاركة:
                *   فيسبوك (يفتح نافذة مشاركة فيسبوك مع تعبئة الرابط وعنوان الصفحة).
                *   تويتر (X) (يفتح نافذة مشاركة تويتر مع تعبئة الرابط وعنوان الصفحة).
                *   واتساب (يفتح تطبيق واتساب مع رسالة مقترحة تتضمن الرابط).
                *   نسخ الرابط (ينسخ رابط الصفحة إلى الحافظة).
                *   (اختياري) مشاركة عبر البريد الإلكتروني (يفتح برنامج البريد الافتراضي مع تعبئة الموضوع والرابط).
            3.  يجب أن تكون الروابط المشاركة نظيفة وتؤدي مباشرة إلى صفحة السيارة.
            4.  (لتحسين SEO) يجب أن تدعم الصفحة المعنية بروتوكول Open Graph (og:title, og:description, og:image) لعرض معاينة جيدة عند المشاركة.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** مشاركة رابط السيارة بنجاح عبر المنصة المختارة.
        *   **قواعد العمل:** لا يوجد.
    *   **التبعيات:** لا يوجد.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-008` (مستمد من `FEAT-CAR-008`)
    *   **اسم الميزة/الوظيفة:** إدارة ماركات السيارات (CRUD)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين مديري النظام من إدارة قائمة ماركات السيارات المستخدمة في النظام عبر لوحة تحكم الإدارة Dash.
        *   **أنواع المستخدمين المتفاعلين:** مديرو النظام (USER-TYPE-003)، الموظفون (USER-TYPE-004) (بصلاحيات محددة).
        *   **المدخلات المطلوبة للميزة (عبر واجهات لوحة تحكم الإدارة Dash - قسم "الماركات والموديلات" أو "إدارة كتالوج السيارات"):**
            *   **لإضافة/تعديل ماركة:**
                *   اسم الماركة (نص، مطلوب، فريد، مثال: تويوتا).
                *   (اختياري) شعار الماركة (ملف صورة: jpg, png, svg، أبعاد مناسبة).
                *   (اختياري) وصف مختصر للماركة.
                *   الحالة (قائمة اختيار: نشطة/غير نشطة، مطلوب).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (في لوحة تحكم الإدارة Dash):**
            1.  **عرض قائمة الماركات:** جدول يعرض (معرف، اسم الماركة، الشعار، عدد الموديلات المرتبطة، الحالة، تاريخ الإنشاء/التعديل). مع خيارات فلترة (بالاسم، بالحالة) وبحث وترقيم صفحات.
            2.  **إضافة ماركة جديدة:** نموذج لإدخال بيانات الماركة (الاسم، رفع الشعار، الوصف، الحالة). عند الحفظ، يتم التحقق من تفرد الاسم.
            3.  **تعديل ماركة موجودة:** نموذج يعرض بيانات الماركة الحالية للسماح بتعديلها.
            4.  **حذف ماركة (يفضل Soft Delete):**
                *   يجب التحقق إذا كانت الماركة مرتبطة بأي موديلات أو سيارات.
                *   إذا كانت مرتبطة، يجب منع الحذف المباشر وعرض رسالة للمستخدم (أو توفير خيار لتعطيل الماركة بدلاً من الحذف، أو نقل ارتباطات الموديلات/السيارات إذا كان ذلك منطقيًا).
                *   إذا لم تكن مرتبطة (أو تم التعامل مع الارتباطات)، يمكن السماح بالحذف (Soft Delete هو الأفضل لتجنب فقدان البيانات التاريخية).
            5.  **تغيير حالة الماركة (نشطة/غير نشطة):** يؤثر على ظهور الماركة في قوائم الاختيار عند إضافة سيارات أو في فلاتر الموقع العام.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** قائمة ماركات سيارات مُدارة ومنظمة بدقة.
        *   **قواعد العمل:**
            *   اسم الماركة يجب أن يكون فريدًا (غير حساس لحالة الأحرف عند التحقق من التفرد).
            *   لا يمكن حذف ماركة (حذف كامل) إذا كانت هناك موديلات أو سيارات مرتبطة بها وما زالت نشطة.
            *   عند رفع شعار، يجب معالجته (تحسين، تغيير حجم) وتخزينه بشكل مناسب (`spatie/laravel-medialibrary`).
    *   **التبعيات:** `MOD-DASHBOARD`, `spatie/laravel-medialibrary`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية (للإدارة).

    *   **(سيتم تفصيل باقي ميزات إدارة الكيانات المرتبطة بالسيارة مثل `FEAT-CAR-009` (الموديلات)، `FEAT-CAR-010` (الميزات/المواصفات القابلة للتعريف)، `FEAT-CAR-011` (الألوان)، `FEAT-CAR-012` (سنوات الصنع)، `FEAT-CAR-013` (أنواع ناقل الحركة)، `FEAT-CAR-014` (أنواع الوقود) كعمليات CRUD مشابهة في لوحة تحكم الإدارة Dash. هذه الكيانات ستستخدم كقوائم منسدلة أو اختيارات عند إضافة/تعديل سيارة.)**

    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-017` (مستمد من `FEAT-CAR-017`)
    *   **اسم الميزة/الوظيفة:** تحديد سيارة مميزة
    *   **الأولوية:** متوسطة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين الإدارة من تحديد سيارات معينة كـ "مميزة" ليتم عرضها بشكل بارز في أقسام خاصة في الموقع العام أو تطبيق Flutter.
        *   **أنواع المستخدمين المتفاعلين:** مديرو النظام (USER-TYPE-003)، الموظفون (USER-TYPE-004) (بصلاحيات محددة).
        *   **المدخلات المطلوبة للميزة:** معرف السيارة، قيمة منطقية (مميزة true/false).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  في نموذج إضافة/تعديل السيارة (`MOD-CAR-CATALOG-FEAT-018`) في لوحة تحكم الإدارة Dash، يوجد checkbox أو مفتاح تبديل "سيارة مميزة".
            2.  عند حفظ السيارة، يتم تخزين هذه القيمة في حقل `is_featured` (boolean) في جدول `cars`.
            3.  في الواجهات العامة (الموقع والتطبيق)، يمكن جلب وعرض السيارات التي `is_featured = true` في أقسام مخصصة (مثل: "أبرز السيارات"، "اخترناها لك").
            4.  (اختياري) يمكن أن يكون هناك حد أقصى لعدد السيارات المميزة التي يمكن عرضها في الواجهة (يُدار من إعدادات النظام).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** تمييز بعض السيارات لعرضها بشكل خاص.
        *   **قواعد العمل:** لا يوجد.
    *   **التبعيات:** `MOD-CAR-CATALOG-FEAT-018`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-002`, `OBJ-003`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، لوحة تحكم احترافية، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-018` (معرف معدل، كان XXX سابقًا)
    *   **اسم الميزة/الوظيفة:** إضافة/تعديل سيارة جديدة عبر لوحة تحكم الإدارة (Dash)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين مديري النظام والموظفين المصرح لهم من إضافة سيارات جديدة إلى كتالوج المعرض أو تعديل بيانات السيارات الموجودة من خلال واجهة Stepper في لوحة تحكم Dash.
        *   **أنواع المستخدمين المتفاعلين:** مديرو النظام (USER-TYPE-003)، الموظفون (USER-TYPE-004) (بصلاحيات محددة).
        *   **المدخلات المطلوبة للميزة (عبر Stepper المكون من 5 خطوات كما هو مقترح في `dashboard.html` و `00-FR.md`, داخل `MOD-DASHBOARD`):**
            *   **الخطوة 1: البيانات الأساسية (`basic-info-part` في Stepper)**
                *   الماركة (قائمة منسدلة من `FEAT-CAR-008` النشطة، مطلوب).
                *   الموديل (قائمة منسدلة تعتمد على الماركة المختارة، من `FEAT-CAR-009` النشطة، مطلوب).
                *   سنة الصنع (قائمة منسدلة من `FEAT-CAR-012`، مطلوب).
                *   اللون الخارجي الأساسي (قائمة منسدلة من `FEAT-CAR-011` النشطة، مطلوب).
                *   (اختياري) ألوان إضافية متاحة لنفس الطراز/السيارة (قائمة متعددة الاختيار من `FEAT-CAR-011`).
                *   رقم الهيكل (VIN) (نص، مكون من 17 حرفًا ورقمًا، غير مطلوب ولكن يُفضل إدخاله، يجب التحقق من تفرده إذا تم إدخاله، قد يستخدم للبحث عن مواصفات أساسية إذا توفرت خدمة خارجية).
                *   اسم الفئة/الطراز (نص، مثال: GLX, Limited, Sport - مطلوب، يساعد في تمييز السيارات من نفس الموديل والسنة).
                *   الوصف العام للسيارة (محرر نصوص غني Rich Text Editor، مطلوب، يسمح بوصف تفصيلي، نقاط تعداد، إلخ. الحد الأدنى 100 حرف، الحد الأقصى 5000 حرف).
            *   **الخطوة 2: المواصفات الفنية (`tech-specs-part` في Stepper)**
                *   نوع المحرك (نص، مثال: 2.5L 4-Cylinder Turbo).
                *   قوة المحرك (حصان) (رقم صحيح، مطلوب).
                *   عزم الدوران (نيوتن.متر) (رقم صحيح).
                *   نوع ناقل الحركة (قائمة منسدلة من `FEAT-CAR-013` النشطة، مطلوب).
                *   عدد السرعات (رقم صحيح، إذا كان ناقل الحركة يدوي أو أوتوماتيك تقليدي).
                *   نظام الدفع (قائمة اختيار: أمامي FWD, خلفي RWD, رباعي AWD/4WD - مطلوب).
                *   نوع الوقود (قائمة منسدلة من `FEAT-CAR-014` النشطة، مطلوب).
                *   معدل استهلاك الوقود (كم/لتر) (رقم عشري، مثال: 15.5).
                *   سعة خزان الوقود (لتر) (رقم صحيح).
                *   الأبعاد (الطول، العرض، الارتفاع - ملم) (أرقام صحيحة).
                *   قاعدة العجلات (ملم) (رقم صحيح).
                *   وزن السيارة فارغة (كجم) (رقم صحيح).
                *   عدد الأبواب (قائمة اختيار: 2, 3, 4, 5، مطلوب).
                *   عدد المقاعد (قائمة اختيار: 2, 4, 5, 7, 8، مطلوب).
                *   مقاس الإطارات (نص، مثال: 225/45R18).
                *   نوع جسم السيارة (قائمة اختيار: سيدان، هاتشباك، SUV، كوبيه، شاحنة صغيرة، فان - مطلوب).
                *   **(إمكانية إضافة حقول مواصفات إضافية من `FEAT-CAR-010` الديناميكية كأزواج مفتاح-قيمة).**
            *   **الخطوة 3: الميزات (`features-part` في Stepper)**
                *   قائمة بالميزات المتاحة (من `FEAT-CAR-010` التي يديرها المسؤول)، مقسمة إلى فئات (مثل: الأمان، الراحة، الترفيه، المظهر الخارجي، المظهر الداخلي).
                *   يتم اختيار الميزات المتوفرة في هذه السيارة المحددة (مجموعة checkboxes).
                *   (اختياري) إمكانية إضافة ميزة غير موجودة في القائمة بشكل نصي لهذه السيارة فقط (تخزن في حقل JSON خاص بالميزات المخصصة للسيارة).
            *   **الخطوة 4: الصور والفيديو (`images-part` في Stepper)**
                *   رفع صور متعددة للسيارة (باستخدام `spatie/laravel-medialibrary`).
                *   تحديد الصورة الرئيسية (Default/Cover Image).
                *   إمكانية إعادة ترتيب الصور (سحب وإفلات).
                *   إمكانية حذف الصور.
                *   قيود على حجم ونوع الملفات (كما هو محدد في `NFR-USAB-007`: 5MB لكل صورة، JPG, PNG, WEBP).
                *   (اختياري) حقل لإضافة رابط فيديو YouTube/Vimeo للسيارة.
            *   **الخطوة 5: السعر والحالة (`price-status-part` في Stepper)**
                *   سعر السيارة الأساسي (رقم عشري، مطلوب، قبل الضريبة).
                *   نسبة الضريبة المضافة (تُجلب من إعدادات النظام `MOD-CORE-FEAT-003`، قابلة للقراءة فقط).
                *   السعر الإجمالي شامل الضريبة (يُحسب تلقائيًا ويُعرض).
                *   (اختياري) سعر العرض الخاص (رقم عشري، قبل الضريبة). إذا أُدخل، يُحسب السعر الإجمالي للعرض شامل الضريبة.
                *   (اختياري) تاريخ بدء وانتهاء العرض الخاص (تقويم لاختيار التواريخ).
                *   حالة السيارة (قائمة اختيار: متوفرة، محجوزة، مباعة، قادمة قريباً، غير متوفرة - مطلوب).
                *   هل السيارة مميزة؟ (`FEAT-CAR-017`) (checkbox).
                *   (اختياري) ملاحظات داخلية للموظفين فقط (محرر نصوص بسيط).
                *   (اختياري) عدد الوحدات المتاحة من هذه السيارة بهذا اللون والمواصفات (إذا كان هناك تتبع للمخزون البسيط).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (في واجهة Stepper بلوحة تحكم Dash):**
            1.  يتم التنقل بين خطوات الـ Stepper (يجب أن يكون `linear: false` للسماح بالانتقال الحر بعد إكمال الخطوة الأولى).
            2.  في كل خطوة، يتم التحقق من صحة المدخلات المطلوبة لتلك الخطوة قبل الانتقال للتالية (أو عند محاولة الحفظ النهائي).
            3.  يمكن حفظ البيانات المدخلة في كل خطوة بشكل مؤقت (session أو تخزين مؤقت في DB) أو تجميعها حتى الخطوة الأخيرة.
            4.  عند الضغط على "حفظ السيارة" في الخطوة الأخيرة:
                *   يتم إجراء تحقق نهائي شامل على جميع البيانات المجمعة من جميع الخطوات.
                *   إذا كان تعديلًا لسيارة موجودة، يتم تحديث سجل السيارة والبيانات المرتبطة بها.
                *   إذا كانت إضافة جديدة، يتم إنشاء سجل جديد للسيارة في جدول `cars`.
                *   يتم ربط السيارة بالماركة، الموديل، اللون، المواصفات المدخلة، الميزات المختارة.
                *   يتم التعامل مع رفع الصور وتخزينها وربطها بالسيارة (باستخدام `spatie/laravel-medialibrary`).
            5.  عرض رسالة نجاح أو خطأ (مع توضيح الأخطاء إذا وجدت).
            6.  إعادة التوجيه إلى قائمة السيارات أو صفحة تفاصيل السيارة المضافة/المعدلة في لوحة التحكم Dash.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** إضافة سيارة جديدة بنجاح إلى الكتالوج أو تعديل بيانات سيارة موجودة. السيارة تصبح متاحة للعرض في الواجهات العامة إذا كانت حالتها "متوفرة" و "نشطة".
        *   **قواعد العمل:**
            *   يجب أن تكون جميع الحقول المطلوبة (*) ممتلئة وصحيحة.
            *   يجب أن تكون البيانات متسقة (مثلاً، لا يمكن أن يكون تاريخ انتهاء العرض قبل تاريخ بدئه).
            *   التعامل مع رفع الصور يجب أن يكون فعالًا وآمنًا.
            *   يجب أن تكون واجهة الـ Stepper سهلة الاستخدام وتوفر تغذية راجعة واضحة للمستخدم.
    *   **التبعيات:** `MOD-DASHBOARD` (لتوفير واجهة Stepper)، `MOD-CAR-CATALOG-FEAT-008` (ماركات)، `FEAT-CAR-009` (موديلات)، `FEAT-CAR-010` (ميزات)، `FEAT-CAR-011` (ألوان)، `FEAT-CAR-012` (سنوات)، `FEAT-CAR-013` (ناقل حركة)، `FEAT-CAR-014` (وقود)، `spatie/laravel-medialibrary`, `MOD-CORE-FEAT-003` (لنسبة الضريبة).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002` (بناء لوحة تحكم)، `OBJ-004` (بنية خلفية).
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية (لإدارة السيارات)، موقع إلكتروني فعال (لعرض السيارات المضافة).

    *   **معرف فريد للميزة:** `MOD-CAR-CATALOG-FEAT-015` (مستمد من `FEAT-CAR-015`)
    *   **اسم الميزة/الوظيفة:** استيراد/تصدير بيانات السيارات (Excel/CSV)
    *   **الأولوية:** متوسطة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين مديري النظام من استيراد مجموعة كبيرة من بيانات السيارات دفعة واحدة، أو تصدير بيانات السيارات الموجودة من خلال لوحة تحكم الإدارة Dash.
        *   **أنواع المستخدمين المتفاعلين:** مديرو النظام (USER-TYPE-003).
        *   **المدخلات المطلوبة للميزة (عبر واجهة في لوحة تحكم الإدارة Dash):**
            *   **للاستيراد:**
                *   ملف Excel (xlsx) أو CSV بتركيبة أعمدة محددة مسبقًا (يجب توفير قالب للمستخدم قابل للتنزيل).
                *   (اختياري) خيار "تحديث السيارات الموجودة إذا تطابق رقم الهيكل (VIN)" أو "تجاهل السجلات المكررة" أو "إنشاء سجلات جديدة فقط".
            *   **للتصدير:**
                *   (اختياري) فلاتر لتحديد السيارات المراد تصديرها (مثل: حسب الماركة، الحالة، تاريخ الإضافة).
                *   (اختياري) اختيار الأعمدة المراد تضمينها في ملف التصدير.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  **الاستيراد (في لوحة تحكم Dash):**
                *   واجهة لرفع ملف الاستيراد وتحديد خيارات الاستيراد.
                *   التحقق من صحة تنسيق الملف (نوع الملف، حجم الملف).
                *   قراءة رؤوس الأعمدة والتحقق من تطابقها مع القالب المتوقع.
                *   معالجة كل صف في الملف (يفضل استخدام Jobs و Queues للمعالجة في الخلفية للملفات الكبيرة):
                    *   التحقق من صحة البيانات في كل حقل (أنواع البيانات، القيم المطلوبة، القيم المسموح بها للقوائم المنسدلة).
                    *   البحث عن/إنشاء الكيانات المرتبطة (ماركات، موديلات، ألوان، إلخ.) إذا لم تكن موجودة (مع خيار للإبلاغ عن خطأ إذا كانت مطلوبة ومفقودة ولا يمكن إنشاؤها تلقائيًا).
                    *   إنشاء/تحديث سجل السيارة بناءً على رقم الهيكل (VIN) وخيارات الاستيراد.
                    *   تسجيل أي أخطاء أو تحذيرات لكل صف.
                *   بعد اكتمال المعالجة (أو بشكل دوري إذا كانت في الخلفية)، يتم عرض تقرير للمستخدم بنتيجة عملية الاستيراد (عدد السجلات الناجحة، عدد السجلات التي تم تحديثها، عدد الأخطاء مع تفاصيلها ورقم الصف).
            2.  **التصدير (في لوحة تحكم Dash):**
                *   واجهة لتحديد معايير التصدير (فلاتر، أعمدة).
                *   استرجاع بيانات السيارات المحددة.
                *   توليد ملف Excel/CSV باستخدام حزمة (مثل `maatwebsite/excel`) يحتوي على بيانات السيارات المطلوبة.
                *   إتاحة الملف للتنزيل المباشر للمستخدم.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** استيراد/تصدير بيانات السيارات بنجاح وبكفاءة.
        *   **قواعد العمل:**
            *   يجب توفير قالب واضح وموثق لملف الاستيراد.
            *   يجب أن تكون عملية الاستيراد قادرة على التعامل مع الأخطاء في البيانات المدخلة بشكل رشيق (تسجيل الخطأ والمتابعة أو التوقف مع تقرير واضح).
            *   عمليات الاستيراد الكبيرة يجب ألا تؤثر على أداء النظام العام (استخدام Queues).
    *   **التبعيات:** `MOD-DASHBOARD`, `maatwebsite/excel` (أو حزمة مشابهة)، Laravel Queues.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية (للإدارة).

---
#### **1.4. موديول: `OrderManagement` (إدارة الطلبات)**
*   **معرف الموديول:** `MOD-ORDER-MGMT`
*   **اسم الموديول:** إدارة الطلبات (OrderManagement)
*   **وصف موجز للموديول:** مسؤول عن إدارة عمليات الشراء (كاش وتمويل)، طلبات الحجز، تتبع حالة الطلبات، إدارة المستندات المتعلقة بالطلبات، وعملية "اطلب سيارتك". يخدم هذا الموديول بشكل مباشر المخرجات النهائية عبر تمكين عمليات الشراء في الموقع العام وتطبيق Flutter، وإدارة هذه الطلبات من خلال لوحة تحكم الإدارة Dash، وعرضها للعميل في لوحة تحكمه.
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-ORDER-MGMT-FEAT-001` (مستمد من `FEAT-ORDER-001`)
    *   **اسم الميزة/الوظيفة:** عملية شراء السيارة (كاش أو تمويل) - الواجهة الأمامية
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** توفير واجهة مستخدم سلسة للعملاء لتقديم طلبات شراء السيارات الجديدة، سواء بالدفع الكاش (مبلغ حجز) أو بتقديم طلب تمويل.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (عبر الموقع العام وتطبيق Flutter).
        *   **المدخلات المطلوبة للميزة:** اختيار السيارة (Car ID)، اختيار نوع الشراء (كاش/تمويل)، بيانات شخصية إضافية (إذا لم تكن مسجلة بالكامل في ملف المستخدم)، موافقات.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  يبدأ المستخدم العملية من صفحة تفاصيل السيارة (`MOD-CAR-CATALOG-FEAT-003`) بالضغط على زر "اطلبها الآن" أو "احجزها الآن".
            2.  يتحقق النظام إذا كان المستخدم مسجلاً دخوله. إذا لم يكن، يتم توجيهه لصفحة التسجيل/الدخول أولاً، ثم يعود لإكمال العملية.
            3.  يعرض النظام خيارين واضحين: "شراء كاش (دفع مبلغ حجز)" أو "طلب تمويل".
            4.  بناءً على اختيار المستخدم، يتم توجيهه إلى النموذج المناسب:
                *   نموذج طلب السيارة كاش متعدد المراحل (`MOD-ORDER-MGMT-FEAT-003`).
                *   نموذج طلب التمويل متعدد المراحل (`MOD-ORDER-MGMT-FEAT-004`).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** انتقال المستخدم إلى الخطوات التفصيلية لعملية الشراء المختارة.
        *   **قواعد العمل:** يجب أن يكون المستخدم مسجلاً دخوله لإتمام الطلب.
    *   **التبعيات:** `MOD-USER-MGMT`, `MOD-CAR-CATALOG-FEAT-003`, `MOD-ORDER-MGMT-FEAT-003`, `MOD-ORDER-MGMT-FEAT-004`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-ORDER-MGMT-FEAT-002` (مستمد من `FEAT-ORDER-002`)
    *   **اسم الميزة/الوظيفة:** صفحة "كيف أشتريها؟" (كاش/تمويل)
    *   **الأولوية:** متوسطة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** توفير صفحة معلوماتية تشرح للمستخدمين خطوات عملية الشراء سواء كاش أو تمويل، والمستندات المطلوبة لكل منهما.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (عبر الموقع العام وتطبيق Flutter).
        *   **المدخلات المطلوبة للميزة:** لا يوجد مدخلات، هي صفحة محتوى ثابت تُدار من `MOD-CMS`.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  عرض محتوى يشرح عملية الشراء كاش (الخطوات، المستندات المطلوبة، طريقة دفع الحجز، إكمال المبلغ في المعرض).
            2.  عرض محتوى يشرح عملية طلب التمويل (الخطوات، المستندات المطلوبة، آلية جمع البيانات، دور المعرض في المساعدة).
            3.  روابط لأقسام ذات صلة (مثل الأسئلة الشائعة، اتصل بنا).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** صفحة معلومات واضحة ومفيدة للعملاء.
        *   **قواعد العمل:** يجب أن يكون المحتوى محدثًا ودقيقًا.
    *   **التبعيات:** `MOD-CMS` (لإدارة محتوى الصفحة).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-ORDER-MGMT-FEAT-003` (مستمد من `FEAT-ORDER-003`)
    *   **اسم الميزة/الوظيفة:** نموذج طلب السيارة كاش (متعدد المراحل)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين العميل من إكمال طلب شراء سيارة بالدفع الكاش (حجز أولي) عبر سلسلة من الخطوات الواضحة والمنظمة.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (عبر الموقع العام وتطبيق Flutter).
        *   **المدخلات المطلوبة للميزة (لكل مرحلة - مع التحقق من صحة البيانات في كل مرحلة):**
            *   **المرحلة 1 (البيانات الشخصية):** (يتم ملء بعضها تلقائيًا من ملف المستخدم إذا كان مسجلاً)
                *   الاسم الكامل (كما في الهوية) (نص، مطلوب).
                *   رقم الهوية/الإقامة (نص، 10 أرقام، مطلوب).
                *   تاريخ الميلاد (ميلادي) (تاريخ، مطلوب، يجب أن يكون العمر فوق 18).
                *   الجنسية (قائمة منسدلة بالدول، مطلوب).
                *   العنوان الوطني الكامل (المدينة، الحي، الشارع، رقم المبنى، الرمز البريدي) (نصوص، مطلوبة).
                *   البريد الإلكتروني (يُجلب من ملف المستخدم، للقراءة فقط).
                *   رقم الجوال (يُجلب من ملف المستخدم، للقراءة فقط).
            *   **المرحلة 2 (تفاصيل السيارة والحجز):**
                *   عرض ملخص للسيارة المختارة (صورة، اسم، سنة، لون، سعر أساسي، سعر شامل الضريبة).
                *   مبلغ الحجز المطلوب دفعه أونلاين (يُعرض بوضوح، يُحدد من إعدادات النظام `MOD-CORE-FEAT-003` أو لكل سيارة).
            *   **المرحلة 3 (اختيار طريقة دفع الحجز):**
                *   قائمة ببوابات/طرق الدفع المتاحة (مثال: بطاقة مدى، فيزا، ماستركارد - يتم إدارتها من `FEAT-ADMIN-003`).
                *   (اختياري) خيار "الدفع في المعرض" لمبلغ الحجز (إذا كانت هذه سياسة مسموح بها).
            *   **المرحلة 4 (المستندات المطلوبة - إذا لم يتم اختيار "الدفع في المعرض" للحجز):** (راجع `MOD-ORDER-MGMT-FEAT-006`)
                *   رفع نسخة واضحة من الهوية/الإقامة (وجه أمامي وخلفي كملفين منفصلين أو مدمجين).
                *   رفع نسخة واضحة من رخصة القيادة سارية المفعول.
                *   تعليمات واضحة حول صيغ الملفات المسموح بها (PDF, JPG, PNG) والحد الأقصى للحجم (2MB لكل ملف).
            *   **المرحلة 5 (المراجعة والتأكيد):**
                *   عرض ملخص كامل للطلب (جميع البيانات الشخصية، تفاصيل السيارة، مبلغ الحجز، طريقة الدفع المختارة، قائمة المستندات المرفوعة).
                *   Checkbox للموافقة على الشروط والأحكام الخاصة بعملية الشراء (مع رابط للصفحة).
                *   Checkbox للإقرار بصحة جميع البيانات المقدمة.
                *   زر "تأكيد الحجز والدفع" (أو "تأكيد الحجز" إذا كان الدفع في المعرض).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  التحقق من صحة المدخلات في كل مرحلة قبل السماح بالانتقال للمرحلة التالية.
            2.  حفظ البيانات المدخلة في كل مرحلة (في الجلسة أو مؤقتًا في DB) للسماح بالرجوع للمراحل السابقة دون فقدان البيانات.
            3.  عند التأكيد النهائي في المرحلة 5:
                *   إنشاء سجل طلب جديد في جدول `orders` بحالة أولية (مثال: "بانتظار دفع الحجز" أو "بانتظار مراجعة الإدارة" إذا كان الدفع في المعرض).
                *   ربط الطلب بالعميل والسيارة.
                *   تخزين جميع البيانات المدخلة (الشخصية، تفاصيل الحجز).
                *   ربط المستندات المرفوعة بالطلب (باستخدام `spatie/laravel-medialibrary` مع مجموعة مخصصة للطلب).
                *   إذا تم اختيار الدفع أونلاين: توجيه المستخدم إلى بوابة الدفع المختارة مع مبلغ الحجز (`MOD-ORDER-MGMT-FEAT-005`).
                *   إذا تم اختيار الدفع في المعرض: عرض رسالة تأكيد الحجز المبدئي مع تعليمات للمتابعة. إرسال إشعار للعميل والإدارة.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** إنشاء طلب حجز مبدئي وتوجيه المستخدم للدفع أو تأكيد الحجز.
        *   **قواعد العمل:**
            *   يجب إكمال جميع الحقول المطلوبة في كل مرحلة.
            *   يجب أن تكون الشروط والأحكام واضحة ومتاحة.
            *   يجب التعامل مع المستندات بشكل آمن.
    *   **التبعيات:** `MOD-USER-MGMT`, `MOD-CAR-CATALOG`, `MOD-ORDER-MGMT-FEAT-005`, `MOD-ORDER-MGMT-FEAT-006`, `MOD-NOTIFICATION`, `spatie/laravel-medialibrary`, `MOD-CORE-FEAT-003` (لإعدادات الدفع ومبلغ الحجز).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **(سيتم تفصيل باقي ميزات `MOD-ORDER-MGMT` مثل `FEAT-ORDER-004` (طلب تمويل - 3 مراحل)، `FEAT-ORDER-005` (تكامل دفع الحجز أونلاين)، `FEAT-ORDER-006` (إدارة رفع المستندات وعرضها للإدارة)، `FEAT-ORDER-007` (إدارة طلبات الشراء في Dash - تغيير الحالة، إضافة ملاحظات، إلخ)، `FEAT-ORDER-008` (إدارة طلبات التمويل في Dash)، `FEAT-ORDER-009` (إضافة فاتورة للدفع المتبقي في المعرض - Dash)، وكذلك `FEAT-REQCAR-001` (عملية طلب سيارة مخصصة متعددة الخطوات عبر الموقع/التطبيق وإدارتها في Dash)).**

---
#### **1.5. موديول: `ServiceManagement` (إدارة الخدمات)**
*   **معرف الموديول:** `MOD-SERVICE-MGMT`
*   **اسم الموديول:** إدارة الخدمات (ServiceManagement)
*   **وصف موجز للموديول:** مسؤول عن إدارة الخدمات الإضافية التي يقدمها المعرض (مثل خدمات ما بعد البيع المدفوعة، خدمات العناية بالسيارة، إلخ)، فئاتها، أسعارها، ومعالجة طلبات هذه الخدمات. يتم عرض الخدمات وطلبها عبر الموقع العام، وإدارتها من خلال لوحة تحكم الإدارة Dash.
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-SERVICE-MGMT-FEAT-001` (مستمد من `FEAT-SERVICE-001`)
    *   **اسم الميزة/الوظيفة:** عرض قائمة الخدمات المتاحة
    *   **الأولوية:** متوسطة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين من تصفح قائمة بالخدمات الإضافية التي يقدمها المعرض.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (عبر الموقع العام).
        *   **المدخلات المطلوبة للميزة:** (اختياري) فلتر حسب فئة الخدمة.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (في صفحة "خدماتنا" بالموقع العام):**
            1.  استرجاع قائمة الخدمات النشطة من قاعدة البيانات، مع إمكانية تجميعها حسب فئات الخدمات (`FEAT-SERVICE-003`).
            2.  لكل خدمة، يتم عرض:
                *   اسم الخدمة.
                *   وصف موجز للخدمة.
                *   سعر الخدمة (أو "يبدأ من" إذا كان متغيرًا).
                *   (اختياري) صورة أو أيقونة مميزة للخدمة.
                *   زر "اطلب الخدمة" أو "اعرف المزيد" (ينقل إلى نموذج طلب الخدمة `FEAT-SERVICE-002` أو صفحة تفاصيل الخدمة إذا كانت معقدة).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** عرض قائمة خدمات منظمة وجذابة.
        *   **قواعد العمل:** يجب عرض الخدمات النشطة فقط.
    *   **التبعيات:** `FEAT-SERVICE-003` (فئات الخدمات), `FEAT-SERVICE-004` (إدارة الخدمات).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال.

    *   **(سيتم تفصيل باقي ميزات `MOD-SERVICE-MGMT` مثل `FEAT-SERVICE-002` (نموذج طلب شراء خدمة - بدون تسجيل دخول)، `FEAT-SERVICE-003` (إدارة فئات الخدمات - Dash)، `FEAT-SERVICE-004` (إدارة الخدمات نفسها - Dash)، `FEAT-SERVICE-005` (إدارة طلبات الخدمات - Dash)).**

---
#### **1.6. موديول: `CorporateSales` (مبيعات الشركات)**
*   **معرف الموديول:** `MOD-CORP-SALES`
*   **اسم الموديول:** مبيعات الشركات (CorporateSales)
*   **وصف موجز للموديول:** مسؤول عن إدارة طلبات شراء السيارات بكميات كبيرة (أساطيل) من قبل الشركات والمؤسسات. يتضمن صفحة مخصصة ونموذج طلب في الموقع العام، وإدارة هذه الطلبات من خلال لوحة تحكم الإدارة Dash.
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-CORP-SALES-FEAT-001` (مستمد من `FEAT-CORP-001`)
    *   **اسم الميزة/الوظيفة:** صفحة مخصصة لطلبات أساطيل الشركات
    *   **الأولوية:** متوسطة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** توفير صفحة معلوماتية في الموقع العام تستهدف الشركات والمؤسسات، تشرح مزايا التعامل مع المعرض لشراء الأساطيل، وتعرض معلومات الاتصال بقسم مبيعات الشركات.
        *   **أنواع المستخدمين المتفاعلين:** ممثلو الشركات (USER-TYPE-002) (عبر الموقع العام).
        *   **المدخلات المطلوبة للميزة:** محتوى الصفحة يُدار من `MOD-CMS`.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  عرض محتوى ترويجي عن خدمات مبيعات الأساطيل.
            2.  عرض معلومات الاتصال المباشر بقسم مبيعات الشركات.
            3.  رابط إلى نموذج طلب عرض أسعار للشركات (`FEAT-CORP-002`).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** صفحة ويب معلوماتية وجاذبة للشركات.
        *   **قواعد العمل:** لا يوجد.
    *   **التبعيات:** `MOD-CMS` (لإدارة محتوى الصفحة), `FEAT-CORP-002`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال.

    *   **(سيتم تفصيل باقي ميزات `MOD-CORP-SALES` مثل `FEAT-CORP-002` (نموذج طلب للشركات - بدون تسجيل دخول، يجمع بيانات الشركة والسيارات المطلوبة والكميات)، `FEAT-CORP-003` (إدارة طلبات الشركات في لوحة تحكم Dash - عرض، تحديث الحالة، تعيين موظف متابعة)).**

---
#### **1.7. موديول: `PromotionManagement` (إدارة العروض)**
*   **معرف الموديول:** `MOD-PROMO-MGMT`
*   **اسم الموديول:** إدارة العروض (PromotionManagement)
*   **وصف موجز للموديول:** مسؤول عن إنشاء وإدارة العروض الترويجية على السيارات. يتم عرض هذه العروض في الموقع العام وتطبيق Flutter، وإدارتها بالكامل من خلال لوحة تحكم الإدارة Dash، بما في ذلك ربطها بسيارات محددة وتحديد مدة صلاحيتها.
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-PROMO-MGMT-FEAT-001` (مستمد من `FEAT-PROMO-001`)
    *   **اسم الميزة/الوظيفة:** عرض بنرات/قائمة العروض الترويجية
    *   **الأولوية:** عالية
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** عرض العروض الترويجية النشطة للمستخدمين في الموقع العام وتطبيق Flutter.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001.
        *   **المدخلات المطلوبة للميزة:** لا يوجد.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  استرجاع قائمة العروض النشطة (التي لم تنتهِ مدتها ومفعلة) من قاعدة البيانات.
            2.  عرض العروض في قسم مخصص (مثال: "أحدث العروض") في الصفحة الرئيسية أو صفحة مخصصة للعروض.
            3.  لكل عرض، يتم عرض:
                *   صورة البنر الخاصة بالعرض.
                *   اسم العرض أو عنوانه الرئيسي.
                *   وصف موجز للعرض.
                *   (اختياري) تاريخ انتهاء العرض.
                *   زر "عرض التفاصيل" أو "تسوق العرض الآن" ينقل إلى صفحة تفاصيل العرض (`FEAT-PROMO-002`).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** عرض جذاب للعروض الترويجية.
        *   **قواعد العمل:** يتم عرض العروض النشطة فقط.
    *   **التبعيات:** `FEAT-PROMO-002`, `FEAT-PROMO-003`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-003`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، تطبيق موبايل كامل.

    *   **(سيتم تفصيل باقي ميزات `MOD-PROMO-MGMT` مثل `FEAT-PROMO-002` (صفحة تفاصيل العرض - تعرض السيارات المشمولة بالعرض وشروط العرض)، `FEAT-PROMO-003` (إدارة العروض في لوحة تحكم Dash - إضافة/تعديل/حذف عرض، تحديد اسم، وصف، صورة بنر، تاريخ بدء وانتهاء، ربط بسيارات معينة، تحديد سعر العرض الخاص للسيارات المشمولة)).**

---
#### **1.8. موديول: `Cms` (إدارة المحتوى)**
*   **معرف الموديول:** `MOD-CMS`
*   **اسم الموديول:** إدارة المحتوى (Cms)
*   **وصف موجز للموديول:** مسؤول عن إدارة محتوى الصفحات الثابتة (مثل: من نحن، سياسة الخصوصية، الشروط والأحكام، الأسئلة الشائعة) ومحتوى بعض أجزاء الصفحة الرئيسية (مثل البنرات الرئيسية، قسم السيارات المميزة المعروضة في الصفحة الرئيسية). يتم إدارة هذا المحتوى عبر لوحة تحكم الإدارة Dash وعرضه في الموقع العام وتطبيق Flutter (إذا كان المحتوى مناسبًا للتطبيق).
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-CMS-FEAT-001` (مستمد من `FEAT-ADMIN-002` - الجزء الخاص بالصفحات)
    *   **اسم الميزة/الوظيفة:** إدارة الصفحات الثابتة (CRUD)
    *   **الأولوية:** عالية
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين مديري النظام من إنشاء وتعديل وحذف محتوى الصفحات الثابتة في الموقع (مثل "من نحن"، "سياسة الخصوصية"، "الشروط والأحكام"، "الأسئلة الشائعة").
        *   **أنواع المستخدمين المتفاعلين:** مديرو النظام (USER-TYPE-003)، الموظفون (USER-TYPE-004) (بصلاحيات إدارة المحتوى).
        *   **المدخلات المطلوبة للميزة (عبر واجهة في لوحة تحكم الإدارة Dash - قسم "إدارة المحتوى > الصفحات"):**
            *   **لإضافة/تعديل صفحة:**
                *   عنوان الصفحة (نص، مطلوب، يستخدم في `<title>` وفي العرض).
                *   المعرف الفريد للصفحة (Slug) (نص، مطلوب، فريد، يستخدم في رابط URL، يتم توليده تلقائيًا من العنوان مع إمكانية التعديل، يجب أن يكون بالإنجليزية وبدون مسافات أو رموز خاصة).
                *   محتوى الصفحة (محرر نصوص غني Rich Text Editor - WYSIWYG، مطلوب، يسمح بإضافة نصوص، صور، جداول، روابط، إلخ).
                *   (اختياري) كلمات مفتاحية (Meta Keywords) (نص، مفصولة بفاصلة).
                *   (اختياري) وصف ميتا (Meta Description) (نص، للمساعدة في SEO).
                *   الحالة (قائمة اختيار: منشورة/مسودة، مطلوب).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (في لوحة تحكم الإدارة Dash):**
            1.  **عرض قائمة الصفحات:** جدول يعرض (العنوان، الرابط (slug)، الحالة، تاريخ آخر تعديل). مع خيارات فلترة (بالعنوان، بالحالة) وبحث وترقيم صفحات.
            2.  **إضافة صفحة جديدة:** نموذج لإدخال بيانات الصفحة.
            3.  **تعديل صفحة موجودة:** نموذج يعرض بيانات الصفحة الحالية للسماح بتعديلها.
            4.  **حذف صفحة (Soft Delete أو منع الحذف لصفحات أساسية):**
                *   يمكن حذف الصفحات المخصصة. الصفحات الأساسية (مثل سياسة الخصوصية) يمكن تعطيلها أو تعديل محتواها ولكن ليس حذفها.
            5.  يتم عرض الصفحات "المنشورة" في الموقع العام عبر مسارات ديناميكية (e.g., `/page/{slug}`).
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** صفحات ثابتة مُدارة ديناميكيًا ومتاحة للعرض في الموقع العام.
        *   **قواعد العمل:**
            *   المعرف الفريد للصفحة (Slug) يجب أن يكون فريدًا على مستوى جميع الصفحات.
            *   محرر النصوص الغني يجب أن يكون سهل الاستخدام وينتج HTML نظيف.
    *   **التبعيات:** `MOD-DASHBOARD`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001` (لتوفير المحتوى للموقع)، `OBJ-002` (لإدارة المحتوى من لوحة التحكم).
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، لوحة تحكم احترافية.

    *   **معرف فريد للميزة:** `MOD-CMS-FEAT-002` (مستمد من `FEAT-ADMIN-002` - الجزء الخاص بالصفحة الرئيسية)
    *   **اسم الميزة/الوظيفة:** إدارة محتوى الصفحة الرئيسية (البنرات، الأقسام المميزة)
    *   **الأولوية:** عالية
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين الإدارة من التحكم في العناصر الديناميكية في الصفحة الرئيسية للموقع العام (مثل بنرات السلايدر، قائمة السيارات المميزة، روابط سريعة).
        *   **أنواع المستخدمين المتفاعلين:** مديرو النظام (USER-TYPE-003)، الموظفون (USER-TYPE-004) (بصلاحيات إدارة المحتوى).
        *   **المدخلات المطلوبة للميزة (عبر واجهات في لوحة تحكم الإدارة Dash - قسم "إدارة المحتوى > الصفحة الرئيسية"):**
            *   **لإدارة البنرات (السلايدر الرئيسي):**
                *   صورة البنر (ملف صورة بأبعاد محددة، مثال: 1920x600px).
                *   (اختياري) عنوان رئيسي على البنر (نص).
                *   (اختياري) عنوان فرعي على البنر (نص).
                *   (اختياري) نص زر الإجراء (مثال: "اكتشف المزيد").
                *   رابط زر الإجراء (URL داخلي أو خارجي).
                *   ترتيب عرض البنر.
                *   الحالة (نشط/غير نشط).
            *   **لإدارة قسم "السيارات المميزة" في الصفحة الرئيسية:**
                *   اختيار السيارات التي ستظهر في هذا القسم (قائمة متعددة الاختيار من السيارات التي تم تحديدها كـ "مميزة" في `MOD-CAR-CATALOG-FEAT-017`, أو اختيار يدوي لسيارات معينة).
                *   (اختياري) عنوان للقسم (مثال: "أحدث وصول" أو "الأكثر طلبًا").
            *   **(اختياري) إدارة أقسام أخرى في الصفحة الرئيسية (مثل: روابط سريعة، قسم عن الشركة، شهادات عملاء).**
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (في لوحة تحكم الإدارة Dash):**
            1.  واجهات CRUD منفصلة لإدارة البنرات (إضافة، تعديل، حذف، إعادة ترتيب).
            2.  واجهة لتكوين قسم السيارات المميزة في الصفحة الرئيسية (اختيار السيارات، تحديد العنوان).
            3.  يتم جلب هذه البيانات وعرضها ديناميكيًا في قالب Blade الخاص بالصفحة الرئيسية للموقع.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** صفحة رئيسية ديناميكية وجذابة يمكن تحديث محتواها بسهولة من لوحة التحكم.
        *   **قواعد العمل:**
            *   يجب أن تكون صور البنرات محسنة للويب.
            *   يجب أن يكون هناك حد أقصى لعدد البنرات النشطة في السلايدر (مثال: 5).
    *   **التبعيات:** `MOD-DASHBOARD`, `MOD-CAR-CATALOG` (لاختيار السيارات المميزة).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-002`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، لوحة تحكم احترافية.

---
#### **1.9. موديول: `Notification` (الإشعارات)**
*   **معرف الموديول:** `MOD-NOTIFICATION`
*   **اسم الموديول:** الإشعارات (Notification)
*   **وصف موجز للموديول:** مسؤول عن إرسال الإشعارات عبر القنوات المختلفة (بريد إلكتروني، SMS إذا تم تفعيله، إشعارات داخل النظام في لوحات التحكم). يخدم هذا الموديول الموديولات الأخرى التي تحتاج لإرسال إشعارات للعملاء أو الإدارة، ويدعم لوحات التحكم Dash بعرض الإشعارات.
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-NOTIFICATION-FEAT-001` (مستمد من `FEAT-NOTIF-001`)
    *   **اسم الميزة/الوظيفة:** إرسال إشعارات (بريد إلكتروني، إشعارات داخل النظام، SMS اختياري)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** توفير آلية موحدة ومرنة لإرسال أنواع مختلفة من الإشعارات بناءً على أحداث النظام.
        *   **أنواع المستخدمين المتفاعلين:** النظام (يستدعيه موديولات أخرى)، USER-TYPE-001, USER-TYPE-003, USER-TYPE-004 (كمستقبلين للإشعارات).
        *   **المدخلات المطلوبة للميزة (من الموديول المستدعي - عادةً عبر استدعاء Laravel Notification Class):**
            *   المستلم (نموذج User أو مجموعة Users).
            *   نوع الإشعار (اسم Notification Class, e.g., `NewUserWelcomeNotification`, `OrderPlacedAdminNotification`).
            *   بيانات الإشعار (مصفوفة بالمتغيرات المطلوبة لتعبئة قالب الإشعار، مثل: اسم المستخدم، رقم الطلب، رابط).
            *   القنوات المطلوبة للإرسال (mail, database, sms - إذا كان متاحًا ومفعلًا للمستخدم/للإشعار).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  إنشاء Laravel Notification Classes لكل نوع من أنواع الإشعارات المطلوبة في النظام (مثال: ترحيب مستخدم جديد، تأكيد طلب، تحديث حالة طلب، إعادة تعيين كلمة مرور، إشعار إداري بطلب جديد، إلخ).
            2.  كل Notification Class تحدد القنوات التي سترسل عبرها (via method) وتوفر طرقًا لتكوين الرسالة لكل قناة (toMail, toDatabase, toSms).
            3.  استخدام قوالب Blade (Mailables) لرسائل البريد الإلكتروني لتصميم احترافي وقابل للتخصيص.
            4.  بالنسبة للإشعارات داخل النظام (database channel)، يتم تخزين الإشعار في جدول `notifications` في Laravel (أو جدول مخصص إذا لزم الأمر) مرتبطًا بالمستخدم المستلم، مع بيانات الإشعار (مثل العنوان، النص، الرابط، الحالة: مقروء/غير مقروء).
            5.  بالنسبة لـ SMS (إذا تم تفعيله):
                *   التكامل مع بوابة SMS مختارة (يتم تحديدها وتكوين مفاتيح API في إعدادات النظام `MOD-CORE-FEAT-003`).
                *   توفير قوالب رسائل SMS (قد تكون بسيطة أو تتطلب موافقة من مزود الخدمة).
            6.  يفضل إرسال الإشعارات (خاصة البريد و SMS) عبر Queues لتحسين أداء الاستجابة للمستخدم.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** إرسال الإشعارات بنجاح عبر القنوات المحددة وتخزينها (إذا كانت database channel).
        *   **قواعد العمل:**
            *   يجب أن تكون هناك إمكانية للإدارة لتعديل محتوى قوالب البريد الإلكتروني من لوحة التحكم Dash (ميزة متقدمة ضمن `MOD-DASHBOARD > FEAT-ADMIN-003` أو `MOD-CMS`).
            *   يجب أن يتمكن المستخدمون (العملاء) من التحكم في تفضيلات الإشعارات التي يتلقونها (مثلاً، إلغاء الاشتراك من رسائل البريد التسويقية - إذا تم تطبيقها).
    *   **التبعيات:** Laravel Queues, بوابة SMS (إذا تم تفعيلها), `MOD-CORE-FEAT-003` (لإعدادات بوابة SMS وقوالب البريد).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-001`, `OBJ-002`, `OBJ-003`, `OBJ-004`.
    *   **ربط بالمخرجات النهائية المتوقعة:** موقع إلكتروني فعال، لوحة تحكم احترافية، تطبيق موبايل كامل.

    *   **معرف فريد للميزة:** `MOD-NOTIFICATION-FEAT-002`
    *   **اسم الميزة/الوظيفة:** عرض الإشعارات داخل النظام في لوحات التحكم Dash
    *   **الأولوية:** عالية
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين المستخدمين (العملاء والإدارة) من رؤية الإشعارات الخاصة بهم داخل لوحات التحكم Dash والتفاعل معها.
        *   **أنواع المستخدمين المتفاعلين:** USER-TYPE-001 (في لوحة تحكم العميل Dash)، USER-TYPE-003, USER-TYPE-004 (في لوحة تحكم الإدارة Dash).
        *   **المدخلات المطلوبة للميزة:** لا يوجد مدخلات مباشرة من المستخدم لهذه الميزة.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (يتم تضمينها في `_topbar.blade.php` و صفحة إشعارات مخصصة ضمن `MOD-DASHBOARD`):**
            1.  **أيقونة جرس الإشعارات في الشريط العلوي (Top Navbar):**
                *   تعرض عدد الإشعارات "غير المقروءة" للمستخدم المسجل دخوله.
                *   عند النقر على الأيقونة، يتم عرض قائمة منسدلة (dropdown) بأحدث (مثال: آخر 5) الإشعارات غير المقروءة.
                *   كل إشعار في القائمة المنسدلة يعرض: عنوان مختصر، وقت الإشعار، ورابط (إذا كان الإشعار مرتبطًا بكيان معين مثل طلب أو سيارة).
                *   عند النقر على إشعار في القائمة المنسدلة، يتم توجيه المستخدم إلى الرابط المرتبط (إن وجد) ويتم تعليم الإشعار كـ "مقروء".
            2.  **صفحة "كل الإشعارات":**
                *   رابط "عرض كل الإشعارات" في القائمة المنسدلة ينقل إلى صفحة مخصصة.
                *   تعرض هذه الصفحة جميع إشعارات المستخدم (مقروءة وغير مقروءة) مع ترقيم صفحات.
                *   إمكانية فلترة الإشعارات (الكل، مقروءة، غير مقروءة).
                *   إمكانية تحديد إشعار واحد أو أكثر كـ "مقروء" أو "غير مقروء".
                *   إمكانية حذف إشعار (Soft Delete).
                *   زر "تحديد الكل كمقروء".
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** نظام عرض إشعارات فعال وسهل الاستخدام داخل لوحات التحكم Dash.
        *   **قواعد العمل:**
            *   يتم جلب وعرض الإشعارات الخاصة بالمستخدم المسجل دخوله فقط.
            *   يجب تحديث عداد الإشعارات غير المقروءة بشكل فوري (أو شبه فوري باستخدام polling أو websockets إذا كان النطاق يسمح) عند وصول إشعار جديد أو عند قراءة إشعار.
    *   **التبعيات:** `MOD-DASHBOARD` (لتوفير واجهات العرض), `MOD-NOTIFICATION-FEAT-001` (لتوليد وتخزين الإشعارات).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية.

---
#### **1.10. موديول: `Dashboard` (لوحة التحكم)**
*   **معرف الموديول:** `MOD-DASHBOARD`
*   **اسم الموديول:** لوحة التحكم (Dashboard)
*   **وصف موجز للموديول:** هذا الموديول مسؤول عن بناء وتجهيز واجهات لوحات التحكم المخصصة للإدارة (Admin) والعملاء (Customer) باستخدام أصول Dash الثابتة المتوفرة (HTML/CSS/JS) وتحويلها إلى واجهات Blade ديناميكية ضمن Laravel. سيتضمن طرق عرض Blade التي تدمج أصول Dash، بالإضافة إلى Controllers و Requests و Resources و Services التي تخدم واجهات Dash بشكل خاص. يتفاعل هذا الموديول مع الموديولات الأخرى لجلب البيانات وتنفيذ الإجراءات، ويعد مكونًا أساسيًا لتحقيق المخرج النهائي الخاص بلوحة التحكم الاحترافية (Deliverable 2).
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-DASHBOARD-FEAT-001` (مستمد من `FEAT-DASH-001`)
    *   **اسم الميزة/الوظيفة:** تكامل أصول Dash (HTML/CSS/JS) وتحويلها إلى هيكل Blade
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تحليل ملفات `dashboard.html`, `style.css`, `script.js` الثابتة وتقسيمها إلى مكونات Blade قابلة لإعادة الاستخدام (layouts, partials, components) لإنشاء هيكل لوحة التحكم الديناميكي لكل من لوحة تحكم الإدارة ولوحة تحكم العميل.
        *   **أنواع المستخدمين المتفاعلين:** لا يوجد تفاعل مباشر، هذه ميزة تأسيسية.
        *   **المدخلات المطلوبة للميزة:** ملفات `dashboard.html`, `style.css`, `script.js` (الموجودة في مجلد `Dash` في جذر المشروع).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية:**
            1.  إنشاء Layouts رئيسية للوحات التحكم:
                *   `admin_layout.blade.php` للوحة تحكم الإدارة.
                *   `customer_layout.blade.php` للوحة تحكم العميل (قد يكون مشابهًا جدًا لـ admin_layout ولكن بقائمة جانبية ومحتوى مختلف).
                *   يجب أن تتضمن هذه الـ Layouts الهيكل العام (HTML head, body, header/topbar, sidebar, main content area, footer, JS script includes).
            2.  تحويل القائمة الجانبية (Sidebar) من `dashboard.html` إلى مكون Blade ديناميكي (`_sidebar.blade.php` أو `_admin_sidebar.blade.php` و `_customer_sidebar.blade.php`).
                *   يتم توليد روابط القائمة بناءً على أدوار وصلاحيات المستخدم المسجل دخوله (`spatie/laravel-permission`).
                *   يتم ربط الروابط بالمسارات (routes) المعرفة في Laravel.
                *   يجب دعم القوائم الفرعية المتداخلة كما في `dashboard.html`.
                *   يجب دعم وضع القائمة المصغرة (mini-mode) وتوسيعه عند التحويم (hover-expand) كما هو في `script.js` الأصلي.
            3.  تحويل الشريط العلوي (Top Navbar) من `dashboard.html` إلى مكون Blade (`_topbar.blade.php`).
                *   يعرض اسم المستخدم المسجل، دوره (أو "عميل").
                *   يعرض أيقونة الإشعارات مع عداد (`MOD-NOTIFICATION-FEAT-002`).
                *   يتضمن قائمة منسدلة للمستخدم (الملف الشخصي، الإعدادات - إذا كانت مخصصة للعميل، تسجيل الخروج).
                *   يتضمن زر تبديل القائمة الجانبية (sidebar-toggler).
            4.  ربط ملف `style.css` الرئيسي وملفات CSS الإضافية (مثل Bootstrap RTL) بالـ Layouts.
            5.  ربط ملف `script.js` الرئيسي وملفات JS الإضافية (مثل Bootstrap bundle, Chart.js, bs-stepper) بالـ Layouts، مع التأكد من أن أي تهيئة JS تتم بعد تحميل DOM بشكل صحيح.
            6.  تحديد الأقسام في `script.js` التي تحتاج إلى تعديل أو إعادة كتابة لتتفاعل مع البيانات الديناميكية من Laravel (مثل تهيئة الرسوم البيانية، تحديث العدادات، ملء النماذج).
            7.  إنشاء طرق عرض Blade للصفحات الداخلية المختلفة (مثل عرض جداول، نماذج CRUD، صفحات الإعدادات) التي تستخدم الـ Layout الرئيسي وتستدعي مكونات Blade الفرعية.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** هيكل Blade أساسي ومرن للوحات التحكم (إدارة وعملاء) جاهز لاستضافة المحتوى الديناميكي من الموديولات الأخرى، مع الحفاظ على شكل ووظائف أصول Dash الأصلية.
        *   **قواعد العمل:**
            *   يجب أن يدعم الهيكل اللغة العربية (RTL) بشكل كامل كما هو في `dashboard.html` و `style.css`.
            *   يجب الحفاظ على التصميم البصري والتجاوب الموجود في أصول Dash الأصلية.
            *   يجب أن تكون مكونات Blade قابلة لإعادة الاستخدام قدر الإمكان.
            *   **مخاطر:** `RISK-DASH-INTEG-001` (تعقيد الدمج)، `RISK-DASH-JS-COMPLEXITY-001` (تعقيد JS)، `RISK-DASH-STYLING-OVERRIDE-001` (تعارض الأنماط).
    *   **التبعيات:** أصول Dash (HTML/CSS/JS), `spatie/laravel-permission` (لتخصيص القائمة الجانبية).
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية.

    *   **معرف فريد للميزة:** `MOD-DASHBOARD-FEAT-002` (مستمد من `FEAT-ADMIN-001`)
    *   **اسم الميزة/الوظيفة:** لوحة بيانات رئيسية للإدارة (Dashboard Home)
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** عرض ملخص شامل لأهم مؤشرات الأداء والنشاطات الحديثة في النظام لمديري النظام والموظفين المصرح لهم، باستخدام مكونات لوحة التحكم Dash.
        *   **أنواع المستخدمين المتفاعلين:** مديرو النظام (USER-TYPE-003)، الموظفون (USER-TYPE-004) (بصلاحيات قد تعرض لهم لوحة بيانات مخصصة أو نسخة محدودة).
        *   **المدخلات المطلوبة للميزة:** لا يوجد مدخلات مباشرة من المستخدم، يتم جلب البيانات من موديولات أخرى.
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (بناءً على `dashboard.html` مع بيانات ديناميكية):**
            1.  **عرض بطاقات إحصائية رئيسية (Stat Cards):**
                *   **طلبات جديدة اليوم/الأسبوع:** (العدد، نسبة التغير) - من `MOD-ORDER-MGMT`.
                *   **طلبات التمويل المعلقة:** (العدد) - من `MOD-ORDER-MGMT`.
                *   **عدد السيارات المتاحة:** (العدد الإجمالي للسيارات بحالة "متوفرة") - من `MOD-CAR-CATALOG`.
                *   **عدد العملاء المسجلين الجدد هذا الشهر:** (العدد) - من `MOD-USER-MGMT`.
                *   **إجمالي المبيعات (مبالغ الحجز المؤكدة) هذا الشهر/هذه السنة:** (القيمة، نسبة التغير) - من `MOD-ORDER-MGMT`.
            2.  **عرض الرسوم البيانية (Charts) - مع بيانات ديناميكية:**
                *   **تقرير المبيعات (Sales Chart - Line Chart):**
                    *   **مصدر البيانات:** `MOD-ORDER-MGMT`.
                    *   **المقاييس:** مجموع قيمة الطلبات المكتملة (SUM of `order_total` where `status` = 'completed') مجمعة شهريًا، وعدد الطلبات المكتملة (COUNT of orders where `status` = 'completed') مجمعة شهريًا (محور Y ثانوي).
                    *   **الفترة الزمنية الافتراضية:** آخر 6 أشهر مكتملة بالإضافة للشهر الحالي.
                    *   **خيارات تغيير الفترة (Dropdown):** هذا الأسبوع، هذا الشهر، آخر 3 أشهر، آخر 6 أشهر، هذا العام.
                *   **أفضل العلامات التجارية مبيعًا (Brands Pie Chart - Pie Chart):**
                    *   **مصدر البيانات:** `MOD-CAR-CATALOG` (الماركات) و `MOD-ORDER-MGMT` (الطلبات المكتملة).
                    *   **المقياس:** عدد السيارات المباعة لكل ماركة كنسبة مئوية من إجمالي المبيعات خلال الفترة المحددة.
                    *   **الفترة الزمنية الافتراضية:** هذا الشهر.
                    *   **خيارات تغيير الفترة (Dropdown):** هذا الأسبوع، هذا الشهر، آخر 3 أشهر، هذا العام.
                *   **أفضل السيارات مبيعًا (Top Cars Chart - Horizontal Bar Chart):**
                    *   **مصدر البيانات:** `MOD-CAR-CATALOG` و `MOD-ORDER-MGMT`.
                    *   **المقياس:** عدد الوحدات المباعة لكل موديل سيارة خلال الفترة المحددة (أعلى 5-10 موديلات).
                    *   **الفترة الزمنية الافتراضية:** هذا الشهر.
                    *   **خيارات تغيير الفترة (Dropdown):** هذا الأسبوع، هذا الشهر، آخر 3 أشهر.
                *   **توزيع المبيعات حسب فئة السيارة (Categories Chart - Doughnut Chart):**
                    *   **مصدر البيانات:** `MOD-CAR-CATALOG` (فئات السيارات - نوع جسم السيارة) و `MOD-ORDER-MGMT`.
                    *   **المقياس:** نسبة المبيعات لكل فئة سيارة.
                    *   **الفترة الزمنية الافتراضية:** آخر 6 أشهر.
                    *   **خيارات تغيير الفترة (Dropdown):** آخر 3 أشهر، آخر 6 أشهر، هذا العام.
                *   **حالة طلبات التمويل (Financing Status Chart - Doughnut Chart):**
                    *   **مصدر البيانات:** `MOD-ORDER-MGMT` (طلبات التمويل).
                    *   **المقياس:** نسبة طلبات التمويل حسب حالتها (موافق عليها، مرفوضة، معلقة، مكتملة).
                    *   **الفترة الزمنية الافتراضية:** جميع الطلبات.
                *   **تسجيل العملاء الجدد (شهريًا) (New Customers Chart - Line Chart):**
                    *   **مصدر البيانات:** `MOD-USER-MGMT`.
                    *   **المقياس:** عدد العملاء الجدد المسجلين لكل شهر.
                    *   **الفترة الزمنية الافتراضية:** آخر 6 أشهر.
                *   **نسب طرق الدفع (Payment Methods Chart - Doughnut Chart):**
                    *   **مصدر البيانات:** `MOD-ORDER-MGMT` (الطلبات المؤكدة).
                    *   **المقياس:** نسبة استخدام كل طريقة دفع للحجوزات (أونلاين كاش، تمويل، دفع في المعرض إذا كان خيارًا).
                    *   **الفترة الزمنية الافتراضية:** آخر 3 أشهر.
            3.  **عرض قائمة بأحدث النشاطات (Recent Activities Table):** (من `MOD-ORDER-MGMT` و `MOD-USER-MGMT`)
                *   عرض آخر 5-10 نشاطات (طلب جديد، تسجيل عميل جديد، تحديث مهم لحالة طلب).
                *   لكل نشاط: النوع، العميل/الموظف المرتبط، التاريخ، رابط للتفاصيل.
            4.  **عرض التنبيهات المهمة (Alerts Card):** (من `MOD-NOTIFICATION` أو استعلامات مخصصة)
                *   طلبات تمويل تحتاج للمراجعة (أكثر من X ساعة).
                *   مدفوعات معلقة (إذا كان هناك نظام فواتير).
                *   مواعيد محجوزة اليوم (إذا كان هناك نظام مواعيد).
            5.  **عرض أحدث السيارات المضافة (Latest Cars Added):** (من `MOD-CAR-CATALOG`)
                *   عرض آخر 2-3 سيارات تم إضافتها (صورة، اسم، سعر، رابط للتعديل/العرض).
            6.  **عرض مؤشرات الأداء (Performance Metrics - Progress Bars):**
                *   نسبة تحويل المبيعات (الطلبات المكتملة / إجمالي الطلبات).
                *   متوسط قيمة الطلب.
                *   رضا العملاء (إذا كان هناك نظام تقييم).
                *   نسبة قبول التمويل.
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** صفحة رئيسية ديناميكية وغنية بالمعلومات في لوحة تحكم الإدارة Dash، مع رسوم بيانية تفاعلية تعكس بيانات حقيقية.
        *   **قواعد العمل:**
            *   يجب أن تكون البيانات المعروضة دقيقة ومحدثة قدر الإمكان (مع توضيح تاريخ آخر تحديث إذا كانت هناك عمليات caching مكثفة).
            *   يجب أن تكون الرسوم البيانية تفاعلية (tooltips، إلخ) وتعمل بشكل صحيح مع البيانات الديناميكية.
            *   يجب مراعاة صلاحيات المستخدم عند عرض البيانات (مثلاً، موظف مبيعات قد يرى فقط بيانات متعلقة بمبيعاته أو طلباته).
            *   **إجابة لسؤال 4 من `00-FR.md`:** تم تفصيل مصادر البيانات والفترات الزمنية لكل رسم بياني أعلاه.
    *   **التبعيات:** `MOD-ORDER-MGMT`, `MOD-CAR-CATALOG`, `MOD-USER-MGMT`, `MOD-NOTIFICATION`.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-002`.
    *   **ربط بالمخرجات النهائية المتوقعة:** لوحة تحكم احترافية.

    *   **(سيتم تفصيل باقي ميزات `MOD-DASHBOARD` مثل `FEAT-ADMIN-003` (واجهة إدارة إعدادات النظام الشاملة)، `FEAT-ADMIN-004` (واجهة النسخ الاحتياطي والاستعادة - إذا كانت ضمن النطاق وتتطلب واجهة)، `FEAT-ADMIN-005` (واجهات التقارير المخصصة إذا لم تكن مغطاة بالكامل في لوحة البيانات الرئيسية)).**

---
#### **1.11. موديول: `Api` (واجهة برمجة التطبيقات)**
*   **معرف الموديول:** `MOD-API`
*   **اسم الموديول:** واجهة برمجة التطبيقات (Api)
*   **وصف موجز للموديول:** هذا الموديول مسؤول عن توفير نقاط نهاية API (Endpoints) لتطبيق Flutter وأي واجهات أمامية أخرى قد تحتاج إلى استهلاك البيانات بشكل منفصل عن Blade views التقليدية. يتفاعل هذا الموديول مع الموديولات الأخرى (UserManagement, CarCatalog, OrderManagement إلخ) لجلب البيانات وتقديمها بتنسيق JSON.
*   **قائمة الميزات/الوظائف للموديول:**
    *   **معرف فريد للميزة:** `MOD-API-FEAT-001` (يخدم `FEAT-FLUTTER-001`)
    *   **اسم الميزة/الوظيفة:** توفير نقاط نهاية API أساسية لتطبيق Flutter
    *   **الأولوية:** حرجة
    *   **الوصف التفصيلي:**
        *   **الهدف من الميزة:** تمكين تطبيق Flutter من أداء الوظائف الأساسية مثل تصفح السيارات، تقديم طلبات الشراء، وإدارة حسابات المستخدمين.
        *   **أنواع المستخدمين المتفاعلين:** مستخدمو تطبيق Flutter (USER-TYPE-001).
        *   **المدخلات المطلوبة للميزة:** تختلف حسب نقطة النهاية (مثال: بيانات اعتماد لتسجيل الدخول، معايير فلترة للسيارات، بيانات طلب الشراء).
        *   **المنطق الأساسي أو خطوات المعالجة الرئيسية (قائمة بنقاط النهاية الرئيسية المتوقعة، مع استخدام Laravel Sanctum للمصادقة):**
            *   **المصادقة (Authentication) - (تستدعي وظائف `MOD-USER-MGMT`):**
                *   `POST /api/v1/auth/register` (بيانات التسجيل) -> `MOD-USER-MGMT-FEAT-001`
                *   `POST /api/v1/auth/verify-otp` (رقم الجوال، OTP) -> `MOD-USER-MGMT-FEAT-001B`
                *   `POST /api/v1/auth/resend-otp` (رقم الجوال) -> `MOD-USER-MGMT-FEAT-001B`
                *   `POST /api/v1/auth/login` (معرف الدخول، كلمة المرور) -> `MOD-USER-MGMT-FEAT-002` (يُرجع token)
                *   `POST /api/v1/auth/logout` (يتطلب token صالح)
                *   `POST /api/v1/auth/password/request-reset` (البريد الإلكتروني) -> `MOD-USER-MGMT-FEAT-003`
                *   `POST /api/v1/auth/password/reset` (token، البريد، كلمة المرور الجديدة، تأكيدها) -> `MOD-USER-MGMT-FEAT-003`
            *   **ملف المستخدم (User Profile) - (تستدعي وظائف `MOD-USER-MGMT`، تتطلب token صالح):**
                *   `GET /api/v1/user/profile` -> `MOD-USER-MGMT-FEAT-005` (عرض البيانات)
                *   `PUT /api/v1/user/profile` (بيانات الملف الشخصي للتحديث) -> `MOD-USER-MGMT-FEAT-005` (تحديث البيانات)
                *   `POST /api/v1/user/profile/change-password` (كلمة المرور الحالية والجديدة) -> `MOD-USER-MGMT-FEAT-005`
                *   `POST /api/v1/user/profile/avatar` (ملف صورة) -> `MOD-USER-MGMT-FEAT-005` (تحديث الصورة)
            *   **كتالوج السيارات (Car Catalog) - (تستدعي وظائف `MOD-CAR-CATALOG`):**
                *   `GET /api/v1/cars` (مع query params للفلترة، الترتيب، الترقيم) -> `MOD-CAR-CATALOG-FEAT-001` & `FEAT-002`
                *   `GET /api/v1/cars/{id}` (معرف السيارة) -> `MOD-CAR-CATALOG-FEAT-003`
                *   `GET /api/v1/cars/filters-options` (لجلب خيارات الفلترة الديناميكية: ماركات، موديلات، ألوان، سنوات، نطاقات أسعار، إلخ)
                *   `GET /api/v1/user/favorites` (تتطلب token صالح) -> `MOD-CAR-CATALOG-FEAT-005B`
                *   `POST /api/v1/user/favorites/{car_id}` (إضافة للمفضلة، تتطلب token صالح) -> `MOD-CAR-CATALOG-FEAT-005`
                *   `DELETE /api/v1/user/favorites/{car_id}` (إزالة من المفضلة، تتطلب token صالح) -> `MOD-CAR-CATALOG-FEAT-005`
                *   `GET /api/v1/cars/compare` (مع query params `car_ids[]=1&car_ids[]=2`) -> `MOD-CAR-CATALOG-FEAT-006`
            *   **الطلبات (Orders) - (تستدعي وظائف `MOD-ORDER-MGMT`، تتطلب token صالح):**
                *   `POST /api/v1/orders/cash` (بيانات طلب الكاش متعدد المراحل) -> `MOD-ORDER-MGMT-FEAT-003`
                *   `POST /api/v1/orders/finance` (بيانات طلب التمويل متعدد المراحل) -> `MOD-ORDER-MGMT-FEAT-004`
                *   `GET /api/v1/user/orders` (مع query params للفلترة والترقيم) -> `MOD-ORDER-MGMT-FEAT-006C`
                *   `GET /api/v1/user/orders/{id}` (معرف الطلب) -> `MOD-ORDER-MGMT-FEAT-006C` (تفاصيل الطلب)
                *   `POST /api/v1/orders/{id}/documents` (ملف المستند، نوع المستند) -> `MOD-ORDER-MGMT-FEAT-006` (رفع مستندات لطلب معين)
            *   **عملية "اطلب سيارتك" (`FEAT-REQCAR-001`) - (تتطلب token صالح):**
                *   `POST /api/v1/custom-car-requests` (بيانات طلب السيارة المخصصة)
            *   **الإشعارات (Notifications) - (تستدعي وظائف `MOD-NOTIFICATION`، تتطلب token صالح):**
                *   `GET /api/v1/user/notifications` (مع query params للترقيم، الفلترة حسب مقروء/غير مقروء) -> `MOD-NOTIFICATION-FEAT-002`
                *   `POST /api/v1/user/notifications/{id}/mark-as-read`
                *   `POST /api/v1/user/notifications/mark-all-as-read`
            *   **الصفحات الثابتة والمحتوى العام (CMS) - (تستدعي وظائف `MOD-CMS`):**
                *   `GET /api/v1/pages/{slug}` (معرف الصفحة) -> `MOD-CMS-FEAT-001` (لعرض محتوى صفحة ثابتة مثل "من نحن")
                *   `GET /api/v1/home-content` (لجلب بنرات الصفحة الرئيسية، السيارات المميزة إذا كانت ستُعرض في التطبيق) -> `MOD-CMS-FEAT-002`
                *   `GET /api/v1/faqs` (لجلب قائمة الأسئلة الشائعة إذا كانت مُدارة)
            *   **الخدمات الإضافية (Services) - (تستدعي وظائف `MOD-SERVICE-MGMT`):**
                *   `GET /api/v1/services` (قائمة الخدمات) -> `MOD-SERVICE-MGMT-FEAT-001`
                *   `POST /api/v1/service-requests` (بيانات طلب الخدمة) -> `MOD-SERVICE-MGMT-FEAT-002`
            *   **العروض الترويجية (Promotions) - (تستدعي وظائف `MOD-PROMO-MGMT`):**
                *   `GET /api/v1/promotions` (قائمة العروض النشطة) -> `MOD-PROMO-MGMT-FEAT-001`
                *   `GET /api/v1/promotions/{id}` (تفاصيل العرض والسيارات المشمولة) -> `MOD-PROMO-MGMT-FEAT-002`
            *   **الإعدادات العامة (Global Settings - إذا احتاج التطبيق بعضها):**
                *   `GET /api/v1/settings` (لجلب بعض الإعدادات العامة مثل معلومات الاتصال، رابط سياسة الخصوصية، إلخ)
        *   **المخرجات المتوقعة أو الحالة النهائية للنظام:** استجابات JSON موحدة (مثال: JSend specification)، موثقة (باستخدام أدوات مثل Swagger/OpenAPI)، وآمنة.
        *   **قواعد العمل:**
            *   يجب استخدام Laravel Sanctum للمصادقة المستندة إلى token.
            *   يجب أن تكون جميع الاستجابات موحدة (هيكل ثابت للنجاح والفشل، مع رسائل واضحة ورموز خطأ مناسبة).
            *   يجب تطبيق ترقيم الصفحات (Pagination) على جميع القوائم التي قد تحتوي على عدد كبير من العناصر.
            *   يجب استخدام HTTP status codes بشكل صحيح ودلالي.
            *   يجب تطبيق التحقق من صحة المدخلات (Validation) بشكل صارم على جميع الطلبات التي تحتوي على بيانات.
            *   يجب تطبيق Rate Limiting على نقاط النهاية الحساسة أو التي قد تُستغل.
            *   توثيق API باستخدام Swagger/OpenAPI.
    *   **التبعيات:** جميع الموديولات الأخرى التي توفر البيانات والوظائف, Laravel Sanctum.
    *   **ربط بأهداف `PO-FR.md`:** `OBJ-003` (تطوير تطبيق موبايل Flutter متكامل), `OBJ-004` (تأسيس بنية خلفية قوية وواجهات برمجية فعالة).
    *   **ربط بالمخرجات النهائية المتوقعة:** تطبيق موبايل (Flutter) كامل.

---
### 2. رحلات المستخدمين الرئيسية (Key User Journeys)

#### **2.1. رحلة: تسجيل عميل جديد وشراء سيارة كاش عبر الموقع العام**
*   **معرف الرحلة:** `UJ-001`
*   **اسم الرحلة:** تسجيل وشراء سيارة كاش (موقع عام)
*   **المستخدم (المستخدمون) المعني:** المستخدم العادي (العميل الفرد) (USER-TYPE-001)
*   **الهدف من الرحلة:** إتمام عملية تسجيل حساب جديد وشراء سيارة بالدفع الكاش (دفع مبلغ حجز أولي).
*   **سلسلة الخطوات:**
    1.  **المستخدم:** يزور الموقع العام وينقر على "تسجيل حساب جديد".
    2.  **النظام (MOD-USER-MGMT-FEAT-001):** يعرض نموذج التسجيل.
    3.  **المستخدم:** يُدخل بيانات التسجيل (الاسم، البريد، الجوال، كلمة المرور) ويوافق على الشروط.
    4.  **النظام (MOD-USER-MGMT-FEAT-001):** يتحقق من البيانات، يرسل OTP لرقم الجوال.
    5.  **المستخدم:** يدخل OTP المستلم في نموذج التحقق.
    6.  **النظام (MOD-USER-MGMT-FEAT-001B):** يتحقق من OTP، ينشئ الحساب، يفعله، ويسجل دخول المستخدم.
    7.  **المستخدم:** يتصفح قائمة السيارات (`MOD-CAR-CATALOG-FEAT-001`) ويستخدم الفلاتر (`MOD-CAR-CATALOG-FEAT-002`).
    8.  **المستخدم:** يختار سيارة وينتقل إلى صفحة تفاصيل السيارة (`MOD-CAR-CATALOG-FEAT-003`).
    9.  **المستخدم:** يضغط على زر "اطلبها الآن" أو "احجزها الآن".
    10. **النظام (MOD-ORDER-MGMT-FEAT-001):** يعرض خياري "شراء كاش" أو "طلب تمويل". المستخدم يختار "شراء كاش".
    11. **النظام (MOD-ORDER-MGMT-FEAT-003):** يعرض نموذج طلب السيارة كاش متعدد المراحل.
        *   **المستخدم (المرحلة 1 - البيانات الشخصية):** يكمل/يؤكد بياناته الشخصية.
        *   **المستخدم (المرحلة 2 - تفاصيل السيارة والحجز):** يراجع تفاصيل السيارة ومبلغ الحجز.
        *   **المستخدم (المرحلة 3 - طريقة دفع الحجز):** يختار "دفع مبلغ الحجز أونلاين" وبوابة الدفع.
        *   **المستخدم (المرحلة 4 - المستندات):** يرفع صورة الهوية ورخصة القيادة (`MOD-ORDER-MGMT-FEAT-006`).
        *   **المستخدم (المرحلة 5 - مراجعة وتأكيد):** يراجع جميع التفاصيل ويؤكد الحجز والموافقة على الشروط.
    12. **النظام (MOD-ORDER-MGMT-FEAT-003):** ينشئ طلب حجز مبدئي بحالة "بانتظار دفع الحجز".
    13. **النظام (MOD-ORDER-MGMT-FEAT-005):** يوجه المستخدم إلى بوابة الدفع المختارة مع مبلغ الحجز.
    14. **المستخدم:** يدخل بيانات الدفع ويتمم عملية دفع مبلغ الحجز عبر بوابة الدفع.
    15. **النظام (MOD-ORDER-MGMT-FEAT-005):** يستقبل تأكيد/فشل الدفع من البوابة.
        *   **في حال النجاح:** يحدث حالة الطلب إلى "تم الحجز/بانتظار المراجعة"، يرسل إشعارًا للعميل والإدارة (`MOD-NOTIFICATION-FEAT-001`). يوجه المستخدم لصفحة تأكيد الطلب.
        *   **في حال الفشل:** يعرض رسالة خطأ، يعيد المستخدم لصفحة اختيار طريقة الدفع أو صفحة الطلب.
    16. **النظام (MOD-USER-MGMT-FEAT-004 عبر لوحة تحكم العميل):** يمكن للمستخدم رؤية الطلب وحالته.

#### **2.2. رحلة: مدير النظام يضيف سيارة جديدة عبر لوحة التحكم Dash**
*   **معرف الرحلة:** `UJ-002`
*   **اسم الرحلة:** إضافة سيارة جديدة (لوحة تحكم الإدارة Dash)
*   **المستخدم (المستخدمون) المعني:** مدير النظام (USER-TYPE-003) أو موظف بصلاحية إدارة السيارات (USER-TYPE-004).
*   **الهدف من الرحلة:** إضافة سيارة جديدة بكامل تفاصيلها إلى كتالوج المعرض باستخدام واجهة Stepper في لوحة التحكم Dash.
*   **سلسلة الخطوات:**
    1.  **المستخدم:** يسجل الدخول إلى لوحة تحكم الإدارة Dash (`MOD-USER-MGMT-FEAT-002`).
    2.  **النظام (MOD-DASHBOARD):** يعرض لوحة البيانات الرئيسية (`MOD-DASHBOARD-FEAT-002`).
    3.  **المستخدم:** يتنقل عبر القائمة الجانبية (المبنية بـ `MOD-DASHBOARD-FEAT-001`) إلى "إدارة السيارات" ثم يختار "إضافة سيارة جديدة".
    4.  **النظام (MOD-CAR-CATALOG-FEAT-018, MOD-DASHBOARD):** يعرض واجهة Stepper لإضافة سيارة جديدة (5 خطوات).
    5.  **المستخدم (الخطوة 1: البيانات الأساسية):**
        *   يختار الماركة، الموديل، سنة الصنع، اللون الأساسي.
        *   يدخل رقم الهيكل (VIN)، اسم الفئة/الطراز، والوصف العام.
        *   يضغط "التالي".
    6.  **النظام:** يتحقق من مدخلات الخطوة 1، ينتقل إلى الخطوة 2.
    7.  **المستخدم (الخطوة 2: المواصفات الفنية):**
        *   يدخل تفاصيل المحرك، ناقل الحركة، الأبعاد، إلخ.
        *   يضغط "التالي".
    8.  **النظام:** يتحقق من مدخلات الخطوة 2، ينتقل إلى الخطوة 3.
    9.  **المستخدم (الخطوة 3: الميزات):**
        *   يختار الميزات المتوفرة في السيارة من القوائم المتاحة.
        *   يضغط "التالي".
    10. **النظام:** ينتقل إلى الخطوة 4.
    11. **المستخدم (الخطوة 4: الصور والفيديو):**
        *   يرفع صور السيارة، يحدد الصورة الرئيسية، يضيف رابط فيديو (إن وجد).
        *   يضغط "التالي".
    12. **النظام:** ينتقل إلى الخطوة 5.
    13. **المستخدم (الخطوة 5: السعر والحالة):**
        *   يدخل السعر الأساسي، يراجع السعر شامل الضريبة، يحدد سعر العرض (إن وجد) وتاريخه.
        *   يختار حالة السيارة، يحدد إذا كانت مميزة، يضيف ملاحظات داخلية.
        *   يضغط "حفظ السيارة".
    14. **النظام (MOD-CAR-CATALOG-FEAT-018):**
        *   يتحقق من جميع البيانات المجمعة من كافة الخطوات.
        *   ينشئ سجل السيارة الجديد في قاعدة البيانات ويربطه بالبيانات الوصفية (ماركة، موديل، ألوان، ميزات، صور، إلخ).
        *   يعرض رسالة نجاح.
        *   يعيد توجيه المستخدم إلى قائمة السيارات أو صفحة تفاصيل السيارة الجديدة في لوحة التحكم Dash.
    15. **النظام:** السيارة الجديدة تصبح متاحة للعرض في الواجهات العامة (الموقع وتطبيق Flutter) إذا كانت حالتها "متوفرة" و "نشطة".

---
### 3. المتطلبات غير الوظيفية (Non-Functional Requirements - NFRs)

#### **3.1. متطلبات الأداء (Performance Requirements)**
*   **معرف المتطلب:** `NFR-PERF-001`
    *   **الوصف:** يجب ألا يتجاوز متوسط زمن تحميل الصفحات الرئيسية في الموقع العام (الصفحة الرئيسية، قائمة السيارات، تفاصيل السيارة) 3 ثوانٍ على اتصال إنترنت بسرعة 5 Mbps، مع ألا يتجاوز مؤشر Largest Contentful Paint (LCP) عن 2.5 ثانية.
    *   **الأولوية:** حرجة
*   **معرف المتطلب:** `NFR-PERF-002`
    *   **الوصف:** يجب ألا يتجاوز متوسط زمن تحميل الصفحات الرئيسية في لوحة تحكم الإدارة Dash (لوحة البيانات، قائمة السيارات، قائمة الطلبات) 4 ثوانٍ عند عرض بيانات متوسطة الحجم (حتى 50 سجل في الجدول)، مع LCP لا يتجاوز 3 ثوانٍ.
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-PERF-003`
    *   **الوصف:** يجب أن تستجيب واجهات برمجة التطبيقات (APIs) المستخدمة من قبل تطبيق Flutter خلال 1.5 ثانية كحد أقصى للطلبات المتوسطة التعقيد (مثل جلب قائمة سيارات مفلترة بـ 20 نتيجة) (Time To First Byte - TTFB).
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-PERF-004`
    *   **الوصف:** يجب أن يكون النظام قادرًا على التعامل مع 100 مستخدم متزامن يقومون بعمليات تصفح أساسية (قراءة) و 20 مستخدمًا يقومون بعمليات كتابة (مثل تقديم طلبات) دون تدهور في زمن الاستجابة يزيد عن 20% عن الحالة العادية.
    *   **الأولوية:** متوسطة
*   **معرف المتطلب:** `NFR-PERF-005`
    *   **الوصف:** يجب ألا يتجاوز حجم ملفات JavaScript و CSS الرئيسية (المجمعة والمضغوطة Gzip/Brotli) التي يتم تحميلها في أول زيارة لصفحة لوحة تحكم Dash عن 1MB (JS) و 0.5MB (CSS) لضمان سرعة تحميل أولية جيدة.
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-PERF-006`
    *   **الوصف:** استعلامات قاعدة البيانات التي تنفذ بشكل متكرر (مثل جلب قائمة السيارات أو الطلبات) يجب أن تكون محسنة (Optimized) باستخدام الفهارس (Indexes) المناسبة وألا تستغرق أكثر من 500 مللي ثانية للتنفيذ في المتوسط.
    *   **الأولوية:** عالية

#### **3.2. متطلبات الأمان (Security Requirements)**
*   **معرف المتطلب:** `NFR-SEC-001`
    *   **الوصف:** يجب تشفير جميع كلمات المرور باستخدام خوارزميات تجزئة قوية وحديثة ومملحة (salted hashing) (مثل Argon2id أو bcrypt مع cost factor مناسب).
    *   **الأولوية:** حرجة
*   **معرف المتطلب:** `NFR-SEC-002`
    *   **الوصف:** يجب استخدام HTTPS (TLS 1.2 أو أعلى) لجميع الاتصالات بين العميل والخادم لجميع الواجهات (الموقع العام، لوحات التحكم Dash، API لتطبيق Flutter).
    *   **الأولوية:** حرجة
*   **معرف المتطلب:** `NFR-SEC-003`
    *   **الوصف:** يجب تطبيق حماية ضد هجمات الويب الشائعة (OWASP Top 10) مثل XSS (Cross-Site Scripting), CSRF (Cross-Site Request Forgery), SQL Injection. (Laravel يوفر حماية مدمجة، يجب التأكد من تفعيلها واستخدامها بشكل صحيح، مثل استخدام Eloquent/Query Builder لمنع SQLi، Blade templating لمنع XSS، CSRF tokens).
    *   **الأولوية:** حرجة
*   **معرف المتطلب:** `NFR-SEC-004`
    *   **الوصف:** يجب أن يتم التحقق من صلاحيات المستخدم (Authorization) بشكل صارم على مستوى الـ Backend (باستخدام Laravel Gates/Policies و `spatie/laravel-permission`) لجميع الإجراءات الحساسة، وعدم الاعتماد فقط على إخفاء/تعطيل العناصر في الواجهة الأمامية.
    *   **الأولوية:** حرجة
*   **معرف المتطلب:** `NFR-SEC-005`
    *   **الوصف:** يجب تطبيق سياسات كلمة مرور قوية للموظفين ومديري النظام (طول، تعقيد، تاريخ انتهاء صلاحية، منع إعادة استخدام كلمات المرور القديمة). (يمكن تحقيق ذلك عبر قواعد تحقق مخصصة أو حزم إضافية).
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-SEC-006`
    *   **الوصف:** يجب التعامل مع رفع الملفات (صور السيارات، مستندات العملاء) بشكل آمن: التحقق من نوع الملف (MIME type validation)، الحد الأقصى للحجم، إعادة تسمية الملفات عند التخزين لتجنب تعارض الأسماء أو تنفيذ تعليمات برمجية، تخزين الملفات خارج الـ web root إذا أمكن، وفحص الفيروسات (إذا توفرت الإمكانية والميزانية لخدمة خارجية).
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-SEC-007`
    *   **الوصف:** يجب أن تخضع واجهات برمجة التطبيقات (APIs) المستخدمة من قبل تطبيق Flutter لنفس معايير المصادقة (Laravel Sanctum API tokens) والتحقق من الصلاحيات المطبقة على الويب. يجب أن تكون الـ API tokens قصيرة الأجل مع آلية تجديد (refresh tokens) إذا كان ذلك مناسبًا.
    *   **الأولوية:** حرجة
*   **معرف المتطلب:** `NFR-SEC-008`
    *   **الوصف:** يجب تسجيل محاولات تسجيل الدخول الفاشلة، وتطبيق آلية قفل مؤقت للحساب أو طلب CAPTCHA بعد عدد معين من المحاولات الفاشلة (Rate Limiting).
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-SEC-009`
    *   **الوصف:** يجب تحديث جميع المكتبات والحزم المستخدمة (بما في ذلك Laravel نفسه وملحقاته) بانتظام لسد الثغرات الأمنية المكتشفة.
    *   **الأولوية:** عالية

#### **3.3. متطلبات قابلية الاستخدام (Usability Requirements)**
*   **معرف المتطلب:** `NFR-USAB-001`
    *   **الوصف:** يجب أن تكون جميع واجهات المستخدم (الموقع العام، لوحات التحكم Dash، تطبيق Flutter) متجاوبة بشكل كامل مع مختلف أحجام الشاشات الشائعة (Desktops عريضة ومتوسطة, Tablets أفقي ورأسي, Mobiles).
    *   **الأولوية:** حرجة
*   **معرف المتطلب:** `NFR-USAB-002`
    *   **الوصف:** يجب أن تدعم جميع واجهات المستخدم اللغة العربية بشكل كامل مع اتجاه النص من اليمين إلى اليسار (RTL) بشكل أصيل وسلس، بما في ذلك تخطيط العناصر، محاذاة النصوص، اتجاه الأيقونات، وتنسيق التواريخ والأرقام في لوحات تحكم Dash والموقع والتطبيق.
    *   **الأولوية:** حرجة
*   **معرف المتطلب:** `NFR-USAB-003`
    *   **الوصف:** يجب أن يكون التنقل (Navigation) في الموقع العام ولوحات التحكم Dash وتطبيق Flutter واضحًا، بديهيًا، متسقًا، وسهل الفهم للمستخدمين المستهدفين. يجب أن تكون بنية القوائم منطقية.
    *   **الأولوية:** حرجة
*   **معرف المتطلب:** `NFR-USAB-004`
    *   **الوصف:** يجب أن تكون نماذج إدخال البيانات سهلة الاستخدام مع توفير تسميات واضحة للحقول، مؤشرات للحقول المطلوبة، رسائل خطأ واضحة ومفيدة بجانب الحقل المعني عند إدخال بيانات غير صحيحة، واستخدام عناصر تحكم مناسبة (مثل date pickers, select lists).
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-USAB-005`
    *   **الوصف:** يجب أن يكون تصميم لوحات التحكم Dash (الإدارة والعميل) متسقًا بصريًا، ويتبع مبادئ تصميم واجهة المستخدم الجيدة (Good UI/UX principles)، مع توفير تجربة مستخدم احترافية وممتعة بصريًا وسهلة. يجب الحفاظ على المظهر العام لأصول Dash المتوفرة مع التعديلات اللازمة للديناميكية.
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-USAB-006`
    *   **الوصف:** يجب توفير آلية بحث فعالة وسريعة في قائمة السيارات (في الموقع العام والتطبيق) وفي قوائم البيانات الرئيسية في لوحة تحكم الإدارة Dash (مثل البحث عن عميل، طلب، سيارة برقم الهيكل). يجب أن يدعم البحث جزئًا من الكلمة ويعرض النتائج بشكل واضح.
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-USAB-007`
    *   **الوصف:** (بناءً على سؤال 3 في `00-FR.md` والافتراض) الحد الأقصى لحجم الملفات المرفوعة: لصور السيارات 5 ميجابايت لكل صورة (JPG, PNG, WEBP مسموح بها). للمستندات المرفوعة من العملاء (مثل الهوية، الرخصة) 2 ميجابايت لكل ملف (PDF, JPG, PNG مسموح بها). يجب عرض رسالة واضحة للمستخدم إذا تجاوز الحد، مع إشارة إلى الحجم المسموح به.
    *   **الأولوية:** متوسطة
*   **معرف المتطلب:** `NFR-USAB-008`
    *   **الوصف:** يجب أن تكون رسائل الخطأ ورسائل التأكيد في النظام واضحة، موجزة، ومفيدة للمستخدم، وتجنب المصطلحات التقنية.
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-USAB-009`
    *   **الوصف:** يجب أن تكون عمليات الـ Stepper (مثل إضافة سيارة، تقديم طلب) سهلة المتابعة، مع إمكانية التنقل بين الخطوات (إذا سمح بذلك) وحفظ التقدم.
    *   **الأولوية:** عالية

#### **3.4. متطلبات التوسع (Scalability Requirements)**
*   **معرف المتطلب:** `NFR-SCAL-001`
    *   **الوصف:** يجب أن تكون بنية النظام (خاصة الـ Backend المبني بـ Laravel Modules وقاعدة البيانات) قادرة على التعامل مع زيادة تدريجية في حجم البيانات (حتى 10,000 سيارة في الكتالوج، 50,000 مستخدم مسجل، 1,000 طلب شهريًا) دون الحاجة إلى إعادة تصميم جذرية للبنية الأساسية.
    *   **الأولوية:** متوسطة
*   **معرف المتطلب:** `NFR-SCAL-002`
    *   **الوصف:** يجب تصميم قاعدة البيانات بشكل يسمح بإضافة حقول وفهارس جديدة بكفاءة لدعم الميزات المستقبلية، وتقليل عمليات الربط المعقدة (joins) في الاستعلامات المتكررة.
    *   **الأولوية:** متوسطة
*   **معرف المتطلب:** `NFR-SCAL-003`
    *   **الوصف:** يجب أن يسمح استخدام الموديولات (`nwidart/laravel-modules`) بتطوير وإضافة ميزات أو موديولات جديدة بشكل مستقل نسبيًا، مما يسهل نمو التطبيق أفقيًا.
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-SCAL-004`
    *   **الوصف:** يجب أن يكون النظام قابلاً للنشر على بيئات متعددة الخوادم (multiple server environments) مع موازن أحمال (load balancer) إذا تطلب الأمر ذلك في المستقبل.
    *   **الأولوية:** منخفضة (للمرحلة الحالية، ولكن يراعى في التصميم).

#### **3.5. متطلبات الصيانة (Maintainability Requirements)**
*   **معرف المتطلب:** `NFR-MAINT-001`
    *   **الوصف:** يجب أن يكون الكود منظمًا بشكل جيد، مع اتباع معايير الترميز المتعارف عليها في Laravel و PHP (مثل PSR-12)، واستخدام أسماء واضحة للمتغيرات والدوال والأصناف.
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-MAINT-002`
    *   **الوصف:** يجب أن يتم توثيق الكود بشكل كافٍ (DocBlocks لـ classes, methods، وتعليقات للأجزاء المعقدة)، خاصة لواجهات API والمكونات الرئيسية.
    *   **الأولوية:** متوسطة
*   **معرف المتطلب:** `NFR-MAINT-003`
    *   **الوصف:** يجب أن يسهل هيكل الموديولات على المطورين فهم أجزاء مختلفة من النظام وتعديلها أو إصلاح الأخطاء فيها دون التأثير بشكل كبير على الموديولات الأخرى.
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-MAINT-004`
    *   **الوصف:** يجب أن تكون عملية دمج وتخصيص أصول Dash في واجهات Blade منظمة وقابلة للصيانة، بحيث يمكن تحديث مكونات Dash أو تعديلها بسهولة نسبية. يجب فصل منطق الواجهة عن منطق الأعمال قدر الإمكان.
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-MAINT-005`
    *   **الوصف:** يجب أن تكون هناك مجموعة من الاختبارات الآلية (Unit tests and Feature tests) تغطي الوظائف الحرجة في النظام لضمان عدم كسر الميزات الموجودة عند إجراء تعديلات أو إضافات جديدة.
    *   **الأولوية:** متوسطة (تحديد نسبة التغطية المطلوبة يعتمد على الميزانية والوقت).

#### **3.6. متطلبات خاصة أخرى (Other Specific NFRs)**
*   **معرف المتطلب:** `NFR-OTHER-001`
    *   **الوصف:** (مستمد من `FEAT-LANG-002`) يجب أن يراعي تصميم قواعد البيانات والواجهات إمكانية إضافة لغة إنجليزية كلغة ثانوية في المستقبل دون إعادة هيكلة كبيرة. (مثال: استخدام ملفات لغة Laravel للترجمة `lang`، واستخدام حزم مثل `spatie/laravel-translatable` للبيانات الديناميكية في DB).
    *   **الأولوية:** متوسطة
*   **معرف المتطلب:** `NFR-OTHER-002`
    *   **الوصف:** يجب أن يكون النظام متوافقًا مع أحدث إصدارين رئيسيين من المتصفحات الشائعة (Chrome, Firefox, Safari, Edge) على أنظمة التشغيل الرئيسية (Windows, macOS, Android, iOS).
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-OTHER-003`
    *   **الوصف:** (مستمد من `FEAT-SEO-001`) يجب أن تكون صفحات الموقع العام، وخاصة صفحات تفاصيل السيارات وقائمة السيارات، محسنة بشكل أساسي لمحركات البحث (SEO-friendly URLs, dynamic meta tags (title, description), semantic HTML structure (H1, H2, alt tags for images), sitemap.xml generation).
    *   **الأولوية:** متوسطة
*   **معرف المتطلب:** `NFR-OTHER-004`
    *   **الوصف:** يجب أن يتم التعامل مع التواريخ والأوقات والمناطق الزمنية بشكل متسق في جميع أنحاء النظام، مع الأخذ في الاعتبار المنطقة الزمنية الافتراضية للمملكة العربية السعودية (Asia/Riyadh).
    *   **الأولوية:** عالية
*   **معرف المتطلب:** `NFR-OTHER-005`
    *   **الوصف:** يجب أن يكون هناك نظام تسجيل (Logging) فعال للأخطاء والتحذيرات الهامة في النظام لتسهيل عملية التشخيص وإصلاح المشاكل (باستخدام نظام Laravel Logging المدمج).
    *   **الأولوية:** عالية

---
*(نهاية مستند المتطلبات التفصيلية `REQ-FR.md`)*
```