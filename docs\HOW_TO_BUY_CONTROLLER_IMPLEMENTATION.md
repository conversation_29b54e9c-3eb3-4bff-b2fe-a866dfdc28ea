# How To Buy Controller Implementation

## نظرة عامة
تم تنفيذ Controller action لعرض صفحة "كيف أشتريها؟" في الموقع العام بناءً على المتطلبات المحددة في `PH03-TASK-031`.

## الميزات المنفذة

### 1. InfoPageController
- **المسار**: `app/Http/Controllers/Site/InfoPageController.php`
- **الغرض**: إدارة عرض صفحات المعلومات في الموقع العام
- **الطريقة الرئيسية**: `showHowToBuy(): View`

### 2. بنية Controller
```php
class InfoPageController extends Controller
{
    public function showHowToBuy(): View
    {
        // محاولة جلب المحتوى من MOD-CMS (عند توفره)
        $pageContent = null;
        $pageTitle = 'كيف أشتريها؟';
        
        // TODO: تفعيل هذا الكود عند إنشاء موديول CMS
        // كود جلب المحتوى من CMS معلق للمستقبل
        
        return view('site.info.how_to_buy', compact('pageContent', 'pageTitle'));
    }
}
```

### 3. المسار المضاف
- **URL**: `/how-to-buy`
- **Route Name**: `site.how-to-buy`
- **Controller**: `InfoPageController@showHowToBuy`

### 4. View الصفحة
- **المسار**: `resources/views/site/info/how_to_buy.blade.php`
- **الميزات**:
  - تصميم متجاوب مع Bootstrap 5
  - استخدام نظام الهوية البصرية الموحد
  - خطوات مفصلة لشراء السيارة بالكاش
  - خطوات مفصلة لطلب التمويل
  - قائمة المستندات المطلوبة
  - قسم التواصل والمساعدة
  - تأثيرات حركية عند التمرير

## التفاصيل التقنية

### دعم CMS المستقبلي
تم إعداد Controller للتعامل مع موديول CMS عند توفره:
- جلب المحتوى من `CmsPage` بناءً على slug `'how-to-buy'`
- التحقق من حالة النشر `'published'`
- استخدام محتوى افتراضي في حالة عدم توفر CMS

### البيانات المرسلة للـ View
- `$pageContent`: محتوى الصفحة من CMS (null حالياً)
- `$pageTitle`: عنوان الصفحة

### معالجة الأخطاء
- استخدام try-catch للتعامل مع عدم توفر موديول CMS
- توفير قيم افتراضية للمحتوى والعنوان

## محتوى الصفحة

### قسم الشراء بالكاش
1. **اختيار السيارة**: تصفح واستخدام الفلاتر
2. **تقديم طلب الحجز**: ملء البيانات الشخصية
3. **رفع المستندات**: الهوية ورخصة القيادة
4. **دفع مبلغ الحجز**: عبر بوابة الدفع الآمنة
5. **استلام السيارة**: إكمال المبلغ في المعرض

### قسم طلب التمويل
1. **اختيار السيارة وطلب التمويل**
2. **ملء البيانات الشخصية والمالية**
3. **رفع مستندات التمويل**: الهوية، الراتب، كشف الحساب
4. **مراجعة الطلب**: خلال 48 ساعة
5. **توقيع العقد واستلام السيارة**

### المستندات المطلوبة

#### للشراء الكاش:
- صورة الهوية الوطنية أو الإقامة (الوجهان)
- صورة رخصة القيادة سارية المفعول

#### للتمويل:
- صورة الهوية الوطنية أو الإقامة (الوجهان)
- صورة رخصة القيادة سارية المفعول
- تعريف بالراتب من جهة العمل
- كشف حساب بنكي لآخر 3 أشهر

## الاستخدام
1. انتقل إلى `/how-to-buy` لعرض صفحة "كيف أشتريها؟"
2. ستظهر الصفحة مع جميع المعلومات والخطوات المطلوبة
3. يمكن للمستخدمين التنقل بين أقسام الشراء المختلفة
4. روابط التواصل متاحة للمساعدة الإضافية

## التحديثات المستقبلية

### عند إنشاء موديول CMS
```php
// إلغاء التعليق عن هذا الكود في showHowToBuy()
if (class_exists('\Modules\Cms\Models\CmsPage')) {
    $page = \Modules\Cms\Models\CmsPage::where('slug', 'how-to-buy')
        ->where('status', 'published')
        ->first();
    
    if ($page) {
        $pageContent = $page->content;
        $pageTitle = $page->title ?? $pageTitle;
    }
}
```

## الأمان والأداء
- لا توجد مخاطر أمنية حيث أن الصفحة للقراءة فقط
- إمكانية إضافة caching للمحتوى من CMS مستقبلاً
- تحسين الأداء باستخدام lazy loading للصور

## التوافق
- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب يعمل على جميع الأجهزة
- دعم كامل للغة العربية مع RTL
