@extends('dashboard::layouts.admin_layout')

@section('title', 'إضافة فئة ميزة جديدة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إضافة فئة ميزة جديدة',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'فئات الميزات', 'url' => route('admin.feature-categories.index')],
            ['name' => 'إضافة جديد', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.feature-categories.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>'
    ])

    {{-- نموذج إضافة فئة الميزة --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">بيانات فئة الميزة</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.feature-categories.store') }}" method="POST">
                @csrf

                @include('carcatalog::admin.featurecategories._form')

                <div class="mt-4 d-flex justify-content-end">
                    <button type="submit" class="btn btn-success me-2">إضافة فئة الميزة</button>
                    <a href="{{ route('admin.feature-categories.index') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
