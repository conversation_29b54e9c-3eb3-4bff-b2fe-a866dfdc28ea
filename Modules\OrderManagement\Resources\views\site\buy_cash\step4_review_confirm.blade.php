{{-- 
    الخطوة 4: المراجعة والتأكيد - عملية شراء السيارة كاش
    
    يعرض هذا الـ view ملخص شامل للطلب والتأكيد النهائي
    بناءً على UIUX-FR.md (SITE-BUY-CASH-STEPX-001) و REQ-FR.md (MOD-ORDER-MGMT-FEAT-003)
--}}

@extends('site.layouts.site_layout')

@section('title', 'شراء السيارة - المراجعة والتأكيد')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            
            {{-- عنوان الصفحة ومؤشر التقدم --}}
            <div class="text-center mb-4">
                <h2 class="mb-3">شراء السيارة كاش</h2>
                
                {{-- مؤشر التقدم (Stepper) --}}
                <div class="progress-stepper mb-4">
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">البيانات الشخصية</div>
                    </div>
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">تفاصيل الحجز</div>
                    </div>
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">المستندات</div>
                    </div>
                    <div class="step active">
                        <div class="step-number">4</div>
                        <div class="step-title">المراجعة والتأكيد</div>
                    </div>
                </div>
            </div>

            <div class="row">
                {{-- ملخص الطلب --}}
                <div class="col-lg-8 mb-4">
                    
                    {{-- تفاصيل السيارة --}}
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                تفاصيل السيارة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    @if($car->getFirstMediaUrl('car_main_image') || $car->getFirstMediaUrl('car_images'))
                                        <img src="{{ $car->getFirstMediaUrl('car_main_image') ?: $car->getFirstMediaUrl('car_images') }}" 
                                             alt="{{ $car->title }}" 
                                             class="img-fluid rounded">
                                    @endif
                                </div>
                                <div class="col-md-8">
                                    <h5 class="fw-bold">{{ $car->title }}</h5>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <p class="mb-2">
                                                <strong>الماركة:</strong> {{ $car->brand->name ?? '' }}
                                            </p>
                                            <p class="mb-2">
                                                <strong>الموديل:</strong> {{ $car->carModel->name ?? '' }}
                                            </p>
                                            <p class="mb-2">
                                                <strong>سنة الصنع:</strong> {{ $car->manufacturingYear->year ?? '' }}
                                            </p>
                                        </div>
                                        <div class="col-sm-6">
                                            <p class="mb-2">
                                                <strong>اللون:</strong> {{ $car->mainColor->name ?? '' }}
                                            </p>
                                            <p class="mb-2">
                                                <strong>نوع الهيكل:</strong> {{ $car->bodyType->name ?? '' }}
                                            </p>
                                            <p class="mb-2">
                                                <strong>ناقل الحركة:</strong> {{ $car->transmissionType->name ?? '' }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- البيانات الشخصية --}}
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                البيانات الشخصية
                                <a href="{{ route('site.order.cash.step1', $car->id) }}" class="btn btn-sm btn-outline-primary float-end">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-2">
                                        <strong>الاسم:</strong> {{ $user->first_name }} {{ $user->last_name }}
                                    </p>
                                    <p class="mb-2">
                                        <strong>رقم الهوية:</strong> {{ $user->national_id ?? 'غير محدد' }}
                                    </p>
                                    <p class="mb-2">
                                        <strong>رقم الجوال:</strong> {{ $user->phone }}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-2">
                                        <strong>البريد الإلكتروني:</strong> {{ $user->email }}
                                    </p>
                                    <p class="mb-2">
                                        <strong>المدينة:</strong> {{ $user->city ?? 'غير محدد' }}
                                    </p>
                                    <p class="mb-2">
                                        <strong>العنوان:</strong> {{ $user->address ?? 'غير محدد' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- تفاصيل الدفع --}}
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                تفاصيل الدفع
                                <a href="{{ route('site.order.cash.step2') }}" class="btn btn-sm btn-outline-primary float-end">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-2">
                                        <strong>طريقة الدفع:</strong> 
                                        <span class="badge bg-info">الدفع الإلكتروني</span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-2">
                                        <strong>حالة الدفع:</strong> 
                                        <span class="badge bg-warning">في انتظار الدفع</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- المستندات المرفوعة --}}
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-file-upload me-2"></i>
                                المستندات المرفوعة
                                <a href="{{ route('site.order.cash.step3') }}" class="btn btn-sm btn-outline-primary float-end">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    الهوية - الوجه الأمامي
                                </div>
                                <div class="col-md-4 mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    الهوية - الوجه الخلفي
                                </div>
                                <div class="col-md-4 mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    رخصة القيادة
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- ملخص الأسعار والتأكيد --}}
                <div class="col-lg-4">
                    <div class="card shadow-sm sticky-top">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator me-2"></i>
                                ملخص الأسعار
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="price-breakdown">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>سعر السيارة:</span>
                                    <span class="fw-bold">{{ number_format($car->price, 0) }} {{ $car->currency }}</span>
                                </div>
                                
                                <hr class="my-3">
                                
                                <div class="d-flex justify-content-between mb-2 text-success">
                                    <span>مبلغ الحجز المطلوب:</span>
                                    <span class="fw-bold fs-5">{{ number_format($reservationAmount, 0) }} {{ $car->currency }}</span>
                                </div>
                                
                                <div class="d-flex justify-content-between mb-3 text-muted">
                                    <span>المبلغ المتبقي:</span>
                                    <span>{{ number_format($remainingAmount, 0) }} {{ $car->currency }}</span>
                                </div>
                                
                                <div class="alert alert-info small">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المبلغ المتبقي يُدفع عند استلام السيارة من المعرض
                                </div>
                            </div>

                            {{-- شروط وأحكام نهائية --}}
                            <div class="form-check mb-3">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="final_terms_agreement" 
                                       required>
                                <label class="form-check-label small" for="final_terms_agreement">
                                    أؤكد صحة جميع البيانات المدخلة وأوافق على 
                                    <a href="#" class="text-primary">الشروط والأحكام</a>
                                    <span class="text-danger">*</span>
                                </label>
                            </div>

                            {{-- زر التأكيد النهائي --}}
                            <form action="#" method="POST" id="finalConfirmForm">
                                @csrf
                                <button type="submit" 
                                        class="btn btn-success w-100 btn-lg" 
                                        id="confirmOrderBtn" 
                                        disabled>
                                    <i class="fas fa-check-circle me-2"></i>
                                    تأكيد الحجز والدفع
                                </button>
                            </form>

                            {{-- أزرار إضافية --}}
                            <div class="mt-3">
                                <a href="{{ route('site.order.cash.step3') }}" class="btn btn-outline-secondary w-100 mb-2">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    السابق
                                </a>
                                
                                <form action="{{ route('site.order.cash.cancel') }}" method="POST" class="d-inline w-100">
                                    @csrf
                                    <button type="submit" 
                                            class="btn btn-outline-danger w-100" 
                                            onclick="return confirm('هل أنت متأكد من إلغاء عملية الطلب؟')">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء الطلب
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress-stepper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.5;
}

.step.active, .step.completed {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.step.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.step.completed .step-number {
    background-color: #198754;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    text-align: center;
}

.price-breakdown {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.sticky-top {
    top: 2rem;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const termsCheckbox = document.getElementById('final_terms_agreement');
    const confirmBtn = document.getElementById('confirmOrderBtn');
    
    termsCheckbox.addEventListener('change', function() {
        confirmBtn.disabled = !this.checked;
    });
    
    document.getElementById('finalConfirmForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!termsCheckbox.checked) {
            alert('يرجى الموافقة على الشروط والأحكام');
            return;
        }
        
        // Show loading state
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
        confirmBtn.disabled = true;
        
        // Here you would normally submit the form to process the order
        // For now, we'll show a success message
        setTimeout(function() {
            alert('تم تأكيد طلبك بنجاح! سيتم توجيهك لصفحة الدفع.');
            // Redirect to payment gateway or success page
            // window.location.href = '/payment/gateway';
        }, 2000);
    });
});
</script>
@endpush
