<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تحديث نوع هيكل سيارة موجود.
 *
 * يتحقق هذا الطلب من صحة بيانات تعديل نوع هيكل سيارة موجود
 */
class UpdateBodyTypeRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:50',
                Rule::unique('body_types')->ignore($this->bodytype->id),
            ],
            'status' => 'required|boolean',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة للقواعد المحددة.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required'   => 'اسم نوع هيكل السيارة مطلوب',
            'name.string'     => 'اسم نوع هيكل السيارة يجب أن يكون نصًا',
            'name.max'        => 'اسم نوع هيكل السيارة يجب ألا يتجاوز 50 حرف',
            'name.unique'     => 'اسم نوع هيكل السيارة موجود بالفعل',
            'status.required' => 'حالة نوع هيكل السيارة مطلوبة',
            'status.boolean'  => 'حالة نوع هيكل السيارة يجب أن تكون صحيحة أو خاطئة',
        ];
    }

    /**
     * الحصول على أسماء الخصائص المخصصة للتحقق
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name'   => 'اسم نوع هيكل السيارة',
            'status' => 'الحالة',
        ];
    }
}
