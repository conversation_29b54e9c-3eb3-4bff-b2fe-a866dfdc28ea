{{-- حقل اسم فئة الميزة --}}
<div class="mb-3">
    <label for="name" class="form-label">اسم فئة الميزة <span class="text-danger">*</span></label>
    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" 
           value="{{ old('name', $featureCategory->name ?? '') }}" 
           maxlength="100" 
           required>
    @error('name')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">أدخل اسم فئة الميزة (حد أقصى 100 حرف)</div>
</div>

{{-- حقل وصف فئة الميزة --}}
<div class="mb-3">
    <label for="description" class="form-label">وصف فئة الميزة</label>
    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" 
              rows="3" maxlength="500">{{ old('description', $featureCategory->description ?? '') }}</textarea>
    @error('description')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">أدخل وصف فئة الميزة (اختياري - حد أقصى 500 حرف)</div>
</div>

{{-- حقل ترتيب العرض --}}
<div class="mb-3">
    <label for="display_order" class="form-label">ترتيب العرض</label>
    <input type="number" class="form-control @error('display_order') is-invalid @enderror" id="display_order" name="display_order" 
           value="{{ old('display_order', $featureCategory->display_order ?? 0) }}" 
           min="0">
    @error('display_order')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">أدخل ترتيب العرض (اختياري - الافتراضي 0)</div>
</div>

{{-- حقل حالة فئة الميزة --}}
<div class="mb-3">
    <label for="status" class="form-label">حالة فئة الميزة <span class="text-danger">*</span></label>
    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
        <option value="">اختر الحالة</option>
        <option value="1" {{ old('status', $featureCategory->status ?? '') == '1' ? 'selected' : '' }}>نشط</option>
        <option value="0" {{ old('status', $featureCategory->status ?? '') === '0' ? 'selected' : '' }}>غير نشط</option>
    </select>
    @error('status')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">اختر حالة فئة الميزة</div>
</div>
