@component('mail::message')
# مرحباً بك، {{ $user->first_name }}!

نشكرك على تسجيلك في {{ config('app.name') }}. نحن متحمسون لانضمامك إلينا.

يمكنك الآن تصفح أحدث السيارات والعروض من خلال موقعنا أو تطبيقنا.

@component('mail::button', ['url' => route('home')])
تفضل بزيارة الموقع
@endcomponent

{{-- Placeholder for app links, to be implemented if app exists and routes are defined --}}
{{-- 
@if(route_exists('app.android_link'))
@component('mail::button', ['url' => route('app.android_link'), 'color' => 'success'])
حمّل تطبيقنا للأندرويد
@endcomponent
@endif
@if(route_exists('app.ios_link'))
@component('mail::button', ['url' => route('app.ios_link'), 'color' => 'primary'])
حمّل تطبيقنا للآيفون
@endcomponent
@endif
--}}

شكراً،<br>
فريق عمل {{ config('app.name') }}
@endcomponent
