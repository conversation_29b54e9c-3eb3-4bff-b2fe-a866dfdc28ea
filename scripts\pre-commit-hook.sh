#!/bin/bash

# Pre-commit Hook لفحص جودة الكود
# يتم تشغيل هذا السكريبت تلقائياً قبل كل commit

echo "🔍 تشغيل فحوصات جودة الكود..."

# متغيرات الألوان
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# متغير لتتبع الأخطاء
ERRORS=0

# دالة لطباعة رسائل ملونة
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# دالة لطباعة رسائل النجاح
print_success() {
    print_status $GREEN "✅ $1"
}

# دالة لطباعة رسائل الخطأ
print_error() {
    print_status $RED "❌ $1"
    ERRORS=$((ERRORS + 1))
}

# دالة لطباعة رسائل التحذير
print_warning() {
    print_status $YELLOW "⚠️  $1"
}

# فحص وجود الملفات المطلوبة
check_required_files() {
    print_status $YELLOW "📋 فحص الملفات المطلوبة..."
    
    if [ ! -f ".php-cs-fixer.php" ]; then
        print_error "ملف .php-cs-fixer.php غير موجود"
        return 1
    fi
    
    if [ ! -f "vendor/bin/php-cs-fixer" ]; then
        print_error "PHP CS Fixer غير مثبت. قم بتشغيل: composer require --dev friendsofphp/php-cs-fixer"
        return 1
    fi
    
    print_success "جميع الملفات المطلوبة موجودة"
    return 0
}

# فحص تنسيق الكود باستخدام PHP CS Fixer
check_code_style() {
    print_status $YELLOW "🎨 فحص تنسيق الكود..."
    
    # الحصول على الملفات المعدلة
    CHANGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep '\.php$')
    
    if [ -z "$CHANGED_FILES" ]; then
        print_success "لا توجد ملفات PHP معدلة"
        return 0
    fi
    
    # فحص التنسيق
    ./vendor/bin/php-cs-fixer fix --dry-run --diff --allow-risky=yes $CHANGED_FILES > /tmp/php-cs-fixer-output 2>&1
    
    if [ $? -ne 0 ]; then
        print_error "توجد مشاكل في تنسيق الكود:"
        cat /tmp/php-cs-fixer-output
        print_warning "لإصلاح التنسيق تلقائياً، قم بتشغيل:"
        print_warning "./vendor/bin/php-cs-fixer fix --allow-risky=yes"
        return 1
    fi
    
    print_success "تنسيق الكود صحيح"
    return 0
}

# فحص الأخطاء النحوية في PHP
check_php_syntax() {
    print_status $YELLOW "🔍 فحص الأخطاء النحوية..."
    
    # الحصول على الملفات المعدلة
    CHANGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep '\.php$')
    
    if [ -z "$CHANGED_FILES" ]; then
        print_success "لا توجد ملفات PHP معدلة"
        return 0
    fi
    
    # فحص كل ملف
    for FILE in $CHANGED_FILES; do
        php -l "$FILE" > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            print_error "خطأ نحوي في الملف: $FILE"
            php -l "$FILE"
            return 1
        fi
    done
    
    print_success "لا توجد أخطاء نحوية"
    return 0
}

# فحص جودة التوثيق
check_documentation() {
    print_status $YELLOW "📚 فحص جودة التوثيق..."
    
    if [ -f "scripts/check_documentation_quality.php" ]; then
        # تشغيل فحص التوثيق وحفظ النتيجة
        php scripts/check_documentation_quality.php > /tmp/doc-quality-output 2>&1
        
        # استخراج النسبة المئوية العامة
        OVERALL_PERCENTAGE=$(grep "التقييم العام:" /tmp/doc-quality-output | grep -o '[0-9]\+%' | grep -o '[0-9]\+')
        
        if [ -n "$OVERALL_PERCENTAGE" ]; then
            if [ "$OVERALL_PERCENTAGE" -lt 80 ]; then
                print_warning "جودة التوثيق: ${OVERALL_PERCENTAGE}% (أقل من 80%)"
                print_warning "يُنصح بتحسين التوثيق قبل الـ commit"
            else
                print_success "جودة التوثيق: ${OVERALL_PERCENTAGE}%"
            fi
        else
            print_warning "لم يتم العثور على نتائج فحص التوثيق"
        fi
    else
        print_warning "سكريبت فحص التوثيق غير موجود"
    fi
    
    return 0
}

# فحص حجم الملفات
check_file_sizes() {
    print_status $YELLOW "📏 فحص حجم الملفات..."
    
    # الحصول على الملفات المعدلة
    CHANGED_FILES=$(git diff --cached --name-only --diff-filter=ACM)
    
    if [ -z "$CHANGED_FILES" ]; then
        print_success "لا توجد ملفات معدلة"
        return 0
    fi
    
    # فحص حجم كل ملف (حد أقصى 1MB)
    MAX_SIZE=1048576  # 1MB in bytes
    
    for FILE in $CHANGED_FILES; do
        if [ -f "$FILE" ]; then
            FILE_SIZE=$(stat -f%z "$FILE" 2>/dev/null || stat -c%s "$FILE" 2>/dev/null)
            if [ "$FILE_SIZE" -gt "$MAX_SIZE" ]; then
                print_warning "الملف $FILE كبير جداً ($(($FILE_SIZE / 1024))KB)"
            fi
        fi
    done
    
    print_success "أحجام الملفات مقبولة"
    return 0
}

# فحص رسالة الـ commit
check_commit_message() {
    print_status $YELLOW "💬 فحص رسالة الـ commit..."
    
    # قراءة رسالة الـ commit
    COMMIT_MSG_FILE=$1
    if [ -n "$COMMIT_MSG_FILE" ] && [ -f "$COMMIT_MSG_FILE" ]; then
        COMMIT_MSG=$(cat "$COMMIT_MSG_FILE")
        
        # فحص طول الرسالة (حد أدنى 10 أحرف)
        if [ ${#COMMIT_MSG} -lt 10 ]; then
            print_error "رسالة الـ commit قصيرة جداً (أقل من 10 أحرف)"
            return 1
        fi
        
        # فحص وجود كلمات محظورة
        FORBIDDEN_WORDS=("TODO" "FIXME" "HACK" "XXX")
        for WORD in "${FORBIDDEN_WORDS[@]}"; do
            if echo "$COMMIT_MSG" | grep -qi "$WORD"; then
                print_warning "رسالة الـ commit تحتوي على كلمة محظورة: $WORD"
            fi
        done
        
        print_success "رسالة الـ commit مقبولة"
    else
        print_success "لا يمكن فحص رسالة الـ commit"
    fi
    
    return 0
}

# الدالة الرئيسية
main() {
    echo "🚀 بدء فحوصات ما قبل الـ commit..."
    echo "=================================="
    
    # تشغيل جميع الفحوصات
    check_required_files
    check_php_syntax
    check_code_style
    check_documentation
    check_file_sizes
    check_commit_message "$1"
    
    echo "=================================="
    
    # التحقق من وجود أخطاء
    if [ $ERRORS -gt 0 ]; then
        print_error "فشل في $ERRORS فحص. يرجى إصلاح الأخطاء قبل الـ commit."
        echo ""
        print_status $YELLOW "💡 نصائح لإصلاح الأخطاء:"
        print_status $YELLOW "   • لإصلاح التنسيق: ./vendor/bin/php-cs-fixer fix --allow-risky=yes"
        print_status $YELLOW "   • لفحص التوثيق: php scripts/check_documentation_quality.php"
        print_status $YELLOW "   • لفحص الأخطاء النحوية: php -l filename.php"
        echo ""
        exit 1
    else
        print_success "جميع الفحوصات نجحت! ✨"
        print_success "يمكن المتابعة مع الـ commit"
        exit 0
    fi
}

# تشغيل الدالة الرئيسية
main "$@"
