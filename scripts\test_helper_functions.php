<?php

/**
 * سكريبت اختبار الدوال المساعدة
 * 
 * يختبر جميع الدوال المساعدة للتأكد من عملها بشكل صحيح
 */

require_once __DIR__ . '/../vendor/autoload.php';

// تحميل Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🧪 اختبار الدوال المساعدة\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// اختبار دوال Core Module
echo "📦 اختبار دوال Core Module:\n";
echo "-" . str_repeat("-", 30) . "\n";

// اختبار format_currency
echo "1. اختبار format_currency():\n";
try {
    $tests = [
        [12500.75, null, "12,500.75 ر.س"],
        [1000, '$', "1,000.00 $"],
        [1234.5, 'ر.س', 0, "1,235 ر.س"]
    ];
    
    foreach ($tests as $i => $test) {
        $amount = $test[0];
        $currency = $test[1] ?? 'ر.س';
        $decimals = $test[2] ?? 2;
        $expected = end($test);
        
        if ($test[1] === null) {
            $result = format_currency($amount);
        } elseif (isset($test[2]) && is_int($test[2])) {
            $result = format_currency($amount, $currency, $decimals);
        } else {
            $result = format_currency($amount, $currency);
        }
        
        $status = $result === $expected ? "✅" : "❌";
        echo "   Test " . ($i + 1) . ": {$status} {$result}\n";
    }
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
}

// اختبار format_datetime_for_display
echo "\n2. اختبار format_datetime_for_display():\n";
try {
    $tests = [
        ['2024-07-27 14:30:00', null, '2024-07-27 02:30 PM'],
        ['2024-07-27 14:30:00', 'Y-m-d', '2024-07-27'],
        [null, null, null]
    ];
    
    foreach ($tests as $i => $test) {
        $datetime = $test[0];
        $format = $test[1];
        $expected = $test[2];
        
        if ($format === null) {
            $result = format_datetime_for_display($datetime);
        } else {
            $result = format_datetime_for_display($datetime, $format);
        }
        
        $status = $result === $expected ? "✅" : "❌";
        $displayResult = $result ?? 'null';
        echo "   Test " . ($i + 1) . ": {$status} {$displayResult}\n";
    }
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
}

// اختبار generate_otp
echo "\n3. اختبار generate_otp():\n";
try {
    $otp6 = generate_otp();
    $otp8 = generate_otp(8);
    
    $status1 = strlen($otp6) === 6 && ctype_digit($otp6) ? "✅" : "❌";
    $status2 = strlen($otp8) === 8 && ctype_digit($otp8) ? "✅" : "❌";
    
    echo "   Test 1: {$status1} OTP 6 digits: {$otp6}\n";
    echo "   Test 2: {$status2} OTP 8 digits: {$otp8}\n";
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
}

// اختبار format_file_size
echo "\n4. اختبار format_file_size():\n";
try {
    $tests = [
        [1024, "1.00 KB"],
        [1048576, "1.00 MB"],
        [1073741824, "1.00 GB"],
        [500, "500.00 B"]
    ];
    
    foreach ($tests as $i => $test) {
        $bytes = $test[0];
        $expected = $test[1];
        $result = format_file_size($bytes);
        
        $status = $result === $expected ? "✅" : "❌";
        echo "   Test " . ($i + 1) . ": {$status} {$result}\n";
    }
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
}

// اختبار truncate_text
echo "\n5. اختبار truncate_text():\n";
try {
    $tests = [
        ['نص قصير', 20, 'نص قصير'],
        ['نص طويل جداً يحتاج إلى قطع', 15, 'نص طويل جداً ي...'],
        ['Hello World', 5, 'Hello...']
    ];
    
    foreach ($tests as $i => $test) {
        $text = $test[0];
        $length = $test[1];
        $expected = $test[2];
        $result = truncate_text($text, $length);
        
        $status = $result === $expected ? "✅" : "❌";
        echo "   Test " . ($i + 1) . ": {$status} {$result}\n";
    }
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
}

// اختبار format_number
echo "\n6. اختبار format_number():\n";
try {
    $tests = [
        [1234567, "1,234,567"],
        [1234.56, 2, "1,234.56"],
        [1000, "1,000"]
    ];
    
    foreach ($tests as $i => $test) {
        $number = $test[0];
        $decimals = $test[1] ?? 0;
        $expected = end($test);
        
        if (isset($test[1]) && is_int($test[1])) {
            $result = format_number($number, $decimals);
        } else {
            $result = format_number($number);
        }
        
        $status = $result === $expected ? "✅" : "❌";
        echo "   Test " . ($i + 1) . ": {$status} {$result}\n";
    }
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
}

// اختبار get_status_badge
echo "\n7. اختبار get_status_badge():\n";
try {
    $result1 = get_status_badge('active', 'نشط');
    $result2 = get_status_badge('pending', 'معلق');
    
    $expected1 = '<span class="badge rounded-pill bg-success">نشط</span>';
    $expected2 = '<span class="badge rounded-pill bg-warning">معلق</span>';
    
    $status1 = $result1 === $expected1 ? "✅" : "❌";
    $status2 = $result2 === $expected2 ? "✅" : "❌";
    
    echo "   Test 1: {$status1} Active badge\n";
    echo "   Test 2: {$status2} Pending badge\n";
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
}

// اختبار get_permission_group_translation
echo "\n8. اختبار get_permission_group_translation():\n";
try {
    $tests = [
        ['cars', 'إدارة السيارات'],
        ['settings', 'إعدادات النظام'],
        ['unknown', 'unknown']
    ];
    
    foreach ($tests as $i => $test) {
        $group = $test[0];
        $expected = $test[1];
        $result = get_permission_group_translation($group);
        
        $status = $result === $expected ? "✅" : "❌";
        echo "   Test " . ($i + 1) . ": {$status} {$result}\n";
    }
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
}

// اختبار دوال CarCatalog Module
echo "\n\n📦 اختبار دوال CarCatalog Module:\n";
echo "-" . str_repeat("-", 35) . "\n";

// اختبار car_status_label
echo "1. اختبار car_status_label():\n";
try {
    $tests = [
        ['active', 'نشطة'],
        ['sold', 'مباعة'],
        ['inactive', 'غير نشطة'],
        ['featured', 'مميزة'],
        ['unknown', 'غير محدد']
    ];
    
    foreach ($tests as $i => $test) {
        $status = $test[0];
        $expected = $test[1];
        $result = car_status_label($status);
        
        $statusIcon = $result === $expected ? "✅" : "❌";
        echo "   Test " . ($i + 1) . ": {$statusIcon} {$result}\n";
    }
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
}

// اختبار car_status_badge_class
echo "\n2. اختبار car_status_badge_class():\n";
try {
    $tests = [
        ['active', 'success'],
        ['sold', 'danger'],
        ['inactive', 'secondary'],
        ['featured', 'warning'],
        ['unknown', 'secondary']
    ];
    
    foreach ($tests as $i => $test) {
        $status = $test[0];
        $expected = $test[1];
        $result = car_status_badge_class($status);
        
        $statusIcon = $result === $expected ? "✅" : "❌";
        echo "   Test " . ($i + 1) . ": {$statusIcon} {$result}\n";
    }
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 انتهى اختبار الدوال المساعدة!\n";
echo "📊 تحقق من النتائج أعلاه للتأكد من عمل جميع الدوال بشكل صحيح.\n";
