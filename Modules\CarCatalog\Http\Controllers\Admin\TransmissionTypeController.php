<?php

namespace Modules\CarCatalog\Http\Controllers\Admin;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\CarCatalog\Http\Requests\Admin\StoreTransmissionTypeRequest;
use Modules\CarCatalog\Http\Requests\Admin\UpdateTransmissionTypeRequest;
use Modules\CarCatalog\Models\TransmissionType;
use Modules\Core\Http\Controllers\BaseController;

/**
 * وحدة تحكم إدارة أنواع ناقل الحركة.
 *
 * تتعامل هذه الوحدة مع عمليات CRUD لأنواع ناقل الحركة في لوحة تحكم الإدارة
 */
class TransmissionTypeController extends BaseController
{
    /**
     * عرض قائمة أنواع ناقل الحركة.
     *
     * @param Request $request طلب HTTP
     *
     * @return View
     */
    public function index(Request $request): View
    {
        $query = TransmissionType::withCount('cars');

        // تطبيق فلتر البحث بالاسم إذا تم تقديمه
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->input('search') . '%');
        }

        // تطبيق فلتر الحالة إذا تم تقديمه
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // ترتيب النتائج حسب الاسم
        $query->orderBy('name');

        // تطبيق التصفح
        $transmissionTypes = $query->paginate(15)->withQueryString();

        return view('carcatalog::admin.transmission-types.index', compact('transmissionTypes'));
    }

    /**
     * عرض نموذج إضافة نوع ناقل حركة جديد.
     *
     * @return View
     */
    public function create(): View
    {
        return view('carcatalog::admin.transmission-types.create');
    }

    /**
     * تخزين نوع ناقل حركة جديد.
     *
     * @param StoreTransmissionTypeRequest $request طلب تخزين نوع ناقل حركة
     *
     * @return RedirectResponse
     */
    public function store(StoreTransmissionTypeRequest $request): RedirectResponse
    {
        // إنشاء نوع ناقل الحركة باستخدام البيانات المتحقق منها
        TransmissionType::create($request->validated());

        return redirect()->route('admin.transmission-types.index')->with('success', 'تمت إضافة نوع ناقل الحركة بنجاح.');
    }

    /**
     * عرض تفاصيل نوع ناقل حركة محدد.
     *
     * @param TransmissionType $transmissiontype نوع ناقل الحركة المراد عرضه
     *
     * @return View
     */
    public function show(TransmissionType $transmissiontype): View
    {
        // تحميل السيارات المرتبطة بنوع ناقل الحركة
        $transmissiontype->loadCount('cars');

        return view('carcatalog::admin.transmission-types.show', compact('transmissiontype'));
    }

    /**
     * عرض نموذج تعديل نوع ناقل حركة موجود.
     *
     * @param TransmissionType $transmissiontype نوع ناقل الحركة المراد تعديله
     *
     * @return View
     */
    public function edit(TransmissionType $transmissiontype): View
    {
        return view('carcatalog::admin.transmission-types.edit', compact('transmissiontype'));
    }

    /**
     * تحديث نوع ناقل حركة موجود.
     *
     * @param UpdateTransmissionTypeRequest $request طلب تحديث نوع ناقل حركة
     * @param TransmissionType $transmissiontype نوع ناقل الحركة المراد تحديثه
     *
     * @return RedirectResponse
     */
    public function update(UpdateTransmissionTypeRequest $request, TransmissionType $transmissiontype): RedirectResponse
    {
        // تحديث نوع ناقل الحركة باستخدام البيانات المتحقق منها
        $transmissiontype->update($request->validated());

        return redirect()->route('admin.transmission-types.index')->with('success', 'تم تعديل نوع ناقل الحركة بنجاح.');
    }

    /**
     * حذف نوع ناقل حركة محدد.
     *
     * @param TransmissionType $transmissiontype نوع ناقل الحركة المراد حذفه
     *
     * @return RedirectResponse
     */
    public function destroy(TransmissionType $transmissiontype): RedirectResponse
    {
        // التحقق من وجود سيارات مرتبطة بنوع ناقل الحركة
        if ($transmissiontype->cars()->count() > 0) {
            return redirect()->route('admin.transmission-types.index')
                ->with('error', 'لا يمكن حذف نوع ناقل الحركة لأنه مرتبط بسيارات موجودة.');
        }

        // حذف نوع ناقل الحركة
        $transmissiontype->delete();

        return redirect()->route('admin.transmission-types.index')->with('success', 'تم حذف نوع ناقل الحركة بنجاح.');
    }
}
