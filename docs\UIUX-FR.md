## UIUX-FR.md - تصميم واجهات المستخدم وتجربة المستخدم (نسخة نهائية معتمدة ذاتيًا)

**تاريخ الإنشاء:** (تاريخ اليوم)
**إصدار المستند:** 1.0 (نهائي معتمد ذاتيًا)

### مقدمة

الغرض من هذا المستند هو تقديم تصميم تفصيلي لواجهات المستخدم (UI) وتجربة المستخدم (UX) لمنصة معرض السيارات الإلكترونية المتكاملة. هذا المستند هو التمثيل البصري والوظيفي الدقيق للمتطلبات المحددة في `REQ-FR.md` (النسخة النهائية المعتمدة ذاتيًا، إصدار 1.0)، ويستند إلى الرؤية من `PO-FR.md`، التحليل الأولي من `00-FR.md`، المواصفات التقنية من `TS-FR.md`، وهيكل المشروع من `STRU-FR.md` (الذي يفترض استخدام Laravel 10، وبناء لوحة تحكم مخصصة من الصفر باستخدام ملفات Dash HTML/CSS/JS، وحزمة `nwidart/laravel-modules`).

يعتبر هذا المستند الدليل الإرشادي الأساسي لتطوير الواجهات الأمامية، بما في ذلك:
1.  **لوحة تحكم Dash المخصصة (للإدارة والعملاء):** سيتم بناؤها عبر تكييف وتخصيص أصول `Dash/index.html`, `Dash/style.css`, و `Dash/script.js` (الموجودة في جذر المشروع كما هو محدد في `STRU-FR.md`) لتصبح واجهات Blade ديناميكية ضمن Laravel.
2.  **واجهة الموقع العامة (Blade):** سيتم تصميمها لتقديم تجربة مستخدم جذابة وفعالة.
3.  **تطبيق Flutter:** سيتم وصف شاشاته وتدفقاته لتوفير تجربة متكاملة على الأجهزة المحمولة.

هذا المستند وصفي بالكامل ولا يتضمن أي كود برمجي، صور، أو رسومات، بل يعتمد على الوصف النصي الدقيق للهياكل السلكية، المكونات، التفاعلات، وتدفقات المستخدم. تم الاستلهام من الصور المرجعية المقدمة للجوانب الهيكلية والتنظيمية فقط.

**الأهداف النهائية للمشروع التي يخدمها هذا التصميم:**
1.  **موقع إلكتروني فعال.**
2.  **لوحة تحكم احترافية (مخصصة من أصول Dash).**
3.  **واجهة موقع (Frontend) جذابة وفعالة (Blade).**
4.  **تطبيق موبايل (Flutter) كامل.**

### 1. شخصيات المستخدمين (User Personas)

**(معرف القسم: `UIUX-PERSONAS-001`)**

تم تطوير شخصيات المستخدمين التالية لتمثيل الجمهور المستهدف الرئيسي للمنصة، وتوجيه قرارات التصميم لضمان تلبية احتياجاتهم وتوقعاتهم بفعالية.

*   **معرف الشخصية:** `PERSONA-ADMIN-01`
    *   **الاسم:** خالد الأحمد
    *   **الدور:** مدير معرض السيارات / مدير النظام
    *   **الأهداف:**
        *   إدارة فعالة لمخزون السيارات الجديدة (إضافة، تعديل، حذف).
        *   متابعة وتحديث حالة طلبات العملاء (شراء، تمويل) بكفاءة.
        *   الحصول على نظرة عامة سريعة على أداء المعرض من خلال إحصائيات وتقارير واضحة.
        *   إدارة محتوى الموقع والعروض الترويجية بسهولة.
        *   إدارة حسابات الموظفين وتعيين الصلاحيات.
    *   **الاحتياجات:**
        *   واجهة تحكم Dash مخصصة، سهلة الاستخدام وبديهية، تستفيد من هيكل أصول Dash مع تكييفها لاحتياجاته، ولا تتطلب تدريبًا مكثفًا.
        *   أدوات قوية لإدارة البيانات ضمن لوحة تحكم Dash مع فلاتر وبحث متقدم (كما هو موضح في واجهات إدارة السيارات والطلبات).
        *   نظام إشعارات فعال مدمج في لوحة تحكم Dash لتنبيهه بالأحداث الهامة (طلبات جديدة، تنبيهات المخزون إذا أضيف مستقبلاً).
        *   تصميم لوحة تحكم Dash يدعم اللغة العربية بشكل كامل (RTL) مع الحفاظ على وظيفية وجمالية أصول Dash الأصلية.
        *   إمكانية تخصيص بعض جوانب لوحة البيانات الرئيسية (Dashboard Home) لعرض المعلومات الأكثر أهمية له.
    *   **نقاط الألم (الحالية أو المتوقعة بدون نظام جيد):**
        *   صعوبة تتبع الطلبات المتعددة.
        *   الوقت المستغرق في تحديث بيانات السيارات يدويًا.
        *   عدم القدرة على الحصول على تقارير دقيقة بسرعة.
    *   **المهارات التقنية:** متوسطة إلى جيدة (يستخدم تطبيقات مكتبية وبرامج إدارة بشكل منتظم).
    *   **سياق الاستخدام:** يستخدم لوحة التحكم بشكل أساسي من جهاز كمبيوتر مكتبي خلال ساعات العمل.
    *   **اقتباس:** "أحتاج إلى نظام يجعل إدارة المعرض أسهل وأكثر كفاءة، حتى أتمكن من التركيز على خدمة عملائنا بشكل أفضل."

*   **معرف الشخصية:** `PERSONA-CUSTOMER-01`
    *   **الاسم:** سارة عبدالله
    *   **الدور:** عميل فردي يبحث عن شراء سيارة جديدة
    *   **الأهداف:**
        *   تصفح سهل وممتع للسيارات الجديدة المتاحة.
        *   الحصول على معلومات تفصيلية ودقيقة عن السيارات (مواصفات، أسعار، صور).
        *   القدرة على مقارنة السيارات بسهولة.
        *   عملية شراء مبسطة عبر الإنترنت (حجز أو طلب تمويل).
        *   تتبع حالة طلبها بسهولة.
    *   **الاحتياجات:**
        *   تصميم جذاب وحديث للموقع والتطبيق.
        *   معلومات واضحة وشفافة عن الأسعار والخيارات.
        *   أدوات فلترة فعالة لمساعدتها في العثور على السيارة المناسبة.
        *   عملية تقديم طلب آمنة وسلسة.
        *   دعم اللغة العربية بشكل كامل.
        *   إمكانية الوصول عبر الجوال.
    *   **نقاط الألم (الحالية أو المتوقعة بدون نظام جيد):**
        *   صعوبة العثور على معلومات كاملة عن السيارات في مكان واحد.
        *   عمليات شراء معقدة وغير واضحة.
        *   عدم القدرة على تتبع الطلب بعد تقديمه.
    *   **المهارات التقنية:** جيدة (تستخدم تطبيقات الجوال والمواقع الإلكترونية للتسوق والخدمات بشكل يومي).
    *   **سياق الاستخدام:** تتصفح الموقع من الكمبيوتر المحمول في المنزل، وتستخدم تطبيق الجوال أثناء التنقل.
    *   **اقتباس:** "أريد تجربة شراء سيارة مريحة وموثوقة عبر الإنترنت، تمامًا مثلما أشتري أي شيء آخر."

### 2. خرائط رحلة المستخدم الرئيسية (Key User Journey Maps)

**(معرف القسم: `UIUX-JOURNEYS-001`)**

تم تحديد خرائط رحلات المستخدم التالية بناءً على `REQ-FR.md` لتوضيح تفاعلات المستخدمين مع النظام عبر مراحله المختلفة، مع التركيز على الواجهات التي سيتم تصميمها.

*   **معرف الرحلة:** `UJM-001` (مستمد من `UJ-001` في `REQ-FR.md`)
    *   **اسم الرحلة:** تسجيل عميل جديد وشراء سيارة كاش عبر الموقع العام.
    *   **الشخصية المعنية:** `PERSONA-CUSTOMER-01` (سارة عبدالله).
    *   **الهدف من الرحلة:** إتمام عملية تسجيل حساب جديد وشراء سيارة بالدفع الكاش (دفع مبلغ حجز أولي).
    *   **المراحل الرئيسية للرحلة:**
        1.  **الاكتشاف والتسجيل:**
            *   **إجراءات المستخدم:** تزور الموقع، تتصفح، تقرر التسجيل، تملأ نموذج التسجيل، تتحقق من OTP.
            *   **نقاط التفاعل:** الصفحة الرئيسية للموقع، صفحة قائمة السيارات، صفحة التسجيل، صفحة التحقق من OTP.
            *   **أفكار/مشاعر:** حماس، فضول، قليل من التردد عند إدخال البيانات الشخصية، ارتياح عند اكتمال التسجيل.
            *   **فرص تحسين:** عملية تسجيل واضحة وسريعة، رسائل تحقق وتوجيه ودودة، توضيح سياسة الخصوصية.
        2.  **البحث واختيار السيارة:**
            *   **إجراءات المستخدم:** تستخدم الفلاتر، تتصفح قائمة السيارات، تقارن، تدخل صفحة تفاصيل سيارة.
            *   **نقاط التفاعل:** صفحة قائمة السيارات (مع الفلاتر)، صفحة تفاصيل السيارة.
            *   **أفكار/مشاعر:** اهتمام، مقارنة، البحث عن أفضل قيمة، إعجاب بتفاصيل معينة.
            *   **فرص تحسين:** فلاتر فعالة وسهلة، صور عالية الجودة، مواصفات واضحة ومقسمة، أداة مقارنة جيدة.
        3.  **بدء عملية الشراء:**
            *   **إجراءات المستخدم:** تضغط "اطلبها الآن"، تختار "شراء كاش".
            *   **نقاط التفاعل:** زر "اطلبها الآن" في صفحة تفاصيل السيارة، مودال اختيار نوع الشراء.
            *   **أفكار/مشاعر:** قرار، حماس لبدء العملية.
            *   **فرص تحسين:** خيارات واضحة ومميزة بين الكاش والتمويل.
        4.  **إكمال نموذج الشراء الكاش (متعدد المراحل):**
            *   **إجراءات المستخدم:** تملأ البيانات الشخصية، تراجع تفاصيل الحجز، تختار طريقة الدفع، ترفع المستندات، تراجع الطلب وتؤكد.
            *   **نقاط التفاعل:** صفحات نموذج الشراء متعدد الخطوات (البيانات، الحجز، الدفع، المستندات، المراجعة).
            *   **أفكار/مشاعر:** تركيز، قلق بشأن صحة البيانات، أمل في سهولة العملية.
            *   **فرص تحسين:** خطوات واضحة ومحددة، حفظ تلقائي للتقدم، إرشادات واضحة للمستندات، ملخص طلب شامل قبل التأكيد.
        5.  **الدفع وتأكيد الطلب:**
            *   **إجراءات المستخدم:** تنتقل لبوابة الدفع، تدخل بيانات الدفع، تتلقى تأكيد الدفع والطلب.
            *   **نقاط التفاعل:** صفحة بوابة الدفع (خارجية)، صفحة تأكيد الطلب في الموقع، إشعار بالبريد/النظام.
            *   **أفكار/مشاعر:** ترقب، ارتياح عند نجاح الدفع، سعادة بتأكيد الحجز.
            *   **فرص تحسين:** انتقال سلس لبوابة الدفع، رسالة تأكيد واضحة ومفصلة، معلومات عن الخطوات التالية.
        6.  **متابعة الطلب (لاحقًا):**
            *   **إجراءات المستخدم:** تسجل الدخول للوحة تحكم العميل، تطلع على حالة طلبها.
            *   **نقاط التفاعل:** لوحة تحكم العميل Dash (قسم "طلباتي").
            *   **أفكار/مشاعر:** فضول، رغبة في معرفة المستجدات.
            *   **فرص تحسين:** عرض حالة طلب واضح ومحدث بانتظام.

*   **معرف الرحلة:** `UJM-002` (مستمد من `UJ-002` في `REQ-FR.md`)
    *   **اسم الرحلة:** مدير النظام يضيف سيارة جديدة عبر لوحة التحكم Dash.
    *   **الشخصية المعنية:** `PERSONA-ADMIN-01` (خالد الأحمد).
    *   **الهدف من الرحلة:** إضافة سيارة جديدة بكامل تفاصيلها إلى كتالوج المعرض باستخدام واجهة Stepper في لوحة التحكم Dash.
    *   **المراحل الرئيسية للرحلة:**
        1.  **الوصول والتنقل:**
            *   **إجراءات المستخدم:** يسجل الدخول للوحة التحكم، يتنقل إلى "إدارة السيارات"، يختار "إضافة سيارة جديدة".
            *   **نقاط التفاعل:** صفحة تسجيل الدخول للوحة التحكم Dash، القائمة الجانبية، صفحة إدارة السيارات.
            *   **أفكار/مشاعر:** روتين عمل، تركيز على المهمة.
            *   **فرص تحسين:** تنقل سريع وواضح، وصول سهل لوظيفة إضافة السيارة.
        2.  **إدخال بيانات السيارة (عبر Stepper):**
            *   **إجراءات المستخدم:** يملأ حقول البيانات الأساسية، المواصفات الفنية، الميزات، يرفع الصور، يحدد السعر والحالة، يتنقل بين خطوات Stepper.
            *   **نقاط التفاعل:** واجهة Stepper لإضافة سيارة (5 خطوات) ضمن لوحة التحكم Dash.
            *   **أفكار/مشاعر:** تركيز على دقة البيانات، متابعة الخطوات بشكل منهجي.
            *   **فرص تحسين:** واجهة Stepper سهلة الاستخدام، إرشادات واضحة لكل حقل، قوائم منسدلة محدثة، تحميل صور فعال، تحقق فوري من بعض المدخلات (إن أمكن).
        3.  **مراجعة وحفظ السيارة:**
            *   **إجراءات المستخدم:** يراجع البيانات المدخلة في الخطوة الأخيرة من Stepper، يضغط "حفظ السيارة".
            *   **نقاط التفاعل:** الخطوة الأخيرة في Stepper، زر "حفظ السيارة".
            *   **أفكار/مشاعر:** تدقيق نهائي، توقع نجاح العملية.
            *   **فرص تحسين:** ملخص واضح للبيانات قبل الحفظ، رسالة تأكيد واضحة عند النجاح أو رسالة خطأ مفصلة عند الفشل.
        4.  **التأكد من العرض (اختياري):**
            *   **إجراءات المستخدم:** ينتقل إلى قائمة السيارات في لوحة التحكم أو الموقع العام ليرى السيارة المضافة.
            *   **نقاط التفاعل:** قائمة السيارات في لوحة التحكم Dash، واجهة الموقع العامة.
            *   **أفكار/مشاعر:** اطمئنان، إنجاز.
            *   **فرص تحسين:** تحديث فوري للقوائم بعد الإضافة.

### 3. إرشادات واجهة المستخدم وتجربة المستخدم العامة (General UI/UX Guidelines)

**(معرف القسم: `UIUX-GUIDELINES-001`)**

تحدد هذه الإرشادات المبادئ والمعايير الأساسية لتصميم واجهات مستخدم متسقة وجذابة وسهلة الاستخدام لجميع مكونات المنصة.

*   **3.0. التعامل مع الصور المرجعية للواجهات:**
    *   **توجيه للـ LLM:** تم تحليل الصور المرجعية المقدمة (التي تظهر واجهات لموقع "شوب باي موتري") لفهم التوجهات العامة في تقسيم الشاشات، ترتيب العناصر (مثل الفلاتر في `filter-bar.png`، عرض تفاصيل السيارة في `screenshot-02.png` و `screenshot-03.png`، خطوات "اطلب سيارتك" في `screenshot-11` إلى `screenshot-15.png`، خطوات الشراء الكاش في `screenshot-16` إلى `screenshot-18.png`)، وأنماط التخطيط الشائعة. سيتم الاستفادة من هذه الأفكار الهيكلية عند وصف الهياكل السلكية النصية لواجهات لوحة تحكم Dash المخصصة وواجهة الموقع العامة أدناه. **يجب التأكيد على أن الهوية البصرية النهائية للمشروع (الألوان، الخطوط، الأيقونات، العلامة التجارية) ستتبع الإرشادات المحددة في الأقسام التالية (3.2، 3.3، إلخ) وليس ما هو موجود في الصور المرجعية، لضمان الحفاظ على الهوية البصرية الفريدة للمشروع الحالي عند تخصيص لوحة تحكم Dash المخصصة وتصميم واجهة الموقع العامة.**

*   **3.1. فلسفة التصميم (`UIUX-PHILOSOPHY-001`):**
    *   **البساطة والوضوح:** تصميم واجهات نظيفة وغير مزدحمة، تركز على المهام الأساسية للمستخدم.
    *   **سهولة الاستخدام:** توفير تجربة مستخدم بديهية لا تتطلب منحنى تعلم حاد.
    *   **الاتساق:** الحفاظ على نمط تصميم متسق عبر جميع شاشات المنصة (Dash, Blade, Flutter).
    *   **الاستجابة:** ضمان عمل الواجهات بشكل ممتاز على مختلف الأجهزة وأحجام الشاشات.
    *   **التركيز على المستخدم:** تصميم يلبي احتياجات وتوقعات المستخدمين المحددين في الشخصيات.
    *   **الأداء:** تصميم واجهات سريعة التحميل والاستجابة.
    *   **اللغة العربية أولاً (RTL):** تصميم جميع الواجهات مع مراعاة اتجاه النص من اليمين لليسار بشكل أصيل.

*   **3.2. لوحة الألوان (Color Palette - `UIUX-COLORS-001`):** (هذه هي الألوان المعتمدة للمشروع والتي ستُطبق على لوحة تحكم Dash المخصصة وواجهة الموقع والتطبيق)
    *   **اللون الأساسي (Primary):** `#0F172A` (أزرق داكن قريب من الكحلي - للاستخدام في العناوين الرئيسية، الخلفيات الهامة، العناصر التي تحتاج بروزًا هادئًا. مستوحى من `style.css` Dash للـ `--primary-color`).
    *   **اللون الثانوي (Secondary):** `#1E3A8A` (أزرق أغمق من سابقه - للاستخدام في الأزرار الرئيسية، الروابط الهامة، تمييز العناصر. مستوحى من `style.css` Dash للـ `--secondary-color`).
    *   **لون التمييز/الإجراء (Accent):** `#F97316` (برتقالي - للأزرار الحث على اتخاذ إجراء (CTAs)، التنبيهات، العناصر التي تتطلب انتباهًا فوريًا. مستوحى من `style.css` Dash للـ `--accent-color`).
    *   **لون النجاح (Success):** `#10B981` (أخضر - لرسائل النجاح، الحالات الإيجابية. مستوحى من `style.css` Dash).
    *   **لون الخطأ (Error/Danger):** `#EF4444` (أحمر - لرسائل الخطأ، الحالات السلبية، التنبيهات الحرجة. مستوحى من `style.css` Dash).
    *   **لون التحذير (Warning):** `#F59E0B` (أصفر/برتقالي فاتح - للتنبيهات غير الحرجة. مستوحى من `style.css` Dash).
    *   **لون المعلومات (Info):** `#06B6D4` (سماوي - للرسائل المعلوماتية. مستوحى من `style.css` Dash).
    *   **الخلفية الفاتحة (Light Background):** `#F8FAFC` (أبيض مائل للرمادي الفاتح جداً - للخلفية الرئيسية للمحتوى. مستوحى من `style.css` Dash).
    *   **النصوص الأساسية (Text Primary):** `#1E293B` (رمادي داكن جداً - للنصوص الرئيسية).
    *   **النصوص الثانوية/المكتومة (Text Secondary/Muted):** `#64748B` (رمادي متوسط - للنصوص الثانوية، التسميات التوضيحية. مستوحى من `style.css` Dash للـ `--text-muted`).
    *   **لون الحدود (Borders):** `#E2E8F0` (رمادي فاتح جداً - للحدود بين العناصر والفواصل).
    *   **خلفية الشريط الجانبي (Sidebar Background - Dash):** تدرج لوني من `#122A89` (بداية) إلى `#071A62` (نهاية) كما هو محدد في `style.css` (`--sidebar-bg-start`, `--sidebar-bg-end`).
    *   **نصوص الشريط الجانبي (Sidebar Text - Dash):** `#F8FAFC` (أبيض مائل للرمادي) و `#94A3B8` (للنصوص المكتومة) كما هو محدد في `style.css`.

*   **3.3. الخطوط (Typography - `UIUX-TYPOGRAPHY-001`):** (هذه هي الخطوط المعتمدة للمشروع والتي ستُطبق على لوحة تحكم Dash المخصصة وواجهة الموقع والتطبيق)
    *   **اللغة العربية:**
        *   **العائلة:** 'IBM Plex Sans Arabic' (نفس الخط المستخدم في `dashboard.html` و `style.css`).
        *   **الأوزان المستخدمة:** Regular (400), Medium (500), SemiBold (600), Bold (700).
    *   **اللغة الإنجليزية (للتوسع المستقبلي أو العناصر غير المترجمة):**
        *   **العائلة:** 'Roboto', sans-serif (خط قياسي وواضح).
        *   **الأوزان المستخدمة:** Regular (400), Medium (500), Bold (700).
    *   **هرمية الخطوط (مثال):**
        *   **H1 (عنوان رئيسي للصفحة):** 32px, Bold (700).
        *   **H2 (عنوان قسم رئيسي):** 28px, Bold (700).
        *   **H3 (عنوان قسم فرعي):** 24px, SemiBold (600).
        *   **H4 (عنوان بطاقة/عنصر):** 20px, SemiBold (600).
        *   **H5 (عنوان أصغر):** 18px, Medium (500).
        *   **النص الأساسي (Body):** 16px, Regular (400).
        *   **النص الصغير/التسميات (Caption/Label):** 14px, Regular (400).
        *   **نص الزر (Button Text):** 16px, Medium (500).
    *   **ارتفاع الأسطر (Line Height):** 1.6 لمعظم النصوص لضمان سهولة القراءة.

*   **3.4. التباعد والتخطيط (Spacing & Layout Principles - `UIUX-SPACING-001`):**
    *   **نظام التباعد:** استخدام نظام تباعد مبني على مضاعفات 4px أو 8px (e.g., 4px, 8px, 12px, 16px, 24px, 32px) لضمان الاتساق.
    *   **التخطيط (Layout):**
        *   **لوحة التحكم Dash:** سيتم الالتزام بهيكل التخطيط العام الموجود في `Dash/index.html` (شريط جانبي، شريط علوي، منطقة محتوى رئيسية). سيتم ضمان أن تكون منطقة المحتوى مرنة وتتكيف مع المحتويات المختلفة.
        *   **واجهة الموقع العامة (Blade):** استخدام نظام شبكي (Grid system) مرن (مثل Bootstrap Grid إذا تم اعتماده، أو نظام مخصص) لتنظيم المحتوى.
        *   **Flutter:** استخدام Widgets التخطيط القياسية في Flutter (Row, Column, Stack, Expanded, etc.) مع مراعاة إرشادات Material Design.
    *   **الهوامش والحشوات (Margins & Paddings):** تطبيقها بشكل متسق حول العناصر والأقسام.
    *   **3.4.1. التعامل مع تصميم الجداول في لوحة تحكم Dash:**
        *   سيتم استخدام هيكل الجداول الأساسي من `dashboard.html` (e.g., class `table table-hover recent-activity-table`) كنقطة انطلاق.
        *   سيتم تخصيص الأعمدة لتناسب البيانات المطلوبة لكل جدول (مثل قائمة السيارات، الطلبات، العملاء).
        *   سيتم دعم الفرز (Sorting) على الأعمدة الرئيسية (من جانب الخادم).
        *   سيتم توفير حقل بحث/فلترة بسيط فوق الجداول الرئيسية (من جانب الخادم).
        *   سيتم استخدام ترقيم الصفحات (Pagination) من Laravel وسيتم تنسيقه ليتناسب مع نمط Dash.
        *   أزرار الإجراءات (عرض، تعديل، حذف) لكل صف ستكون واضحة ومتسقة، مع استخدام أيقونات Font Awesome كما في `dashboard.html`.

*   **3.5. نمط الأيقونات (Iconography - `UIUX-ICONS-001`):**
    *   **المكتبة المقترحة:** Font Awesome 6 (نفس المستخدم في `dashboard.html`).
    *   **النمط:** استخدام الأيقونات الصلبة (Solid) بشكل أساسي، مع إمكانية استخدام الأيقونات الخفيفة (Light) عند الحاجة لتنويع بصري مدروس.
    *   **الحجم:** تحديد أحجام قياسية للأيقونات (e.g., 16px, 20px, 24px) واستخدامها بشكل متسق.
    *   **الاستخدام:** استخدام الأيقونات لتعزيز الفهم البصري ودعم النصوص، وليس كبديل كامل لها.

*   **3.6. سلوك العناصر التفاعلية (Interactive Elements Behavior - `UIUX-INTERACTIONS-001`):**
    *   **الأزرار:**
        *   حالات واضحة: عادي (Default), عند التحويم (Hover), عند النقر (Active/Pressed), معطل (Disabled).
        *   استخدام ألوان مميزة (اللون الثانوي أو لون التمييز) للأزرار الرئيسية.
        *   توفير تغذية راجعة مرئية عند النقر (مثل تأثير بسيط أو تغيير طفيف في الظل).
    *   **النماذج (Forms):**
        *   تسميات واضحة للحقول (Labels).
        *   مؤشرات للحقول المطلوبة (*).
        *   رسائل تحقق (Validation messages) واضحة ومفيدة تظهر بالقرب من الحقل المعني.
        *   حقول إدخال سهلة الاستخدام مع حجم مناسب.
        *   استخدام عناصر تحكم مناسبة (مثل منتقي التاريخ Date Pickers، قوائم منسدلة Selects).
    *   **الروابط (Links):**
        *   تمييز واضح للروابط عن النص العادي (لون مختلف و/أو خط سفلي عند التحويم).
    *   **الجداول (Tables - خاصة في Dash):**
        *   عرض البيانات بشكل منظم وواضح.
        *   تمييز صف الرأس (Header row).
        *   تمييز الصفوف عند التحويم (Hover).
        *   توفير خيارات للفرز والترقيم والبحث إذا كانت البيانات كبيرة (كما هو موضح في `REQ-FR.md` لأقسام لوحة التحكم).
    *   **حالات الخطأ والفراغ (Error and Empty States):**
        *   **حالات الخطأ:** عرض رسائل خطأ واضحة وموجزة داخل المكون أو الصفحة المعنية. استخدام أيقونة خطأ ولون الخطأ (Error/Danger) بشكل مناسب. توفير إرشادات للمستخدم حول كيفية تصحيح الخطأ إن أمكن.
        *   **حالات الفراغ (Empty States):** عندما لا تكون هناك بيانات لعرضها (مثل قائمة طلبات فارغة، أو لا توجد نتائج بحث)، يجب عرض رسالة ودودة توضح ذلك مع أيقونة مناسبة، وربما دعوة لاتخاذ إجراء (مثل "ابدأ بإضافة سيارتك الأولى" أو "جرّب مصطلح بحث مختلف").

*   **3.7. إرشادات الوصولية (Accessibility Guidelines - `UIUX-ACCESSIBILITY-001`):**
    *   الالتزام بمعايير WCAG 2.1 المستوى AA كحد أدنى.
    *   ضمان تباين ألوان كافٍ بين النص والخلفية.
    *   توفير نصوص بديلة (Alt text) للصور الهامة.
    *   تصميم يمكن التنقل فيه باستخدام لوحة المفاتيح فقط.
    *   استخدام HTML دلالي (Semantic HTML).
    *   اختبار الواجهات باستخدام أدوات فحص الوصولية.

*   **3.8. النبرة والصوت (Tone and Voice - `UIUX-TONE-001`):**
    *   **النبرة:** احترافية، ودودة، وموثوقة.
    *   **الصوت:** واضح، موجز، وسهل الفهم. تجنب المصطلحات التقنية المعقدة في الواجهات الموجهة للعملاء.
    *   استخدام اللغة العربية الفصحى المبسطة.

*   **3.9. استخدام وتخصيص أصول Dash (لـ [لوحة التحكم الاحترافية] - `UIUX-DASH-CUSTOMIZATION-001`):**
    *   سيتم استخدام ملفات `Dash/index.html`, `Dash/style.css`, و `Dash/script.js` كنقطة انطلاق قوية لبناء لوحة تحكم الإدارة والعملاء، **مع الحفاظ على أكبر قدر ممكن من هيكل ووظائف Dash الأصلية لتقليل التعقيد وضمان تجربة مستخدم متوقعة لمن هو على دراية بتصميمات Dash الشائعة.**
    *   **التخصيص عبر Blade:** سيتم تقسيم `index.html` إلى تخطيطات Blade رئيسية (`admin_layout.blade.php`, `customer_layout.blade.php`) ومكونات جزئية (`_sidebar.blade.php`, `_topbar.blade.php`, `_footer.blade.php`). سيتم ملء هذه المكونات ببيانات ديناميكية من Laravel Controllers. **سيتم تحديد أي عناصر HTML في `index.html` التي تحتاج إلى تعديل (مثل إضافة معرفات ID أو classes إضافية) لتسهيل التفاعل مع JavaScript المخصص أو ربط بيانات Laravel.**
    *   **تخصيص CSS:** سيتم **في المقام الأول** الاعتماد على `Dash/style.css` الأصلي. سيتم إنشاء ملف CSS مخصص (`dash_custom.css`، يُحمّل بعد `style.css`) **فقط لتجاوز الأنماط عند الضرورة القصوى** لتطبيق لوحة الألوان والخطوط المحددة في هذا المستند (القسم 3.2 و 3.3)، ولضمان التوافق الكامل مع RTL إذا لم يكن مدعومًا بالكامل في `style.css` الأصلي. **الهدف هو تقليل التعديلات المباشرة على `style.css` الأصلي قدر الإمكان.**
    *   **تخصيص JS:**
        *   **الأساس:** سيتم الاعتماد على `Dash/script.js` الأصلي لتشغيل الوظائف الأساسية للوحة التحكم (القائمة، الرسوم البيانية الأولية، Stepper).
        *   **التكامل مع Laravel:** سيتم إنشاء ملف JavaScript مخصص (`dash_app.js` كما هو مقترح في `STRU-FR.md`)، **يُحمّل بعد `Dash/script.js`**. هذا الملف سيتولى:
            *   **تهيئة البيانات الديناميكية للرسوم البيانية:** بدلاً من تعديل `Dash/script.js` مباشرة، سيقوم `dash_app.js` باستدعاء دوال تهيئة الرسوم البيانية المعرفة في `Dash/script.js` (إذا كانت تسمح بذلك) وتمرير البيانات الديناميكية إليها من متغيرات JavaScript التي تم تمريرها من Blade. إذا لم تكن الدوال الأصلية قابلة للتخصيص، سيقوم `dash_app.js` بإعادة تهيئة الرسوم البيانية باستخدام Chart.js مع البيانات الديناميكية. **سيتم توثيق كيفية تمرير البيانات من Laravel Blade إلى JavaScript لكل رسم بياني.**
            *   **تفاعلات القائمة الديناميكية:** ضمان عمل وظائف القائمة الجانبية مع الروابط الديناميكية والحالات النشطة التي يولدها Laravel. **أي تعديلات على محددات JS لتتوافق مع HTML المُولّد بواسطة Blade ستتم في `dash_app.js` إذا أمكن، أو بتوثيق دقيق للتعديلات المطلوبة في `Dash/script.js` الأصلي إذا كان ذلك ضروريًا وحتميًا.**
            *   **تفاعلات AJAX (إذا لزم الأمر):** أي تفاعلات AJAX داخل لوحة التحكم Dash سيتم تنفيذها عبر `dash_app.js`.
            *   **الهدف هو الحفاظ على `Dash/script.js` الأصلي سليمًا قدر الإمكان، واستخدام `dash_app.js` للتوسيع والتخصيص.**
    *   **المكونات:**
        *   **المكونات التي سيتم استخدامها كما هي من `Dash/index.html` (مع تعديلات طفيفة للبيانات):** الهيكل العام للصفحة (مع الحفاظ على `div` IDs و `class` names الأصلية)، تصميم البطاقات الإحصائية، تصميم جداول البيانات الأساسي (مع تكييف الأعمدة والصفوف)، تصميم النماذج الأساسي (مع تكييف الحقول).
        *   **المكونات التي سيتم تعديلها بشكل كبير من خلال Blade و JavaScript المخصص (`dash_app.js`):** القائمة الجانبية (لتصبح ديناميكية بناءً على الأدوار والصلاحيات)، الشريط العلوي (لعرض بيانات المستخدم والإشعارات الديناميكية)، جميع الرسوم البيانية (لتلقي بيانات ديناميكية من Laravel).
        *   **المكونات التي سيتم بناؤها كهياكل Blade جديدة ولكن ستُنسق لتتناسب مع نمط Dash:** واجهة Stepper لإضافة/تعديل السيارات ستستخدم هيكل HTML من `dashboard.html` لـ Stepper، ولكن سيتم ملء كل خطوة بمحتوى Blade ديناميكي (نماذج Laravel). أي نماذج معقدة أو واجهات تفاعلية غير موجودة في `dashboard.html` (مثل إدارة فئات الخدمات، أو إعدادات النظام المفصلة) سيتم بناؤها كملفات Blade جديدة، مع تطبيق classes CSS من `Dash/style.css` (أو `dash_custom.css`) لضمان الاتساق البصري.

*   **3.10. استخدام Blade (لـ [واجهة الموقع الفعالة] - `UIUX-BLADE-SITE-001`):**
    *   سيتم بناء واجهة الموقع العامة باستخدام Laravel Blade.
    *   سيتم استخدام CSS مخصص (قد يكون مع إطار عمل مثل Bootstrap 5 RTL إذا كان ذلك يسرع التطوير ويتوافق مع التصميم المطلوب) لتحقيق المظهر المطلوب المستوحى هيكليًا من الصور المرجعية ولكن مع تطبيق الهوية البصرية المحددة في هذا المستند (الألوان، الخطوط).
    *   التركيز على التصميم المتجاوب، سهولة التنقل، ووضوح عرض المعلومات.

*   **3.11. إرشادات Flutter (لـ [تطبيق الموبايل الكامل] - `UIUX-FLUTTER-GUIDELINES-001`):**
    *   اتباع مبادئ Material Design (مع تكييفها لتناسب الهوية البصرية المحددة).
    *   تصميم واجهات نظيفة ومناسبة للشاشات الصغيرة، مع التركيز على سهولة الاستخدام بإصبع واحد.
    *   توفير تجربة مستخدم سلسة ومألوفة لمستخدمي تطبيقات الجوال.
    *   مراعاة الفروق بين منصتي iOS و Android في بعض عناصر الواجهة إذا لزم الأمر (Platform-adaptive UI) لتقديم تجربة أصيلة لكل منصة.
    *   استخدام عناصر تحكم لمسية واضحة ومناطق لمس كافية (Minimum touch target size).
    *   استخدام نمط إدارة حالة (State Management) متسق وفعال (مثل Riverpod أو BLoC كما هو مقترح في `00-FR.md`).

---

### 4. واجهات مستخدم لوحة التحكم (لوحة تحكم Dash UI المخصصة) - تصميم تفصيلي وصفي

**(معرف القسم: `UIUX-DASH-VIEWS-001`)**

يصف هذا القسم الهياكل السلكية النصية المقترحة لواجهات لوحة التحكم المبنية عبر تخصيص أصول Dash (HTML/CSS/JS الموجودة في `Dash/` ضمن جذر المشروع) ودمجها مع Laravel Blade. الهيكل العام مستوحى من `Dash/index.html`، مع تطبيق الهوية البصرية المحددة في القسم 3 من هذا المستند.

#### 4.1. لوحة تحكم الإدارة (Admin Dashboard)

##### 4.1.1. صفحة تسجيل الدخول للوحة التحكم (`DASH-ADMIN-LOGIN-001`)
*   **الغرض من الواجهة:** تمكين مديري النظام والموظفين المصرح لهم من تسجيل الدخول إلى لوحة تحكم الإدارة.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** صفحة بسيطة تتوسط الشاشة، خلفية بلون محايد (`--light-bg`).
    *   **المكونات الرئيسية:**
        *   **حاوية مركزية:** بعرض محدد (e.g., 400px)، ذات حواف دائرية وظل خفيف.
        *   **شعار المعرض:** في الأعلى داخل الحاوية المركزية.
        *   **عنوان:** "تسجيل الدخول إلى لوحة التحكم الإدارية" (H3، لون `--primary-color`).
        *   **نموذج تسجيل الدخول:**
            *   **حقل "معرف تسجيل الدخول":** (input type="text") تسمية واضحة فوق الحقل "البريد الإلكتروني أو رقم الجوال"، أيقونة Font Awesome (`fas fa-user` أو `fas fa-envelope`) داخل الحقل على اليمين. placeholder: "أدخل البريد الإلكتروني أو رقم الجوال". مطلوب.
            *   **حقل "كلمة المرور":** (input type="password") تسمية واضحة فوق الحقل "كلمة المرور"، أيقونة Font Awesome (`fas fa-lock`) داخل الحقل على اليمين. placeholder: "أدخل كلمة المرور". مطلوب. أيقونة "إظهار/إخفاء كلمة المرور" (`fas fa-eye` / `fas fa-eye-slash`) على يسار الحقل.
            *   **صف يحتوي على:**
                *   **خيار "تذكرني":** (Checkbox) على اليمين مع تسمية "تذكرني".
                *   **رابط "نسيت كلمة المرور؟":** على اليسار، بلون `--secondary-color`.
            *   **زر "تسجيل الدخول":** (زر رئيسي، بعرض كامل، لون `--secondary-color`، نص أبيض) نص "تسجيل الدخول".
    *   **البيانات المعروضة:** لا يوجد بيانات ديناميكية قبل تسجيل الدخول.
    *   **التفاعلات الرئيسية:**
        *   إدخال بيانات الاعتماد والضغط على "تسجيل الدخول".
        *   عرض رسائل خطأ تحقق واضحة (مثل "حقل مطلوب"، "تنسيق بريد إلكتروني غير صالح"، "كلمة المرور يجب أن تكون 8 أحرف على الأقل") أسفل الحقل المعني أو كرسالة مجمعة.
        *   عند فشل المصادقة (بيانات اعتماد خاطئة): عرض رسالة خطأ عامة فوق زر التسجيل (مثل "بيانات الاعتماد غير صحيحة أو الحساب غير نشط").
        *   الانتقال إلى صفحة استعادة كلمة المرور عند الضغط على الرابط.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-002`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.1.2. الصفحة الرئيسية للوحة تحكم الإدارة (لوحة البيانات) (`DASH-ADMIN-HOME-001`)
*   **الغرض من الواجهة:** عرض ملخص شامل لأهم مؤشرات الأداء والنشاطات الحديثة في النظام. (مستوحاة من محتوى `Dash/index.html` الرئيسي).
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:**
        *   تستخدم `admin_layout.blade.php` (المشتق من `Dash/index.html`).
        *   **الشريط العلوي (`_topbar.blade.php`):** يعرض اسم المستخدم المسجل (خالد الأحمد)، دوره (مدير النظام)، أيقونة الإشعارات (`fas fa-bell`) مع عداد رقمي للإشعارات غير المقروءة، قائمة منسدلة للمستخدم (رابط للملف الشخصي، رابط لتسجيل الخروج). زر تبديل القائمة الجانبية (`fas fa-bars`). شريط بحث عام.
        *   **الشريط الجانبي (`_sidebar.blade.php`):** عنصر "الرئيسية" يكون نشطًا (highlighted). القائمة ديناميكية بناءً على صلاحيات المستخدم، مع أيقونات Font Awesome كما في `dashboard.html`.
        *   **منطقة المحتوى الرئيسية:**
            *   **عنوان الصفحة:** "الرئيسية" (H4، وزن خط غامق).
            *   **صف من بطاقات الإحصائيات العلوية (Stat Cards - First Row):** (4 بطاقات أفقية كما في `dashboard.html`، مع بيانات من `MOD-DASHBOARD-FEAT-002`)
                *   بطاقة 1: أيقونة (`fas fa-shopping-cart` بلون `--success-color`)، عنوان "طلبات جديدة اليوم"، الرقم (e.g., "28") بخط كبير، نص صغير "▲ 7.2% من الأسبوع السابق" (بلون أخضر للزيادة، أحمر للنقصان).
                *   بطاقة 2: أيقونة (`fas fa-money-check-alt` بلون مخصص `--purple-color`)، عنوان "طلبات التمويل المعلقة"، الرقم (e.g., "42")، نص صغير "▲ 8.5% من الشهر السابق".
                *   بطاقة 3: أيقونة (`fas fa-car` بلون `--info-color`)، عنوان "عدد السيارات المتاحة"، الرقم (e.g., "142")، نص صغير "▼ 3.1% من الشهر السابق".
                *   بطاقة 4: أيقونة (`fas fa-users` بلون `--warning-color`)، عنوان "عملاء جدد هذا الشهر"، الرقم (e.g., "865")، نص صغير "▲ 5.7% من الشهر السابق".
            *   **صف من بطاقات الإحصائيات المالية (Stat Cards - Second Row):** (4 بطاقات كما في `dashboard.html`)
                *   بطاقة 1: أيقونة (`fas fa-calendar-day` بلون `--success-color`)، عنوان "مبيعات اليوم"، القيمة (e.g., "156,500 ر.س")، نص صغير.
                *   بطاقة 2: أيقونة (`fas fa-calendar-week` بلون `--primary-color`)، عنوان "مبيعات الأسبوع"، القيمة، نص صغير.
                *   بطاقة 3: أيقونة (`fas fa-calendar-alt` بلون مخصص `--indigo-color`)، عنوان "مبيعات الشهر"، القيمة، نص صغير.
                *   بطاقة 4: أيقونة (`fas fa-dollar-sign` بلون `--primary-color`)، عنوان "إجمالي المبيعات السنوية"، القيمة، نص صغير.
            *   **صف الرسوم البيانية الأول (Charts Row):** (تصميم مستوحى من `dashboard.html`)
                *   **الرسم البياني للمبيعات (Sales Chart):** (مخطط خطي، كبير، يشغل حوالي 2/3 عرض الصف).
                    *   عنوان: "تقرير المبيعات". زر منسدل لاختيار الفترة الزمنية (هذا الأسبوع، هذا الشهر، آخر 3 أشهر، آخر 6 أشهر، هذا العام). الافتراضي: آخر 6 أشهر.
                    *   يعرض خط المبيعات (القيمة بالريال السعودي) وخط عدد الطلبات (المحور Y الثانوي). البيانات ديناميكية.
                *   **الرسم البياني لأفضل العلامات التجارية (Brands Pie Chart):** (مخطط دائري، يشغل حوالي 1/3 عرض الصف).
                    *   عنوان: "أفضل العلامات التجارية مبيعًا". زر منسدل لاختيار الفترة الزمنية. الافتراضي: هذا الشهر. البيانات ديناميكية.
            *   **صف الرسوم البيانية الإضافي الأول (Additional Charts Row):** (تصميم مستوحى من `dashboard.html`)
                *   **الرسم البياني لأفضل السيارات مبيعًا (Top Cars Chart):** (مخطط شريطي أفقي).
                    *   عنوان: "أفضل السيارات مبيعًا". زر منسدل للفترة. الافتراضي: هذا الشهر. البيانات ديناميكية.
                *   **الرسم البياني لتوزيع المبيعات حسب الفئة (Categories Chart):** (مخطط دائري مجوف - Doughnut).
                    *   عنوان: "توزيع المبيعات حسب الفئة". زر منسدل للفترة. الافتراضي: آخر 6 أشهر. البيانات ديناميكية.
            *   **صف الرسوم البيانية الإضافي الثاني (More Charts Row):** (تصميم مستوحى من `dashboard.html`)
                *   **الرسم البياني لحالة طلبات التمويل (Financing Status Chart):** (مخطط دائري مجوف).
                    *   عنوان: "حالة طلبات التمويل". البيانات ديناميكية.
                *   **الرسم البياني لتسجيل العملاء الجدد (New Customers Chart):** (مخطط خطي).
                    *   عنوان: "تسجيل العملاء الجدد (شهريًا)". البيانات ديناميكية.
                *   **الرسم البياني لنسب طرق الدفع (Payment Methods Chart):** (مخطط دائري مجوف).
                    *   عنوان: "نسب طرق الدفع". البيانات ديناميكية.
            *   **قسم أحدث السيارات المضافة (Latest Cars Added Table - إذا كان مطلوبًا ومناسبًا للوحة البيانات الرئيسية، وإلا يُركز عليه في صفحة إدارة السيارات):**
                *   (مستوحى من تصميم الجدول في `dashboard.html`، ولكن سيعرض بيانات ديناميكية من `MOD-CAR-CATALOG`).
                *   عنوان للقسم: "أحدث السيارات المضافة".
                *   جدول يعرض آخر 3-5 سيارات تم إضافتها.
                *   الأعمدة: صورة مصغرة، اسم السيارة (ماركة - موديل - فئة - سنة)، السعر، تاريخ الإضافة، رابط "تعديل".
            *   **صف آخر النشاطات والتنبيهات (Recent Activities & Alerts Row):** (تصميم مستوحى من `dashboard.html`)
                *   **جدول آخر النشاطات:** أعمدة: #, النشاط, العميل, التاريخ, الحالة (مع شارة ملونة), الإجراءات (أيقونات عرض، تعديل). رابط "عرض الكل". بيانات ديناميكية.
                *   **بطاقة التنبيهات المهمة:** تعرض تنبيهات هامة مثل "طلبات تمويل تحتاج للمراجعة (3)"، "مدفوعات معلقة (5)". بيانات ديناميكية.
            *   **صف أحدث السيارات المضافة ومؤشرات الأداء (Latest Cars & Performance Metrics Row):** (تصميم مستوحى من `dashboard.html`)
                *   **بطاقات أحدث السيارات المضافة:** صورتان أو ثلاث لسيارات مضافة حديثًا مع الاسم والسعر وأزرار عرض/تعديل. بيانات ديناميكية.
                *   **بطاقة مؤشرات الأداء:** "نسبة تحويل المبيعات"، "متوسط قيمة الطلب"، "رضا العملاء"، "نسبة قبول التمويل" باستخدام أشرطة تقدم (Progress Bars). بيانات ديناميكية.
    *   **البيانات المعروضة:** بيانات إحصائية ورسوم بيانية ديناميكية (كما هو مفصل في `MOD-DASHBOARD-FEAT-002` ضمن `REQ-FR.md`).
    *   **التفاعلات الرئيسية:**
        *   اختيار فترة زمنية للرسوم البيانية لتحديث البيانات المعروضة (يتم التحديث عبر AJAX أو إعادة تحميل جزئي إذا كان أسهل في التنفيذ الأولي).
        *   النقر على عناصر في جداول النشاطات أو التنبيهات للانتقال إلى التفاصيل.
        *   التنقل في ترقيم الصفحات للجداول (إذا كانت مستخدمة في هذه الصفحة).
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-DASHBOARD-FEAT-001`, `MOD-DASHBOARD-FEAT-002`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.1.3. صفحة إضافة/تعديل سيارة (Admin Car CRUD - Stepper) (`DASH-CAR-CRUD-001`)
*   **الغرض من الواجهة:** تمكين الإدارة من إضافة أو تعديل بيانات السيارات باستخدام واجهة Stepper متعددة الخطوات.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `admin_layout.blade.php`. منطقة المحتوى تعرض واجهة Stepper (المبنية على `bs-stepper` كما في `dashboard.html`).
    *   **عنوان الصفحة:** "إضافة سيارة جديدة" أو "تعديل بيانات السيارة: [اسم السيارة]". (يتم عرض اسم السيارة ديناميكيًا في حالة التعديل).
    *   **مكون Stepper (5 خطوات كما هو محدد في `MOD-CAR-CATALOG-FEAT-018` ضمن `REQ-FR.md` ومستوحى من `dashboard.html`):**
        *   **رأس Stepper (bs-stepper-header):** يعرض أيقونات وأسماء الخطوات الخمس بشكل واضح (1. البيانات الأساسية، 2. المواصفات الفنية، 3. الميزات، 4. الصور والفيديو، 5. السعر والحالة). الخطوة الحالية تكون مميزة بصريًا (باستخدام `class="active"` من bs-stepper).
        *   **محتوى Stepper (bs-stepper-content):**
            *   **الخطوة 1: البيانات الأساسية (`basic-info-part`):**
                *   جميع الحقول كما هي محددة في `REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-018`، الخطوة 1)، مع تسميات واضحة، مؤشرات للمطلوب (*)، وأي نصوص مساعدة (form-text) كما في `dashboard.html`.
                *   قوائم منسدلة (select) لـ "الماركة"، "الموديل"، "سنة الصنع"، "اللون الخارجي".
                *   محرر نصوص غني (Rich Text Editor - WYSIWYG، يمكن استخدام مكتبة مثل TinyMCE أو CKEditor مدمجة بشكل بسيط) لحقل "الوصف العام للسيارة".
                *   زر "التالي: المواصفات الفنية".
            *   **الخطوة 2: المواصفات الفنية (`tech-specs-part`):**
                *   جميع الحقول كما هي محددة في `REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-018`، الخطوة 2).
                *   استخدام `input type="number"` للحقول الرقمية مع تحديد `min` و `max` إذا كان مناسبًا.
                *   زر "السابق: البيانات الأساسية" و زر "التالي: الميزات".
            *   **الخطوة 3: الميزات (`features-part`):**
                *   عرض الميزات كقائمة Checkboxes مجمعة حسب الفئة (e.g., "الأمان والسلامة"، "الراحة والرفاهية"). كل فئة عنوان، تليها قائمة بالـ checkboxes للميزات ضمن تلك الفئة.
                *   زر "السابق: المواصفات الفنية" و زر "التالي: الصور والفيديو".
            *   **الخطوة 4: الصور والفيديو (`images-part`):**
                *   مكون رفع صور متقدم:
                    *   زر "اختيار ملفات" لرفع صور متعددة.
                    *   منطقة لمعاينة الصور المصغرة للصور المرفوعة.
                    *   إمكانية تحديد صورة واحدة كـ "صورة رئيسية" (باستخدام checkbox أو زر بجانب كل صورة).
                    *   إمكانية حذف صورة من قائمة المعاينة.
                    *   (متقدم) إمكانية إعادة ترتيب الصور بالسحب والإفلات.
                    *   عرض قيود الملفات (الحجم، الأنواع المسموح بها).
                *   حقل نصي لرابط الفيديو (اختياري).
                *   زر "السابق: الميزات" و زر "التالي: السعر والحالة".
            *   **الخطوة 5: السعر والحالة (`price-status-part`):**
                *   جميع الحقول كما هي محددة في `REQ-FR.md` (`MOD-CAR-CATALOG-FEAT-018`، الخطوة 5).
                *   عرض "نسبة الضريبة" و "السعر شامل الضريبة" كحقول للقراءة فقط، يتم تحديثها ديناميكيًا (عبر JavaScript إذا أمكن، أو عند الحفظ في الـ backend) بناءً على السعر الأساسي.
                *   استخدام منتقي تاريخ (Date Picker) لحقول تاريخ بدء وانتهاء العرض.
                *   زر "السابق: الصور والفيديو" و زر "حفظ السيارة" (بلون النجاح، e.g., أخضر).
    *   **البيانات المعروضة:** بيانات السيارة الحالية (في حالة التعديل)، قوائم الخيارات الديناميكية (ماركات، موديلات، إلخ).
    *   **التفاعلات الرئيسية:**
        *   التنقل بين خطوات Stepper باستخدام أزرار "التالي" و "السابق".
        *   تعبئة الحقول واختيار الخيارات من القوائم المنسدلة والـ checkboxes.
        *   رفع ومعاينة وإدارة الصور.
        *   تقديم النموذج بالكامل عند الضغط على "حفظ السيارة".
        *   عرض رسائل التحقق من الصحة (Validation messages) بجانب الحقول المعنية إذا فشل التحقق من جانب الخادم بعد التقديم.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CAR-CATALOG-FEAT-018`, `FEAT-CAR-008` إلى `FEAT-CAR-014`, `FEAT-CAR-016`, `FEAT-CAR-017`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.1.4. صفحة قائمة السيارات (`DASH-CAR-LIST-001`)
*   **الغرض من الواجهة:** عرض جميع السيارات المضافة إلى النظام مع أدوات للبحث، الفلترة، الترتيب، وإجراءات الإدارة.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `admin_layout.blade.php`.
    *   **عنوان الصفحة:** "إدارة السيارات" أو "قائمة السيارات".
    *   **أدوات التحكم العلوية (فوق الجدول):**
        *   **زر "إضافة سيارة جديدة":** (لون ثانوي أو تمييز، أيقونة `fas fa-plus`) ينقل إلى `DASH-CAR-CRUD-001`.
        *   **شريط البحث/الفلترة:** (مستوحى من تصميم مرن مشابه لـ `filter-bar.png` ولكن ضمن إطار Dash)
            *   حقل بحث نصي: "بحث بالاسم، الموديل، رقم الهيكل...".
            *   قائمة منسدلة "فلترة حسب الماركة".
            *   قائمة منسدلة "فلترة حسب الموديل" (تتحدث ديناميكيًا بناءً على الماركة).
            *   قائمة منسدلة "فلترة حسب الحالة" (متوفرة، محجوزة، مباعة، إلخ).
            *   (اختياري) فلاتر إضافية: سنة الصنع، نطاق السعر.
            *   زر "تطبيق الفلاتر" و زر "إعادة تعيين".
    *   **جدول السيارات:** (مستوحى من تصميم الجدول في `dashboard.html` وجداول البيانات القياسية)
        *   **رأس الجدول (thead):**
            *   أعمدة قابلة للفرز (مع أيقونات فرز `fas fa-sort`, `fa-sort-up`, `fa-sort-down`): "#", "صورة مصغرة", "اسم السيارة (ماركة-موديل-فئة-سنة)", "السعر", "الحالة", "مميزة؟", "تاريخ الإضافة", "الإجراءات".
        *   **جسم الجدول (tbody):**
            *   لكل سيارة (صف `<tr>`):
                *   الرقم التسلسلي أو ID.
                *   صورة مصغرة للسيارة (e.g., 60x40px).
                *   الاسم الكامل للسيارة.
                *   السعر (مع تمييز سعر العرض إن وجد).
                *   الحالة (شارة ملونة: أخضر لـ"متوفرة"، أصفر لـ"محجوزة"، أحمر لـ"مباعة").
                *   "مميزة؟" (أيقونة نجمة `fas fa-star` إذا كانت مميزة، أو نص "نعم/لا").
                *   تاريخ الإضافة (بتنسيق DD/MM/YYYY).
                *   **الإجراءات (عمود `<td>` يحتوي على أزرار أيقونات):**
                    *   زر "عرض" (`fas fa-eye`, ينقل إلى صفحة عرض تفاصيل السيارة في لوحة التحكم أو معاينة).
                    *   زر "تعديل" (`fas fa-edit`, ينقل إلى `DASH-CAR-CRUD-001` مع ملء بيانات السيارة).
                    *   زر "حذف" (`fas fa-trash`, يفتح مودال تأكيد الحذف).
                    *   (اختياري) زر سريع لـ "تغيير الحالة" (يفتح قائمة منسدلة بالحالات المتاحة).
    *   **ترقيم الصفحات (Pagination):** أسفل الجدول، يعرض روابط ترقيم الصفحات من Laravel منسقة لتناسب نمط Dash.
    *   **مودال تأكيد الحذف:**
        *   عنوان: "تأكيد الحذف".
        *   رسالة: "هل أنت متأكد أنك تريد حذف السيارة: [اسم السيارة]؟ لا يمكن التراجع عن هذا الإجراء."
        *   زر "إلغاء" (لون محايد) و زر "حذف" (لون الخطأ/Danger).
*   **البيانات المعروضة:** قائمة السيارات من `MOD-CAR-CATALOG` مع تفاصيلها الرئيسية.
*   **التفاعلات الرئيسية:**
    *   البحث والفلترة والترتيب لتحديث قائمة السيارات المعروضة (يفضل عبر AJAX أو تحديث جزئي للصفحة).
    *   النقر على أزرار الإجراءات (عرض، تعديل، حذف).
    *   التنقل بين صفحات النتائج.
    *   تأكيد أو إلغاء الحذف من المودال.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CAR-CATALOG-FEAT-001` (الجزء الإداري), `FEAT-CAR-008` إلى `FEAT-CAR-017`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.1.5. صفحات إدارة البيانات الوصفية للسيارات (ماركات، موديلات، ميزات، إلخ.)
*   **(مثال: صفحة إدارة الماركات `DASH-BRANDS-MGMT-001`)**
*   **الغرض من الواجهة:** تمكين الإدارة من إضافة، تعديل، وحذف ماركات السيارات. (واجهات مشابهة للموديلات، الألوان، سنوات الصنع، أنواع ناقل الحركة، أنواع الوقود، فئات الميزات، والميزات نفسها).
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `admin_layout.blade.php`.
    *   **عنوان الصفحة:** "إدارة الماركات".
    *   **أدوات التحكم العلوية:**
        *   زر "إضافة ماركة جديدة" (يفتح مودال أو ينقل إلى صفحة/قسم نموذج).
    *   **جدول الماركات:**
        *   **رأس الجدول:** "#", "شعار الماركة", "اسم الماركة", "الحالة (نشطة/غير نشطة)", "عدد الموديلات المرتبطة", "الإجراءات".
        *   **جسم الجدول:**
            *   لكل ماركة: ID, صورة الشعار (مصغرة), اسم الماركة, الحالة (شارة ملونة), عدد الموديلات المرتبطة بها.
            *   **الإجراءات:** زر "تعديل" (يفتح مودال/صفحة نموذج ببيانات الماركة)، زر "حذف" (مع تأكيد، مع التحقق من عدم وجود موديلات مرتبطة قبل الحذف النهائي أو تقديم خيار التعطيل).
    *   **ترقيم الصفحات (إذا كان عدد الماركات كبيرًا).**
    *   **نموذج (مودال أو صفحة/قسم منفصل) لإضافة/تعديل ماركة:**
        *   حقل "اسم الماركة" (نص، مطلوب، فريد).
        *   حقل "رفع شعار الماركة" (رفع ملف صورة). معاينة للشعار الحالي عند التعديل.
        *   حقل "وصف مختصر للماركة" (textarea، اختياري).
        *   حقل "الحالة" (قائمة اختيار: نشطة/غير نشطة، مطلوب).
        *   زر "حفظ" و زر "إلغاء".
*   **البيانات المعروضة:** قائمة الماركات من `MOD-CAR-CATALOG` (`brands` table).
*   **التفاعلات الرئيسية:** إضافة ماركة، تعديل بيانات ماركة، حذف/تعطيل ماركة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CAR-CATALOG-FEAT-008` (وللميزات الأخرى، `FEAT-CAR-009` إلى `FEAT-CAR-014`, `MOD-SERVICE-MGMT-FEAT-003`, `FEAT-SERVICE-004`).
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".
    *   **ملاحظة:** سيتم تطبيق هيكل مشابه لصفحات إدارة:
        *   الموديلات (`DASH-MODELS-MGMT-001`)
        *   الألوان (`DASH-COLORS-MGMT-001`)
        *   سنوات الصنع (`DASH-YEARS-MGMT-001`)
        *   أنواع ناقل الحركة (`DASH-TRANSMISSIONS-MGMT-001`)
        *   أنواع الوقود (`DASH-FUELS-MGMT-001`)
        *   ميزات السيارات (`DASH-CARFEATURES-MGMT-001`)
        *   فئات الميزات (`DASH-FEATURECATS-MGMT-001`)

##### 4.1.6. صفحة إدارة الطلبات (الكاش والتمويل) (`DASH-ORDERS-LIST-001`)
*   **الغرض من الواجهة:** عرض جميع طلبات العملاء (شراء كاش وتمويل) مع أدوات للبحث، الفلترة، وتحديث الحالة.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `admin_layout.blade.php`.
    *   **عنوان الصفحة:** "إدارة الطلبات".
    *   **أدوات التحكم العلوية (فوق الجدول):**
        *   **شريط تبويبات (Tabs):** "كل الطلبات", "طلبات الكاش", "طلبات التمويل", "طلبات جديدة", "قيد المعالجة", "مكتملة", "ملغاة". (النقر على تبويب يفلتر القائمة).
        *   **شريط البحث/الفلترة (مشابه لصفحة قائمة السيارات):**
            *   حقل بحث نصي: "بحث برقم الطلب، اسم العميل، رقم جوال العميل...".
            *   قائمة منسدلة "فلترة حسب نوع الطلب" (كاش، تمويل، طلب سيارة مخصصة).
            *   قائمة منسدلة "فلترة حسب حالة الطلب".
            *   منتقي تاريخ "فلترة حسب تاريخ الطلب" (نطاق من-إلى).
            *   زر "تطبيق الفلاتر" و زر "إعادة تعيين".
    *   **جدول الطلبات:**
        *   **رأس الجدول:** "رقم الطلب", "اسم العميل", "رقم جوال العميل", "السيارة المطلوبة", "نوع الطلب", "مبلغ الحجز", "السعر الإجمالي", "تاريخ الطلب", "حالة الطلب", "الموظف المسؤول (إن وجد)", "الإجراءات".
        *   **جسم الجدول:**
            *   لكل طلب: البيانات المذكورة أعلاه. "السيارة المطلوبة" تكون رابطًا لصفحة السيارة. "حالة الطلب" تكون شارة ملونة.
            *   **الإجراءات:**
                *   زر "عرض التفاصيل" (`fas fa-eye`, ينقل إلى صفحة تفاصيل الطلب `DASH-ORDER-DETAIL-001`).
                *   (اختياري) زر سريع لـ "تغيير الحالة" (يفتح قائمة منسدلة بالحالات المتاحة التالية).
                *   (اختياري) زر "تعيين موظف".
    *   **ترقيم الصفحات.**
*   **البيانات المعروضة:** قائمة الطلبات من `MOD-ORDER-MGMT`.
*   **التفاعلات الرئيسية:** البحث، الفلترة، الترتيب، عرض تفاصيل الطلب، تغيير حالة الطلب.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-ORDER-MGMT-FEAT-007`, `MOD-ORDER-MGMT-FEAT-008`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.1.7. صفحة تفاصيل الطلب (`DASH-ORDER-DETAIL-001`)
*   **الغرض من الواجهة:** عرض جميع تفاصيل طلب محدد، المستندات المرفقة، سجل التغييرات، وإمكانية تحديث الحالة وإضافة ملاحظات.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `admin_layout.blade.php`.
    *   **عنوان الصفحة:** "تفاصيل الطلب رقم: [رقم الطلب]".
    *   **أقسام الصفحة (باستخدام بطاقات أو تبويبات Tabs داخل بطاقة رئيسية):**
        *   **القسم 1: معلومات الطلب الأساسية:**
            *   رقم الطلب، تاريخ الطلب، نوع الطلب، حالة الطلب الحالية (مع قائمة منسدلة لتغيير الحالة وزر "تحديث الحالة").
            *   السعر الإجمالي، مبلغ الحجز، المبلغ المتبقي.
            *   طريقة الدفع، حالة الدفع، معرّف معاملة الدفع (إن وجد).
        *   **القسم 2: تفاصيل العميل:**
            *   اسم العميل (رابط لملف العميل `DASH-CUSTOMER-DETAIL-001` إذا كان مسجلاً)، رقم الجوال، البريد الإلكتروني.
            *   رقم الهوية، تاريخ الميلاد، الجنسية، تفاصيل العنوان (المدخلة وقت الطلب).
        *   **القسم 3: تفاصيل السيارة المطلوبة:**
            *   صورة السيارة، اسم السيارة (رابط لصفحة السيارة `DASH-CAR-VIEW-001`)، الماركة، الموديل، السنة، اللون.
        *   **القسم 4: المستندات المرفوعة (إذا كانت مطلوبة للطلب):** (يخدم `MOD-ORDER-MGMT-FEAT-006`)
            *   قائمة بالمستندات المرفوعة (اسم المستند، نوعه، تاريخ الرفع).
            *   لكل مستند: زر "معاينة/تنزيل".
            *   (اختياري) إمكانية "طلب مستند إضافي" من العميل.
        *   **القسم 5: تفاصيل التمويل (إذا كان طلب تمويل):**
            *   عرض البيانات التي أدخلها العميل في نموذج التمويل (الدخل، الالتزامات، جهة العمل، إلخ).
        *   **القسم 6: سجل ملاحظات وتحديثات الطلب:**
            *   عرض تاريخي للتغييرات التي طرأت على حالة الطلب ومن قام بها.
            *   حقل textarea "إضافة ملاحظة داخلية" مع زر "إضافة".
            *   قائمة بالملاحظات المضافة مسبقًا مع تاريخها واسم الموظف.
        *   **القسم 7: (اختياري) الفواتير والدفعات:** (يخدم `MOD-ORDER-MGMT-FEAT-009`)
            *   إذا كان الدفع المتبقي في المعرض، يمكن هنا إنشاء فاتورة للمبلغ المتبقي، وتسجيل الدفعات.
*   **البيانات المعروضة:** بيانات طلب محدد من `MOD-ORDER-MGMT`.
*   **التفاعلات الرئيسية:** تغيير حالة الطلب، إضافة ملاحظات، معاينة المستندات، (اختياري) إنشاء فاتورة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-ORDER-MGMT-FEAT-007`, `MOD-ORDER-MGMT-FEAT-008`, `MOD-ORDER-MGMT-FEAT-006`, `MOD-ORDER-MGMT-FEAT-009`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.1.8. صفحة إدارة العملاء (`DASH-CUSTOMERS-LIST-001`)
*   **الغرض من الواجهة:** عرض قائمة بالعملاء المسجلين مع إمكانية البحث، الفلترة، وعرض تفاصيل كل عميل.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `admin_layout.blade.php`.
    *   **عنوان الصفحة:** "إدارة العملاء".
    *   **أدوات التحكم العلوية:** زر "إضافة عميل جديد" (إذا كان مسموحًا به إداريًا)، شريط بحث/فلترة (بالاسم، البريد، الجوال، الحالة).
    *   **جدول العملاء:**
        *   **رأس الجدول:** "#", "الاسم الكامل", "البريد الإلكتروني", "رقم الجوال", "تاريخ التسجيل", "آخر تسجيل دخول", "عدد الطلبات", "الحالة (نشط/غير نشط/محظور)", "الإجراءات".
        *   **جسم الجدول:** بيانات العملاء.
        *   **الإجراءات:** زر "عرض التفاصيل" (`DASH-CUSTOMER-DETAIL-001`), زر "تعديل", زر "تغيير الحالة".
    *   **ترقيم الصفحات.**
*   **البيانات المعروضة:** قائمة العملاء من `MOD-USER-MGMT`.
*   **التفاعلات الرئيسية:** البحث، الفلترة، عرض تفاصيل عميل، تعديل، تغيير الحالة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-009`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.1.9. صفحة تفاصيل العميل (`DASH-CUSTOMER-DETAIL-001`)
*   **الغرض من الواجهة:** عرض معلومات مفصلة عن عميل محدد، طلباته، ترشيحاته، إلخ.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `admin_layout.blade.php`.
    *   **عنوان الصفحة:** "ملف العميل: [اسم العميل]".
    *   **أقسام الصفحة (باستخدام بطاقات أو تبويبات Tabs):**
        *   **معلومات العميل الشخصية:** الاسم، البريد، الجوال، تاريخ التسجيل، آخر تسجيل دخول، الحالة، العنوان، الصورة الشخصية (إن وجدت). زر "تعديل بيانات العميل" (ينقل إلى نموذج تعديل).
        *   **تبويب "طلبات العميل":** جدول بقائمة طلبات العميل (رقم الطلب، نوعه، تاريخه، حالته، رابط للتفاصيل).
        *   **تبويب "ترشيحات العميل" (إن وجدت):** جدول بقائمة العملاء الذين رشحهم هذا العميل وحالة كل ترشيح.
        *   (اختياري) تبويب "المعاملات المالية".
        *   (اختياري) تبويب "ملاحظات إدارية على العميل".
        *   خيار لتفعيل/تعطيل ميزة "رشح عميل" لهذا العميل (`can_refer_customer`).
*   **البيانات المعروضة:** بيانات عميل محدد من `MOD-USER-MGMT` وبيانات مرتبطة من موديولات أخرى.
*   **التفاعلات الرئيسية:** عرض البيانات، التنقل بين التبويبات، تعديل بيانات العميل، تفعيل/تعطيل ميزة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-009`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.1.10. صفحة إدارة الموظفين (`DASH-EMPLOYEES-LIST-001`)
*   **الغرض من الواجهة:** عرض وإدارة حسابات الموظفين وتعيين الأدوار والصلاحيات.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01` (مدير النظام فقط).
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `admin_layout.blade.php`.
    *   **عنوان الصفحة:** "إدارة الموظفين".
    *   **أدوات التحكم العلوية:** زر "إضافة موظف جديد".
    *   **جدول الموظفين:**
        *   **رأس الجدول:** "#", "الاسم الكامل", "البريد الإلكتروني", "الأدوار المعينة", "الحالة (نشط/غير نشط)", "الإجراءات".
        *   **جسم الجدول:** بيانات الموظفين.
        *   **الإجراءات:** زر "تعديل" (يفتح نموذج تعديل الموظف مع أدواره وحالته), زر "إعادة تعيين كلمة المرور", زر "تغيير الحالة".
    *   **نموذج إضافة/تعديل موظف (مودال أو صفحة):**
        *   الاسم الكامل، البريد الإلكتروني، (اختياري) رقم الجوال.
        *   قائمة متعددة الاختيار (checkboxes) للأدوار المتاحة في النظام (من `spatie/laravel-permission`).
        *   قائمة اختيار للحالة (نشط/غير نشط).
        *   زر "حفظ".
*   **البيانات المعروضة:** قائمة الموظفين من `MOD-USER-MGMT`، قائمة الأدوار من `spatie/laravel-permission`.
*   **التفاعلات الرئيسية:** إضافة موظف، تعديل بياناته وأدواره، إعادة تعيين كلمة المرور، تغيير الحالة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-010`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.1.11. صفحة إدارة الأدوار والصلاحيات (`DASH-ROLES-PERMISSIONS-MGMT-001`)
*   **الغرض من الواجهة:** إدارة أدوار المستخدمين وتعيين الصلاحيات لكل دور.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01` (مدير النظام فقط).
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `admin_layout.blade.php`.
    *   **عنوان الصفحة:** "إدارة الأدوار والصلاحيات".
    *   **القسم الأيسر: قائمة الأدوار:**
        *   جدول يعرض الأدوار الموجودة (اسم الدور، عدد المستخدمين بهذا الدور).
        *   زر "إضافة دور جديد".
        *   لكل دور: زر "تعديل" (يُحمّل تفاصيل الدور والصلاحيات في القسم الأيمن).
    *   **القسم الأيمن: تفاصيل الدور والصلاحيات (يظهر عند اختيار دور للتعديل أو عند إضافة دور جديد):**
        *   حقل "اسم الدور" (نص، فريد).
        *   (اختياري) حقل "اسم عرض للدور".
        *   **قائمة الصلاحيات:** شجرة أو قائمة مجمعة (grouped list) بجميع الصلاحيات المعرفة في النظام (e.g., `cars.view`, `cars.create`, `orders.edit_status`). يتم عرضها كـ checkboxes.
        *   زر "حفظ التغييرات للدور".
*   **البيانات المعروضة:** الأدوار والصلاحيات من `spatie/laravel-permission`.
*   **التفاعلات الرئيسية:** إضافة دور، تعديل اسم دور، تحديد/إلغاء تحديد الصلاحيات لدور.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-010`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.1.12. صفحات إدارة المحتوى (CMS)
*   **(مثال: صفحة إدارة الصفحات الثابتة `DASH-CMS-PAGES-LIST-001`)**
*   **الغرض:** تمكين الإدارة من إنشاء وتعديل الصفحات الثابتة (من نحن، سياسة، إلخ).
*   **الهيكل السلكي:** مشابه لصفحة إدارة الماركات، مع جدول يعرض (عنوان الصفحة، الرابط slug، الحالة، تاريخ آخر تعديل، إجراءات "تعديل" و "حذف/تعطيل"). نموذج الإضافة/التعديل يتضمن (عنوان، slug، محتوى الصفحة عبر محرر Rich Text، حقول SEO، الحالة).
*   **ربط بـ `REQ-FR.md`:** `MOD-CMS-FEAT-001`.
*   **(مثال: صفحة إدارة بنرات الصفحة الرئيسية `DASH-CMS-BANNERS-LIST-001`)**
*   **الغرض:** إدارة بنرات السلايدر في الصفحة الرئيسية.
*   **الهيكل السلكي:** جدول يعرض (صورة البنر المصغرة، العنوان، الرابط، الترتيب، الحالة، إجراءات). نموذج إضافة/تعديل يتضمن (رفع صورة، حقول العنوان، الرابط، الترتيب، الحالة).
*   **ربط بـ `REQ-FR.md`:** `MOD-CMS-FEAT-002`.

##### 4.1.13. صفحة إعدادات النظام (`DASH-SYSTEM-SETTINGS-001`)
*   **الغرض من الواجهة:** تمكين مدير النظام من تعديل إعدادات النظام العامة.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-ADMIN-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `admin_layout.blade.php`.
    *   **عنوان الصفحة:** "إعدادات النظام".
    *   **نظام تبويبات (Tabs) لتنظيم الإعدادات (مستوحى من `dashboard.html` إذا كان يحتوي على نظام تبويبات مشابه أو يتم بناؤه باستخدام مكونات Bootstrap Tabs):**
        *   **تبويب "الإعدادات العامة":**
            *   حقل "اسم المعرض/الموقع".
            *   حقل "البريد الإلكتروني للإدارة".
            *   حقل "رقم الهاتف الرئيسي للمعرض".
            *   حقل "عنوان المعرض".
            *   حقل "شعار الموقع (للرأس والتذييل)" (رفع صورة).
            *   حقل "أيقونة المفضلة (Favicon)" (رفع صورة).
            *   حقل "العملة الافتراضية" (e.g., "ر.س").
            *   حقل "نسبة الضريبة المضافة (%)" (رقم).
            *   حقل "مبلغ الحجز الافتراضي للسيارات" (رقم).
            *   حقل "نص خدمات ما بعد البيع المجانية" (textarea، للقيمة 500 ريال).
        *   **تبويب "إعدادات SEO":**
            *   حقل "الكلمات المفتاحية الافتراضية للموقع".
            *   حقل "الوصف الافتراضي للموقع".
            *   (اختياري) حقول لإضافة أكواد تتبع (Google Analytics ID).
        *   **تبويب "إعدادات الدفع":**
            *   قائمة ببوابات الدفع المدعومة (يتم عرضها).
            *   لكل بوابة دفع: حقول لتكوين مفاتيح API الخاصة بها (merchant ID, secret key, etc.).
            *   زر "تفعيل/تعطيل" لكل بوابة.
        *   **تبويب "إعدادات الإشعارات (SMS/Email)":**
            *   تكوين بوابة SMS (API Key, Sender ID).
            *   تكوين إعدادات خادم البريد (SMTP Host, Port, Username, Password, Encryption).
            *   (متقدم) قائمة بقوالب البريد الإلكتروني القابلة للتعديل (إذا تم تطبيق `MOD-NOTIFICATION-FEAT-001` مع هذه الميزة).
        *   **تبويب "الحدود والقيود":**
            *   حقل "الحد الأقصى لحجم صور السيارات المرفوعة (MB)".
            *   حقل "الحد الأقصى لحجم المستندات المرفوعة من العملاء (MB)".
        *   **زر "حفظ الإعدادات"** كبير وواضح أسفل كل تبويب أو زر حفظ عام للصفحة.
*   **البيانات المعروضة:** قيم الإعدادات الحالية من جدول `settings` (`MOD-CORE-FEAT-003`).
*   **التفاعلات الرئيسية:** تعديل قيم الإعدادات، رفع الصور، حفظ التغييرات.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CORE-FEAT-003`, `FEAT-ADMIN-003`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

#### 4.2. لوحة تحكم العميل (Customer Dashboard)

ستعتمد لوحة تحكم العميل على نفس الهيكل الأساسي والتصميم البصري للوحة تحكم الإدارة (المستمدة من أصول Dash) لضمان الاتساق، ولكن مع مجموعة مختلفة من عناصر القائمة والميزات الموجهة للعميل. سيتم استخدام `customer_layout.blade.php` (الذي قد يكون نسخة معدلة قليلاً من `admin_layout.blade.php` أو يعيد استخدام نفس التخطيط مع تمرير قائمة جانبية مختلفة).

##### 4.2.1. صفحة تسجيل الدخول للوحة تحكم العميل
*   **ملاحظة:** يفترض أن يستخدم العملاء نفس صفحة تسجيل الدخول العامة للموقع أو التطبيق. بعد تسجيل الدخول الناجح، إذا كان المستخدم عميلاً، يتم توجيهه إلى لوحة تحكم العميل (`DASH-CUSTOMER-HOME-001`). إذا كانت هناك حاجة لصفحة تسجيل دخول منفصلة للوحة تحكم العميل (مثلاً إذا كان لها نطاق فرعي خاص)، فسيكون تصميمها مشابهًا جدًا لـ `DASH-ADMIN-LOGIN-001` مع تغيير العنوان إلى "تسجيل دخول العملاء".
*   **الربط بالمتطلبات:** `MOD-USER-MGMT-FEAT-002`.

##### 4.2.2. الصفحة الرئيسية للوحة تحكم العميل (ملخص الحساب) (`DASH-CUSTOMER-HOME-001`)
*   **الغرض من الواجهة:** عرض ملخص لحساب العميل، طلباته الأخيرة، والوصول السريع إلى الوظائف الهامة.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `customer_layout.blade.php`.
    *   **الشريط العلوي (`_topbar.blade.php` - نسخة العميل):** يعرض اسم العميل المسجل، أيقونة الإشعارات (`fas fa-bell`) مع عداد، قائمة منسدلة للمستخدم (رابط للملف الشخصي `DASH-CUSTOMER-PROFILE-001`، رابط لتسجيل الخروج). زر تبديل القائمة الجانبية. (قد لا يحتوي على شريط بحث عام).
    *   **الشريط الجانبي (`_customer_sidebar.blade.php`):** قائمة مخصصة للعميل:
        *   "الرئيسية" (نشط)
        *   "طلباتي" (ينقل إلى `DASH-CUSTOMER-ORDERS-001`)
        *   "طلبات التمويل" (ينقل إلى `DASH-CUSTOMER-FINANCE-ORDERS-001`)
        *   "المفضلة" (ينقل إلى `DASH-CUSTOMER-FAVORITES-001`)
        *   "رشح عميل" (إذا كان مؤهلاً، ينقل إلى `DASH-CUSTOMER-REFERRAL-001`)
        *   "المعاملات المالية" (ينقل إلى `DASH-CUSTOMER-TRANSACTIONS-001`)
        *   "الإشعارات" (ينقل إلى `DASH-CUSTOMER-NOTIFICATIONS-001`)
        *   "الملف الشخصي" (ينقل إلى `DASH-CUSTOMER-PROFILE-001`)
        *   "تسجيل الخروج"
    *   **منطقة المحتوى الرئيسية:**
        *   **رسالة ترحيب:** "مرحباً بك، [اسم العميل]!" (H4، وزن خط غامق).
        *   **صف من بطاقات الملخص (Stat Cards - مشابهة لتصميم Dash ولكن بمحتوى مختلف):**
            *   **بطاقة "الطلبات الحالية":** أيقونة (`fas fa-tasks`)، عنوان "الطلبات قيد المعالجة"، الرقم (عدد الطلبات التي حالتها ليست "مكتمل" أو "ملغى"). رابط "عرض كل الطلبات".
            *   **بطاقة "السيارات المفضلة":** أيقونة (`fas fa-heart`)، عنوان "سياراتك المفضلة"، الرقم (عدد السيارات في قائمة المفضلة). رابط "إدارة المفضلة".
            *   **بطاقة "آخر طلب":** (اختياري) تعرض تفاصيل مختصرة لآخر طلب قدمه العميل (رقم الطلب، السيارة، الحالة). رابط "عرض تفاصيل الطلب".
            *   **بطاقة "الإشعارات غير المقروءة":** أيقونة (`fas fa-bell`)، عنوان "لديك إشعارات جديدة"، الرقم (عدد الإشعارات غير المقروءة). رابط "عرض الإشعارات".
        *   **(اختياري) قسم "إجراءات سريعة":**
            *   أزرار كبيرة أو روابط لأكثر الإجراءات شيوعًا: "تصفح السيارات الجديدة"، "اطلب سيارتك المخصصة".
        *   **(اختياري) قسم "عروض قد تهمك":**
            *   عرض 2-3 بطاقات لعروض ترويجية نشطة أو سيارات مميزة قد تهم العميل.
*   **البيانات المعروضة:** بيانات ملخصة من `MOD-USER-MGMT`, `MOD-ORDER-MGMT`, `MOD-CAR-CATALOG`, `MOD-NOTIFICATION`.
*   **التفاعلات الرئيسية:** النقر على الروابط للانتقال إلى الأقسام المختلفة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-004` (القسم الرئيسي).
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.2.3. صفحة "طلباتي" (`DASH-CUSTOMER-ORDERS-001`)
*   **الغرض من الواجهة:** تمكين العميل من عرض قائمة بجميع طلباته (شراء، تمويل، خدمات) وتتبع حالتها.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `customer_layout.blade.php`.
    *   **عنوان الصفحة:** "طلباتي".
    *   **أدوات التحكم العلوية (فوق الجدول):**
        *   (اختياري) قائمة منسدلة "فلترة حسب نوع الطلب" (الكل، شراء كاش، تمويل، خدمات).
        *   (اختياري) قائمة منسدلة "فلترة حسب حالة الطلب".
    *   **جدول الطلبات:** (تصميم جدول مشابه لـ Dash Admin)
        *   **رأس الجدول:** "رقم الطلب", "تاريخ الطلب", "نوع الطلب", "السيارة/الخدمة المطلوبة", "المبلغ الإجمالي/الحجز", "حالة الطلب", "الإجراءات".
        *   **جسم الجدول:**
            *   لكل طلب: البيانات المذكورة. "السيارة/الخدمة المطلوبة" قد تكون نصًا أو رابطًا إذا كان هناك صفحة تفاصيل للخدمة. "حالة الطلب" تكون شارة ملونة.
            *   **الإجراءات:** زر "عرض التفاصيل" (`fas fa-eye`, ينقل إلى `DASH-CUSTOMER-ORDER-DETAIL-001`). (اختياري) زر "رفع مستندات" إذا كانت الحالة تتطلب ذلك.
    *   **ترقيم الصفحات.**
    *   **حالة الفراغ (Empty State):** إذا لم يكن هناك طلبات، تُعرض رسالة "ليس لديك أي طلبات حاليًا. يمكنك تصفح سياراتنا وبدء طلب جديد!" مع زر "تصفح السيارات".
*   **البيانات المعروضة:** قائمة طلبات العميل من `MOD-ORDER-MGMT` (و `MOD-SERVICE-MGMT` إذا كانت طلبات الخدمات مدرجة هنا).
*   **التفاعلات الرئيسية:** فلترة الطلبات، عرض تفاصيل طلب، التنقل بين الصفحات.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-006`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.2.4. صفحة تفاصيل الطلب للعميل (`DASH-CUSTOMER-ORDER-DETAIL-001`)
*   **الغرض من الواجهة:** عرض تفاصيل طلب محدد للعميل، المستندات المطلوبة/المرفوعة، والخطوات التالية.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `customer_layout.blade.php`.
    *   **عنوان الصفحة:** "تفاصيل الطلب رقم: [رقم الطلب]".
    *   **أقسام الصفحة (باستخدام بطاقات Dash أو تصميم مشابه):**
        *   **معلومات الطلب الأساسية:** رقم الطلب، تاريخ الطلب، نوع الطلب، حالة الطلب الحالية (نص واضح).
        *   **تفاصيل السيارة/الخدمة المطلوبة:** صورة، اسم، مواصفات رئيسية، السعر.
        *   **ملخص الدفع:** مبلغ الحجز المدفوع، المبلغ المتبقي (إذا كان شراء كاش)، طريقة الدفع المستخدمة.
        *   **المستندات المطلوبة/المرفوعة:** (يخدم `MOD-ORDER-MGMT-FEAT-006` - جزء العميل)
            *   قائمة بالمستندات (مثل: نسخة من الهوية، رخصة القيادة).
            *   لكل مستند: حالته (مطلوب، تم الرفع، قيد المراجعة، مقبول، مرفوض - مع سبب الرفض).
            *   إذا كان "مطلوبًا" أو "مرفوضًا": زر "رفع مستند" (يفتح مودال لرفع الملف).
            *   إذا كان "تم الرفع" أو "مقبولاً": أيقونة لعرض/تنزيل المستند المرفوع.
        *   **الخطوات التالية / تعليمات:** نص يوضح للعميل ما هي الخطوات التالية المتوقعة منه أو من المعرض.
        *   (اختياري) سجل بسيط بحالة الطلب (قائمة بالتحديثات الرئيسية على حالة الطلب).
    *   **زر "العودة إلى طلباتي".**
*   **البيانات المعروضة:** بيانات طلب محدد للعميل من `MOD-ORDER-MGMT`.
*   **التفاعلات الرئيسية:** رفع المستندات، عرض/تنزيل المستندات.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-006`, `MOD-ORDER-MGMT-FEAT-006`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.2.5. صفحة "طلبات التمويل" (`DASH-CUSTOMER-FINANCE-ORDERS-001`)
*   **الغرض:** تمكين العميل من عرض طلبات التمويل التي قدمها وحالتها.
*   **التصميم:** مشابه جدًا لصفحة "طلباتي" (`DASH-CUSTOMER-ORDERS-001`) ولكن يتم فلترتها لعرض طلبات التمويل فقط. قد تكون الأعمدة مختلفة قليلاً لتعكس طبيعة طلب التمويل (مثل "جهة التمويل المقترحة" إذا كانت هذه البيانات متوفرة).
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-004` (الجزء الخاص بطلبات التمويل).

##### 4.2.6. صفحة "المفضلة" (`DASH-CUSTOMER-FAVORITES-001`)
*   **الغرض من الواجهة:** عرض السيارات التي أضافها العميل إلى قائمته المفضلة، مع إمكانية إزالتها أو مقارنتها.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `customer_layout.blade.php`.
    *   **عنوان الصفحة:** "قائمة المفضلة".
    *   **عرض السيارات المفضلة:**
        *   تُعرض السيارات كشبكة من البطاقات (مشابهة لبطاقات السيارات في الموقع العام أو Dash Admin).
        *   كل بطاقة تعرض: صورة السيارة المصغرة، اسم السيارة، السعر.
        *   أزرار على كل بطاقة:
            *   زر "عرض التفاصيل" (ينقل إلى صفحة تفاصيل السيارة في الموقع العام).
            *   زر "إزالة من المفضلة" (أيقونة قلب مكسور أو زر نصي).
            *   Checkbox "للمقارنة" (لاختيار السيارة لإضافتها إلى قائمة المقارنة).
    *   **أدوات التحكم العلوية (إذا كانت القائمة طويلة):**
        *   زر "قارن السيارات المختارة" (يصبح مفعلاً عند اختيار سيارتين على الأقل، ينقل إلى صفحة المقارنة).
        *   (اختياري) زر "إفراغ المفضلة".
    *   **حالة الفراغ (Empty State):** إذا لم يكن هناك سيارات في المفضلة، تُعرض رسالة "قائمة مفضلتك فارغة. ابدأ بإضافة السيارات التي تعجبك!" مع زر "تصفح السيارات".
*   **البيانات المعروضة:** قائمة السيارات المفضلة للعميل من `MOD-CAR-CATALOG` (`user_favorites` table).
*   **التفاعلات الرئيسية:** إزالة سيارة من المفضلة، اختيار سيارات للمقارنة، الانتقال لصفحة المقارنة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CAR-CATALOG-FEAT-005B`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.2.7. صفحة "رشح عميل" (`DASH-CUSTOMER-REFERRAL-001`)
*   **الغرض من الواجهة:** تمكين العملاء المؤهلين من ترشيح عملاء جدد للمعرض.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01` (المؤهلون فقط - يتم التحكم في ظهور هذا العنصر في القائمة الجانبية بناءً على حقل `can_refer_customer` للمستخدم).
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `customer_layout.blade.php`.
    *   **عنوان الصفحة:** "رشح عميل واكسب مكافأة" (أو عنوان مشابه).
    *   **نص توضيحي:** يشرح برنامج الترشيح والمكافآت المحتملة (إذا وجدت).
    *   **نموذج الترشيح:** (تصميم نموذج Dash بسيط)
        *   حقل "اسم العميل المرشح" (نص، مطلوب).
        *   حقل "رقم جوال العميل المرشح" (نص، مطلوب، بتنسيق سعودي).
        *   حقل "البريد الإلكتروني للعميل المرشح" (اختياري، بريد إلكتروني).
        *   حقل "ملاحظات إضافية" (textarea، اختياري).
        *   زر "إرسال الترشيح" (لون ثانوي).
    *   **قسم "ترشيحاتك السابقة":**
        *   جدول يعرض الترشيحات التي قدمها العميل سابقًا.
        *   الأعمدة: اسم العميل المرشح، رقم جواله، تاريخ الترشيح، حالة الترشيح (جديد، تم التواصل، مهتم، إلخ - شارة ملونة).
*   **البيانات المعروضة:** نموذج فارغ للترشيح، قائمة بالترشيحات السابقة للعميل من `MOD-USER-MGMT` (`customer_referrals` table).
*   **التفاعلات الرئيسية:** ملء وتقديم نموذج الترشيح، عرض الترشيحات السابقة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-008`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.2.8. صفحة "المعاملات المالية" (`DASH-CUSTOMER-TRANSACTIONS-001`)
*   **الغرض من الواجهة:** عرض سجل بالمعاملات المالية للعميل (مثل دفعات الحجز) وأي فواتير تم إنشاؤها.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `customer_layout.blade.php`.
    *   **عنوان الصفحة:** "المعاملات المالية والفواتير".
    *   **جدول المعاملات/الفواتير:**
        *   **رأس الجدول:** "رقم المعاملة/الفاتورة", "تاريخ المعاملة", "الوصف (e.g., حجز سيارة تويوتا كامري)", "المبلغ", "الحالة (مدفوعة/غير مدفوعة)", "الإجراءات".
        *   **جسم الجدول:** بيانات المعاملات.
        *   **الإجراءات:** زر "عرض التفاصيل" (إذا كانت هناك تفاصيل إضافية للمعاملة)، زر "تنزيل الفاتورة (PDF)" (إذا كانت فاتورة).
    *   **حالة الفراغ:** "لا توجد معاملات مالية لعرضها حاليًا."
*   **البيانات المعروضة:** سجل المعاملات من `MOD-ORDER-MGMT` (المرتبطة بدفعات الحجز) أو من نظام فواتير مخصص إذا تم بناؤه.
*   **التفاعلات الرئيسية:** عرض قائمة المعاملات، تنزيل الفواتير.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-007`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.2.9. صفحة "الإشعارات" (`DASH-CUSTOMER-NOTIFICATIONS-001`)
*   **الغرض من الواجهة:** عرض جميع الإشعارات الخاصة بالعميل في مكان مركزي.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `customer_layout.blade.php`.
    *   **عنوان الصفحة:** "مركز الإشعارات".
    *   **أدوات التحكم العلوية:**
        *   زر "تحديد الكل كمقروء".
        *   (اختياري) قائمة منسدلة "فلترة" (الكل، غير مقروءة فقط).
    *   **قائمة الإشعارات:** (تُعرض كقائمة من العناصر أو بطاقات صغيرة)
        *   لكل إشعار:
            *   أيقونة تمثل نوع الإشعار (e.g., طلب، تحديث، عرض).
            *   نص الإشعار (قد يتضمن رابطًا للجزء المعني من الموقع/اللوحة).
            *   تاريخ ووقت الإشعار.
            *   علامة "غير مقروء" (نقطة ملونة أو خلفية مختلفة) إذا لم يتم قراءته.
            *   (اختياري) زر "تعليم كمقروء/غير مقروء" أو زر "حذف الإشعار".
    *   **ترقيم الصفحات إذا كانت القائمة طويلة.**
    *   **حالة الفراغ:** "ليس لديك أي إشعارات جديدة."
*   **البيانات المعروضة:** إشعارات العميل من `MOD-NOTIFICATION` (جدول `notifications` في Laravel).
*   **التفاعلات الرئيسية:** قراءة الإشعارات، تعليمها كمقروءة، حذفها، الانتقال إلى الروابط المضمنة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-NOTIFICATION-FEAT-002` (جزء العميل).
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

##### 4.2.10. صفحة "الملف الشخصي" (`DASH-CUSTOMER-PROFILE-001`)
*   **الغرض من الواجهة:** تمكين العميل من تعديل معلوماته الشخصية وتغيير كلمة المرور.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `customer_layout.blade.php`.
    *   **عنوان الصفحة:** "الملف الشخصي وإعدادات الحساب".
    *   **نظام تبويبات (Tabs) أو أقسام منفصلة ببطاقات Dash:**
        *   **تبويب/قسم "المعلومات الشخصية":**
            *   عرض الصورة الشخصية الحالية مع زر "تغيير الصورة" (يفتح مربع حوار لاختيار ملف).
            *   حقل "الاسم الأول" (input type="text", قابل للتعديل).
            *   حقل "اسم العائلة" (input type="text", قابل للتعديل).
            *   حقل "البريد الإلكتروني" (للقراءة فقط).
            *   حقل "رقم الجوال" (input type="text", قابل للتعديل - يتطلب تحقق OTP جديد إذا تم تغييره).
            *   حقل "العنوان - السطر الأول" (input type="text", قابل للتعديل).
            *   حقل "المدينة" (input type="text", أو قائمة منسدلة إذا كانت المدن محددة).
            *   حقل "رقم الهوية/الإقامة" (input type="text", قابل للتعديل).
            *   حقل "تاريخ الميلاد" (منتقي تاريخ, قابل للتعديل).
            *   حقل "الجنسية" (قائمة منسدلة من `nationalities` table, قابل للتعديل).
            *   زر "حفظ التغييرات للمعلومات الشخصية".
        *   **تبويب/قسم "تغيير كلمة المرور":**
            *   حقل "كلمة المرور الحالية" (input type="password").
            *   حقل "كلمة المرور الجديدة" (input type="password").
            *   حقل "تأكيد كلمة المرور الجديدة" (input type="password").
            *   زر "تغيير كلمة المرور".
        *   **(اختياري) تبويب/قسم "تفضيلات الإشعارات":**
            *   Checkboxes للتحكم في أنواع الإشعارات التي يرغب العميل في تلقيها (e.g., إشعارات عن العروض الجديدة، تحديثات حالة الطلب عبر البريد).
            *   زر "حفظ تفضيلات الإشعارات".
*   **البيانات المعروضة:** بيانات الملف الشخصي الحالية للعميل من `MOD-USER-MGMT`.
*   **التفاعلات الرئيسية:** تعديل الحقول، رفع صورة، حفظ التغييرات، تغيير كلمة المرور. عرض رسائل تأكيد أو خطأ.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-005`.
*   **ربط بالمخرجات النهائية:** "لوحة تحكم احترافية".

### 5. واجهات مستخدم الموقع العام (Public Blade UI) - تصميم تفصيلي وصفي

**(معرف القسم: `UIUX-SITE-VIEWS-001`)**

يصف هذا القسم الهياكل السلكية النصية المقترحة لواجهات الموقع العام التي سيتم بناؤها باستخدام Laravel Blade. ستركز هذه الواجهات على توفير تجربة تصفح وشراء سلسة للعملاء الأفراد وممثلي الشركات.

#### 5.1. التخطيط العام للموقع (Site Layout - `SITE-LAYOUT-MAIN-001`)
*   **الغرض:** توفير هيكل أساسي مشترك لجميع صفحات الموقع العام.
*   **الهيكل السلكي النصي:**
    *   **الرأس (Header):**
        *   **الشريط العلوي (Top Bar - اختياري):** قد يحتوي على روابط سريعة (مثل "تواصل معنا"، "فروعنا") أو أيقونات وسائل التواصل الاجتماعي.
        *   **الشريط الرئيسي (Main Navigation Bar):**
            *   **شعار المعرض:** على اليمين.
            *   **قائمة التنقل الرئيسية (Items):** "الرئيسية", "السيارات الجديدة", "خدماتنا", "مبيعات الشركات", "عروض السيارات", "اطلب سيارتك", "من نحن", "اتصل بنا". (يتم تمييز العنصر النشط).
            *   **على اليسار:**
                *   أيقونة بحث (تفتح حقل بحث أو مودال بحث).
                *   أيقونة "المفضلة" (مع عداد للسيارات في المفضلة، تنقل إلى صفحة المفضلة أو تفتح قائمة منسدلة بها).
                *   زر/رابط "تسجيل الدخول / إنشاء حساب" (إذا لم يكن المستخدم مسجلاً دخوله).
                *   اسم المستخدم وصورة مصغرة مع قائمة منسدلة (إذا كان المستخدم مسجلاً دخوله، تنقل إلى لوحة تحكم العميل، الملف الشخصي، تسجيل الخروج).
    *   **منطقة المحتوى الرئيسية (`@yield('content')`):** يتم فيها عرض محتوى كل صفحة.
    *   **التذييل (Footer):**
        *   **أقسام (Columns):**
            *   **عن المعرض:** نبذة مختصرة، روابط لـ "من نحن"، "الوظائف" (إن وجدت).
            *   **روابط سريعة:** "السيارات الجديدة"، "الخدمات"، "العروض".
            *   **دعم العملاء:** "الأسئلة الشائعة"، "سياسة الخصوصية"، "الشروط والأحكام"، "اتصل بنا".
            *   **معلومات الاتصال:** العنوان، رقم الهاتف، البريد الإلكتروني. أيقونات وسائل التواصل الاجتماعي.
        *   **شريط حقوق النشر:** "جميع الحقوق محفوظة © [السنة] [اسم المعرض]". (رابط لـ Bluemedia كما هو مطلوب).

#### 5.2. الصفحة الرئيسية (`SITE-HOME-001`)
*   **الغرض:** عرض نظرة عامة جذابة عن المعرض، أحدث السيارات، العروض، والخدمات.
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`, زوار جدد.
*   **الهيكل السلكي النصي (مستوحى هيكليًا من تنظيم صفحات رئيسية لمعارض سيارات شائعة مع تطبيق هوية المشروع):**
    *   **التخطيط العام:** تستخدم `SITE-LAYOUT-MAIN-001`.
    *   **المكونات الرئيسية (مرتبة من الأعلى للأسفل):**
        *   **قسم السلايدر الرئيسي (Hero Section / Main Slider):**
            *   سلايدر يعرض صورًا عالية الجودة (بنرات مُدارة من `MOD-CMS-FEAT-002`).
            *   كل سلايد يحتوي على: صورة خلفية جذابة، عنوان رئيسي، عنوان فرعي (اختياري)، زر "اكتشف المزيد" أو "تسوق الآن" (يربط بصفحة العرض أو قائمة السيارات).
            *   أسهم تنقل لليسار واليمين، ونقاط للإشارة إلى السلايد الحالي.
        *   **قسم البحث السريع عن سيارة (Quick Car Search Bar - اختياري):**
            *   نموذج مبسط يحتوي على قوائم منسدلة: "اختر الماركة"، "اختر الموديل"، (اختياري) "السنة"، (اختياري) "نطاق السعر".
            *   زر "بحث".
        *   **قسم "أحدث السيارات" أو "السيارات المميزة":**
            *   عنوان للقسم (e.g., "أحدث ما وصلنا" أو "سيارات مميزة نوصي بها").
            *   شبكة (Grid) تعرض 4-8 بطاقات سيارات (تصميم بطاقة موحد).
            *   كل بطاقة سيارة تعرض: صورة السيارة الرئيسية، اسم السيارة (ماركة-موديل-سنة)، السعر، زر "عرض التفاصيل".
            *   رابط "عرض جميع السيارات" أسفل القسم.
        *   **قسم "العروض الحالية":**
            *   عنوان للقسم (e.g., "لا تفوت عروضنا الحصرية").
            *   عرض 2-4 بطاقات عروض (صورة العرض، اسم العرض، زر "اكتشف العرض").
            *   رابط "عرض جميع العروض" أسفل القسم.
        *   **قسم "خدماتنا":**
            *   عنوان للقسم (e.g., "خدمات متكاملة تلبي احتياجاتك").
            *   عرض 3-4 أيقونات أو صور مصغرة تمثل الخدمات الرئيسية (مثل الضمان، الصيانة، العناية بالسيارة) مع وصف موجز لكل خدمة.
            *   رابط "اكتشف جميع خدماتنا" أسفل القسم.
        *   **قسم "لماذا تختارنا؟" (Why Choose Us - اختياري):**
            *   نقاط قوة المعرض (e.g., تشكيلة واسعة، أسعار تنافسية، خدمة عملاء ممتازة، سهولة الشراء أونلاين) مع أيقونات داعمة.
        *   **قسم "آراء العملاء" (Testimonials - اختياري):**
            *   سلايدر صغير يعرض اقتباسات من عملاء راضين مع أسمائهم (إذا توفرت).
        *   **قسم "اطلب سيارتك المخصصة":**
            *   دعوة لاتخاذ إجراء (Call to Action) مع وصف موجز لخدمة "اطلب سيارتك".
            *   زر "اطلب سيارتك الآن" (ينقل إلى صفحة `SITE-REQUEST-CAR-STEP1-001`).
*   **البيانات المعروضة:** بيانات ديناميكية من `MOD-CMS` (للبنرات والمحتوى)، `MOD-CAR-CATALOG` (للسيارات)، `MOD-PROMO-MGMT` (للعروض)، `MOD-SERVICE-MGMT` (للخدمات).
*   **التفاعلات الرئيسية:** التنقل في السلايدر، البحث عن سيارة، النقر على بطاقات السيارات/العروض/الخدمات، الانتقال لصفحة طلب سيارة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CMS-FEAT-002`, `MOD-CAR-CATALOG-FEAT-001`, `FEAT-PROMO-001`, `FEAT-SERVICE-001`, `FEAT-REQCAR-001`.
*   **ربط بالمخرجات النهائية:** "موقع إلكتروني فعال", "واجهة موقع جذابة وفعالة".

#### 5.3. صفحة قائمة السيارات الجديدة (`SITE-CAR-LIST-001`)
*   **الغرض:** عرض قائمة بجميع السيارات الجديدة المتاحة مع فلاتر متقدمة للبحث والتصفية. (مستوحاة هيكليًا من `cars-list.png` و `filter-bar.png`).
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `SITE-LAYOUT-MAIN-001`.
    *   **مسار التنقل (Breadcrumbs):** الرئيسية > السيارات الجديدة.
    *   **عنوان الصفحة:** "السيارات الجديدة".
    *   **شريط الفلاتر (Filter Bar - على اليمين أو أفقي في الأعلى، تصميم مشابه لـ `filter-bar.png`):**
        *   **قسم الفلاتر الرئيسية (مرئية دائمًا أو قابلة للطي/الفتح):**
            *   "الماركة" (قائمة Checkboxes أو قائمة منسدلة متعددة الاختيار).
            *   "الموديل" (يتم تحديثها بناءً على الماركة، قائمة Checkboxes أو منسدلة متعددة).
            *   "سنة الصنع" (نطاق باستخدام slider مزدوج، أو قائمة سنوات محددة Checkboxes).
            *   "السعر" (نطاق باستخدام slider مزدوج، أو حقول إدخال لـ "من" و "إلى").
        *   **قسم الفلاتر الإضافية (قد يكون ضمن قسم "المزيد من الفلاتر" قابل للفتح):**
            *   "اللون الخارجي" (قائمة Checkboxes مع عينات ألوان صغيرة).
            *   "نوع ناقل الحركة" (قائمة Checkboxes).
            *   "نوع الوقود" (قائمة Checkboxes).
            *   "نوع هيكل السيارة" (قائمة Checkboxes: سيدان، SUV، إلخ).
            *   (اختياري) "عدد الأبواب", "عدد المقاعد".
            *   (اختياري) "ميزات محددة" (قائمة Checkboxes لأبرز الميزات القابلة للفلترة).
        *   **أزرار:** "تطبيق الفلاتر", "إعادة تعيين الفلاتر".
    *   **منطقة عرض نتائج السيارات (على اليسار أو أسفل شريط الفلاتر الأفقي):**
        *   **شريط أدوات النتائج:**
            *   "تم العثور على [X] سيارات".
            *   قائمة منسدلة "ترتيب حسب:" (الأحدث، السعر: من الأقل للأعلى، السعر: من الأعلى للأقل، الأكثر مشاهدة - إن وجدت).
            *   (اختياري) أزرار لتبديل طريقة العرض (شبكة Grid / قائمة List).
        *   **عرض السيارات (افتراضيًا كشبكة Grid):**
            *   لكل سيارة، يتم عرض بطاقة (Card) تحتوي على:
                *   صورة رئيسية للسيارة (محسنة للويب، مع تأثير تحويم Zoom خفيف).
                *   شارة "مميزة" أو "عرض خاص" إذا انطبق.
                *   اسم السيارة الكامل (ماركة - موديل - فئة - سنة).
                *   السعر (واضح، مع توضيح السعر الأصلي إذا كان هناك سعر عرض).
                *   أبرز 3-4 مواصفات (أيقونة + قيمة، مثل: ناقل حركة أوتوماتيك، بنزين، 203 حصان).
                *   أزرار إجراءات أسفل البطاقة:
                    *   زر "عرض التفاصيل" (لون ثانوي، ينقل إلى `SITE-CAR-DETAIL-001`).
                    *   أيقونة "إضافة للمفضلة" (قلب).
                    *   (اختياري) أيقونة "إضافة للمقارنة".
        *   **ترقيم الصفحات (Pagination):** أسفل قائمة السيارات.
    *   **حالة الفراغ (Empty State):** إذا لم تكن هناك نتائج تطابق الفلاتر، تُعرض رسالة "لم يتم العثور على سيارات تطابق معايير بحثك. حاول تعديل الفلاتر."
*   **البيانات المعروضة:** قائمة السيارات من `MOD-CAR-CATALOG` مع بيانات الفلاتر.
*   **التفاعلات الرئيسية:**
    *   اختيار وتطبيق الفلاتر (يتم تحديث قائمة السيارات ديناميكيًا عبر AJAX أو إعادة تحميل الصفحة).
    *   تغيير ترتيب العرض.
    *   إضافة للمفضلة/المقارنة.
    *   النقر على "عرض التفاصيل".
    *   التنقل بين صفحات النتائج.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CAR-CATALOG-FEAT-001`, `MOD-CAR-CATALOG-FEAT-002`.
*   **ربط بالمخرجات النهائية:** "موقع إلكتروني فعال", "واجهة موقع جذابة وفعالة".

#### 5.4. صفحة تفاصيل السيارة (`SITE-CAR-DETAIL-001`)
*   **الغرض:** عرض معلومات شاملة ومفصلة عن سيارة محددة. (مستوحاة هيكليًا من `screenshot-02.png` و `screenshot-03.png`).
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `SITE-LAYOUT-MAIN-001`.
    *   **مسار التنقل (Breadcrumbs):** الرئيسية > السيارات الجديدة > [ماركة السيارة] > [موديل السيارة] > [اسم السيارة الكامل].
    *   **الجزء العلوي (Above the Fold):**
        *   **على اليمين (حوالي 60-70% من العرض): معرض الصور والفيديو (Image/Video Gallery):**
            *   صورة رئيسية كبيرة وواضحة للسيارة.
            *   شريط صور مصغرة (thumbnails) أسفل الصورة الرئيسية (أو على جانبها) لباقي صور السيارة (خارجية، داخلية، تفاصيل). النقر على صورة مصغرة يغير الصورة الرئيسية.
            *   أيقونة تكبير على الصورة الرئيسية (تفتح الصور في Lightbox/Modal مع أسهم تنقل).
            *   (اختياري) أيقونة فيديو ضمن الصور المصغرة (تفتح مودال يعرض فيديو السيارة).
        *   **على اليسار (حوالي 30-40% من العرض): معلومات الشراء الأساسية:**
            *   اسم السيارة الكامل (ماركة - موديل - فئة - سنة) (H2 أو H3).
            *   رقم الإعلان (إذا كان مطبقًا).
            *   السعر: السعر الحالي (شامل الضريبة) بخط كبير وواضح. إذا كان هناك سعر عرض، يُعرض السعر الأصلي مشطوبًا فوقه.
            *   (اختياري) عبارة "مزايا مجانية وخصم [X] ريال" إذا كانت السيارة مشمولة بعرض ترويجي معين.
            *   **زر "اشترها الآن أو اطلبها أونلاين" (زر أساسي كبير، لون التمييز/Accent):** يفتح مودال اختيار نوع الشراء (كاش/تمويل) كما في `screenshot-04.png`.
            *   **رابط "كيف أشتريها أونلاين؟":** أسفل الزر الرئيسي، يفتح مودال يشرح خطوات الشراء الكاش (كما في `screenshot-05.png`) أو التمويل.
            *   **قسم "هل تحتاج مساعدة؟":**
                *   زر "تواصل معنا" (أيقونة هاتف، يفتح معلومات الاتصال أو نموذج تواصل).
                *   زر "واتساب" (أيقونة واتساب، يفتح محادثة واتساب).
            *   **أزرار إجراءات إضافية:**
                *   زر/أيقونة "أضف للمفضلة".
                *   زر/أيقونة "شارك الإعلان" (يفتح خيارات المشاركة).
                *   (اختياري) زر/أيقونة "أضف للمقارنة".
    *   **الجزء السفلي (Below the Fold - قد يكون بتصميم تبويبات Tabs أو أقسام متتالية):**
        *   **نبذة عن السيارة (Description):**
            *   عنوان "نبذة عن [اسم السيارة]".
            *   الوصف النصي المفصل للسيارة من `MOD-CAR-CATALOG-FEAT-018`.
        *   **المواصفات (Specifications):** (مستوحاة من `screenshot-03.png`)
            *   عنوان "مواصفات السيارة".
            *   عرض المواصفات في شبكة (Grid) من البطاقات الصغيرة أو قائمة مقسمة. كل مواصفة تعرض أيقونة + اسم المواصفة (مثل "ناقل الحركة") + قيمة المواصفة (مثل "أوتوماتيك").
            *   تصنيف المواصفات (مثل: المحرك والأداء، الأبعاد، الأمان، إلخ).
        *   **الميزات (الكماليات) (Features):**
            *   عنوان "الميزات والكماليات".
            *   عرض الميزات مجمعة حسب الفئة (مثل: الراحة والسهولة، الترفيه والتكنولوجيا، الأمان) كقائمة من العناصر مع أيقونة صح بجانب كل ميزة متوفرة.
        *   **تفاصيل "خدمات ما بعد البيع المجانية بقيمة 500 ريال":** (إذا كانت السيارة تشملها، كما هو موضح في `screenshot-02.png` أسفل اليمين)
            *   عنوان "خدمات ما بعد البيع المجانية بقيمة 500 ريال وهي:".
            *   قائمة بالخدمات المحددة (e.g., "توصيل مجاني"، "غسيل مجاني أول شهر").
        *   **زر "تحميل مواصفات السيارة (PDF)":** (أسفل قسم المواصفات).
        *   **(اختياري) قسم "سيارات مشابهة قد تعجبك":** شبكة من 3-4 بطاقات لسيارات أخرى.
*   **البيانات المعروضة:** بيانات سيارة محددة من `MOD-CAR-CATALOG-FEAT-003`, تفاصيل العروض من `MOD-PROMO-MGMT`, إعدادات الخدمات المجانية من `MOD-CORE-FEAT-003`.
*   **التفاعلات الرئيسية:**
    *   التنقل في معرض الصور/الفيديو.
    *   النقر على "اشترها الآن" لفتح مودال اختيار نوع الشراء.
    *   النقر على "كيف أشتريها أونلاين؟" لفتح مودال الشرح.
    *   التواصل عبر الأزرار المخصصة.
    *   إضافة للمفضلة/المقارنة، مشاركة الإعلان.
    *   تحميل PDF المواصفات.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CAR-CATALOG-FEAT-003`, `MOD-CAR-CATALOG-FEAT-004`, `FEAT-CAR-005`, `FEAT-CAR-006`, `FEAT-CAR-007`, `FEAT-ORDER-001`, `FEAT-ORDER-002`.
*   **ربط بالمخرجات النهائية:** "موقع إلكتروني فعال", "واجهة موقع جذابة وفعالة".

#### 5.5. صفحة "خدماتنا" (`SITE-SERVICES-LIST-001`)
*   **الغرض:** عرض قائمة بالخدمات الإضافية التي يقدمها المعرض مع إمكانية طلبها. (مستوحاة هيكليًا من `services-01.png` و `services-02.png`).
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`, زوار عامون.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `SITE-LAYOUT-MAIN-001`.
    *   **مسار التنقل (Breadcrumbs):** الرئيسية > خدماتنا.
    *   **عنوان الصفحة:** "خدماتنا" أو "خدمات المساعدة والضمان" (H2 أو H1، مع نص وصفي قصير أسفله كما في `services-01.png`).
    *   **أقسام الخدمات (مرتبة حسب الفئات المحددة في `FEAT-SERVICE-003`):**
        *   لكل فئة خدمة (e.g., "خدمات المساعدة والضمان"، "خدمات العناية بالسيارة" كما يظهر في `services-02.png` عند النقر):
            *   عنوان الفئة (H3).
            *   شبكة (Grid) من بطاقات الخدمات (2 أو 3 أو 4 بطاقات في الصف حسب حجم الشاشة).
            *   كل بطاقة خدمة تعرض:
                *   (اختياري) أيقونة أو صورة مصغرة تمثل الخدمة (كما في `services-01.png`).
                *   اسم الخدمة (بخط واضح).
                *   (اختياري) وصف موجز للخدمة أو مدة الخدمة (مثل "مدة الخدمة: 10 سنوات").
                *   سعر الخدمة (واضح، مع توضيح "شامل الضريبة").
                *   زر "اطلب الخدمة" أو "ماذا تشمل الخدمة؟" (إذا كان الزر "ماذا تشمل الخدمة؟" ينقل إلى تفاصيل أكثر أو يفتح مودال، وبعدها زر "اطلب الخدمة").
                *   عند النقر على "اطلب الخدمة"، يظهر نموذج جانبي (off-canvas) أو مودال لطلب الخدمة (كما في `services-02.png` على اليمين).
    *   **نموذج طلب الخدمة (جانبي Off-canvas أو مودال - يخدم `FEAT-SERVICE-002`):**
        *   عنوان: "طلب خدمة: [اسم الخدمة]" (يتم تحديث اسم الخدمة ديناميكيًا).
        *   السعر المعروض للخدمة.
        *   حقل "الاسم الكامل" (نص، مطلوب).
        *   حقل "رقم الجوال" (نص، مطلوب، بتنسيق سعودي).
        *   حقل "البريد الإلكتروني" (اختياري، بريد إلكتروني).
        *   (اختياري) حقل "ملاحظات إضافية".
        *   زر "أرسل طلبك الآن" (لون التمييز/Accent).
        *   رسالة تأكيد بعد الإرسال الناجح ("تم استلام طلبك بنجاح، سنتواصل معك قريبًا.") أو رسائل خطأ تحقق.
*   **البيانات المعروضة:** قائمة الخدمات وفئاتها من `MOD-SERVICE-MGMT`.
*   **التفاعلات الرئيسية:** تصفح الخدمات، النقر على "اطلب الخدمة" لفتح النموذج، ملء وتقديم نموذج طلب الخدمة.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-SERVICE-MGMT-FEAT-001`, `FEAT-SERVICE-002`, `FEAT-SERVICE-003`.
*   **ربط بالمخرجات النهائية:** "موقع إلكتروني فعال", "واجهة موقع جذابة وفعالة".

#### 5.6. صفحة "مبيعات الشركات" (`SITE-CORPORATE-SALES-001`)
*   **الغرض:** توفير معلومات عن خدمات مبيعات الأساطيل للشركات وتمكينهم من تقديم طلب. (مستوحاة هيكليًا من `corporate-sales.png`).
*   **شخصيات المستخدمين المتفاعلة:** ممثلو الشركات (USER-TYPE-002).
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `SITE-LAYOUT-MAIN-001`.
    *   **مسار التنقل (Breadcrumbs):** الرئيسية > مبيعات الشركات.
    *   **القسم العلوي (Hero Section - قد يكون أقل بروزًا من الصفحة الرئيسية):**
        *   عنوان رئيسي جذاب (e.g., "سارع بطلب أسطول سياراتك" كما في `corporate-sales.png`).
        *   نص وصفي قصير يشجع الشركات على تقديم طلبات.
        *   (اختياري) صورة خلفية احترافية ذات صلة بالأعمال أو الأساطيل.
    *   **قسم نموذج الطلب للشركات (يخدم `FEAT-CORP-002`):**
        *   **على اليمين (أو الأعلى في الموبايل):** صورة توضيحية (مثل صورة يد توقع عقدًا كما في `corporate-sales.png`).
        *   **على اليسار (أو الأسفل في الموبايل): نموذج الطلب:**
            *   حقل "اسم الشركة" (نص، مطلوب).
            *   حقل "اسم مسؤول الاتصال" (نص، مطلوب).
            *   حقل "البريد الإلكتروني لمسؤول الاتصال" (بريد إلكتروني، مطلوب).
            *   حقل "رقم الجوال لمسؤول الاتصال" (نص، مطلوب، بتنسيق سعودي).
            *   (اختياري) حقل "المدينة" (قائمة منسدلة أو نص).
            *   حقل "تفاصيل الطلب" (textarea، مطلوب، placeholder: "مثال: 20 سيارة تويوتا كامري، لون أبيض").
            *   زر "أرسل طلبك الآن" (لون التمييز/Accent).
    *   **(اختياري) قسم "لماذا تختارنا لمبيعات الأساطيل؟":**
        *   عرض 3-4 نقاط قوة (مثل: أسعار خاصة للشركات، حلول تمويل مخصصة، خدمة ما بعد البيع متميزة، تشكيلة واسعة).
    *   **(اختياري) قسم "شركاؤنا في النجاح":**
        *   عرض شعارات لبعض الشركات الكبرى التي تم التعامل معها (إذا وجدت موافقات).
*   **البيانات المعروضة:** محتوى ثابت يُدار جزئيًا من `MOD-CMS` (للنصوص الترويجية)، ونموذج الطلب.
*   **التفاعلات الرئيسية:** ملء وتقديم نموذج طلب الشركات.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CORP-SALES-FEAT-001`, `FEAT-CORP-002`.
*   **ربط بالمخرجات النهائية:** "موقع إلكتروني فعال", "واجهة موقع جذابة وفعالة".

#### 5.7. صفحة "عروض السيارات" (`SITE-PROMOTIONS-LIST-001`)
*   **الغرض:** عرض قائمة بجميع العروض الترويجية النشطة على السيارات. (مستوحاة هيكليًا من `promotions-list.png`).
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `SITE-LAYOUT-MAIN-001`.
    *   **مسار التنقل (Breadcrumbs):** الرئيسية > عروض السيارات.
    *   **عنوان الصفحة:** "عروض السيارات" أو "أحدث العروض الترويجية".
    *   **نص وصفي قصير:** (كما في `promotions-list.png`) "في هذه الصفحة يمكن الاطلاع دائمًا بأحدث عروض السيارات الأونلاين المقدمة من شوب. هنا ستجد عدد كبير من عروض..."
    *   **شبكة (Grid) من بطاقات العروض:**
        *   لكل عرض نشط، يتم عرض بطاقة (Card) تحتوي على:
            *   صورة البنر الخاصة بالعرض (كما هو مستخدم في `promotions-list.png`).
            *   (اختياري) اسم العرض أو عنوانه الرئيسي أسفل الصورة.
            *   النقر على البطاقة/الصورة ينقل إلى صفحة تفاصيل العرض (`SITE-PROMOTION-DETAIL-001`).
    *   **حالة الفراغ (Empty State):** إذا لم تكن هناك عروض نشطة، تُعرض رسالة "لا توجد عروض متاحة حاليًا. يرجى التحقق مرة أخرى قريبًا!".
*   **البيانات المعروضة:** قائمة العروض النشطة من `MOD-PROMO-MGMT`.
*   **التفاعلات الرئيسية:** تصفح العروض، النقر على بطاقة العرض للانتقال إلى تفاصيله.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-PROMO-MGMT-FEAT-001`.
*   **ربط بالمخرجات النهائية:** "موقع إلكتروني فعال", "واجهة موقع جذابة وفعالة".

#### 5.8. صفحة تفاصيل العرض (`SITE-PROMOTION-DETAIL-001`)
*   **الغرض:** عرض تفاصيل عرض ترويجي محدد والسيارات المشمولة به. (مستوحاة هيكليًا من `promotion-detail.png`).
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `SITE-LAYOUT-MAIN-001`.
    *   **مسار التنقل (Breadcrumbs):** الرئيسية > عروض السيارات > [اسم العرض].
    *   **القسم العلوي:**
        *   عرض صورة البنر الكبيرة الخاصة بالعرض.
        *   أسفل الصورة، اسم العرض (H2 أو H3) ووصف تفصيلي لشروط وأحكام العرض ومدته.
    *   **قسم "السيارات المشمولة بالعرض":**
        *   عنوان للقسم.
        *   شبكة (Grid) من بطاقات السيارات (مشابهة لبطاقات السيارات في صفحة قائمة السيارات `SITE-CAR-LIST-001`) تعرض فقط السيارات المربوطة بهذا العرض.
        *   كل بطاقة سيارة تعرض السعر الخاص بالعرض بشكل بارز.
    *   **حالة الفراغ:** إذا لم تكن هناك سيارات مشمولة بالعرض (نادر الحدوث إذا تم تكوينه بشكل صحيح)، تُعرض رسالة مناسبة.
*   **البيانات المعروضة:** تفاصيل عرض محدد والسيارات المرتبطة به من `MOD-PROMO-MGMT` و `MOD-CAR-CATALOG`.
*   **التفاعلات الرئيسية:** تصفح السيارات المشمولة بالعرض، النقر على بطاقة سيارة للانتقال إلى صفحة تفاصيلها.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-PROMO-MGMT-FEAT-002`.
*   **ربط بالمخرجات النهائية:** "موقع إلكتروني فعال", "واجهة موقع جذابة وفعالة".

#### 5.9. صفحات "اطلب سيارتك" (متعددة الخطوات - `SITE-REQUEST-CAR-STEPX-001`)
*   **الغرض:** تمكين المستخدمين من تقديم طلب لسيارة مخصصة غير موجودة حاليًا في المخزون. (مستوحاة هيكليًا من `request-car-s1.png` إلى `request-car-s5-login.png`).
*   **شخصيات المستخدمين المتفاعلة:** `PERSONA-CUSTOMER-01`.
*   **الهيكل السلكي النصي العام (Stepper UI):**
    *   **التخطيط العام:** تستخدم `SITE-LAYOUT-MAIN-001`.
    *   **عنوان رئيسي للصفحة/العملية:** "اطلب سيارتك من شوب".
    *   **مؤشر الخطوات (Stepper Indicator):** يعرض الخطوات الأربعة الرئيسية بشكل واضح (e.g., 1. اختر الماركة، 2. اختر الموديل، 3. اختر سنة الصنع، 4. معلومات إضافية). الخطوة الحالية تكون مميزة.
    *   **في كل خطوة:**
        *   عنوان للخطوة (e.g., "الخطوة 1 من 4: اختر الماركة").
        *   عرض الاختيارات المتاحة للخطوة الحالية.
        *   أزرار "السابق" (إذا لم تكن الخطوة الأولى) و "التالي" أو "حفظ ومتابعة".
        *   عرض الاختيارات التي تمت في الخطوات السابقة كـ "tags" أو ملخص صغير (كما في `request-car-s2.png` وما بعدها).

    *   **الخطوة 1: اختر الماركة (`SITE-REQUEST-CAR-STEP1-001` - مستوحاة من `request-car-s1.png`):**
        *   شبكة (Grid) من شعارات الماركات المتاحة مع أسمائها.
        *   عند النقر على ماركة، يتم تحديدها والانتقال للخطوة التالية.
        *   رابط "أنت توافق على سياسة الخصوصية والشروط والأحكام" في الأسفل.
    *   **الخطوة 2: اختر الموديل (`SITE-REQUEST-CAR-STEP2-001` - مستوحاة من `request-car-s2.png`):**
        *   عرض الماركة المختارة.
        *   حقل بحث "ابحث عن الموديل".
        *   قائمة بالموديلات المتاحة للماركة المختارة (تُعرض كأسماء نصية في صفوف).
        *   عند النقر على موديل، يتم تحديده والانتقال للخطوة التالية.
    *   **الخطوة 3: اختر سنة الصنع (`SITE-REQUEST-CAR-STEP3-001` - مستوحاة من `request-car-s3.png`):**
        *   عرض الماركة والموديل المختارين.
        *   قائمة بسنوات الصنع المتاحة (تُعرض كأزرار أو صفوف).
        *   عند النقر على سنة، يتم تحديدها.
        *   زر "حفظ ومتابعة".
    *   **الخطوة 4: معلومات إضافية (`SITE-REQUEST-CAR-STEP4-001` - مستوحاة من `request-car-s4-cash.png` و `request-car-s4-finance.png`):**
        *   عرض الماركة والموديل والسنة المختارة.
        *   **قسم "الفئة":** أزرار اختيار (نص فل، فل، ستاندرد).
        *   **قسم "طريقة الدفع":** أزرار اختيار (كاش، تقسيط).
            *   إذا تم اختيار "تقسيط"، يظهر حقل إضافي "الراتب الشهري (لاستخدام شركة التمويل)" (كما في `request-car-s4-finance.png`).
        *   زر "أرسل الطلب".
    *   **بعد "أرسل الطلب" (إذا لم يكن مسجلاً دخوله):**
        *   يظهر مودال تسجيل الدخول/إنشاء حساب (كما في `request-car-s5-login.png`).
        *   يجب على المستخدم تسجيل الدخول أو إنشاء حساب لإكمال الطلب.
*   **البيانات المعروضة:** قوائم الماركات، الموديلات، السنوات من `MOD-CAR-CATALOG`.
*   **التفاعلات الرئيسية:** الاختيار من القوائم، التنقل بين الخطوات، تقديم الطلب، تسجيل الدخول/إنشاء حساب.
*   **ربط بمتطلبات `REQ-FR.md`:** `FEAT-REQCAR-001`.
*   **ربط بالمخرجات النهائية:** "موقع إلكتروني فعال", "واجهة موقع جذابة وفعالة".

#### 5.10. صفحات شراء السيارة (كاش وتمويل - متعددة المراحل)
*   **الغرض:** تمكين المستخدم من إكمال طلب شراء سيارة محددة (من صفحة تفاصيل السيارة) سواء كاش أو تمويل. (مستوحاة هيكليًا من `buy-process-s1.png` إلى `buy-process-s2-cash-form.png` و `buy-process-s3-finance-form.png`).
*   **المدخل العام:** يبدأ المستخدم من صفحة تفاصيل السيارة (`SITE-CAR-DETAIL-001`) ويضغط "اشترها الآن"، يظهر مودال اختيار نوع الشراء (كاش/تمويل - `screenshot-04.png`).
*   **الهيكل السلكي العام (Stepper UI أو صفحات متتالية واضحة):**
    *   **التخطيط العام:** تستخدم `SITE-LAYOUT-MAIN-001`.
    *   **مؤشر تقدم واضح (Progress Indicator / Stepper):** يعرض الخطوات الرئيسية للعملية (e.g., 1. إملأ البيانات الشخصية، 2. تفاصيل الحجز/التمويل، 3. رفع الملفات، 4. تأكيد الطلب).
    *   **في كل خطوة/صفحة:**
        *   عرض ملخص للسيارة المختارة (صورة مصغرة، اسم، سعر) في الشريط الجانبي أو العلوي.
        *   حقول النموذج المطلوبة للخطوة الحالية.
        *   أزرار "السابق" و "التالي" أو "حفظ ومتابعة".

    *   **عملية الشراء كاش (`SITE-BUY-CASH-STEPX-001` - يخدم `MOD-ORDER-MGMT-FEAT-003`):**
        *   (مستوحاة من `buy-process-s1.png` التي تظهر خطوات متعددة مثل "إملأ البيانات الشخصية"، "طريقة الدفع"، "مبلغ الحجز"، "خدمات إضافية"، "رفع ملفات الطلب").
        *   **الخطوة 1: البيانات الشخصية:** (كما هو مفصل في `MOD-ORDER-MGMT-FEAT-003`، المرحلة 1). اسم كامل، رقم هوية، تاريخ ميلاد، جنسية، عنوان، إلخ.
        *   **الخطوة 2: تفاصيل السيارة والحجز والدفع:** (مراحل 2 و 3 من `MOD-ORDER-MGMT-FEAT-003`). عرض السيارة، مبلغ الحجز، اختيار طريقة دفع الحجز (أونلاين/في المعرض).
        *   **الخطوة 3: رفع المستندات:** (المرحلة 4 من `MOD-ORDER-MGMT-FEAT-003`). رفع الهوية ورخصة القيادة.
        *   **الخطوة 4: المراجعة والتأكيد:** (المرحلة 5 من `MOD-ORDER-MGMT-FEAT-003`). ملخص الطلب، موافقة على الشروط، زر "تأكيد الحجز والدفع".
    *   **عملية طلب التمويل (`SITE-BUY-FINANCE-STEPX-001` - يخدم `MOD-ORDER-MGMT-FEAT-004`):**
        *   (مستوحاة من `buy-process-s3-finance-form.png`).
        *   **الخطوة 1: البيانات الشخصية:** مشابهة للشراء الكاش.
        *   **الخطوة 2: معلومات التمويل:** (كما هو مفصل في `MOD-ORDER-MGMT-FEAT-004`). قيمة الدفعة الأولى، الدخل الشهري، الالتزامات الشهرية، المهنة/العمل، المسمى الوظيفي، البنك المحول عليه الراتب.
        *   **الخطوة 3: رفع المستندات:** مستندات إضافية قد تكون مطلوبة للتمويل (مثل تعريف بالراتب، كشف حساب).
        *   **الخطوة 4: المراجعة والتأكيد:** ملخص الطلب، موافقة على الشروط، زر "إرسال طلب التمويل".
*   **البيانات المعروضة:** تفاصيل السيارة، حقول النموذج، قوائم الخيارات.
*   **التفاعلات الرئيسية:** ملء النماذج، التنقل بين الخطوات، رفع الملفات، تقديم الطلب.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-ORDER-MGMT-FEAT-001`, `MOD-ORDER-MGMT-FEAT-003`, `MOD-ORDER-MGMT-FEAT-004`, `MOD-ORDER-MGMT-FEAT-006`.
*   **ربط بالمخرجات النهائية:** "موقع إلكتروني فعال", "واجهة موقع جذابة وفعالة".

#### 5.11. صفحات المصادقة (تسجيل الدخول، إنشاء حساب)
*   **صفحة تسجيل الدخول (`SITE-AUTH-LOGIN-001`):**
    *   **الغرض:** تمكين المستخدمين من تسجيل الدخول إلى حساباتهم.
    *   **الهيكل السلكي:** مشابه لـ `DASH-ADMIN-LOGIN-001` ولكن بتصميم يتناسب مع الموقع العام (قد يكون أقل رسمية، مع التركيز على تجربة العميل). عنوان "تسجيل الدخول" أو "أهلاً بعودتك". حقول "البريد الإلكتروني أو رقم الجوال" و "كلمة المرور". خيار "تذكرني". زر "تسجيل الدخول". رابط "نسيت كلمة المرور؟". رابط "ليس لديك حساب؟ إنشاء حساب جديد".
    *   **ربط بـ `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-002`.
*   **صفحة إنشاء حساب جديد (`SITE-AUTH-REGISTER-001`):**
    *   **الغرض:** تمكين المستخدمين الجدد من إنشاء حسابات.
    *   **الهيكل السلكي:** عنوان "إنشاء حساب جديد". حقول: الاسم الأول، اسم العائلة، البريد الإلكتروني، رقم الجوال، كلمة المرور، تأكيد كلمة المرور. Checkbox للموافقة على الشروط والأحكام (مع رابط). زر "إنشاء حساب". رابط "لديك حساب بالفعل؟ تسجيل الدخول".
    *   بعد التقديم، يتم توجيه المستخدم لصفحة التحقق من OTP.
    *   **ربط بـ `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-001`.
*   **صفحة التحقق من OTP (`SITE-AUTH-VERIFY-OTP-001`):**
    *   **الغرض:** إدخال رمز OTP المرسل للجوال.
    *   **الهيكل السلكي:** عنوان "التحقق من رقم الجوال". رسالة توضح أنه تم إرسال OTP إلى [رقم الجوال]. حقل إدخال OTP (4-6 خانات). زر "تحقق". رابط "إعادة إرسال الرمز".
    *   **ربط بـ `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-001B`.
*   **صفحات استعادة كلمة المرور:**
    *   **طلب إعادة التعيين (`SITE-AUTH-FORGOT-PASSWORD-001`):** عنوان "استعادة كلمة المرور". حقل "البريد الإلكتروني المسجل". زر "إرسال رابط إعادة التعيين".
    *   **إعادة التعيين (`SITE-AUTH-RESET-PASSWORD-001`):** عنوان "إعادة تعيين كلمة المرور". حقل "كلمة المرور الجديدة"، حقل "تأكيد كلمة المرور الجديدة". زر "حفظ كلمة المرور الجديدة". (يتم الوصول لهذه الصفحة عبر الرابط المرسل للبريد).
    *   **ربط بـ `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-003`.

#### 5.12. الصفحات الثابتة (من نحن، سياسة الخصوصية، إلخ.)
*   **(مثال: صفحة "من نحن" `SITE-STATIC-ABOUT-US-001`)**
*   **الغرض:** عرض معلومات عن المعرض.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** تستخدم `SITE-LAYOUT-MAIN-001`.
    *   **مسار التنقل (Breadcrumbs):** الرئيسية > من نحن.
    *   **عنوان الصفحة:** "من نحن".
    *   **منطقة المحتوى:**
        *   نص وصفي عن تاريخ المعرض، رؤيته، رسالته، قيمه.
        *   (اختياري) صور للمعرض أو فريق العمل.
        *   (اختياري) قسم "فريقنا" مع صور وأسماء ومناصب لأعضاء الإدارة الرئيسيين.
*   **البيانات المعروضة:** محتوى ثابت يُدار من `MOD-CMS-FEAT-001`.
*   **التفاعلات الرئيسية:** قراءة المحتوى.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CMS-FEAT-001`.
*   **ربط بالمخرجات النهائية:** "موقع إلكتروني فعال", "واجهة موقع جذابة وفعالة".
    *   **ملاحظة:** سيتم تطبيق هيكل مشابه للصفحات الثابتة الأخرى:
        *   سياسة الخصوصية (`SITE-STATIC-PRIVACY-001`)
        *   الشروط والأحكام (`SITE-STATIC-TERMS-001`)
        *   الأسئلة الشائعة (`SITE-STATIC-FAQ-001` - قد تكون بتصميم سؤال وجواب قابل للفتح/الطي).
        *   صفحة التواصل (`SITE-STATIC-CONTACT-US-001` - تتضمن نموذج تواصل بسيط: الاسم، البريد، الجوال، الرسالة؛ بالإضافة إلى خريطة موقع المعرض، أرقام الهواتف، وعناوين البريد).


### 6. واجهات مستخدم تطبيق Flutter (Flutter UI) - تصميم تفصيلي وصفي

**(معرف القسم: `UIUX-FLUTTER-VIEWS-001`)**

يصف هذا القسم الهياكل السلكية النصية المقترحة لشاشات تطبيق Flutter. سيتبع التطبيق إرشادات Material Design مع تكييفها لتناسب الهوية البصرية للمشروع، وسيركز على توفير الوظائف الأساسية للعملاء بشكل مبسط وفعال. سيتم استخدام نمط إدارة حالة (State Management) مناسب (مثل Riverpod أو BLoC كما هو مقترح) للتفاعل مع البيانات من `MOD-API`.

#### 6.1. شاشة البداية (Splash Screen - `FLUTTER-SPLASH-001`)
*   **الغرض:** عرض شاشة تحميل أولية عند فتح التطبيق.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** شاشة كاملة.
    *   **المكونات الرئيسية:**
        *   شعار المعرض بحجم كبير في المنتصف.
        *   (اختياري) اسم التطبيق أسفل الشعار.
        *   (اختياري) مؤشر تحميل بسيط (Loading indicator) إذا كانت هناك عمليات تهيئة أولية.
    *   **البيانات المعروضة:** شعار ثابت.
    *   **التفاعلات الرئيسية:** لا يوجد تفاعل مباشر من المستخدم. تنتقل تلقائيًا إلى شاشة المصادقة أو الشاشة الرئيسية بعد فترة قصيرة أو بعد اكتمال التحميل الأولي.
*   **ربط بمتطلبات `REQ-FR.md`:** (جزء من تجربة `FEAT-FLUTTER-001`).
*   **ربط بالمخرجات النهائية:** "تطبيق موبايل (Flutter) كامل".

#### 6.2. شاشات المصادقة (Authentication Screens)

##### 6.2.1. شاشة تسجيل الدخول (`FLUTTER-AUTH-LOGIN-001`)
*   **الغرض:** تمكين المستخدمين من تسجيل الدخول إلى حساباتهم.
*   **الهيكل السلكي النصي:**
    *   **التخطيط العام:** شاشة واحدة قابلة للتمرير (SingleChildScrollView).
    *   **شريط التطبيق (AppBar):** (اختياري) قد لا يكون هناك AppBar، أو عنوان بسيط "تسجيل الدخول".
    *   **المكونات الرئيسية (مرتبة عموديًا في المنتصف):**
        *   شعار المعرض (أصغر من شاشة البداية).
        *   عنوان "تسجيل الدخول".
        *   حقل إدخال "البريد الإلكتروني أو رقم الجوال" (TextFormField مع أيقونة على اليمين، placeholder، نوع لوحة مفاتيح مناسب).
        *   حقل إدخال "كلمة المرور" (TextFormField مع أيقونة قفل، placeholder، إمكانية إظهار/إخفاء النص).
        *   صف يحتوي على Checkbox "تذكرني" (إذا كانت الميزة مدعومة) ورابط نصي "نسيت كلمة المرور؟" (ينقل إلى `FLUTTER-AUTH-FORGOT-PASSWORD-001`).
        *   زر "تسجيل الدخول" (زر رئيسي بعرض كامل، لون ثانوي أو تمييز).
        *   نص ورابط: "ليس لديك حساب؟ إنشاء حساب جديد" (ينقل إلى `FLUTTER-AUTH-REGISTER-001`).
        *   (اختياري) فاصل مع نص "أو سجل الدخول باستخدام" وأزرار تسجيل الدخول عبر الشبكات الاجتماعية (Google, Apple) إذا كانت مدعومة.
*   **البيانات المعروضة:** حقول فارغة.
*   **التفاعلات الرئيسية:** إدخال البيانات، الضغط على الأزرار والروابط. عرض رسائل خطأ تحقق (تحت الحقول) أو رسالة خطأ عامة عند فشل تسجيل الدخول.
*   **ربط بـ API:** `API-EP-AUTH-004`.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-002`.
*   **ربط بالمخرجات النهائية:** "تطبيق موباイル (Flutter) كامل".

##### 6.2.2. شاشة إنشاء حساب جديد (`FLUTTER-AUTH-REGISTER-001`)
*   **الغرض:** تمكين المستخدمين الجدد من إنشاء حسابات.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "إنشاء حساب جديد"، مع زر "رجوع" للعودة إلى شاشة تسجيل الدخول.
    *   **المكونات الرئيسية (مرتبة عموديًا في SingleChildScrollView):**
        *   عنوان فرعي "أدخل بياناتك لإنشاء حساب جديد".
        *   حقل "الاسم الأول" (TextFormField).
        *   حقل "اسم العائلة" (TextFormField).
        *   حقل "البريد الإلكتروني" (TextFormField, keyboardType: emailAddress).
        *   حقل "رقم الجوال" (TextFormField, keyboardType: phone, مع أيقونة علم السعودية أو بادئة +966).
        *   حقل "كلمة المرور" (TextFormField, obscureText: true).
        *   حقل "تأكيد كلمة المرور" (TextFormField, obscureText: true).
        *   Checkbox "أوافق على الشروط والأحكام" (مع رابط نصي لفتح صفحة الشروط والأحكام `FLUTTER-CMS-PAGE-001`).
        *   زر "إنشاء حساب" (زر رئيسي بعرض كامل).
        *   نص ورابط: "لديك حساب بالفعل؟ تسجيل الدخول" (ينقل إلى `FLUTTER-AUTH-LOGIN-001`).
*   **التفاعلات الرئيسية:** ملء النموذج، عرض رسائل التحقق، عند النجاح ينتقل إلى `FLUTTER-AUTH-VERIFY-OTP-001`.
*   **ربط بـ API:** `API-EP-AUTH-001`.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-001`.
*   **ربط بالمخرجات النهائية:** "تطبيق موبايل (Flutter) كامل".

##### 6.2.3. شاشة التحقق من OTP (`FLUTTER-AUTH-VERIFY-OTP-001`)
*   **الغرض:** إدخال رمز OTP المرسل للجوال لتفعيل الحساب أو التحقق من الرقم.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "التحقق من رقم الجوال".
    *   **المكونات الرئيسية (مرتبة عموديًا):**
        *   نص توضيحي: "تم إرسال رمز التحقق إلى رقم الجوال: [رقم الجوال المدخل]".
        *   حقل إدخال OTP (مكون مخصص لإدخال 4-6 أرقام، مثل استخدام PinCodeTextField).
        *   مؤقت تنازلي يوضح صلاحية الرمز.
        *   زر "تحقق".
        *   رابط نصي "لم تستلم الرمز؟ إعادة إرسال" (يصبح مفعلاً بعد انتهاء المؤقت أو بعد فترة).
*   **التفاعلات الرئيسية:** إدخال الرمز، طلب إعادة إرسال، عند النجاح يتم تسجيل الدخول وتوجيه المستخدم للشاشة الرئيسية.
*   **ربط بـ API:** `API-EP-AUTH-002`, `API-EP-AUTH-003`.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-001B`.
*   **ربط بالمخرجات النهائية:** "تطبيق موباイル (Flutter) كامل".

##### 6.2.4. شاشة نسيت كلمة المرور (`FLUTTER-AUTH-FORGOT-PASSWORD-001`)
*   **الغرض:** تمكين المستخدمين من طلب إعادة تعيين كلمة المرور.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "استعادة كلمة المرور"، مع زر "رجوع".
    *   **المكونات الرئيسية:**
        *   نص توضيحي: "أدخل بريدك الإلكتروني المسجل لإرسال رابط إعادة تعيين كلمة المرور."
        *   حقل "البريد الإلكتروني" (TextFormField).
        *   زر "إرسال رابط إعادة التعيين".
*   **التفاعلات الرئيسية:** إدخال البريد والضغط على إرسال. عرض رسالة تأكيد ("تم إرسال الرابط بنجاح، يرجى التحقق من بريدك.") أو خطأ.
*   **ربط بـ API:** `API-EP-AUTH-006`.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-003`.
*   **ربط بالمخرجات النهائية:** "تطبيق موباイル (Flutter) كامل".
    *   **(ملاحظة: عملية إعادة التعيين الفعلية قد تتم عبر متصفح الويب عند فتح الرابط المرسل للبريد، لتجنب تعقيد التعامل مع deep linking للرمز داخل التطبيق في المرحلة الأولى، ما لم يُطلب خلاف ذلك بشكل صريح.)**

#### 6.3. الشاشة الرئيسية للتطبيق (`FLUTTER-HOME-001`)
*   **الغرض:** عرض نظرة عامة، بنرات، سيارات مميزة، وروابط سريعة.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):**
        *   شعار المعرض أو اسم التطبيق.
        *   على اليسار: أيقونة إشعارات (`Icons.notifications_none` مع عداد إذا كان هناك إشعارات غير مقروءة، تنقل إلى `FLUTTER-NOTIFICATIONS-LIST-001`).
        *   (اختياري) أيقونة بحث (`Icons.search`).
    *   **جسم الشاشة (SingleChildScrollView):**
        *   **سلايدر بنرات ترويجية (CarouselSlider):** يعرض بنرات من `API-EP-HOME-CONTENT` (مستمدة من `MOD-CMS-FEAT-002`). كل بنر قابل للنقر للانتقال إلى تفاصيل العرض أو قائمة سيارات معينة.
        *   **قسم "سيارات مميزة" أو "أحدث السيارات":**
            *   عنوان للقسم (e.g., "سيارات مميزة"). رابط "عرض الكل" (ينقل إلى `FLUTTER-CAR-LIST-001`).
            *   قائمة أفقية قابلة للتمرير (Horizontal ListView) من بطاقات السيارات المصغرة (صورة، اسم، سعر). النقر على بطاقة ينقل إلى `FLUTTER-CAR-DETAIL-001`.
        *   **قسم "روابط سريعة" أو "تصفح حسب الماركة":**
            *   عنوان للقسم.
            *   شبكة (Grid) من شعارات الماركات الشهيرة أو أزرار لـ "اطلب سيارتك"، "خدماتنا"، "عروضنا".
        *   **(اختياري) قسم "لماذا تختارنا؟" (نص بسيط مع أيقونات).**
    *   **شريط التنقل السفلي (BottomNavigationBar - إذا كان التصميم يعتمده لعدة أقسام رئيسية):**
        *   "الرئيسية" (نشط)
        *   "السيارات" (ينقل إلى `FLUTTER-CAR-LIST-001`)
        *   "المفضلة" (ينقل إلى `FLUTTER-CUSTOMER-FAVORITES-001` - يتطلب تسجيل دخول)
        *   "حسابي" (ينقل إلى `FLUTTER-CUSTOMER-PROFILE-OVERVIEW-001` أو `FLUTTER-AUTH-LOGIN-001` إذا لم يكن مسجلاً).
*   **البيانات المعروضة:** بيانات ديناميكية من `API-EP-HOME-CONTENT`, `API-EP-CAR-001` (للسيارات المميزة).
*   **التفاعلات الرئيسية:** التنقل في السلايدر، النقر على العناصر المختلفة، استخدام شريط التنقل السفلي.
*   **ربط بمتطلبات `REQ-FR.md`:** `FEAT-FLUTTER-001` (الشاشة الرئيسية).
*   **ربط بالمخرجات النهائية:** "تطبيق موبايل (Flutter) كامل".

#### 6.4. شاشة قائمة السيارات (`FLUTTER-CAR-LIST-001`)
*   **الغرض:** عرض قائمة السيارات الجديدة مع فلاتر وبحث.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):**
        *   عنوان "السيارات الجديدة".
        *   أيقونة "فلترة" (`Icons.filter_list`، تفتح شاشة/مودال الفلاتر `FLUTTER-CAR-FILTER-001`).
        *   (اختياري) أيقونة "بحث" (توسع حقل بحث في AppBar).
    *   **جسم الشاشة:**
        *   (اختياري) شريط فرعي يعرض الفلاتر المطبقة حاليًا مع إمكانية إزالة فلتر فردي.
        *   قائمة عمودية قابلة للتمرير (ListView.builder) من بطاقات السيارات.
        *   **بطاقة السيارة (Card):**
            *   صورة السيارة الرئيسية (بنسبة عرض إلى ارتفاع مناسبة).
            *   اسم السيارة (ماركة، موديل، سنة).
            *   السعر (مع تمييز سعر العرض إن وجد).
            *   أبرز 2-3 مواصفات (أيقونة + قيمة).
            *   أيقونة قلب "إضافة/إزالة من المفضلة" (تتطلب تسجيل دخول).
        *   مؤشر تحميل في الأسفل عند جلب المزيد من السيارات (Infinite Scrolling / Pagination).
        *   **حالة الفراغ:** رسالة "لا توجد سيارات تطابق بحثك" مع زر "إعادة تعيين الفلاتر".
*   **التفاعلات الرئيسية:** التمرير، النقر على بطاقة سيارة (ينقل إلى `FLUTTER-CAR-DETAIL-001`)، فتح الفلاتر، البحث، إضافة للمفضلة.
*   **ربط بـ API:** `API-EP-CAR-001`.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CAR-CATALOG-FEAT-001`, `MOD-CAR-CATALOG-FEAT-002`.
*   **ربط بالمخرجات النهائية:** "تطبيق موبايل (Flutter) كامل".

#### 6.5. شاشة الفلاتر (`FLUTTER-CAR-FILTER-001`)
*   **الغرض:** تمكين المستخدم من تطبيق فلاتر متعددة على قائمة السيارات.
*   **الهيكل السلكي النصي (تُعرض كشاشة كاملة أو BottomSheetDialog كبير):**
    *   **شريط التطبيق (AppBar) أو رأس المودال:** عنوان "فلترة السيارات"، زر "إعادة تعيين"، زر "تطبيق".
    *   **جسم الشاشة (SingleChildScrollView مع أقسام):**
        *   **قسم "الماركة":** قائمة Checkboxes أو Chips لاختيار ماركة واحدة أو أكثر.
        *   **قسم "الموديل":** قائمة Checkboxes أو Chips (تُحمّل بناءً على الماركات المختارة).
        *   **قسم "سنة الصنع":** (RangeSlider أو قائمة Checkboxes).
        *   **قسم "السعر":** (RangeSlider أو حقلين "من" و "إلى").
        *   **قسم "اللون الخارجي":** قائمة Checkboxes مع عينات ألوان.
        *   أقسام أخرى مشابهة لـ "نوع ناقل الحركة"، "نوع الوقود"، إلخ.
*   **التفاعلات الرئيسية:** اختيار/إلغاء اختيار الفلاتر، الضغط على "تطبيق" للعودة إلى قائمة السيارات مع تطبيق الفلاتر.
*   **ربط بـ API:** `API-EP-CAR-003` (لجلب خيارات الفلاتر).
*   **ربط بالمخرجات النهائية:** "تطبيق موبايل (Flutter) كامل".

#### 6.6. شاشة تفاصيل السيارة (`FLUTTER-CAR-DETAIL-001`)
*   **الغرض:** عرض معلومات شاملة عن سيارة محددة.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** اسم السيارة (ماركة وموديل)، زر "رجوع"، أيقونة "مشاركة"، أيقونة "إضافة/إزالة من المفضلة".
    *   **جسم الشاشة (CustomScrollView مع Slivers أو SingleChildScrollView مع Column):**
        *   **سلايدر صور السيارة (CarouselSlider):** يعرض جميع صور السيارة مع نقاط مؤشر.
        *   **قسم المعلومات الأساسية:**
            *   اسم السيارة الكامل، سنة الصنع.
            *   السعر (مع تمييز سعر العرض).
            *   (اختياري) تقييم السيارة (نجوم).
        *   **زر "اطلبها الآن" أو "احجزها الآن" (زر رئيسي كبير):** ينقل إلى شاشات عملية الشراء (`FLUTTER-BUY-PROCESS-START-001`).
        *   **أزرار التواصل:** "اتصل بنا"، "واتساب".
        *   **تبويبات (TabBarView) أو أقسام متتالية (ExpansionPanelList):**
            *   **"نظرة عامة/وصف":** الوصف النصي للسيارة.
            *   **"المواصفات":** قائمة منظمة بالمواصفات الفنية.
            *   **"الميزات":** قائمة بالميزات والكماليات مجمعة حسب الفئة.
            *   **(اختياري) "العرض (إن وجد)":** تفاصيل العرض الترويجي إذا كانت السيارة مشمولة.
        *   زر "تحميل المواصفات (PDF)".
*   **التفاعلات الرئيسية:** تصفح الصور، النقر على الأزرار المختلفة، التمرير.
*   **ربط بـ API:** `API-EP-CAR-002`, (لتحميل PDF) `API-EP-CAR-006`.
*   **ربط بالمخرجات النهائية:** "تطبيق موبايل (Flutter) كامل".

#### 6.7. شاشات عملية "اطلب سيارتك" (Custom Car Request - `FLUTTER-REQUEST-CAR-STEPX-001`)
*   **الغرض:** تمكين المستخدمين من تقديم طلب لسيارة مخصصة عبر سلسلة من الخطوات.
*   **الهيكل السلكي العام (Stepper UI داخل Navigator أو PageView):**
    *   **شريط التطبيق (AppBar) مشترك للخطوات:** عنوان "اطلب سيارتك"، زر "رجوع" (يعود للخطوة السابقة أو يخرج من العملية)، (اختياري) مؤشر الخطوة الحالي (e.g., "الخطوة X من Y").
    *   **في كل خطوة/شاشة:**
        *   عنوان للخطوة (e.g., "اختر الماركة").
        *   محتوى الخطوة (قوائم اختيار، حقول إدخال).
        *   زر "التالي" (أو "إرسال الطلب" في الخطوة الأخيرة).

    *   **الخطوة 1: اختر الماركة (`FLUTTER-REQUEST-CAR-STEP1-001`):**
        *   قائمة أو شبكة (Grid) من شعارات/أسماء الماركات. النقر على ماركة يحددها وينتقل للخطوة التالية.
        *   **ربط بـ API:** لجلب قائمة الماركات من `API-EP-CAR-003` (جزء الماركات).
    *   **الخطوة 2: اختر الموديل (`FLUTTER-REQUEST-CAR-STEP2-001`):**
        *   عرض الماركة المختارة. حقل بحث للموديلات. قائمة بالموديلات للماركة المختارة.
        *   **ربط بـ API:** لجلب قائمة الموديلات بناءً على الماركة المختارة من `API-EP-CAR-003`.
    *   **الخطوة 3: اختر سنة الصنع (`FLUTTER-REQUEST-CAR-STEP3-001`):**
        *   عرض الماركة والموديل المختارين. قائمة بسنوات الصنع المتاحة للاختيار.
        *   **ربط بـ API:** لجلب قائمة سنوات الصنع المتاحة.
    *   **الخطوة 4: معلومات إضافية والدفع (`FLUTTER-REQUEST-CAR-STEP4-001`):**
        *   عرض الاختيارات السابقة.
        *   أزرار اختيار "الفئة" (نص فل، فل، ستاندرد).
        *   أزرار اختيار "طريقة الدفع" (كاش، تقسيط).
        *   إذا "تقسيط": حقل "الراتب الشهري".
        *   (اختياري) حقل "ملاحظات إضافية".
        *   زر "إرسال الطلب".
        *   **تنبيه:** يتطلب تسجيل دخول لإرسال الطلب. إذا لم يكن مسجلاً، يتم توجيهه إلى `FLUTTER-AUTH-LOGIN-001` ثم يعود لإكمال الإرسال.
*   **التفاعلات الرئيسية:** الاختيار من القوائم، الإدخال، التنقل بين الخطوات، تقديم الطلب.
*   **ربط بـ API:** `API-EP-CUSTOM-CAR-REQUEST` (لنقطة نهاية مخصصة لـ `FEAT-REQCAR-001`).
*   **ربط بمتطلبات `REQ-FR.md`:** `FEAT-REQCAR-001`.
*   **ربط بالمخرجات النهائية:** "تطبيق موبايل (Flutter) كامل".

#### 6.8. شاشات عملية الشراء (كاش وتمويل)

*   **الشاشة الأولية بعد اختيار السيارة من التفاصيل (`FLUTTER-BUY-PROCESS-START-001`):**
    *   **الغرض:** السماح للمستخدم باختيار نوع الشراء (كاش أو تمويل) لسيارة محددة.
    *   **الهيكل السلكي (قد تكون BottomSheetDialog أو شاشة بسيطة):**
        *   عرض ملخص صغير للسيارة المختارة (صورة، اسم، سعر).
        *   زر "شراء كاش (دفع مبلغ حجز)".
        *   زر "تقديم طلب تمويل".
        *   رابط "كيف أشتريها؟" (يفتح شاشة معلومات `FLUTTER-HOW-TO-BUY-001`).
    *   **التفاعلات:** النقر على أحد الأزرار ينقل إلى مسار الشراء المناسب. يتطلب تسجيل دخول.

*   **عملية الشراء كاش (متعددة الخطوات - `FLUTTER-BUY-CASH-STEPX-001`):**
    *   **الهيكل السلكي العام (Stepper UI مشابه لـ "اطلب سيارتك"):**
        *   **الخطوة 1: البيانات الشخصية:** (حقول مشابهة لـ `MOD-ORDER-MGMT-FEAT-003`, المرحلة 1). يتم ملء بعضها تلقائيًا من ملف المستخدم.
        *   **الخطوة 2: تفاصيل الحجز وطريقة الدفع:** عرض مبلغ الحجز، اختيار طريقة الدفع (إذا كانت هناك خيارات متعددة للدفع أونلاين أو خيار "الدفع في المعرض" للحجز).
        *   **الخطوة 3: رفع المستندات:** (مكون رفع ملفات لـ الهوية، رخصة القيادة). معاينة للملفات المرفوعة.
        *   **الخطوة 4: المراجعة والتأكيد:** ملخص كامل للطلب، زر "تأكيد الحجز والدفع" (أو "تأكيد الحجز").
        *   **بعد التأكيد:** إذا كان الدفع أونلاين، يتم فتح WebView أو متصفح خارجي لإتمام الدفع عبر بوابة الدفع. ثم يتم عرض شاشة تأكيد/فشل.
*   **ربط بـ API:** `API-EP-ORDERS-CASH`.

*   **عملية طلب التمويل (متعددة الخطوات - `FLUTTER-BUY-FINANCE-STEPX-001`):**
    *   **الهيكل السلكي العام (Stepper UI):**
        *   **الخطوة 1: البيانات الشخصية:** مشابهة للشراء الكاش.
        *   **الخطوة 2: معلومات التمويل:** (حقول مشابهة لـ `MOD-ORDER-MGMT-FEAT-004`, المرحلة 2).
        *   **الخطوة 3: رفع المستندات:** مستندات إضافية (تعريف بالراتب، كشف حساب).
        *   **الخطوة 4: المراجعة والتأكيد:** ملخص الطلب، زر "إرسال طلب التمويل".
*   **ربط بـ API:** `API-EP-ORDERS-FINANCE`.
*   **ربط بمتطلبات `REQ-FR.md`:** `FEAT-ORDER-001`, `MOD-ORDER-MGMT-FEAT-003`, `MOD-ORDER-MGMT-FEAT-004`, `MOD-ORDER-MGMT-FEAT-005`, `MOD-ORDER-MGMT-FEAT-006`.
*   **ربط بالمخرجات النهائية:** "تطبيق موبايل (Flutter) كامل".

#### 6.9. لوحة تحكم العميل المصغرة (Customer Mini-Dashboard)
جميع هذه الشاشات تتطلب أن يكون المستخدم مسجلاً دخوله.

##### 6.9.1. شاشة "طلباتي" (`FLUTTER-CUSTOMER-ORDERS-001`)
*   **الغرض:** عرض قائمة بطلبات العميل وحالتها.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "طلباتي". (اختياري) أيقونة فلترة (حسب الحالة، النوع).
    *   **جسم الشاشة:**
        *   قائمة عمودية (ListView.builder) من بطاقات الطلبات.
        *   **بطاقة الطلب (Card):**
            *   رقم الطلب، تاريخ الطلب.
            *   اسم السيارة/الخدمة المطلوبة (مع صورة مصغرة للسيارة).
            *   حالة الطلب (نص مع لون مميز أو شارة).
            *   المبلغ الإجمالي/الحجز.
        *   النقر على البطاقة ينقل إلى `FLUTTER-CUSTOMER-ORDER-DETAIL-001`.
        *   **حالة الفراغ:** "ليس لديك أي طلبات حاليًا."
*   **ربط بـ API:** `API-EP-USER-ORDERS`.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-006`.

##### 6.9.2. شاشة تفاصيل الطلب للعميل (`FLUTTER-CUSTOMER-ORDER-DETAIL-001`)
*   **الغرض:** عرض تفاصيل طلب محدد للعميل.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "تفاصيل الطلب #[رقم الطلب]".
    *   **جسم الشاشة (SingleChildScrollView):**
        *   معلومات الطلب الأساسية (نوع، تاريخ، حالة).
        *   تفاصيل السيارة/الخدمة (صورة، اسم، سعر).
        *   ملخص الدفع.
        *   **قسم المستندات:** قائمة بالمستندات المطلوبة/المرفوعة، مع حالتها وزر لرفع مستند إذا لزم الأمر.
        *   (اختياري) سجل بسيط لتحديثات حالة الطلب.
*   **ربط بـ API:** `API-EP-USER-ORDERS-ID`, `API-EP-ORDERS-ID-DOCUMENTS` (لرفع المستندات).

##### 6.9.3. شاشة "المفضلة" (`FLUTTER-CUSTOMER-FAVORITES-001`)
*   **الغرض:** عرض قائمة السيارات المفضلة للعميل.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "السيارات المفضلة".
    *   **جسم الشاشة:**
        *   قائمة عمودية (ListView.builder) أو شبكة (GridView.builder) من بطاقات السيارات المفضلة (مشابهة لبطاقات السيارات في `FLUTTER-CAR-LIST-001` ولكن مع زر "إزالة من المفضلة" بارز).
        *   النقر على البطاقة ينقل إلى `FLUTTER-CAR-DETAIL-001`.
        *   **حالة الفراغ:** "قائمة مفضلتك فارغة."
*   **ربط بـ API:** `API-EP-USER-FAVORITES`, `API-EP-USER-FAVORITES-CAR-ID` (للإزالة).
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CAR-CATALOG-FEAT-005B`.

##### 6.9.4. شاشة "الملف الشخصي" (`FLUTTER-CUSTOMER-PROFILE-OVERVIEW-001`)
*   **الغرض:** عرض معلومات الملف الشخصي للعميل مع روابط لتعديلها أو لإعدادات أخرى.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "حسابي" أو "الملف الشخصي".
    *   **جسم الشاشة (SingleChildScrollView):**
        *   **قسم معلومات المستخدم:**
            *   صورة شخصية (Avatar)، اسم المستخدم، البريد الإلكتروني، رقم الجوال.
            *   زر "تعديل الملف الشخصي" (ينقل إلى `FLUTTER-CUSTOMER-PROFILE-EDIT-001`).
        *   **قائمة روابط (ListView مع ListTiles):**
            *   "طلباتي" (ينقل إلى `FLUTTER-CUSTOMER-ORDERS-001`).
            *   "السيارات المفضلة" (ينقل إلى `FLUTTER-CUSTOMER-FAVORITES-001`).
            *   "الإشعارات" (ينقل إلى `FLUTTER-NOTIFICATIONS-LIST-001`).
            *   "تغيير كلمة المرور" (ينقل إلى `FLUTTER-CUSTOMER-PROFILE-CHANGE-PASSWORD-001`).
            *   "رشح عميل" (إذا كان مؤهلاً، ينقل إلى شاشة مخصصة مشابهة لـ `DASH-CUSTOMER-REFERRAL-001` ولكن بتصميم Flutter).
            *   "الشروط والأحكام" (ينقل إلى `FLUTTER-CMS-PAGE-001`).
            *   "سياسة الخصوصية" (ينقل إلى `FLUTTER-CMS-PAGE-001`).
            *   "عن التطبيق".
            *   "تسجيل الخروج" (يؤكد ثم يسجل الخروج).
*   **ربط بـ API:** `API-EP-USER-PROFILE` (لعرض البيانات).
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-USER-MGMT-FEAT-004`, `MOD-USER-MGMT-FEAT-005`.

##### 6.9.5. شاشة تعديل الملف الشخصي (`FLUTTER-CUSTOMER-PROFILE-EDIT-001`)
*   **الغرض:** تمكين العميل من تعديل بياناته الشخصية.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "تعديل الملف الشخصي"، زر "حفظ".
    *   **جسم الشاشة (SingleChildScrollView مع Form):**
        *   مكون اختيار/عرض الصورة الشخصية.
        *   حقول TextFormFields لـ: الاسم الأول، اسم العائلة، رقم الجوال (يتطلب OTP إذا تغير)، العنوان، المدينة، رقم الهوية، تاريخ الميلاد، الجنسية.
*   **التفاعلات:** تعديل الحقول، حفظ التغييرات.
*   **ربط بـ API:** `API-EP-USER-002`.

##### 6.9.6. شاشة تغيير كلمة المرور (`FLUTTER-CUSTOMER-PROFILE-CHANGE-PASSWORD-001`)
*   **الغرض:** تمكين العميل من تغيير كلمة المرور.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "تغيير كلمة المرور"، زر "حفظ".
    *   **جسم الشاشة (Form):**
        *   حقل "كلمة المرور الحالية".
        *   حقل "كلمة المرور الجديدة".
        *   حقل "تأكيد كلمة المرور الجديدة".
*   **ربト بـ API:** `API-EP-USER-003`.

##### 6.9.7. شاشة الإشعارات (`FLUTTER-NOTIFICATIONS-LIST-001`)
*   **الغرض:** عرض قائمة بإشعارات العميل.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "الإشعارات". (اختياري) زر "تحديد الكل كمقروء".
    *   **جسم الشاشة:**
        *   قائمة عمودية (ListView.builder) من عناصر الإشعارات.
        *   **عنصر الإشعار (ListTile أو Card):** أيقونة، نص الإشعار، تاريخ/وقت الإشعار. تمييز الإشعارات غير المقروءة.
        *   النقر على إشعار قد ينقله إلى شاشة ذات صلة أو يعلمه كمقروء.
        *   **حالة الفراغ:** "لا توجد إشعارات جديدة."
*   **ربط بـ API:** `API-EP-NOTIFICATIONS-LIST`, `API-EP-NOTIFICATIONS-MARK-READ`.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-NOTIFICATION-FEAT-002`.

#### 6.10. شاشة عرض الخدمات (`FLUTTER-SERVICES-LIST-001`)
*   **الغرض:** عرض قائمة الخدمات المتاحة مع إمكانية طلبها.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "خدماتنا".
    *   **جسم الشاشة (SingleChildScrollView مع أقسام للفئات):**
        *   لكل فئة خدمة: عنوان الفئة.
        *   قائمة عمودية أو شبكة من بطاقات الخدمات ضمن كل فئة.
        *   **بطاقة الخدمة (Card):** صورة/أيقونة، اسم الخدمة، سعر الخدمة، زر "اطلب الخدمة".
        *   النقر على "اطلب الخدمة" يفتح شاشة/مودال نموذج طلب الخدمة (`FLUTTER-SERVICE-REQUEST-FORM-001`).
*   **ربط بـ API:** `API-EP-SERVICES-LIST`.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-SERVICE-MGMT-FEAT-001`.

#### 6.11. شاشة نموذج طلب الخدمة (`FLUTTER-SERVICE-REQUEST-FORM-001`)
*   **الغرض:** تمكين المستخدم من تقديم طلب لخدمة محددة.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "طلب خدمة: [اسم الخدمة]".
    *   **جسم الشاشة (Form):**
        *   عرض اسم وسعر الخدمة المختارة.
        *   حقول: الاسم الكامل، رقم الجوال، البريد الإلكتروني (اختياري)، ملاحظات.
        *   زر "إرسال الطلب".
*   **ربط بـ API:** `API-EP-SERVICE-REQUESTS-CREATE`.
*   **ربط بمتطلبات `REQ-FR.md`:** `FEAT-SERVICE-002`.

#### 6.12. شاشة عرض العروض (`FLUTTER-PROMOTIONS-LIST-001`)
*   **الغرض:** عرض قائمة بالعروض الترويجية النشطة.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "عروض السيارات".
    *   **جسم الشاشة:**
        *   قائمة عمودية (ListView.builder) من بطاقات العروض.
        *   **بطاقة العرض (Card):** صورة بنر العرض، اسم العرض.
        *   النقر على البطاقة ينقل إلى `FLUTTER-PROMOTION-DETAIL-001`.
*   **ربط بـ API:** `API-EP-PROMOTIONS-LIST`.
*   **ربط بمتطلبات `REQ-FR.md`:** `FEAT-PROMO-001`.

#### 6.13. شاشة تفاصيل العرض (`FLUTTER-PROMOTION-DETAIL-001`)
*   **الغرض:** عرض تفاصيل عرض محدد والسيارات المشمولة به.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** اسم العرض.
    *   **جسم الشاشة (CustomScrollView أو SingleChildScrollView):**
        *   صورة بنر العرض الكبيرة في الأعلى.
        *   وصف تفصيلي لشروط العرض.
        *   عنوان "السيارات المشمولة بالعرض".
        *   قائمة أفقية أو شبكة من بطاقات السيارات المشمولة بالعرض (مشابهة لـ `FLUTTER-CAR-LIST-001` ولكن أصغر).
*   **ربط بـ API:** `API-EP-PROMOTIONS-ID`.
*   **ربط بمتطلبات `REQ-FR.md`:** `FEAT-PROMO-002`.

#### 6.14. شاشة معلومات التواصل (`FLUTTER-CONTACT-US-001`)
*   **الغرض:** عرض معلومات الاتصال بالمعرض.
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان "اتصل بنا".
    *   **جسم الشاشة:**
        *   أرقام هواتف المعرض (قابلة للنقر للاتصال).
        *   عنوان البريد الإلكتروني (قابل للنقر لفتح تطبيق البريد).
        *   رابط حساب واتساب (قابل للنقر).
        *   عنوان المعرض (قد يتضمن خريطة مصغرة أو زر لفتح الخرائط).
        *   أيقونات وروابط لوسائل التواصل الاجتماعي.
*   **البيانات:** تُجلب من `API-EP-SETTINGS` أو تكون ثابتة.
*   **ربط بمتطلبات `REQ-FR.md`:** `FEAT-COMM-001`.

#### 6.15. شاشة عرض الصفحات الثابتة (مثل "من نحن") (`FLUTTER-CMS-PAGE-001`)
*   **الغرض:** عرض محتوى الصفحات الثابتة (من نحن، سياسة الخصوصية، الشروط).
*   **الهيكل السلكي النصي:**
    *   **شريط التطبيق (AppBar):** عنوان الصفحة (e.g., "من نحن").
    *   **جسم الشاشة (SingleChildScrollView مع WebView أو RichText renderer):**
        *   يعرض محتوى HTML أو النص المنسق للصفحة.
*   **ربط بـ API:** `API-EP-PAGES-SLUG`.
*   **ربط بمتطلبات `REQ-FR.md`:** `MOD-CMS-FEAT-001` (للعرض في التطبيق).

---

*نهاية مستند `UIUX-FR.md` (النسخة النهائية المعتمدة ذاتيًا)*