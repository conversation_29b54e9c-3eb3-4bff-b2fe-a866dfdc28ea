<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('car_car_feature', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('car_id')->comment('معرّف السيارة');
            $table->unsignedBigInteger('feature_id')->comment('معرّف الميزة');
            $table->string('value', 255)->nullable()->comment('قيمة الميزة (إذا كانت قابلة للتخصيص)');
            $table->timestamps();

            // إنشاء الفهارس
            $table->index('car_id');
            $table->index('feature_id');

            // إنشاء المفاتيح الخارجية
            $table->foreign('car_id')
                ->references('id')
                ->on('cars')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            $table->foreign('feature_id')
                ->references('id')
                ->on('car_features')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            // إنشاء فهرس مركب للفرادة
            $table->unique(['car_id', 'feature_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('car_car_feature');
    }
};
