<?php

namespace Modules\CarCatalog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Core\Models\BaseModel;
use Spatie\Translatable\HasTranslations;

/**
 * CarModel Model.
 *
 * يمثل هذا النموذج جدول موديلات السيارات في النظام
 *
 * @property int $id
 * @property int $brand_id معرّف الماركة
 * @property string $name اسم الموديل
 * @property bool $status حالة الموديل (نشط/غير نشط)
 * @property \Illuminate\Support\Carbon|null $created_at تاريخ الإنشاء
 * @property \Illuminate\Support\Carbon|null $updated_at تاريخ التحديث
 * @property \Illuminate\Support\Carbon|null $deleted_at تاريخ الحذف الناعم
 * @property \Modules\CarCatalog\Models\Brand $brand علاقة الماركة
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\CarCatalog\Models\Car[] $cars علاقة السيارات
 */
class CarModel extends BaseModel
{
    use HasFactory;
    use HasTranslations;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي.
     *
     * @var array
     */
    protected $fillable = [
        'brand_id',
        'name',
        'status',
    ];

    /**
     * الخصائص التي يمكن ترجمتها.
     *
     * @var array
     */
    public $translatable = [
        'name',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة.
     *
     * @var array
     */
    protected $casts = [
        'status'     => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * علاقة الماركة التي ينتمي إليها هذا الموديل.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * علاقة السيارات المرتبطة بهذا الموديل.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cars()
    {
        return $this->hasMany(Car::class);
    }

    /**
     * مصنع النموذج.
     *
     * @return \Modules\CarCatalog\Database\factories\CarModelFactory
     */
    protected static function newFactory()
    {
        return \Modules\CarCatalog\Database\factories\CarModelFactory::new();
    }
}
