<?php

namespace Modules\UserManagement\Http\Requests\Site;

use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تسجيل مستخدم جديد من الموقع العام
 *
 * يتحقق هذا الطلب من صحة بيانات تسجيل العميل الجديد
 * ويطبق جميع قواعد التحقق المطلوبة حسب المتطلبات
 */
class RegisterRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب
     *
     * @return array
     */
    public function rules()
    {
        return [
            'first_name' => [
                'required',
                'string',
                'min:2',
                'max:50',
                'regex:/^[\p{L}\s\'-]+$/u'
            ],
            'last_name' => [
                'required',
                'string',
                'min:2',
                'max:50',
                'regex:/^[\p{L}\s\'-]+$/u'
            ],
            'email' => [
                'required',
                'email',
                'max:255',
                'unique:users,email'
            ],
            'phone_number' => [
                'required',
                'string',
                'regex:/^05\d{8}$/',
                'unique:users,phone_number'
            ],
            'password' => [
                'required',
                'string',
                'min:8',
                'confirmed',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?~])[A-Za-z\d!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?~]+$/'
            ],
            'agree_terms' => [
                'required',
                'accepted'
            ]
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة للتحقق من صحة البيانات
     *
     * @return array
     */
    public function messages()
    {
        return [
            'first_name.required' => 'الاسم الأول مطلوب.',
            'first_name.min' => 'الاسم الأول يجب أن يكون على الأقل حرفين.',
            'first_name.max' => 'الاسم الأول يجب ألا يتجاوز 50 حرفاً.',
            'first_name.regex' => 'الاسم الأول يجب أن يحتوي على حروف فقط.',
            
            'last_name.required' => 'اسم العائلة مطلوب.',
            'last_name.min' => 'اسم العائلة يجب أن يكون على الأقل حرفين.',
            'last_name.max' => 'اسم العائلة يجب ألا يتجاوز 50 حرفاً.',
            'last_name.regex' => 'اسم العائلة يجب أن يحتوي على حروف فقط.',
            
            'email.required' => 'البريد الإلكتروني مطلوب.',
            'email.email' => 'يرجى إدخال بريد إلكتروني صالح.',
            'email.unique' => 'هذا البريد الإلكتروني مسجل مسبقاً.',
            
            'phone_number.required' => 'رقم الجوال مطلوب.',
            'phone_number.regex' => 'رقم الجوال يجب أن يكون رقماً سعودياً صالحاً يبدأ بـ 05 ويتبعه 8 أرقام.',
            'phone_number.unique' => 'هذا الرقم مسجل مسبقاً.',
            
            'password.required' => 'كلمة المرور مطلوبة.',
            'password.min' => 'كلمة المرور يجب أن تكون على الأقل 8 أحرف.',
            'password.confirmed' => 'تأكيد كلمة المرور غير متطابق.',
            'password.regex' => 'كلمة المرور يجب أن تحتوي على حروف كبيرة وصغيرة وأرقام ورمز خاص واحد على الأقل.',
            
            'agree_terms.required' => 'يجب الموافقة على الشروط والأحكام.',
            'agree_terms.accepted' => 'يجب الموافقة على الشروط والأحكام.'
        ];
    }

    /**
     * الحصول على أسماء الخصائص المخصصة للتحقق من صحة البيانات
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'first_name' => 'الاسم الأول',
            'last_name' => 'اسم العائلة',
            'email' => 'البريد الإلكتروني',
            'phone_number' => 'رقم الجوال',
            'password' => 'كلمة المرور',
            'agree_terms' => 'الموافقة على الشروط والأحكام'
        ];
    }

    /**
     * تحضير البيانات للتحقق
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // تحويل البريد الإلكتروني إلى أحرف صغيرة
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->email)
            ]);
        }
    }
}
