<?php

namespace Modules\OrderManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب التحقق من صحة بيانات الخطوة الأولى لطلب شراء سيارة كاش
 * 
 * يتحقق من البيانات الشخصية للعميل المطلوبة لإتمام عملية الشراء
 * بناءً على MOD-ORDER-MGMT-FEAT-003 في REQ-FR.md
 */
class CashOrderStep1Request extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // البيانات الشخصية الأساسية
            'full_name' => [
                'required',
                'string',
                'min:3',
                'max:100',
                'regex:/^[\p{L}\s\-\'\.]+$/u'
            ],
            
            'national_id' => [
                'required',
                'string',
                'regex:/^[12]\d{9}$/', // رقم هوية سعودي صالح
                'unique:orders,customer_national_id'
            ],
            
            'date_of_birth' => [
                'required',
                'date',
                'before:' . now()->subYears(18)->format('Y-m-d'), // يجب أن يكون فوق 18 سنة
                'after:' . now()->subYears(100)->format('Y-m-d')  // حد أقصى 100 سنة
            ],
            
            'nationality_id' => [
                'required',
                'integer',
                'exists:nationalities,id'
            ],
            
            'phone_number' => [
                'required',
                'string',
                'regex:/^05\d{8}$/', // رقم جوال سعودي صالح
                'unique:users,phone_number,' . auth()->id()
            ],
            
            'email' => [
                'required',
                'email',
                'max:255',
                'unique:users,email,' . auth()->id()
            ],
            
            // العنوان
            'address_details' => [
                'required',
                'string',
                'min:10',
                'max:500'
            ],
            
            'city' => [
                'required',
                'string',
                'min:2',
                'max:50'
            ],
            
            'district' => [
                'required',
                'string',
                'min:2',
                'max:50'
            ],
            
            // معلومات الاتصال الإضافية
            'emergency_contact_name' => [
                'nullable',
                'string',
                'min:3',
                'max:100'
            ],
            
            'emergency_contact_phone' => [
                'nullable',
                'string',
                'regex:/^05\d{8}$/'
            ],
            
            // الموافقات المطلوبة
            'terms_accepted' => [
                'required',
                'accepted'
            ],
            
            'privacy_policy_accepted' => [
                'required',
                'accepted'
            ]
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'full_name.required' => 'الاسم الكامل مطلوب',
            'full_name.min' => 'الاسم الكامل يجب أن يكون على الأقل 3 أحرف',
            'full_name.max' => 'الاسم الكامل يجب ألا يزيد عن 100 حرف',
            'full_name.regex' => 'الاسم الكامل يجب أن يحتوي على أحرف وأرقام فقط',
            
            'national_id.required' => 'رقم الهوية الوطنية مطلوب',
            'national_id.regex' => 'رقم الهوية الوطنية غير صالح',
            'national_id.unique' => 'رقم الهوية الوطنية مستخدم مسبقاً في طلب آخر',
            
            'date_of_birth.required' => 'تاريخ الميلاد مطلوب',
            'date_of_birth.date' => 'تاريخ الميلاد غير صالح',
            'date_of_birth.before' => 'يجب أن يكون العمر 18 سنة أو أكثر',
            'date_of_birth.after' => 'تاريخ الميلاد غير منطقي',
            
            'nationality_id.required' => 'الجنسية مطلوبة',
            'nationality_id.exists' => 'الجنسية المختارة غير صالحة',
            
            'phone_number.required' => 'رقم الجوال مطلوب',
            'phone_number.regex' => 'رقم الجوال يجب أن يبدأ بـ 05 ويتكون من 10 أرقام',
            'phone_number.unique' => 'رقم الجوال مستخدم مسبقاً',
            
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صالح',
            'email.unique' => 'البريد الإلكتروني مستخدم مسبقاً',
            
            'address_details.required' => 'تفاصيل العنوان مطلوبة',
            'address_details.min' => 'تفاصيل العنوان يجب أن تكون على الأقل 10 أحرف',
            'address_details.max' => 'تفاصيل العنوان يجب ألا تزيد عن 500 حرف',
            
            'city.required' => 'المدينة مطلوبة',
            'district.required' => 'الحي مطلوب',
            
            'emergency_contact_phone.regex' => 'رقم جوال جهة الاتصال الطارئ غير صالح',
            
            'terms_accepted.required' => 'يجب الموافقة على الشروط والأحكام',
            'terms_accepted.accepted' => 'يجب الموافقة على الشروط والأحكام',
            
            'privacy_policy_accepted.required' => 'يجب الموافقة على سياسة الخصوصية',
            'privacy_policy_accepted.accepted' => 'يجب الموافقة على سياسة الخصوصية'
        ];
    }

    /**
     * أسماء الحقول المخصصة للعرض في رسائل الخطأ
     */
    public function attributes(): array
    {
        return [
            'full_name' => 'الاسم الكامل',
            'national_id' => 'رقم الهوية الوطنية',
            'date_of_birth' => 'تاريخ الميلاد',
            'nationality_id' => 'الجنسية',
            'phone_number' => 'رقم الجوال',
            'email' => 'البريد الإلكتروني',
            'address_details' => 'تفاصيل العنوان',
            'city' => 'المدينة',
            'district' => 'الحي',
            'emergency_contact_name' => 'اسم جهة الاتصال الطارئ',
            'emergency_contact_phone' => 'رقم جهة الاتصال الطارئ',
            'terms_accepted' => 'الموافقة على الشروط والأحكام',
            'privacy_policy_accepted' => 'الموافقة على سياسة الخصوصية'
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'national_id' => preg_replace('/\D/', '', $this->national_id ?? ''),
            'phone_number' => preg_replace('/\D/', '', $this->phone_number ?? ''),
            'emergency_contact_phone' => preg_replace('/\D/', '', $this->emergency_contact_phone ?? ''),
            'full_name' => trim($this->full_name ?? ''),
            'address_details' => trim($this->address_details ?? ''),
            'city' => trim($this->city ?? ''),
            'district' => trim($this->district ?? ''),
        ]);
    }
}
