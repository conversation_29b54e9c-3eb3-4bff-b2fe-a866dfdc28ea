<?php

namespace Modules\Dashboard\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Modules\CarCatalog\Models\Car;
use Modules\CarCatalog\Models\Brand;
use Modules\CarCatalog\Models\BodyType;
use Modules\UserManagement\Models\User;

/**
 * خدمة تجميع البيانات الإحصائية للوحة البيانات الرئيسية
 *
 * هذه الخدمة مسؤولة عن جلب وتجميع جميع البيانات المطلوبة لعرضها في لوحة البيانات الرئيسية
 * بما في ذلك البطاقات الإحصائية والرسوم البيانية
 */
class DashboardDataService
{
    /**
     * جلب جميع البيانات المطلوبة للوحة البيانات الرئيسية
     *
     * @return array
     */
    public function getDashboardData(): array
    {
        return [
            // البطاقات الإحصائية
            'stats' => $this->getStatCards(),

            // بيانات الرسوم البيانية
            'charts' => [
                'sales' => $this->getSalesChartData(),
                'brands' => $this->getBrandsPieChartData(),
                'topCars' => $this->getTopCarsChartData(),
                'categories' => $this->getCategoriesChartData(),
                'financing' => $this->getFinancingStatusChartData(),
                'newCustomers' => $this->getNewCustomersChartData(),
                'paymentMethods' => $this->getPaymentMethodsChartData(),
            ],

            // الأقسام الأخرى
            'recentActivities' => $this->getRecentActivities(),
            'latestCars' => $this->getLatestCars(),
            'alerts' => $this->getAlerts(),
            'performanceMetrics' => $this->getPerformanceMetrics(),
        ];
    }

    /**
     * جلب بيانات البطاقات الإحصائية
     *
     * @return array
     */
    private function getStatCards(): array
    {
        // نظرًا لعدم وجود OrderManagement module بعد، سنستخدم بيانات وهمية مع بعض البيانات الحقيقية
        return [
            'newOrdersToday' => $this->getNewOrdersTodayCount(),
            'pendingFinanceRequests' => $this->getPendingFinanceRequestsCount(),
            'availableCars' => $this->getAvailableCarsCount(),
            'newCustomersThisMonth' => $this->getNewCustomersThisMonthCount(),
            'totalSalesThisMonth' => $this->getTotalSalesThisMonth(),
            'totalSalesThisYear' => $this->getTotalSalesThisYear(),
        ];
    }

    /**
     * عدد الطلبات الجديدة اليوم
     * ملاحظة: بيانات وهمية حتى يتم إنشاء OrderManagement module
     */
    private function getNewOrdersTodayCount(): int
    {
        // TODO: استبدال بـ Order::whereDate('created_at', today())->count() عند توفر OrderManagement
        return rand(5, 15);
    }

    /**
     * عدد طلبات التمويل المعلقة
     * ملاحظة: بيانات وهمية حتى يتم إنشاء OrderManagement module
     */
    private function getPendingFinanceRequestsCount(): int
    {
        // TODO: استبدال بـ Order::where('order_type', 'finance')->where('status', 'pending_review')->count()
        return rand(8, 25);
    }

    /**
     * عدد السيارات المتاحة
     */
    private function getAvailableCarsCount(): int
    {
        return Car::where('is_active', true)
                  ->where('is_sold', false)
                  ->count();
    }

    /**
     * عدد العملاء الجدد هذا الشهر
     */
    private function getNewCustomersThisMonthCount(): int
    {
        return User::whereHas('roles', function ($query) {
                        $query->where('name', 'Customer');
                    })
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count();
    }

    /**
     * إجمالي المبيعات هذا الشهر
     * ملاحظة: بيانات وهمية حتى يتم إنشاء OrderManagement module
     */
    private function getTotalSalesThisMonth(): array
    {
        // TODO: استبدال بـ Order::where('status', 'completed')->whereMonth('created_at', now()->month)->sum('total_price')
        $amount = rand(500000, 2000000);
        $lastMonthAmount = rand(400000, 1800000);
        $changePercentage = round((($amount - $lastMonthAmount) / $lastMonthAmount) * 100, 1);

        return [
            'amount' => $amount,
            'change_percentage' => $changePercentage,
            'is_increase' => $changePercentage > 0,
        ];
    }

    /**
     * إجمالي المبيعات هذا العام
     * ملاحظة: بيانات وهمية حتى يتم إنشاء OrderManagement module
     */
    private function getTotalSalesThisYear(): array
    {
        // TODO: استبدال بـ Order::where('status', 'completed')->whereYear('created_at', now()->year)->sum('total_price')
        $amount = rand(8000000, 15000000);
        $lastYearAmount = rand(7000000, 14000000);
        $changePercentage = round((($amount - $lastYearAmount) / $lastYearAmount) * 100, 1);

        return [
            'amount' => $amount,
            'change_percentage' => $changePercentage,
            'is_increase' => $changePercentage > 0,
        ];
    }

    /**
     * بيانات رسم المبيعات البياني (خطي)
     * الفترة الافتراضية: آخر 6 أشهر
     */
    private function getSalesChartData(): array
    {
        $months = [];
        $salesData = [];
        $ordersData = [];

        // إنشاء بيانات لآخر 6 أشهر
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $months[] = $date->format('M Y');

            // بيانات وهمية للمبيعات والطلبات
            // TODO: استبدال بـ استعلامات حقيقية من جدول orders
            $salesData[] = rand(200000, 800000);
            $ordersData[] = rand(15, 45);
        }

        return [
            'labels' => $months,
            'datasets' => [
                [
                    'label' => 'المبيعات (ريال)',
                    'data' => $salesData,
                    'borderColor' => '#2563eb',
                    'backgroundColor' => 'rgba(37, 99, 235, 0.1)',
                    'tension' => 0.4,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'عدد الطلبات',
                    'data' => $ordersData,
                    'borderColor' => '#f97316',
                    'backgroundColor' => 'rgba(249, 115, 22, 0.1)',
                    'tension' => 0.4,
                    'yAxisID' => 'y1',
                ]
            ]
        ];
    }

    /**
     * بيانات رسم الماركات البياني (دائري)
     */
    private function getBrandsPieChartData(): array
    {
        $brands = Brand::where('status', true)->take(5)->get();

        if ($brands->isEmpty()) {
            return [
                'labels' => ['لا توجد بيانات'],
                'datasets' => [
                    [
                        'data' => [100],
                        'backgroundColor' => ['#e5e7eb'],
                    ]
                ]
            ];
        }

        $labels = [];
        $data = [];
        $colors = ['#2563eb', '#10b981', '#f59e0b', '#f97316', '#06b6d4'];

        foreach ($brands as $index => $brand) {
            $labels[] = $brand->name;
            // بيانات وهمية لعدد المبيعات لكل ماركة
            // TODO: استبدال بـ استعلام حقيقي من جدول orders مع join على cars و brands
            $data[] = rand(10, 50);
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data)),
                    'borderWidth' => 0,
                    'hoverOffset' => 8,
                ]
            ]
        ];
    }

    /**
     * بيانات رسم أفضل السيارات البياني (شريطي أفقي)
     */
    private function getTopCarsChartData(): array
    {
        $cars = Car::with(['brand', 'carModel'])
                   ->where('is_active', true)
                   ->take(5)
                   ->get();

        if ($cars->isEmpty()) {
            return [
                'labels' => ['لا توجد بيانات'],
                'datasets' => [
                    [
                        'data' => [0],
                        'backgroundColor' => ['#e5e7eb'],
                    ]
                ]
            ];
        }

        $labels = [];
        $data = [];

        foreach ($cars as $car) {
            $labels[] = $car->brand->name . ' ' . $car->carModel->name;
            // بيانات وهمية لعدد المبيعات
            // TODO: استبدال بـ استعلام حقيقي من جدول orders
            $data[] = rand(5, 25);
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'عدد المبيعات',
                    'data' => $data,
                    'backgroundColor' => '#2563eb',
                    'borderColor' => '#1d4ed8',
                    'borderWidth' => 1,
                ]
            ]
        ];
    }

    /**
     * بيانات رسم الفئات البياني (دائري مجوف)
     */
    private function getCategoriesChartData(): array
    {
        $bodyTypes = BodyType::where('status', true)->take(5)->get();

        if ($bodyTypes->isEmpty()) {
            return [
                'labels' => ['لا توجد بيانات'],
                'datasets' => [
                    [
                        'data' => [100],
                        'backgroundColor' => ['#e5e7eb'],
                    ]
                ]
            ];
        }

        $labels = [];
        $data = [];
        $colors = ['#2563eb', '#10b981', '#f59e0b', '#f97316', '#06b6d4'];

        foreach ($bodyTypes as $index => $bodyType) {
            $labels[] = $bodyType->name;
            // بيانات وهمية لنسبة المبيعات
            // TODO: استبدال بـ استعلام حقيقي
            $data[] = rand(10, 40);
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data)),
                    'borderWidth' => 0,
                    'hoverOffset' => 8,
                ]
            ]
        ];
    }

    /**
     * بيانات رسم حالة طلبات التمويل البياني (دائري مجوف)
     */
    private function getFinancingStatusChartData(): array
    {
        // بيانات وهمية لحالة طلبات التمويل
        // TODO: استبدال بـ استعلام حقيقي من جدول orders
        return [
            'labels' => ['موافق عليها', 'مرفوضة', 'معلقة', 'مكتملة'],
            'datasets' => [
                [
                    'data' => [45, 15, 25, 15],
                    'backgroundColor' => ['#10b981', '#ef4444', '#f59e0b', '#2563eb'],
                    'borderWidth' => 0,
                    'hoverOffset' => 8,
                ]
            ]
        ];
    }

    /**
     * بيانات رسم العملاء الجدد البياني (خطي)
     */
    private function getNewCustomersChartData(): array
    {
        $months = [];
        $data = [];

        // إنشاء بيانات لآخر 6 أشهر
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $months[] = $date->format('M Y');

            // عدد العملاء الجدد الحقيقي لكل شهر
            $count = User::whereHas('roles', function ($query) {
                            $query->where('name', 'Customer');
                        })
                        ->whereMonth('created_at', $date->month)
                        ->whereYear('created_at', $date->year)
                        ->count();

            // إضافة بيانات وهمية إذا لم توجد بيانات حقيقية
            $data[] = $count > 0 ? $count : rand(5, 20);
        }

        return [
            'labels' => $months,
            'datasets' => [
                [
                    'label' => 'عدد العملاء الجدد',
                    'data' => $data,
                    'borderColor' => '#10b981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'tension' => 0.4,
                ]
            ]
        ];
    }

    /**
     * بيانات رسم طرق الدفع البياني (دائري مجوف)
     */
    private function getPaymentMethodsChartData(): array
    {
        // بيانات وهمية لطرق الدفع
        // TODO: استبدال بـ استعلام حقيقي من جدول orders
        return [
            'labels' => ['كاش أونلاين', 'تمويل', 'دفع في المعرض'],
            'datasets' => [
                [
                    'data' => [40, 45, 15],
                    'backgroundColor' => ['#2563eb', '#f97316', '#10b981'],
                    'borderWidth' => 0,
                    'hoverOffset' => 8,
                ]
            ]
        ];
    }

    /**
     * جلب آخر النشاطات
     */
    private function getRecentActivities(): array
    {
        // بيانات وهمية للنشاطات الحديثة
        // TODO: استبدال بـ استعلام حقيقي من جدول orders و users
        return [
            [
                'id' => 'ORD-001',
                'description' => 'طلب شراء سيارة جديد',
                'user_name' => 'أحمد محمد',
                'created_at' => now()->subHours(2),
                'status' => 'pending',
                'status_label' => 'معلق',
                'status_class' => 'warning',
            ],
            [
                'id' => 'ORD-002',
                'description' => 'تم تأكيد طلب التمويل',
                'user_name' => 'فاطمة علي',
                'created_at' => now()->subHours(4),
                'status' => 'approved',
                'status_label' => 'موافق عليه',
                'status_class' => 'success',
            ],
            [
                'id' => 'USR-003',
                'description' => 'تسجيل عميل جديد',
                'user_name' => 'محمد سالم',
                'created_at' => now()->subHours(6),
                'status' => 'active',
                'status_label' => 'نشط',
                'status_class' => 'success',
            ],
            [
                'id' => 'ORD-004',
                'description' => 'طلب مراجعة مستندات',
                'user_name' => 'نورا أحمد',
                'created_at' => now()->subHours(8),
                'status' => 'review',
                'status_label' => 'تحت المراجعة',
                'status_class' => 'info',
            ],
            [
                'id' => 'ORD-005',
                'description' => 'تم إكمال عملية الشراء',
                'user_name' => 'خالد عبدالله',
                'created_at' => now()->subDay(),
                'status' => 'completed',
                'status_label' => 'مكتمل',
                'status_class' => 'success',
            ],
        ];
    }

    /**
     * جلب أحدث السيارات المضافة
     */
    private function getLatestCars(): array
    {
        return Car::with(['brand', 'carModel', 'media'])
                  ->where('is_active', true)
                  ->latest()
                  ->take(5)
                  ->get()
                  ->map(function ($car) {
                      return [
                          'id' => $car->id,
                          'title' => $car->title,
                          'brand_name' => $car->brand->name ?? 'غير محدد',
                          'model_name' => $car->carModel->name ?? 'غير محدد',
                          'price' => $car->price,
                          'currency' => $car->currency,
                          'image_url' => $car->getFirstMediaUrl('car_images') ?: asset('vendor/dash/images/placeholder-car.jpg'),
                          'created_at' => $car->created_at,
                      ];
                  })
                  ->toArray();
    }

    /**
     * جلب التنبيهات المهمة
     */
    private function getAlerts(): array
    {
        $alerts = [];

        // تنبيه طلبات التمويل المعلقة
        $pendingFinanceCount = $this->getPendingFinanceRequestsCount();
        if ($pendingFinanceCount > 10) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'طلبات تمويل معلقة',
                'message' => "يوجد {$pendingFinanceCount} طلب تمويل بحاجة للمراجعة",
                'action_url' => '#', // TODO: رابط لصفحة طلبات التمويل
            ];

            // TODO: PH02-TASK-025 - إرسال إشعار تلقائي عند تجاوز حد طلبات التمويل المعلقة
            // NotificationService::sendAutoAlert([
            //     'type' => 'pending_finance_requests_high',
            //     'title' => 'تحذير: طلبات تمويل معلقة كثيرة',
            //     'message' => "يوجد {$pendingFinanceCount} طلب تمويل معلق يحتاج للمراجعة العاجلة",
            //     'data' => ['count' => $pendingFinanceCount, 'threshold' => 10],
            //     'recipients' => ['admin', 'finance_manager'],
            //     'channels' => ['database', 'email', 'push'],
            //     'priority' => 'high',
            //     'auto_repeat' => false // لتجنب الإرسال المتكرر
            // ]);
        }

        // تنبيه السيارات قليلة المخزون
        $lowStockCars = Car::where('is_active', true)
                           ->where('is_sold', false)
                           ->count();

        if ($lowStockCars < 10) {
            $alerts[] = [
                'type' => 'info',
                'title' => 'مخزون السيارات منخفض',
                'message' => "يوجد {$lowStockCars} سيارة متاحة فقط",
                'action_url' => '#', // TODO: رابط لصفحة إدارة السيارات
            ];

            // TODO: PH02-TASK-025 - إرسال إشعار تلقائي عند انخفاض مخزون السيارات
            // NotificationService::sendAutoAlert([
            //     'type' => 'low_car_inventory',
            //     'title' => 'تحذير: مخزون السيارات منخفض',
            //     'message' => "المخزون المتاح من السيارات منخفض ({$lowStockCars} سيارة فقط)",
            //     'data' => ['available_cars' => $lowStockCars, 'threshold' => 10],
            //     'recipients' => ['admin', 'inventory_manager', 'sales_manager'],
            //     'channels' => ['database', 'email'],
            //     'priority' => 'medium',
            //     'auto_repeat' => 'daily' // إرسال يومي حتى يتم حل المشكلة
            // ]);
        }

        return $alerts;
    }

    /**
     * جلب مؤشرات الأداء
     */
    private function getPerformanceMetrics(): array
    {
        // بيانات وهمية لمؤشرات الأداء
        // TODO: استبدال بـ حسابات حقيقية من جدول orders
        return [
            'conversion_rate' => [
                'value' => 75.5,
                'label' => 'نسبة تحويل المبيعات',
                'target' => 80,
            ],
            'average_order_value' => [
                'value' => 125000,
                'label' => 'متوسط قيمة الطلب',
                'currency' => 'ريال',
            ],
            'customer_satisfaction' => [
                'value' => 4.2,
                'label' => 'رضا العملاء',
                'max' => 5,
            ],
            'finance_approval_rate' => [
                'value' => 68.3,
                'label' => 'نسبة قبول التمويل',
                'target' => 70,
            ],
        ];
    }
}
