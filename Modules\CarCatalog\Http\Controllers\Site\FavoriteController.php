<?php

namespace Modules\CarCatalog\Http\Controllers\Site;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Modules\CarCatalog\Models\Car;

/**
 * وحدة تحكم إدارة المفضلة في الموقع العام.
 *
 * تتعامل هذه الوحدة مع إضافة وإزالة السيارات من قائمة مفضلة المستخدم
 * وتوفر واجهات AJAX للتفاعل مع المفضلة من الواجهة الأمامية
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024
 */
class FavoriteController extends Controller
{
    /**
     * إضافة سيارة إلى قائمة المفضلة.
     *
     * يتحقق من أن المستخدم مسجل دخوله ثم يضيف السيارة إلى قائمة المفضلة
     * إذا لم تكن مضافة مسبقاً. يستخدم syncWithoutDetaching لتجنب التكرار
     *
     * @param Car $car السيارة المراد إضافتها للمفضلة (Route Model Binding)
     * @return JsonResponse استجابة JSON تحتوي على حالة العملية
     */
    public function add(Car $car): JsonResponse
    {
        // التحقق من أن المستخدم مسجل دخوله
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً لإضافة السيارات للمفضلة',
                'redirect' => route('login')
            ], 401);
        }

        $user = Auth::user();

        // التحقق من أن السيارة متاحة (غير مباعة ونشطة)
        if ($car->is_sold || !$car->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'هذه السيارة غير متاحة حالياً'
            ], 400);
        }

        // إضافة السيارة للمفضلة (syncWithoutDetaching يتجنب التكرار)
        $user->favorites()->syncWithoutDetaching([$car->id]);

        // التحقق من أن السيارة تم إضافتها فعلاً
        $isInFavorites = $user->favorites()->where('car_id', $car->id)->exists();

        return response()->json([
            'success' => true,
            'message' => 'تم إضافة السيارة إلى المفضلة بنجاح',
            'is_favorite' => $isInFavorites,
            'favorites_count' => $user->favorites()->count()
        ]);
    }

    /**
     * إزالة سيارة من قائمة المفضلة.
     *
     * يتحقق من أن المستخدم مسجل دخوله ثم يزيل السيارة من قائمة المفضلة
     *
     * @param Car $car السيارة المراد إزالتها من المفضلة (Route Model Binding)
     * @return JsonResponse استجابة JSON تحتوي على حالة العملية
     */
    public function remove(Car $car): JsonResponse
    {
        // التحقق من أن المستخدم مسجل دخوله
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'redirect' => route('login')
            ], 401);
        }

        $user = Auth::user();

        // إزالة السيارة من المفضلة
        $user->favorites()->detach($car->id);

        return response()->json([
            'success' => true,
            'message' => 'تم إزالة السيارة من المفضلة بنجاح',
            'is_favorite' => false,
            'favorites_count' => $user->favorites()->count()
        ]);
    }

    /**
     * تبديل حالة السيارة في المفضلة (إضافة أو إزالة).
     *
     * طريقة مريحة للتبديل بين إضافة وإزالة السيارة من المفضلة
     * بناءً على حالتها الحالية
     *
     * @param Car $car السيارة المراد تبديل حالتها (Route Model Binding)
     * @return JsonResponse استجابة JSON تحتوي على حالة العملية
     */
    public function toggle(Car $car): JsonResponse
    {
        // التحقق من أن المستخدم مسجل دخوله
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً لإضافة السيارات للمفضلة',
                'redirect' => route('login')
            ], 401);
        }

        $user = Auth::user();

        // التحقق من أن السيارة متاحة (غير مباعة ونشطة)
        if ($car->is_sold || !$car->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'هذه السيارة غير متاحة حالياً'
            ], 400);
        }

        // التحقق من حالة السيارة الحالية في المفضلة
        $isCurrentlyFavorite = $user->favorites()->where('car_id', $car->id)->exists();

        if ($isCurrentlyFavorite) {
            // إزالة من المفضلة
            $user->favorites()->detach($car->id);
            $message = 'تم إزالة السيارة من المفضلة بنجاح';
            $isFavorite = false;
        } else {
            // إضافة للمفضلة
            $user->favorites()->attach($car->id);
            $message = 'تم إضافة السيارة إلى المفضلة بنجاح';
            $isFavorite = true;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
            'is_favorite' => $isFavorite,
            'favorites_count' => $user->favorites()->count()
        ]);
    }

    /**
     * الحصول على عدد السيارات في المفضلة للمستخدم الحالي.
     *
     * @return JsonResponse استجابة JSON تحتوي على عدد السيارات المفضلة
     */
    public function count(): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => true,
                'favorites_count' => 0
            ]);
        }

        $count = Auth::user()->favorites()->count();

        return response()->json([
            'success' => true,
            'favorites_count' => $count
        ]);
    }

    /**
     * التحقق من حالة سيارة معينة في المفضلة.
     *
     * @param Car $car السيارة المراد التحقق من حالتها
     * @return JsonResponse استجابة JSON تحتوي على حالة السيارة في المفضلة
     */
    public function status(Car $car): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => true,
                'is_favorite' => false
            ]);
        }

        $isFavorite = Auth::user()->favorites()->where('car_id', $car->id)->exists();

        return response()->json([
            'success' => true,
            'is_favorite' => $isFavorite
        ]);
    }
}
