# تنفيذ دوال Controller لعملية طلب التمويل - OrderManagement Module

## نظرة عامة

تم تنفيذ دوال Controller في `SiteOrderController` لعرض صفحات عملية طلب التمويل متعددة المراحل بناءً على المتطلبات المحددة في `REQ-FR.md` (MOD-ORDER-MGMT-FEAT-004) و `UIUX-FR.md` (SITE-BUY-FINANCE-STEPX-001).

## الميزات المنفذة

### 1. دوال Controller الجديدة

تم إضافة الدوال التالية إلى `SiteOrderController`:

#### دوال عرض الخطوات:
- `showFinanceOrderStep1PersonalDetails($request, $carId)` - عرض الخطوة الأولى (البيانات الشخصية)
- `showFinanceOrderStep2FinanceInfo()` - عرض الخطوة الثانية (معلومات التمويل)
- `showFinanceOrderStep3Documents()` - عرض الخطوة الثالثة (رفع المستندات)
- `showFinanceOrderStep4ReviewConfirm()` - عرض الخطوة الرابعة (المراجعة والتأكيد)

#### دوال معالجة البيانات:
- `processFinanceOrderStep1(FinanceOrderStep1Request $request)` - معالجة البيانات الشخصية
- `processFinanceOrderStep2(FinanceOrderStep2Request $request)` - معالجة معلومات التمويل
- `processFinanceOrderStep3(FinanceOrderStep3Request $request)` - معالجة المستندات
- `processFinanceOrderStep4(FinanceOrderStep4Request $request)` - معالجة التأكيد النهائي

#### دوال إضافية:
- `storeFinanceOrder()` - إنشاء طلب التمويل النهائي
- `cancelFinanceOrder()` - إلغاء عملية طلب التمويل
- `clearFinanceOrderSession()` - تنظيف بيانات الجلسة
- `collectFinanceOrderDataFromSession()` - جمع البيانات من الجلسة
- `uploadFinanceOrderDocuments(Order $order)` - رفع مستندات التمويل

### 2. FormRequests الجديدة

تم إنشاء FormRequests متخصصة لكل خطوة:

#### FinanceOrderStep1Request
- التحقق من البيانات الشخصية (مشابه للكاش مع تعديلات طفيفة)
- التحقق من صحة رقم الهوية والعمر والجنسية
- التحقق من بيانات الاتصال والعنوان

#### FinanceOrderStep2Request
- التحقق من معلومات التمويل الأساسية:
  - مبلغ الدفعة الأولى (حد أقصى 50% من سعر السيارة)
  - الدخل الشهري (حد أدنى 3000 ريال)
  - الالتزامات الشهرية (حد أقصى 80% من الدخل)
- التحقق من معلومات العمل:
  - نوع العمل (حكومي/خاص/أعمال حرة/متقاعد)
  - المسمى الوظيفي وجهة العمل
  - سنوات الخبرة والعمل الحالي
- التحقق من معلومات البنك:
  - البنك المحول عليه الراتب
  - رقم الحساب البنكي ورقم الآيبان
- التحقق من معلومات إضافية:
  - وجود قروض أخرى وتفاصيلها
  - فترة التمويل المطلوبة (12-60 شهر)

#### FinanceOrderStep3Request
- التحقق من المستندات الأساسية:
  - صورة الهوية الوطنية (الوجهان)
  - رخصة القيادة
- التحقق من مستندات التمويل الإضافية:
  - تعريف بالراتب
  - كشف الحساب البنكي
- التحقق من المستندات الإضافية (حد أقصى 3 مستندات)
- التحقق من صحة الملفات (نوع وحجم)

#### FinanceOrderStep4Request
- التحقق من الموافقات النهائية:
  - تأكيد مراجعة البيانات
  - الموافقة على شروط التمويل
  - الموافقة على فحص السجل الائتماني
  - الموافقة على مشاركة البيانات مع جهات التمويل
- التحقق من اكتمال جميع الخطوات السابقة
- التحقق من صافي الدخل (حد أدنى 2000 ريال)

### 3. Routes الجديدة

تم إضافة مسارات التمويل إلى `web.php`:

```php
Route::prefix('finance')->name('finance.')->group(function() {
    // الخطوة 1: البيانات الشخصية
    Route::get('step1/{car}', 'Site\SiteOrderController@showFinanceOrderStep1PersonalDetails')->name('step1');
    Route::post('step1/process', 'Site\SiteOrderController@processFinanceOrderStep1')->name('step1.process');

    // الخطوة 2: معلومات التمويل
    Route::get('step2', 'Site\SiteOrderController@showFinanceOrderStep2FinanceInfo')->name('step2');
    Route::post('step2/process', 'Site\SiteOrderController@processFinanceOrderStep2')->name('step2.process');

    // الخطوة 3: رفع المستندات
    Route::get('step3', 'Site\SiteOrderController@showFinanceOrderStep3Documents')->name('step3');
    Route::post('step3/process', 'Site\SiteOrderController@processFinanceOrderStep3')->name('step3.process');

    // الخطوة 4: المراجعة والتأكيد
    Route::get('step4', 'Site\SiteOrderController@showFinanceOrderStep4ReviewConfirm')->name('step4');
    Route::post('step4/process', 'Site\SiteOrderController@processFinanceOrderStep4')->name('step4.process');

    // إنشاء طلب التمويل النهائي
    Route::post('store', 'Site\SiteOrderController@storeFinanceOrder')->name('store');

    // إلغاء عملية طلب التمويل
    Route::post('cancel', 'Site\SiteOrderController@cancelFinanceOrder')->name('cancel');
});
```

### 4. تحديثات OrderProcessingService

تم إضافة الدوال التالية:

#### createFinanceOrder(array $orderData)
- إنشاء طلب تمويل جديد
- تحضير تفاصيل التمويل في `finance_details` JSON field
- تحديد نوع الطلب كـ `finance_application`
- تحديد الحالة الأولية كـ `pending_finance_review`
- تحديث حالة السيارة إلى `finance_pending`

#### generateFinanceOrderNumber()
- توليد رقم طلب تمويل فريد بالبادئة `FIN`
- تنسيق: `FIN-YYYYMM0001`

### 5. إدارة الجلسة

تم استخدام مفاتيح جلسة منفصلة لطلب التمويل:
- `finance_order_car_id` - معرف السيارة المختارة
- `finance_order_step` - الخطوة الحالية
- `finance_order_personal_data` - البيانات الشخصية
- `finance_order_finance_data` - معلومات التمويل
- `finance_order_documents_data` - بيانات المستندات
- `finance_order_temp_documents` - الملفات المؤقتة
- `finance_order_confirmation_data` - بيانات التأكيد النهائي

### 6. معالجة المستندات

تم دعم أنواع المستندات الإضافية للتمويل:
- `salary_certificate` - تعريف بالراتب
- `bank_statement` - كشف الحساب البنكي

### 7. تفاصيل التمويل

يتم حفظ تفاصيل التمويل في `finance_details` JSON field:
```json
{
    "down_payment_amount": 50000,
    "monthly_income": 15000,
    "monthly_obligations": 3000,
    "employment_type": "government",
    "job_title": "مهندس",
    "employer_name": "وزارة الصحة",
    "work_experience_years": 10,
    "current_job_years": 5,
    "salary_bank": "البنك الأهلي السعودي",
    "bank_account_number": "****************",
    "iban_number": "SA****************789012",
    "has_other_loans": false,
    "other_loans_details": null,
    "requested_financing_period": 48,
    "net_income": 12000,
    "application_date": "2024-01-15",
    "application_status": "pending_review"
}
```

## الأمان والتحقق

### 1. التحقق من الصلاحيات
- جميع الدوال تتطلب تسجيل دخول المستخدم (`auth` middleware)
- التحقق من وجود البيانات المطلوبة في الجلسة قبل كل خطوة

### 2. التحقق من صحة البيانات
- تحقق شامل من جميع البيانات المدخلة
- تحقق من صحة الملفات المرفوعة (نوع وحجم)
- تحقق من المنطق المالي (الدخل مقابل الالتزامات)

### 3. معالجة الأخطاء
- تسجيل مفصل للأخطاء في الـ logs
- رسائل خطأ واضحة للمستخدم
- معالجة آمنة للملفات المؤقتة

## الخطوات التالية

1. **إنشاء Blade Views**: إنشاء واجهات المستخدم لكل خطوة
2. **اختبار الوظائف**: كتابة واختبار الوحدة للدوال الجديدة
3. **تكامل مع OrderProcessingService**: التأكد من عمل جميع الخدمات بشكل صحيح
4. **إنشاء Views للإدارة**: واجهات إدارة طلبات التمويل في لوحة التحكم

## الملفات المتأثرة

- `Modules/OrderManagement/Http/Controllers/Site/SiteOrderController.php`
- `Modules/OrderManagement/Http/Requests/FinanceOrderStep1Request.php`
- `Modules/OrderManagement/Http/Requests/FinanceOrderStep2Request.php`
- `Modules/OrderManagement/Http/Requests/FinanceOrderStep3Request.php`
- `Modules/OrderManagement/Http/Requests/FinanceOrderStep4Request.php`
- `Modules/OrderManagement/Routes/web.php`
- `Modules/OrderManagement/Services/OrderProcessingService.php`

## ملاحظات

- تم اتباع نفس النمط المستخدم في عملية الشراء كاش
- جميع النصوص والرسائل باللغة العربية
- تم مراعاة المتطلبات المحددة في REQ-FR.md و UIUX-FR.md
- الكود قابل للصيانة والتوسع
