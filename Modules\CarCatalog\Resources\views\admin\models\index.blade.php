@extends('dashboard::layouts.admin_layout')

@section('title', 'إدارة موديلات السيارات')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h4 class="fw-bold mb-0" style="color: var(--primary-color);">إدارة موديلات السيارات</h4>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb brand-breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="brand-link">لوحة التحكم</a>
                    </li>
                    <li class="breadcrumb-item">إدارة السيارات</li>
                    <li class="breadcrumb-item active" aria-current="page">الموديلات</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.models.create') }}" class="btn btn-brand-primary">
                <i class="fas fa-plus me-1"></i> إضافة موديل جديد
            </a>
        </div>
    </div>

    {{-- عرض رسائل النجاح والخطأ --}}
    @if(session('success'))
        <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {{-- نموذج البحث والفلترة --}}
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.models.index') }}" class="mb-0">
                <div class="row gx-2 gy-2 align-items-center">
                    <div class="col-md-3">
                        <input type="text" name="search" class="form-control" placeholder="بحث باسم الموديل..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3">
                        <select name="brand_id" class="form-select">
                            <option value="">كل الماركات</option>
                            @foreach($brands as $id => $name)
                                <option value="{{ $id }}" {{ request('brand_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-select">
                            <option value="">كل الحالات</option>
                            <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>نشط</option>
                            <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-brand-info w-100">بحث</button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ route('admin.models.index') }}" class="btn btn-brand-secondary w-100">إعادة تعيين</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- جدول الموديلات --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">قائمة الموديلات</h5>
        </div>
        <div class="card-body table-responsive p-0">
            <table class="table table-hover recent-activity-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الموديل</th>
                        <th>الماركة</th>
                        <th>الحالة</th>
                        <th>عدد السيارات</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($models as $model)
                        <tr>
                            <td>{{ $model->id }}</td>
                            <td>{{ $model->name }}</td>
                            <td>{{ $model->brand->name }}</td>
                            <td>
                                <span class="badge rounded-pill bg-{{ $model->status ? 'success' : 'danger' }}">
                                    {{ $model->status ? 'نشط' : 'غير نشط' }}
                                </span>
                            </td>
                            <td>{{ $model->cars_count }}</td>
                            <td>{{ format_datetime_for_display($model->created_at, 'Y-m-d') }}</td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('admin.models.edit', $model->id) }}" class="btn btn-sm btn-warning me-1 action-btn edit" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.models.destroy', $model->id) }}" method="POST" class="d-inline"
                                          onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا الموديل؟ سيتم حذف الموديل بشكل ناعم.');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger action-btn delete" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">لا توجد موديلات مسجلة</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    {{-- ترقيم الصفحات --}}
    <div class="mt-3">
        {{ $models->appends(request()->query())->links('pagination::bootstrap-5') }}
    </div>
</div>
@endsection
