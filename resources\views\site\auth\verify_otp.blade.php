@extends('site.layouts.site_layout')

@section('title', 'التحقق من رقم الجوال')

@section('content')
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="auth-card">
                    <!-- عنوان الصفحة -->
                    <div class="auth-header text-center mb-4">
                        <div class="auth-icon mb-3">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h2 class="auth-title">التحقق من رقم الجوال</h2>
                        <p class="auth-subtitle text-muted">
                            تم إرسال رمز التحقق إلى رقم جوالك
                            @if($phone_number ?? session('registration_phone_number'))
                                <br><strong class="text-primary">{{ $phone_number ?? session('registration_phone_number') }}</strong>
                            @endif
                        </p>
                    </div>

                    <!-- عرض رسائل الخطأ العامة -->
                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- عرض رسائل النجاح -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- نموذج التحقق من OTP -->
                    {{-- ملاحظة: مسار site.auth.verify.otp.submit سيتم تفعيله في مهمة لاحقة (PH03-TASK-010) --}}
                    <form method="POST" action="{{ route('site.auth.verify.otp.submit') }}" class="auth-form" id="otpForm">
                        @csrf

                        <!-- حقل إدخال OTP -->
                        <div class="form-group mb-4">
                            <label for="otp_code" class="form-label required text-center d-block">رمز التحقق</label>

                            <!-- حقول OTP منفصلة (6 خانات) -->
                            <div class="otp-input-container d-flex justify-content-center gap-2 mb-3">
                                <input type="text" class="otp-digit form-control text-center" maxlength="1" data-index="0">
                                <input type="text" class="otp-digit form-control text-center" maxlength="1" data-index="1">
                                <input type="text" class="otp-digit form-control text-center" maxlength="1" data-index="2">
                                <input type="text" class="otp-digit form-control text-center" maxlength="1" data-index="3">
                                <input type="text" class="otp-digit form-control text-center" maxlength="1" data-index="4">
                                <input type="text" class="otp-digit form-control text-center" maxlength="1" data-index="5">
                            </div>

                            <!-- الحقل المخفي لإرسال OTP كاملاً -->
                            <input type="hidden" name="otp_code" id="otp_code" value="{{ old('otp_code') }}">

                            <small class="form-text text-muted text-center d-block">أدخل الرمز المكون من 6 أرقام</small>

                            @error('otp_code')
                                <span class="text-danger small d-block text-center mt-2">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- مؤقت انتهاء الصلاحية -->
                        <div class="text-center mb-4">
                            <div class="otp-timer" id="otpTimer">
                                <i class="fas fa-clock me-1"></i>
                                <span>ينتهي الرمز خلال: <strong id="timerDisplay">05:00</strong></span>
                            </div>
                        </div>

                        <!-- زر التحقق -->
                        <div class="form-group mb-4">
                            <button type="submit" class="btn btn-primary btn-lg w-100" id="verifyBtn">
                                <i class="fas fa-check me-2"></i>
                                تحقق من الرمز
                            </button>
                        </div>

                        <!-- رابط إعادة الإرسال -->
                        <div class="text-center">
                            <p class="mb-2">لم تستلم الرمز؟</p>
                            <button type="button" class="btn btn-link text-primary fw-bold" id="resendOtpBtn" disabled>
                                <i class="fas fa-redo me-1"></i>
                                إعادة إرسال الرمز
                            </button>
                            <div class="resend-timer text-muted small" id="resendTimer">
                                يمكنك طلب رمز جديد خلال <span id="resendDisplay">60</span> ثانية
                            </div>
                        </div>

                        <!-- روابط إضافية -->
                        <div class="text-center mt-4 pt-3 border-top">
                            <p class="mb-0">
                                <a href="{{ route('site.auth.register.form') }}" class="text-muted">
                                    <i class="fas fa-arrow-right me-1"></i>
                                    العودة للتسجيل
                                </a>
                                <span class="mx-2 text-muted">|</span>
                                <a href="{{ route('site.auth.login.form') }}" class="text-primary">
                                    <i class="fas fa-sign-in-alt me-1"></i>
                                    تسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* أنماط خاصة بصفحة التحقق من OTP */
.auth-container {
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    padding: 2rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.auth-icon {
    font-size: 3rem;
    color: var(--primary-color);
}

.auth-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    font-size: 0.95rem;
    line-height: 1.5;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
}

/* أنماط حقول OTP */
.otp-input-container {
    direction: ltr;
}

.otp-digit {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
    font-weight: bold;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.otp-digit:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
    outline: none;
}

.otp-digit.filled {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.otp-digit.error {
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

/* أنماط المؤقت */
.otp-timer {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.otp-timer.expired {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* أنماط الأزرار */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-primary:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-link {
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-link:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.btn-link:disabled {
    color: #6c757d !important;
    cursor: not-allowed;
}

/* أنماط إعادة الإرسال */
.resend-timer {
    margin-top: 0.5rem;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-danger {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .auth-card {
        padding: 1.5rem;
        margin: 1rem;
    }

    .auth-container {
        padding: 1rem 0;
    }

    .otp-digit {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }

    .auth-icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .otp-input-container {
        gap: 0.5rem !important;
    }

    .otp-digit {
        width: 35px;
        height: 35px;
        font-size: 1.1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // متغيرات العناصر
    const otpDigits = document.querySelectorAll('.otp-digit');
    const otpCodeInput = document.getElementById('otp_code');
    const otpForm = document.getElementById('otpForm');
    const verifyBtn = document.getElementById('verifyBtn');
    const resendBtn = document.getElementById('resendOtpBtn');
    const timerDisplay = document.getElementById('timerDisplay');
    const resendDisplay = document.getElementById('resendDisplay');
    const otpTimer = document.getElementById('otpTimer');
    const resendTimer = document.getElementById('resendTimer');

    // متغيرات المؤقتات
    let otpExpiryTime = 5 * 60; // 5 دقائق بالثواني
    let resendCooldown = 60; // 60 ثانية
    let otpInterval;
    let resendInterval;

    // تهيئة المؤقتات
    initializeTimers();

    // وظائف إدارة حقول OTP
    otpDigits.forEach((digit, index) => {
        // التركيز على الحقل الأول عند تحميل الصفحة
        if (index === 0) {
            digit.focus();
        }

        // معالجة الإدخال
        digit.addEventListener('input', function(e) {
            const value = e.target.value;

            // السماح بالأرقام فقط
            if (!/^\d$/.test(value)) {
                e.target.value = '';
                return;
            }

            // إضافة كلاس filled
            e.target.classList.add('filled');
            e.target.classList.remove('error');

            // الانتقال للحقل التالي
            if (value && index < otpDigits.length - 1) {
                otpDigits[index + 1].focus();
            }

            // تحديث الحقل المخفي
            updateOtpCode();
        });

        // معالجة مفاتيح الحذف والتنقل
        digit.addEventListener('keydown', function(e) {
            // مفتاح Backspace
            if (e.key === 'Backspace') {
                e.target.value = '';
                e.target.classList.remove('filled', 'error');

                // الانتقال للحقل السابق
                if (index > 0) {
                    otpDigits[index - 1].focus();
                }

                updateOtpCode();
            }

            // مفاتيح الأسهم
            if (e.key === 'ArrowLeft' && index > 0) {
                otpDigits[index - 1].focus();
            }
            if (e.key === 'ArrowRight' && index < otpDigits.length - 1) {
                otpDigits[index + 1].focus();
            }
        });

        // معالجة اللصق
        digit.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedData = e.clipboardData.getData('text');
            const digits = pastedData.replace(/\D/g, '').slice(0, 6);

            digits.split('').forEach((digit, i) => {
                if (otpDigits[i]) {
                    otpDigits[i].value = digit;
                    otpDigits[i].classList.add('filled');
                    otpDigits[i].classList.remove('error');
                }
            });

            updateOtpCode();

            // التركيز على آخر حقل مملوء أو التالي
            const lastFilledIndex = Math.min(digits.length - 1, otpDigits.length - 1);
            if (lastFilledIndex < otpDigits.length - 1) {
                otpDigits[lastFilledIndex + 1].focus();
            } else {
                otpDigits[lastFilledIndex].focus();
            }
        });
    });

    // تحديث قيمة OTP المخفية
    function updateOtpCode() {
        const otpValue = Array.from(otpDigits).map(digit => digit.value).join('');
        otpCodeInput.value = otpValue;

        // تفعيل/تعطيل زر التحقق
        verifyBtn.disabled = otpValue.length !== 6;
    }

    // تهيئة المؤقتات
    function initializeTimers() {
        startOtpTimer();
        startResendTimer();
    }

    // مؤقت انتهاء صلاحية OTP
    function startOtpTimer() {
        otpInterval = setInterval(() => {
            const minutes = Math.floor(otpExpiryTime / 60);
            const seconds = otpExpiryTime % 60;

            timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            if (otpExpiryTime <= 0) {
                clearInterval(otpInterval);
                handleOtpExpiry();
            }

            otpExpiryTime--;
        }, 1000);
    }

    // مؤقت إعادة الإرسال
    function startResendTimer() {
        resendInterval = setInterval(() => {
            resendDisplay.textContent = resendCooldown;

            if (resendCooldown <= 0) {
                clearInterval(resendInterval);
                enableResendButton();
            }

            resendCooldown--;
        }, 1000);
    }

    // معالجة انتهاء صلاحية OTP
    function handleOtpExpiry() {
        otpTimer.classList.add('expired');
        timerDisplay.textContent = 'انتهت الصلاحية';

        // تعطيل النموذج
        otpDigits.forEach(digit => {
            digit.disabled = true;
            digit.classList.add('error');
        });

        verifyBtn.disabled = true;
        verifyBtn.innerHTML = '<i class="fas fa-clock me-2"></i>انتهت صلاحية الرمز';

        // إظهار رسالة
        showAlert('انتهت صلاحية رمز التحقق. يرجى طلب رمز جديد.', 'warning');
    }

    // تفعيل زر إعادة الإرسال
    function enableResendButton() {
        resendBtn.disabled = false;
        resendTimer.style.display = 'none';
    }

    // معالجة إعادة إرسال OTP
    resendBtn.addEventListener('click', function() {
        // إرسال طلب AJAX لإعادة الإرسال
        // ملاحظة: مسار site.auth.resend.otp سيتم تفعيله في مهمة لاحقة (PH03-TASK-010)
        fetch('{{ route("site.auth.resend.otp") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                phone_number: '{{ $phone_number ?? session("registration_phone_number") }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إرسال رمز تحقق جديد بنجاح', 'success');
                resetForm();
            } else {
                showAlert(data.message || 'حدث خطأ أثناء إرسال الرمز', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ أثناء إرسال الرمز', 'error');
        });
    });

    // إعادة تعيين النموذج
    function resetForm() {
        // إعادة تعيين حقول OTP
        otpDigits.forEach(digit => {
            digit.value = '';
            digit.disabled = false;
            digit.classList.remove('filled', 'error');
        });

        // إعادة تعيين المؤقتات
        clearInterval(otpInterval);
        clearInterval(resendInterval);

        otpExpiryTime = 5 * 60;
        resendCooldown = 60;

        // إعادة تشغيل المؤقتات
        otpTimer.classList.remove('expired');
        resendTimer.style.display = 'block';
        resendBtn.disabled = true;

        initializeTimers();

        // إعادة تعيين زر التحقق
        verifyBtn.disabled = true;
        verifyBtn.innerHTML = '<i class="fas fa-check me-2"></i>تحقق من الرمز';

        // التركيز على الحقل الأول
        otpDigits[0].focus();

        updateOtpCode();
    }

    // عرض الرسائل
    function showAlert(message, type) {
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'warning' ? 'alert-warning' : 'alert-danger';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إزالة الرسائل السابقة
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // إضافة الرسالة الجديدة
        const authHeader = document.querySelector('.auth-header');
        authHeader.insertAdjacentHTML('afterend', alertHtml);
    }

    // معالجة إرسال النموذج
    otpForm.addEventListener('submit', function(e) {
        const otpValue = otpCodeInput.value;

        if (otpValue.length !== 6) {
            e.preventDefault();
            showAlert('يرجى إدخال رمز التحقق كاملاً', 'error');

            // إضافة كلاس خطأ للحقول الفارغة
            otpDigits.forEach(digit => {
                if (!digit.value) {
                    digit.classList.add('error');
                }
            });

            return false;
        }

        // إظهار مؤشر التحميل
        verifyBtn.disabled = true;
        verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحقق...';
    });

    // تحديث قيمة OTP عند تحميل الصفحة (في حالة وجود قيمة قديمة)
    const oldOtpValue = otpCodeInput.value;
    if (oldOtpValue) {
        oldOtpValue.split('').forEach((digit, index) => {
            if (otpDigits[index]) {
                otpDigits[index].value = digit;
                otpDigits[index].classList.add('filled');
            }
        });
        updateOtpCode();
    }
});
</script>
@endpush
