{{--
    ملف الرأس (Header) للموقع العام
    يحتوي على الشريط العلوي والقائمة الرئيسية وأيقونات التفاعل
    مستوحى من UIUX-FR.md القسم SITE-LAYOUT-MAIN-001
--}}

<header class="site-header">
    {{-- الشريط العلوي الاختياري --}}
    <div class="top-bar d-none d-lg-block">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="top-bar-links">
                        <a href="{{ route('site.contact') }}" class="brand-link me-3">
                            <i class="fas fa-phone me-1"></i>
                            تواصل معنا
                        </a>
                        <a href="{{ route('site.branches') }}" class="brand-link">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            فروعنا
                        </a>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="social-links">
                        <a href="#" class="brand-link me-2" title="تويتر">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="brand-link me-2" title="إنستغرام">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="brand-link me-2" title="واتساب">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                        <a href="#" class="brand-link" title="يوتيوب">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- الشريط الرئيسي للتنقل --}}
    <nav class="main-navbar navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            {{-- شعار المعرض --}}
            <a class="navbar-brand" href="{{ route('site.home') }}">
                <img src="{{ asset('images/logo.png') }}" alt="شعار المعرض" class="logo-img">
                <span class="brand-name">موتور لاين</span>
            </a>

            {{-- زر القائمة للموبايل --}}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar" 
                    aria-controls="mainNavbar" aria-expanded="false" aria-label="تبديل التنقل">
                <span class="navbar-toggler-icon"></span>
            </button>

            {{-- القائمة الرئيسية --}}
            <div class="collapse navbar-collapse" id="mainNavbar">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('site.home') ? 'active' : '' }}" 
                           href="{{ route('site.home') }}">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('site.cars.*') ? 'active' : '' }}" 
                           href="{{ route('site.cars.index') }}">السيارات الجديدة</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('site.services.*') ? 'active' : '' }}" 
                           href="{{ route('site.services.index') }}">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('site.corporate.*') ? 'active' : '' }}" 
                           href="{{ route('site.corporate.index') }}">مبيعات الشركات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('site.promotions.*') ? 'active' : '' }}" 
                           href="{{ route('site.promotions.index') }}">عروض السيارات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('site.request-car.*') ? 'active' : '' }}" 
                           href="{{ route('site.request-car.step1') }}">اطلب سيارتك</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('site.about') ? 'active' : '' }}" 
                           href="{{ route('site.about') }}">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('site.contact') ? 'active' : '' }}" 
                           href="{{ route('site.contact') }}">اتصل بنا</a>
                    </li>
                </ul>

                {{-- أيقونات التفاعل والمستخدم --}}
                <div class="navbar-actions d-flex align-items-center">
                    {{-- أيقونة البحث --}}
                    <button class="btn btn-link nav-action-btn me-2" type="button" data-bs-toggle="modal" 
                            data-bs-target="#searchModal" title="البحث">
                        <i class="fas fa-search"></i>
                    </button>

                    {{-- أيقونة المفضلة --}}
                    <a href="{{ route('site.favorites') }}" class="btn btn-link nav-action-btn me-2 position-relative" 
                       title="المفضلة">
                        <i class="fas fa-heart"></i>
                        <span class="favorites-count badge bg-danger position-absolute top-0 start-100 translate-middle">
                            {{ auth()->check() ? auth()->user()->favorites()->count() : 0 }}
                        </span>
                    </a>

                    {{-- حالة المستخدم --}}
                    @guest
                        <div class="auth-buttons">
                            <a href="{{ route('login') }}" class="btn btn-brand-secondary btn-sm me-2">
                                تسجيل الدخول
                            </a>
                            <a href="{{ route('register') }}" class="btn btn-brand-primary btn-sm">
                                إنشاء حساب
                            </a>
                        </div>
                    @else
                        <div class="dropdown">
                            <button class="btn btn-link nav-action-btn dropdown-toggle" type="button" 
                                    id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="{{ auth()->user()->profile_image_url ?? asset('images/default-avatar.png') }}" 
                                     alt="صورة المستخدم" class="user-avatar me-1">
                                <span class="user-name">{{ auth()->user()->first_name }}</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li>
                                    <a class="dropdown-item" href="{{ route('customer.dashboard') }}">
                                        <i class="fas fa-tachometer-alt me-2"></i>
                                        لوحة التحكم
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('customer.profile') }}">
                                        <i class="fas fa-user me-2"></i>
                                        الملف الشخصي
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('customer.orders') }}">
                                        <i class="fas fa-shopping-cart me-2"></i>
                                        طلباتي
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-sign-out-alt me-2"></i>
                                            تسجيل الخروج
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    @endguest
                </div>
            </div>
        </div>
    </nav>
</header>

{{-- مودال البحث --}}
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="searchModalLabel">البحث عن السيارات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('site.cars.index') }}" method="GET">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="search_brand" class="brand-form-label">الماركة</label>
                            <select name="brand" id="search_brand" class="form-select brand-form-control">
                                <option value="">اختر الماركة</option>
                                {{-- سيتم ملؤها ديناميكياً --}}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="search_model" class="brand-form-label">الموديل</label>
                            <select name="model" id="search_model" class="form-select brand-form-control">
                                <option value="">اختر الموديل</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="search_year" class="brand-form-label">سنة الصنع</label>
                            <select name="year" id="search_year" class="form-select brand-form-control">
                                <option value="">اختر السنة</option>
                                @for($year = date('Y') + 2; $year >= 2000; $year--)
                                    <option value="{{ $year }}">{{ $year }}</option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="search_price_range" class="brand-form-label">نطاق السعر</label>
                            <select name="price_range" id="search_price_range" class="form-select brand-form-control">
                                <option value="">اختر النطاق</option>
                                <option value="0-50000">أقل من 50,000 ريال</option>
                                <option value="50000-100000">50,000 - 100,000 ريال</option>
                                <option value="100000-200000">100,000 - 200,000 ريال</option>
                                <option value="200000-500000">200,000 - 500,000 ريال</option>
                                <option value="500000+">أكثر من 500,000 ريال</option>
                            </select>
                        </div>
                    </div>
                    <div class="text-center">
                        <button type="submit" class="btn btn-brand-primary">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
/* أنماط خاصة بالرأس */
.site-header .top-bar {
    background-color: var(--light-bg);
    padding: 0.5rem 0;
    font-size: 0.875rem;
}

.site-header .main-navbar {
    padding: 1rem 0;
}

.site-header .logo-img {
    height: 40px;
    margin-left: 10px;
}

.site-header .brand-name {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.site-header .nav-link {
    font-weight: 500;
    color: var(--text-color);
    transition: color var(--transition-speed) ease;
    padding: 0.75rem 1rem;
}

.site-header .nav-link:hover,
.site-header .nav-link.active {
    color: var(--secondary-color);
}

.site-header .nav-action-btn {
    color: var(--text-color);
    border: none;
    background: none;
    font-size: 1.1rem;
    transition: color var(--transition-speed) ease;
}

.site-header .nav-action-btn:hover {
    color: var(--secondary-color);
}

.site-header .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.site-header .favorites-count {
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
    border-radius: 50%;
}

.site-header .auth-buttons .btn {
    font-size: 0.875rem;
}

@media (max-width: 991.98px) {
    .site-header .navbar-actions {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }
    
    .site-header .auth-buttons {
        width: 100%;
    }
    
    .site-header .auth-buttons .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
