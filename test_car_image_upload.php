<?php

/**
 * اختبار بسيط لوظيفة رفع صور السيارات
 * 
 * هذا الملف يحتوي على اختبارات بسيطة للتأكد من أن إصلاحات رفع الصور تعمل بشكل صحيح
 */

require_once 'vendor/autoload.php';

use Modules\CarCatalog\Models\Car;
use Modules\CarCatalog\Models\Brand;
use Modules\CarCatalog\Models\CarModel;
use Modules\CarCatalog\Models\ManufacturingYear;
use Modules\CarCatalog\Models\Color;
use Modules\CarCatalog\Models\BodyType;
use Modules\CarCatalog\Models\TransmissionType;
use Modules\CarCatalog\Models\FuelType;

echo "=== اختبار وظيفة رفع صور السيارات ===\n\n";

// 1. التحقق من وجود النماذج المطلوبة
echo "1. التحقق من وجود النماذج المطلوبة:\n";

try {
    $brand = Brand::first();
    echo "   ✓ Brand model exists\n";
    
    $carModel = CarModel::first();
    echo "   ✓ CarModel model exists\n";
    
    $year = ManufacturingYear::first();
    echo "   ✓ ManufacturingYear model exists\n";
    
    $color = Color::first();
    echo "   ✓ Color model exists\n";
    
    $bodyType = BodyType::first();
    echo "   ✓ BodyType model exists\n";
    
    $transmissionType = TransmissionType::first();
    echo "   ✓ TransmissionType model exists\n";
    
    $fuelType = FuelType::first();
    echo "   ✓ FuelType model exists\n";
    
} catch (Exception $e) {
    echo "   ✗ خطأ في النماذج: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. التحقق من إعدادات Media Library في نموذج Car
echo "2. التحقق من إعدادات Media Library في نموذج Car:\n";

try {
    $car = new Car();
    
    // التحقق من استخدام traits
    $traits = class_uses($car);
    if (in_array('Spatie\MediaLibrary\InteractsWithMedia', $traits)) {
        echo "   ✓ InteractsWithMedia trait is used\n";
    } else {
        echo "   ✗ InteractsWithMedia trait is NOT used\n";
    }
    
    // التحقق من تطبيق HasMedia interface
    if ($car instanceof \Spatie\MediaLibrary\HasMedia) {
        echo "   ✓ HasMedia interface is implemented\n";
    } else {
        echo "   ✗ HasMedia interface is NOT implemented\n";
    }
    
    // التحقق من وجود دالة registerMediaCollections
    if (method_exists($car, 'registerMediaCollections')) {
        echo "   ✓ registerMediaCollections method exists\n";
    } else {
        echo "   ✗ registerMediaCollections method does NOT exist\n";
    }
    
    // التحقق من وجود دالة registerMediaConversions
    if (method_exists($car, 'registerMediaConversions')) {
        echo "   ✓ registerMediaConversions method exists\n";
    } else {
        echo "   ✗ registerMediaConversions method does NOT exist\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ خطأ في فحص نموذج Car: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. التحقق من وجود Controller وطرقه
echo "3. التحقق من وجود Controller وطرقه:\n";

try {
    $controller = new \Modules\CarCatalog\Http\Controllers\Admin\CarController();
    
    if (method_exists($controller, 'store')) {
        echo "   ✓ store method exists\n";
    } else {
        echo "   ✗ store method does NOT exist\n";
    }
    
    if (method_exists($controller, 'update')) {
        echo "   ✓ update method exists\n";
    } else {
        echo "   ✗ update method does NOT exist\n";
    }
    
    // التحقق من وجود دالة handleImageUploads (private method)
    $reflection = new ReflectionClass($controller);
    if ($reflection->hasMethod('handleImageUploads')) {
        echo "   ✓ handleImageUploads method exists\n";
    } else {
        echo "   ✗ handleImageUploads method does NOT exist\n";
    }
    
    if ($reflection->hasMethod('generateUniqueFileName')) {
        echo "   ✓ generateUniqueFileName method exists\n";
    } else {
        echo "   ✗ generateUniqueFileName method does NOT exist\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ خطأ في فحص Controller: " . $e->getMessage() . "\n";
}

echo "\n";

// 4. التحقق من FormRequests
echo "4. التحقق من FormRequests:\n";

try {
    $storeRequest = new \Modules\CarCatalog\Http\Requests\Admin\StoreCarRequest();
    echo "   ✓ StoreCarRequest exists\n";
    
    $updateRequest = new \Modules\CarCatalog\Http\Requests\Admin\UpdateCarRequest();
    echo "   ✓ UpdateCarRequest exists\n";
    
    // التحقق من قواعد التحقق للصور
    $storeRules = $storeRequest->rules();
    if (isset($storeRules['car_images'])) {
        echo "   ✓ car_images validation rules exist in StoreCarRequest\n";
    } else {
        echo "   ✗ car_images validation rules do NOT exist in StoreCarRequest\n";
    }
    
    $updateRules = $updateRequest->rules();
    if (isset($updateRules['car_images'])) {
        echo "   ✓ car_images validation rules exist in UpdateCarRequest\n";
    } else {
        echo "   ✗ car_images validation rules do NOT exist in UpdateCarRequest\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ خطأ في فحص FormRequests: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. التحقق من إعدادات Media Library
echo "5. التحقق من إعدادات Media Library:\n";

try {
    // التحقق من وجود جدول media
    if (Schema::hasTable('media')) {
        echo "   ✓ media table exists\n";
    } else {
        echo "   ✗ media table does NOT exist\n";
    }
    
    // التحقق من إعدادات filesystem
    $defaultDisk = config('filesystems.default');
    echo "   ✓ Default filesystem disk: $defaultDisk\n";
    
    $mediaDisk = config('media-library.disk_name');
    echo "   ✓ Media library disk: $mediaDisk\n";
    
} catch (Exception $e) {
    echo "   ✗ خطأ في فحص إعدادات Media Library: " . $e->getMessage() . "\n";
}

echo "\n=== انتهاء الاختبار ===\n";
