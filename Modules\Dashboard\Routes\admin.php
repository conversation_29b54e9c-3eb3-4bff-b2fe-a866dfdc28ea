<?php

use Illuminate\Support\Facades\Route;
use Modules\Dashboard\Http\Controllers\Admin\DashboardController;
use Modules\Dashboard\Http\Controllers\Admin\SystemSettingsController;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| هنا يمكنك تسجيل مسارات لوحة التحكم الإدارية للموديول. هذه المسارات
| يتم تحميلها بواسطة RouteServiceProvider ضمن مجموعة تحتوي على
| وسيط 'web'. قم بإنشاء شيء رائع!
|
*/

Route::middleware(['web', 'auth:web', 'verified', 'role:Super Admin|Employee'])
    ->prefix(config('modules.AppConfig.admin_prefix', 'admin')) // استخدام تكوين للبادئة الإدارية إذا كان متاحًا، وإلا استخدام 'admin' كقيمة افتراضية
    ->name('admin.')
    ->group(function () {
        // مسار لوحة البيانات الرئيسية
        Route::get('/dashboard', [DashboardController::class, 'home'])->name('dashboard');

        // اختصار لـ /admin لإعادة التوجيه إلى /admin/dashboard
        Route::get('/', function () {
            return redirect()->route('admin.dashboard');
        })->name('index'); // هذا يجعل admin.index متاحًا

        // مسارات إعدادات النظام
        Route::middleware('permission:manage_system_settings')->group(function () {
            Route::get('settings/system', [SystemSettingsController::class, 'index'])->name('settings.system.index');
            Route::put('settings/system', [SystemSettingsController::class, 'update'])->name('settings.system.update');
        });
    });
