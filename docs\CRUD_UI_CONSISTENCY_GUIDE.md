# دليل تطبيق الاتساق في واجهات CRUD

## نظرة عامة

هذا الدليل يوضح كيفية تطبيق النمط الموحد لواجهات CRUD في مودول CarCatalog. يجب اتباع هذا النمط لضمان الاتساق في جميع الصفحات.

## النمط الأساسي

### 1. هيكل الملف الأساسي

```blade
@extends('dashboard::layouts.admin_layout')

@section('title', 'عنوان الصفحة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- المحتوى هنا --}}
</div>
@endsection
```

### 2. عنوان الصفحة والتنقل

```blade
{{-- عنوان الصفحة ومسار التنقل --}}
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <h4 class="fw-bold mb-0" style="color: var(--primary-color);">عنوان الصفحة</h4>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb brand-breadcrumb mb-0">
                <li class="breadcrumb-item">
                    <a href="{{ route('admin.dashboard') }}" class="brand-link">لوحة التحكم</a>
                </li>
                <li class="breadcrumb-item">إدارة السيارات</li>
                <li class="breadcrumb-item active" aria-current="page">الصفحة الحالية</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ route('admin.resource.create') }}" class="btn btn-brand-primary">
            <i class="fas fa-plus me-1"></i> إضافة جديد
        </a>
    </div>
</div>
```

### 3. رسائل التنبيه

```blade
{{-- عرض رسائل النجاح والخطأ --}}
@if(session('success'))
    <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif
```

### 4. نموذج البحث والفلترة

```blade
{{-- نموذج البحث والفلترة --}}
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.resource.index') }}" class="mb-0">
            <div class="row gx-2 gy-2 align-items-center">
                <div class="col-md-4">
                    <input type="text" name="search" class="form-control" placeholder="بحث..." value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="">كل الحالات</option>
                        <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>نشط</option>
                        <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-brand-info w-100">بحث</button>
                </div>
                <div class="col-md-2">
                    <a href="{{ route('admin.resource.index') }}" class="btn btn-brand-secondary w-100">إعادة تعيين</a>
                </div>
            </div>
        </form>
    </div>
</div>
```

### 5. الجدول

```blade
{{-- جدول البيانات --}}
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">قائمة البيانات</h5>
    </div>
    <div class="card-body table-responsive p-0">
        <table class="table table-hover recent-activity-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>العمود 1</th>
                    <th>العمود 2</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                @forelse($items as $item)
                    <tr>
                        <td>{{ $item->id }}</td>
                        <td>{{ $item->name }}</td>
                        <td>{{ $item->description }}</td>
                        <td>
                            <span class="badge rounded-pill bg-{{ $item->status ? 'success' : 'danger' }}">
                                {{ $item->status ? 'نشط' : 'غير نشط' }}
                            </span>
                        </td>
                        <td>
                            <div class="d-flex">
                                <a href="{{ route('admin.resource.edit', $item) }}" class="btn btn-sm btn-warning me-1 action-btn edit" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.resource.destroy', $item) }}" method="POST" class="d-inline" 
                                      onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا العنصر؟');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger action-btn delete" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="text-center py-4">لا توجد بيانات لعرضها حاليًا.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
```

### 6. ترقيم الصفحات

```blade
{{-- ترقيم الصفحات --}}
<div class="mt-3">
    {{ $items->appends(request()->query())->links('pagination::bootstrap-5') }}
</div>
```

## الفئات المطلوبة

### أزرار Brand
- `btn-brand-primary` - للأزرار الأساسية
- `btn-brand-secondary` - للأزرار الثانوية
- `btn-brand-info` - لأزرار البحث
- `btn-brand-success` - للنجاح
- `btn-brand-warning` - للتحذير
- `btn-brand-danger` - للخطر

### أزرار الإجراءات
- `action-btn edit` - لأزرار التعديل
- `action-btn delete` - لأزرار الحذف
- `action-btn view` - لأزرار العرض

### التنبيهات
- `brand-alert brand-alert-success` - للنجاح
- `brand-alert brand-alert-danger` - للخطأ
- `brand-alert brand-alert-warning` - للتحذير
- `brand-alert brand-alert-info` - للمعلومات

### التنقل
- `brand-breadcrumb` - للـ breadcrumbs
- `brand-link` - للروابط

## نصائح مهمة

### 1. الألوان
- استخدم دائماً `style="color: var(--primary-color);"` للعناوين الرئيسية
- لا تستخدم ألوان مباشرة، استخدم CSS variables

### 2. الأيقونات
- استخدم Font Awesome icons
- أضف `me-1` للمسافة بين الأيقونة والنص

### 3. الجداول
- استخدم دائماً `recent-activity-table` class
- استخدم `table-responsive` للجداول الكبيرة

### 4. النماذج
- استخدم `gx-2 gy-2` للمسافات
- استخدم `w-100` للأزرار في الأعمدة

### 5. الترقيم
- استخدم دائماً `appends(request()->query())`
- استخدم `pagination::bootstrap-5` template

## مثال كامل

```blade
@extends('dashboard::layouts.admin_layout')

@section('title', 'إدارة أنواع الوقود')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h4 class="fw-bold mb-0" style="color: var(--primary-color);">إدارة أنواع الوقود</h4>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb brand-breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="brand-link">لوحة التحكم</a>
                    </li>
                    <li class="breadcrumb-item">إدارة السيارات</li>
                    <li class="breadcrumb-item active" aria-current="page">أنواع الوقود</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.fuel-types.create') }}" class="btn btn-brand-primary">
                <i class="fas fa-plus me-1"></i> إضافة نوع وقود جديد
            </a>
        </div>
    </div>

    {{-- باقي المحتوى... --}}
</div>
@endsection
```

## قائمة التحقق

عند تحديث أي صفحة CRUD، تأكد من:

- [ ] استخدام `dashboard::layouts.admin_layout`
- [ ] إضافة `brand-identity.css` في `@push('styles')`
- [ ] استخدام العنوان الموحد مع `var(--primary-color)`
- [ ] استخدام `brand-breadcrumb` للتنقل
- [ ] استخدام `brand-alert` للرسائل
- [ ] استخدام `btn-brand-*` للأزرار
- [ ] استخدام `action-btn` لأزرار الإجراءات
- [ ] استخدام `recent-activity-table` للجداول
- [ ] استخدام الترقيم الموحد

## الخطوات التالية

1. تطبيق هذا النمط على باقي الصفحات
2. مراجعة صفحات Create و Edit
3. تحديث صفحات Show
4. اختبار التوافق مع الأجهزة المختلفة
