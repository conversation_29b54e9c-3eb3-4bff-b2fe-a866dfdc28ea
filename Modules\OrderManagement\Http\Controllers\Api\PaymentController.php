<?php

namespace Modules\OrderManagement\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Modules\OrderManagement\Services\PaymentGatewayService;
use Modules\OrderManagement\Services\OrderProcessingService;
use Modules\OrderManagement\Models\Order;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Payment API Controller
 * 
 * يتولى معالجة webhooks وطلبات API المتعلقة بالدفع
 * بناءً على MOD-ORDER-MGMT-FEAT-005 في REQ-FR.md
 */
class PaymentController extends Controller
{
    /**
     * معالجة webhook من بوابة الدفع
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            // تسجيل استلام webhook
            Log::info('Payment webhook received', [
                'headers' => $request->headers->all(),
                'payload' => $request->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // التحقق من صحة الـ webhook
            $this->validateWebhook($request);

            // استخراج بيانات الدفع
            $paymentData = $this->extractPaymentData($request);

            // معالجة الدفع
            $paymentService = new PaymentGatewayService();
            $result = $paymentService->processPaymentResponse($paymentData);

            if ($result['success']) {
                Log::info('Payment webhook processed successfully', [
                    'order_id' => $result['order']->id ?? null,
                    'status' => 'success'
                ]);

                return response()->json([
                    'status' => 'success',
                    'message' => 'تم معالجة الدفع بنجاح'
                ], 200);
            } else {
                Log::warning('Payment webhook processing failed', [
                    'order_id' => $result['order']->id ?? null,
                    'message' => $result['message'],
                    'status' => 'failed'
                ]);

                return response()->json([
                    'status' => 'failed',
                    'message' => $result['message']
                ], 400);
            }

        } catch (Exception $e) {
            Log::error('Payment webhook error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'حدث خطأ في معالجة الدفع'
            ], 500);
        }
    }

    /**
     * الحصول على حالة الدفع لطلب معين
     */
    public function getPaymentStatus(Request $request, $orderId): JsonResponse
    {
        try {
            $order = Order::findOrFail($orderId);

            // التحقق من صلاحية المستخدم
            if (auth()->check() && $order->user_id !== auth()->id()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'غير مصرح لك بالوصول لهذا الطلب'
                ], 403);
            }

            return response()->json([
                'status' => 'success',
                'data' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'payment_status' => $order->payment_status,
                    'order_status' => $order->status,
                    'amount' => $order->reservation_amount,
                    'currency' => 'SAR',
                    'payment_method' => $order->payment_method,
                    'transaction_id' => $order->payment_transaction_id,
                    'created_at' => $order->created_at,
                    'updated_at' => $order->updated_at
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Error getting payment status', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'حدث خطأ في جلب حالة الدفع'
            ], 500);
        }
    }

    /**
     * إعادة محاولة الدفع لطلب معين
     */
    public function retryPayment(Request $request, $orderId): JsonResponse
    {
        try {
            $order = Order::findOrFail($orderId);

            // التحقق من صلاحية المستخدم
            if ($order->user_id !== auth()->id()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'غير مصرح لك بالوصول لهذا الطلب'
                ], 403);
            }

            // التحقق من إمكانية إعادة المحاولة
            if ($order->payment_status === 'completed') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'تم الدفع بالفعل لهذا الطلب'
                ], 400);
            }

            // إنشاء جلسة دفع جديدة
            $paymentService = new PaymentGatewayService();
            $paymentUrl = $paymentService->createPaymentSession($order);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'payment_url' => $paymentUrl,
                    'order_id' => $order->id
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Error retrying payment', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'حدث خطأ في إعادة محاولة الدفع'
            ], 500);
        }
    }

    /**
     * التحقق من صحة webhook
     */
    private function validateWebhook(Request $request): void
    {
        // التحقق من IP المرسل إذا كان محدد
        $allowedIps = config('payment.webhook.allowed_ips');
        if (!empty($allowedIps)) {
            $allowedIpsArray = explode(',', $allowedIps);
            if (!in_array($request->ip(), $allowedIpsArray)) {
                throw new Exception('IP غير مصرح به: ' . $request->ip());
            }
        }

        // التحقق من التوقيع إذا كان مفعل
        if (config('payment.webhook.verify_signature', true)) {
            $this->verifyWebhookSignature($request);
        }
    }

    /**
     * التحقق من توقيع webhook
     */
    private function verifyWebhookSignature(Request $request): void
    {
        $signature = $request->header('X-Signature') ?? $request->header('X-Webhook-Signature');
        
        if (!$signature) {
            throw new Exception('توقيع webhook مفقود');
        }

        $payload = $request->getContent();
        $secret = config('payment.gateways.test.webhook_secret'); // يجب تحديد البوابة المناسبة
        
        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        
        if (!hash_equals($expectedSignature, $signature)) {
            throw new Exception('توقيع webhook غير صحيح');
        }
    }

    /**
     * استخراج بيانات الدفع من webhook
     */
    private function extractPaymentData(Request $request): array
    {
        $data = $request->all();
        
        // تحويل البيانات لصيغة موحدة
        return [
            'order_id' => $data['order_id'] ?? $data['metadata']['order_id'] ?? null,
            'transaction_id' => $data['transaction_id'] ?? $data['id'] ?? null,
            'status' => $this->normalizePaymentStatus($data['status'] ?? 'unknown'),
            'amount' => $data['amount'] ?? 0,
            'currency' => $data['currency'] ?? 'SAR',
            'gateway' => $data['gateway'] ?? 'unknown',
            'error_message' => $data['error_message'] ?? $data['failure_reason'] ?? null,
            'raw_response' => $data
        ];
    }

    /**
     * توحيد حالة الدفع
     */
    private function normalizePaymentStatus(string $status): string
    {
        return match (strtolower($status)) {
            'paid', 'completed', 'success', 'successful' => 'success',
            'failed', 'error', 'declined', 'cancelled' => 'failed',
            'pending', 'processing', 'initiated' => 'pending',
            default => 'unknown'
        };
    }
}
