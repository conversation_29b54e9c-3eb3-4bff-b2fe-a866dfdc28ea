<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('car_features', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('category_id')->comment('معرّف فئة الميزة');
            $table->string('name', 100)->comment('اسم الميزة');
            $table->text('description')->nullable()->comment('وصف الميزة');
            $table->unsignedBigInteger('icon_id')->nullable()->comment('معرّف أيقونة الميزة من جدول media');
            $table->boolean('is_filterable')->default(false)->comment('قابلة للتصفية في البحث المتقدم');
            $table->boolean('is_highlighted')->default(false)->comment('ميزة مميزة تظهر في الصفحة الرئيسية');
            $table->boolean('status')->default(true)->comment('نشطة/غير نشطة');
            $table->unsignedInteger('display_order')->default(0)->comment('ترتيب العرض');
            $table->timestamps();

            // إنشاء الفهارس
            $table->index('category_id');
            $table->index('name');
            $table->index('icon_id');
            $table->index('is_filterable');
            $table->index('is_highlighted');
            $table->index('status');
            $table->index('display_order');

            // إنشاء المفتاح الخارجي
            $table->foreign('category_id')
                ->references('id')
                ->on('feature_categories')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            // إنشاء فهرس مركب للفرادة
            $table->unique(['category_id', 'name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('car_features');
    }
};
