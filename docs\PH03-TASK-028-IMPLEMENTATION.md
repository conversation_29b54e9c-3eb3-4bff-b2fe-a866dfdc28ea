# تنفيذ PH03-TASK-028: منطق رفع المستندات للطلبات

## نظرة عامة

تم تنفيذ منطق الـ Backend لرفع المستندات المطلوبة للطلبات (كاش وتمويل) باستخدام `spatie/laravel-medialibrary` بناءً على متطلبات `MOD-ORDER-MGMT-FEAT-006` في `REQ-FR.md`.

## المكونات المنفذة

### 1. دوال جديدة في SiteOrderController

#### أ. دوال AJAX لإدارة المستندات
```php
- uploadDocumentAjax(Request $request): JsonResponse
- deleteDocumentAjax(Request $request): JsonResponse
- previewDocument(Order $order, int $mediaId): Response
- downloadDocument(Order $order, int $mediaId): Response
- getOrderDocuments(Order $order): JsonResponse
```

#### ب. الميزات الأمنية المطبقة
- التحقق من ملكية الطلب للمستخدم الحالي
- التحقق من صحة أنواع الملفات (MIME type validation)
- التحقق من حجم الملف (2MB كحد أقصى)
- إعادة تسمية الملفات بأسماء آمنة
- تسجيل جميع العمليات في الـ logs

### 2. تحسينات DocumentUploadService

#### أ. دوال جديدة
```php
- uploadSingleDocumentFromRequest(): رفع مستند من Request مباشرة
- deleteDocumentById(): حذف مستند بواسطة معرف الوسائط
- hasDocument(): التحقق من وجود مستند معين
- getDocumentCount(): عدد المستندات لنوع معين
- getSupportedDocumentTypes(): أنواع المستندات المدعومة
- getMaxFileSize(): الحد الأقصى لحجم الملف
- getAllowedMimeTypes(): أنواع الملفات المدعومة
- validateDocumentTypeWithMessage(): التحقق مع رسائل مفصلة
- validateFileWithMessage(): التحقق من الملف مع رسائل مفصلة
```

#### ب. تحسين دالة uploadDocument
- تم إصلاح استخدام `addMedia()` بدلاً من `addMediaFromRequest()`
- تحسين معالجة الأخطاء والتسجيل

### 3. DocumentUploadRequest جديد

#### أ. قواعد التحقق
```php
- order_id: مطلوب، رقم صحيح، موجود في جدول orders، ينتمي للمستخدم الحالي
- document_type: مطلوب، نص، من الأنواع المدعومة
- file: مطلوب، ملف، أنواع مدعومة (jpeg,png,jpg,webp,pdf), حد أقصى 2MB
- description: اختياري، نص، حد أقصى 255 حرف
```

#### ب. الميزات الإضافية
- رسائل خطأ مخصصة باللغة العربية
- تحقق إضافي من صحة الملف
- تنظيف البيانات قبل التحقق
- دالة للحصول على البيانات مع معلومات إضافية

### 4. Routes جديدة

#### أ. مسارات AJAX
```php
POST /site/order/document/upload - رفع مستند
DELETE /site/order/document/delete - حذف مستند
GET /site/order/document/list/{order} - قائمة المستندات
```

#### ب. مسارات المعاينة والتحميل
```php
GET /site/order/document/preview/{order}/{mediaId} - معاينة مستند
GET /site/order/document/download/{order}/{mediaId} - تحميل مستند
```

#### ج. الحماية الأمنية
- تطبيق middleware: `['web', 'auth']`
- قيود على معرفات الطلبات والوسائط (أرقام فقط)

## الميزات الأمنية المطبقة

### 1. التحقق من الهوية والصلاحيات
- ✅ تسجيل دخول المستخدم مطلوب
- ✅ التحقق من ملكية الطلب للمستخدم
- ✅ منع الوصول للمستندات غير المملوكة

### 2. التحقق من الملفات
- ✅ التحقق من نوع الملف (MIME type)
- ✅ التحقق من حجم الملف (2MB حد أقصى)
- ✅ التحقق من صحة الملف (غير تالف)
- ✅ إعادة تسمية الملفات بأسماء آمنة

### 3. معالجة الأخطاء والتسجيل
- ✅ تسجيل جميع العمليات الناجحة والفاشلة
- ✅ رسائل خطأ واضحة ومفيدة
- ✅ معالجة استثناءات شاملة

## أنواع المستندات المدعومة

### 1. المستندات الأساسية (مطلوبة لجميع الطلبات)
- `national_id_front`: صورة الهوية الوطنية (الوجه الأمامي)
- `national_id_back`: صورة الهوية الوطنية (الوجه الخلفي)
- `driving_license`: رخصة القيادة

### 2. مستندات التمويل (مطلوبة لطلبات التمويل)
- `salary_certificate`: شهادة الراتب
- `bank_statement`: كشف حساب بنكي

### 3. مستندات إضافية (اختيارية)
- `additional_documents`: مستندات إضافية

## أنواع الملفات المدعومة

- **الصور**: JPEG, PNG, JPG, WEBP
- **المستندات**: PDF
- **الحد الأقصى للحجم**: 2 ميجابايت لكل ملف

## استخدام الـ API

### 1. رفع مستند عبر AJAX
```javascript
const formData = new FormData();
formData.append('order_id', orderId);
formData.append('document_type', 'national_id_front');
formData.append('file', fileInput.files[0]);
formData.append('description', 'وصف المستند');

fetch('/site/order/document/upload', {
    method: 'POST',
    body: formData,
    headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('تم رفع المستند بنجاح', data.document);
    } else {
        console.error('خطأ في رفع المستند', data.message);
    }
});
```

### 2. حذف مستند عبر AJAX
```javascript
fetch('/site/order/document/delete', {
    method: 'DELETE',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify({
        order_id: orderId,
        media_id: mediaId
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('تم حذف المستند بنجاح');
    } else {
        console.error('خطأ في حذف المستند', data.message);
    }
});
```

### 3. الحصول على قائمة المستندات
```javascript
fetch(`/site/order/document/list/${orderId}`)
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('مستندات الطلب', data.documents);
    }
});
```

## التكامل مع النظام الحالي

### 1. التكامل مع Order Model
- ✅ استخدام Media Collections الموجودة
- ✅ الحفاظ على البنية الحالية للمستندات
- ✅ عدم تعديل الجداول الموجودة

### 2. التكامل مع SiteOrderController
- ✅ إضافة دوال جديدة دون تعديل الموجود
- ✅ استخدام DocumentUploadService الموجود
- ✅ الحفاظ على نمط التسجيل والمعالجة

### 3. التكامل مع Routes
- ✅ إضافة مسارات جديدة دون تعديل الموجود
- ✅ تطبيق نفس مستوى الأمان
- ✅ استخدام نفس نمط التسمية

## الاختبار والتحقق

### 1. اختبارات الأمان
- [ ] اختبار منع الوصول للمستندات غير المملوكة
- [ ] اختبار رفع ملفات غير مدعومة
- [ ] اختبار رفع ملفات كبيرة الحجم
- [ ] اختبار رفع ملفات تالفة

### 2. اختبارات الوظائف
- [ ] اختبار رفع المستندات عبر AJAX
- [ ] اختبار حذف المستندات
- [ ] اختبار معاينة وتحميل المستندات
- [ ] اختبار الحصول على قائمة المستندات

### 3. اختبارات الأداء
- [ ] اختبار رفع ملفات متعددة
- [ ] اختبار الاستجابة مع ملفات كبيرة
- [ ] اختبار التحميل المتزامن

## الخطوات التالية المقترحة

1. **إنشاء واجهة مستخدم AJAX** لرفع المستندات في صفحات الطلبات
2. **إضافة إشعارات فورية** عند رفع أو حذف المستندات
3. **تطبيق معاينة الصور** في المتصفح قبل الرفع
4. **إضافة شريط تقدم** لعملية رفع الملفات
5. **تطبيق ضغط الصور** تلقائياً قبل الحفظ
6. **إضافة إمكانية رفع ملفات متعددة** في مرة واحدة
