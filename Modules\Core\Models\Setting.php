<?php

namespace Modules\Core\Models;

/**
 * نموذج إعدادات النظام
 *
 * يستخدم هذا النموذج للتعامل مع إعدادات النظام المخزنة في قاعدة البيانات
 * ويوفر واجهة للوصول إلى الإعدادات وتعديلها
 *
 * @property int $id
 * @property string $key مفتاح الإعداد (فريد)
 * @property string|null $value قيمة الإعداد
 * @property string|null $group_name مجموعة الإعداد (للتصنيف في واجهة الإدارة)
 * @property string|null $display_name الاسم المعروض في واجهة الإدارة
 * @property string $type نوع الإعداد (text, number, boolean, textarea, select)
 * @property array|null $options خيارات إضافية للنوع (مثل خيارات قائمة select)
 * @property bool $is_translatable هل قيمة الإعداد قابلة للترجمة؟
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 */
class Setting extends BaseModel
{
    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي
     *
     * @var array
     */
    protected $fillable = [
        'key',
        'value',
        'group_name',
        'display_name',
        'type',
        'options',
        'is_translatable',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
        'is_translatable' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];
}
