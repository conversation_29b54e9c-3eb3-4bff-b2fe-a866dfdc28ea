<?php

use Illuminate\Support\Facades\Route;
use Modules\UserManagement\Http\Controllers\Admin\RoleController;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| هنا يمكنك تسجيل مسارات لوحة التحكم الإدارية لموديول إدارة المستخدمين. 
| هذه المسارات يتم تحميلها بواسطة RouteServiceProvider ضمن مجموعة تحتوي على
| وسيط 'web'. قم بإنشاء شيء رائع!
|
*/

// مسارات إدارة الأدوار والصلاحيات (تتطلب صلاحية إدارة الأدوار والصلاحيات)
Route::middleware(['web', 'auth:web', 'verified', 'role:Super Admin|Employee', 'permission:manage_roles_permissions'])
    ->prefix(config('modules.AppConfig.admin_prefix', 'admin'))
    ->name('admin.')
    ->group(function () {
        // مسارات إدارة الأدوار
        Route::resource('roles', RoleController::class);
    });
