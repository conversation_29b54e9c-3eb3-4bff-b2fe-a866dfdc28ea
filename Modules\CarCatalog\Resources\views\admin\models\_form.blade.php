{{-- حقل الماركة --}}
<div class="mb-3">
    <label for="brand_id" class="form-label">الماركة <span class="text-danger">*</span></label>
    <select class="form-select @error('brand_id') is-invalid @enderror" id="brand_id" name="brand_id" required>
        <option value="">-- اختر الماركة --</option>
        @foreach($brands as $id => $name)
            <option value="{{ $id }}" {{ (old('brand_id', isset($model) ? $model->brand_id : '')) == $id ? 'selected' : '' }}>{{ $name }}</option>
        @endforeach
    </select>
    @error('brand_id')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>

{{-- حقل اسم الموديل --}}
<div class="mb-3">
    <label for="name" class="form-label">اسم الموديل <span class="text-danger">*</span></label>
    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $model->name ?? '') }}" required>
    @error('name')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>

{{-- حقل حالة الموديل --}}
<div class="mb-3">
    <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
        <option value="1" {{ (old('status', isset($model) ? $model->status : 1) == 1) ? 'selected' : '' }}>نشط</option>
        <option value="0" {{ (old('status', isset($model) ? $model->status : '') == '0') ? 'selected' : '' }}>غير نشط</option>
    </select>
    @error('status')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>
