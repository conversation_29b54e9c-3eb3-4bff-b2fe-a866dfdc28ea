<?php

namespace Modules\Core\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Core\Services\SettingsService;

/**
 * DefaultSettingsSeeder
 *
 * هذا الـ Seeder مسؤول عن إنشاء الإعدادات الافتراضية للنظام
 */
class DefaultSettingsSeeder extends Seeder
{
    protected SettingsService $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * تشغيل عملية إضافة البيانات
     *
     * @return void
     */
    public function run()
    {
        // إنشاء الإعدادات العامة
        $this->createGeneralSettings();

        // إنشاء إعدادات SEO
        $this->createSeoSettings();
    }

    /**
     * إنشاء الإعدادات العامة
     *
     * @return void
     */
    private function createGeneralSettings()
    {
        // اسم الموقع
        $this->settingsService->set(
            'site_name',
            'معرض موتور لاين',
            'general',
            'اسم الموقع',
            'text'
        );

        // البريد الإلكتروني للإدارة
        $this->settingsService->set(
            'admin_email',
            '<EMAIL>',
            'general',
            'البريد الإلكتروني للإدارة',
            'email'
        );

        // رقم الهاتف الرئيسي
        $this->settingsService->set(
            'main_phone',
            '+966501234567',
            'general',
            'رقم الهاتف الرئيسي',
            'text'
        );

        // العنوان
        $this->settingsService->set(
            'address',
            'الرياض، المملكة العربية السعودية',
            'general',
            'العنوان',
            'textarea'
        );

        // العملة الافتراضية
        $this->settingsService->set(
            'default_currency',
            'ر.س',
            'general',
            'العملة الافتراضية',
            'text'
        );

        // نسبة ضريبة القيمة المضافة
        $this->settingsService->set(
            'vat_percentage',
            '15',
            'general',
            'نسبة ضريبة القيمة المضافة (%)',
            'number'
        );

        // رسوم الحجز الافتراضية
        $this->settingsService->set(
            'default_booking_fee',
            '1000',
            'general',
            'رسوم الحجز الافتراضية',
            'number'
        );

        // نص خدمة ما بعد البيع
        $this->settingsService->set(
            'after_sales_service_text',
            'نحن نقدم خدمة ما بعد البيع المتميزة لضمان رضاكم التام عن سياراتكم.',
            'general',
            'نص خدمة ما بعد البيع',
            'textarea'
        );
    }

    /**
     * إنشاء إعدادات SEO
     *
     * @return void
     */
    private function createSeoSettings()
    {
        // الكلمات المفتاحية الافتراضية
        $this->settingsService->set(
            'seo_default_keywords',
            'سيارات، معرض سيارات، شراء سيارة، تمويل سيارات، سيارات جديدة، موتور لاين',
            'seo',
            'الكلمات المفتاحية الافتراضية',
            'textarea'
        );

        // الوصف الافتراضي للصفحات
        $this->settingsService->set(
            'seo_default_description',
            'معرض موتور لاين - وجهتك الأولى لشراء السيارات الجديدة بأفضل الأسعار وخدمات التمويل المتميزة في المملكة العربية السعودية.',
            'seo',
            'الوصف الافتراضي للصفحات',
            'textarea'
        );

        // معرف Google Analytics
        $this->settingsService->set(
            'ga_tracking_id',
            '',
            'seo',
            'معرف Google Analytics',
            'text'
        );
    }
}
