# دليل الدوال المساعدة - Helper Functions Reference

## نظرة عامة

يحتوي المشروع على مجموعة من الدوال المساعدة المنظمة في موديولين رئيسيين:
- **Core Module**: للدوال العامة المستخدمة في جميع أنحاء التطبيق
- **CarCatalog Module**: للدوال الخاصة بإدارة السيارات

## 🔧 الدوال العامة (Core Module)

### 📍 الموقع: `Modules/Core/Helpers/helpers.php`

#### 1. `setting(string $key, $default = null)`
**الغرض:** الحصول على قيمة إعداد معين من نظام الإعدادات

```php
// الاستخدام
$siteName = setting('site_name', 'MotorLine');
$currency = setting('default_currency', 'SAR');
```

#### 2. `format_currency(float $amount, string $currencySymbol = 'ر.س', int $decimals = 2, string $decimalSeparator = '.', string $thousandsSeparator = ','): string`
**الغرض:** تنسيق رقم كعملة

```php
// الاستخدام
echo format_currency(12500.75);           // "12,500.75 ر.س"
echo format_currency(1000, '$');          // "1,000.00 $"
echo format_currency(1234.5, 'ر.س', 0);  // "1,235 ر.س"
```

#### 3. `format_datetime_for_display(?string $datetimeString, string $format = 'Y-m-d h:i A'): ?string`
**الغرض:** تحويل سلسلة تاريخ ووقت إلى تنسيق عرض محدد

```php
// الاستخدام
echo format_datetime_for_display('2024-07-27 14:30:00');           // "2024-07-27 02:30 PM"
echo format_datetime_for_display($car->created_at, 'Y-m-d');       // "2024-07-27"
echo format_datetime_for_display($user->created_at, 'd/m/Y H:i');  // "27/07/2024 14:30"
```

#### 4. `generate_otp(int $length = 6): string`
**الغرض:** توليد رمز OTP رقمي عشوائي

```php
// الاستخدام
$otp = generate_otp();      // "123456"
$longOtp = generate_otp(8); // "12345678"
```

#### 5. `get_permission_translation(string $permission): array`
**الغرض:** الحصول على ترجمة الصلاحية

```php
// الاستخدام
$translation = get_permission_translation('manage_cars_admin');
// ['name' => 'إدارة السيارات', 'description' => 'يسمح بإضافة وتعديل وحذف السيارات...']
```

## 🚗 دوال السيارات (CarCatalog Module)

### 📍 الموقع: `Modules/CarCatalog/Helpers/helpers.php`

#### 1. `car_status_label(string $status): string`
**الغرض:** إرجاع تسمية حالة السيارة باللغة العربية

```php
// الاستخدام
echo car_status_label('active');    // "نشطة"
echo car_status_label('sold');      // "مباعة"
echo car_status_label('inactive');  // "غير نشطة"
echo car_status_label('featured');  // "مميزة"
```

#### 2. `car_status_badge_class(string $status): string`
**الغرض:** إرجاع كلاس Bootstrap badge للحالة

```php
// الاستخدام
echo car_status_badge_class('active');    // "success"
echo car_status_badge_class('sold');      // "danger"
echo car_status_badge_class('inactive');  // "secondary"
echo car_status_badge_class('featured');  // "warning"

// في Blade
<span class="badge bg-{{ car_status_badge_class($car->status) }}">
    {{ car_status_label($car->status) }}
</span>
```

#### 3. `car_condition_label(string $condition): string`
**الغرض:** إرجاع تسمية حالة السيارة (جديدة/مستعملة) باللغة العربية

```php
// الاستخدام
echo car_condition_label('new');                 // "جديدة"
echo car_condition_label('used');                // "مستعملة"
echo car_condition_label('certified_pre_owned'); // "مستعملة معتمدة"
```

#### 4. `car_condition_badge_class(string $condition): string`
**الغرض:** إرجاع كلاس Bootstrap badge لحالة السيارة (جديدة/مستعملة)

```php
// الاستخدام
echo car_condition_badge_class('new');                 // "success"
echo car_condition_badge_class('used');                // "primary"
echo car_condition_badge_class('certified_pre_owned'); // "info"
```

## 📋 أمثلة الاستخدام في Blade

### تنسيق العملة
```blade
{{-- عرض السعر --}}
<strong>{{ format_currency($car->price) }}</strong>

{{-- عرض السعر مع العرض --}}
@if($car->offer_price)
    <span class="text-decoration-line-through text-muted">{{ format_currency($car->price) }}</span><br>
    <strong class="text-success">{{ format_currency($car->offer_price) }}</strong>
@endif
```

### تنسيق التاريخ
```blade
{{-- عرض تاريخ الإنشاء --}}
<td>{{ format_datetime_for_display($brand->created_at, 'Y-m-d') }}</td>

{{-- عرض تاريخ مفصل --}}
<small>{{ format_datetime_for_display($car->created_at, 'd/m/Y H:i') }}</small>
```

### حالة السيارة
```blade
{{-- عرض حالة السيارة مع Badge --}}
<span class="badge rounded-pill bg-{{ car_status_badge_class($status) }}">
    {{ car_status_label($status) }}
</span>

{{-- تحديد الحالة ديناميكياً --}}
@php
    $status = 'active';
    if ($car->is_sold) {
        $status = 'sold';
    } elseif (!$car->is_active) {
        $status = 'inactive';
    } elseif ($car->is_featured) {
        $status = 'featured';
    }
@endphp
```

## 🔄 التحميل والتسجيل

### Core Module
```php
// في CoreServiceProvider.php
protected function loadHelpers()
{
    $helpersPath = module_path($this->moduleName, 'Helpers/helpers.php');
    if (file_exists($helpersPath)) {
        require_once $helpersPath;
    }
}
```

### CarCatalog Module
```php
// في CarCatalogServiceProvider.php
protected function loadHelpers()
{
    $helpersPath = module_path($this->moduleName, 'Helpers/helpers.php');
    if (file_exists($helpersPath)) {
        require_once $helpersPath;
    }
}
```

## ✅ حالة التنظيم

| الدالة | الموقع | الحالة | الاستخدام |
|--------|--------|--------|-----------|
| `setting()` | Core | ✅ منظمة | عام |
| `format_currency()` | Core | ✅ منظمة | عام |
| `format_datetime_for_display()` | Core | ✅ منظمة | عام |
| `generate_otp()` | Core | ✅ منظمة | عام |
| `get_permission_translation()` | Core | ✅ منظمة | عام |
| `car_status_label()` | CarCatalog | ✅ منظمة | خاص بالسيارات |
| `car_status_badge_class()` | CarCatalog | ✅ منظمة | خاص بالسيارات |
| `car_condition_label()` | CarCatalog | ✅ منظمة | خاص بالسيارات |
| `car_condition_badge_class()` | CarCatalog | ✅ منظمة | خاص بالسيارات |

## 📝 إرشادات التطوير

### إضافة دالة مساعدة جديدة

1. **تحديد الموقع المناسب:**
   - **Core**: للدوال العامة المستخدمة في عدة موديولات
   - **Module Specific**: للدوال الخاصة بموديول معين

2. **نمط التسمية:**
   - استخدم أسماء وصفية وواضحة
   - اتبع نمط `snake_case`
   - أضف prefix للدوال الخاصة بموديول معين

3. **التوثيق:**
   - أضف DocBlock كامل لكل دالة
   - وضح المعاملات والقيم المرجعة
   - أضف أمثلة للاستخدام

4. **الحماية:**
   - استخدم دائماً `if (!function_exists('function_name'))`
   - تعامل مع القيم الفارغة والاستثناءات

## 🎯 الخلاصة

✅ **جميع الدوال المساعدة منظمة بشكل صحيح**  
✅ **التحميل يتم تلقائياً في ServiceProviders**  
✅ **الاستخدام متسق عبر التطبيق**  
✅ **التوثيق شامل ومفصل**
