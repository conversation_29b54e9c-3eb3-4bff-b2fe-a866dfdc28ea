@extends('dashboard::layouts.admin_layout')

@section('title', 'إدارة سنوات الصنع')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h4 class="fw-bold mb-0" style="color: var(--primary-color);">إدارة سنوات الصنع</h4>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb brand-breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="brand-link">لوحة التحكم</a>
                    </li>
                    <li class="breadcrumb-item">إدارة السيارات</li>
                    <li class="breadcrumb-item active" aria-current="page">سنوات الصنع</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.years.create') }}" class="btn btn-brand-primary">
                <i class="fas fa-plus me-1"></i> إضافة سنة جديدة
            </a>
        </div>
    </div>

    {{-- عرض رسائل النجاح والخطأ --}}
    @if(session('success'))
        <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {{-- نموذج البحث والفلترة --}}
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.years.index') }}" class="mb-0">
                <div class="row gx-2 gy-2 align-items-center">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="بحث بالسنة..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">كل الحالات</option>
                            <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>نشط</option>
                            <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-brand-info w-100">بحث</button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ route('admin.years.index') }}" class="btn btn-brand-secondary w-100">إعادة تعيين</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- جدول سنوات الصنع --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">قائمة سنوات الصنع</h5>
        </div>
        <div class="card-body table-responsive p-0">
            <table class="table table-hover recent-activity-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>سنة الصنع</th>
                        <th>الحالة</th>
                        <th>عدد السيارات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($years as $year)
                        <tr>
                            <td>{{ $year->id }}</td>
                            <td>{{ $year->year }}</td>
                            <td>
                                <span class="badge rounded-pill bg-{{ $year->status ? 'success' : 'danger' }}">
                                    {{ $year->status ? 'نشط' : 'غير نشط' }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $year->cars_count }}</span>
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('admin.years.edit', $year) }}" class="btn btn-sm btn-warning me-1 action-btn edit" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.years.destroy', $year) }}" method="POST" class="d-inline"
                                          onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذه السنة؟');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger action-btn delete" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                                    <p>لا توجد سنوات صنع مسجلة</p>
                                    <a href="{{ route('admin.years.create') }}" class="btn btn-primary">
                                        إضافة أول سنة صنع
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

    </div>

    {{-- ترقيم الصفحات --}}
    <div class="mt-3">
        {{ $years->appends(request()->query())->links('pagination::bootstrap-5') }}
    </div>
</div>
@endsection
