{{--
    صفحة تفاصيل السيارة - الموقع العام
    تعرض معلومات شاملة ومفصلة عن سيارة محددة
    مستوحاة من UIUX-FR.md القسم SITE-CAR-DETAIL-001
--}}

@extends('site.layouts.site_layout')

@section('title', $car->title . ' - ' . $car->brand->name . ' ' . $car->carModel->name)

@section('meta_description', 'تفاصيل ' . $car->title . ' - ' . $car->brand->name . ' ' . $car->carModel->name . ' موديل ' . $car->manufacturingYear->year)

@section('meta_keywords', $car->brand->name . ', ' . $car->carModel->name . ', سيارة جديدة, ' . $car->manufacturingYear->year)

{{-- Open Graph Meta Tags --}}
@section('og_title', $car->brand->name . ' ' . $car->carModel->name . ' ' . $car->manufacturingYear->year . ' - موتور لاين')
@section('og_description', 'اكتشف ' . $car->title . ' - ' . $car->brand->name . ' ' . $car->carModel->name . ' موديل ' . $car->manufacturingYear->year . ' بسعر ' . format_currency($car->offer_price ?: $car->price) . '. ' . Str::limit(strip_tags($car->description ?? 'سيارة جديدة بأفضل الأسعار والمواصفات'), 150))
@section('og_image', $car->getFirstMedia('car_main_image') ? $car->getFirstMedia('car_main_image')->getUrl('large') : ($car->getFirstMedia('car_images') ? $car->getFirstMedia('car_images')->getUrl('large') : asset('images/logo-og.jpg')))
@section('og_url', url()->current())
@section('og_type', 'product')

{{-- Twitter Card Meta Tags --}}
@section('twitter_card', 'summary_large_image')
@section('twitter_title', $car->brand->name . ' ' . $car->carModel->name . ' ' . $car->manufacturingYear->year . ' - موتور لاين')
@section('twitter_description', 'اكتشف ' . $car->title . ' - ' . $car->brand->name . ' ' . $car->carModel->name . ' موديل ' . $car->manufacturingYear->year . ' بسعر ' . format_currency($car->offer_price ?: $car->price))
@section('twitter_image', $car->getFirstMedia('car_main_image') ? $car->getFirstMedia('car_main_image')->getUrl('large') : ($car->getFirstMedia('car_images') ? $car->getFirstMedia('car_images')->getUrl('large') : asset('images/logo-og.jpg')))

@push('meta_tags')
{{-- Additional Product Meta Tags --}}
<meta property="product:price:amount" content="{{ $car->offer_price ?: $car->price }}">
<meta property="product:price:currency" content="SAR">
<meta property="product:availability" content="{{ $car->is_sold ? 'out of stock' : 'in stock' }}">
<meta property="product:condition" content="{{ $car->condition === 'new' ? 'new' : 'used' }}">
<meta property="product:brand" content="{{ $car->brand->name }}">
<meta property="product:category" content="سيارات">

{{-- Schema.org Structured Data --}}
<script type="application/ld+json">
{
  "@context": "https://schema.org/",
  "@type": "Product",
  "name": "{{ $car->title }}",
  "image": [
    @if($car->getMedia('car_images')->count() > 0)
      @foreach($car->getMedia('car_images') as $index => $image)
        "{{ $image->getUrl('large') }}"{{ !$loop->last ? ',' : '' }}
      @endforeach
    @else
      "{{ asset('images/logo-og.jpg') }}"
    @endif
  ],
  "description": "{{ strip_tags($car->description ?? $car->title) }}",
  "brand": {
    "@type": "Brand",
    "name": "{{ $car->brand->name }}"
  },
  "model": "{{ $car->carModel->name }}",
  "vehicleModelDate": "{{ $car->manufacturingYear->year }}",
  "offers": {
    "@type": "Offer",
    "url": "{{ url()->current() }}",
    "priceCurrency": "SAR",
    "price": "{{ $car->offer_price ?: $car->price }}",
    "availability": "{{ $car->is_sold ? 'https://schema.org/OutOfStock' : 'https://schema.org/InStock' }}",
    "itemCondition": "{{ $car->condition === 'new' ? 'https://schema.org/NewCondition' : 'https://schema.org/UsedCondition' }}"
  },
  "manufacturer": {
    "@type": "Organization",
    "name": "{{ $car->brand->name }}"
  }
}
</script>
@endpush

@push('styles')
<style>
/* أنماط خاصة بصفحة تفاصيل السيارة */
.car-detail-page {
    padding: 2rem 0;
}

/* معرض الصور */
.car-gallery {
    position: relative;
}

.main-car-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.main-car-image:hover {
    transform: scale(1.02);
}

.car-thumbnails {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding: 0.5rem 0;
}

.thumbnail-item {
    flex-shrink: 0;
    width: 80px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.thumbnail-item.active {
    border-color: var(--primary-color);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.zoom-icon {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.3s ease;
}

.zoom-icon:hover {
    background: rgba(0,0,0,0.9);
}

/* معلومات الشراء */
.purchase-info {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: sticky;
    top: 2rem;
}

.car-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.car-subtitle {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

.price-section {
    margin-bottom: 2rem;
}

.current-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--success-color);
}

.original-price {
    font-size: 1.2rem;
    color: var(--text-muted);
    text-decoration: line-through;
    margin-right: 0.5rem;
}

.offer-badge {
    background: var(--accent-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-top: 0.5rem;
    display: inline-block;
}

.action-buttons {
    margin-bottom: 2rem;
}

.btn-purchase {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    width: 100%;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.btn-purchase:hover {
    background: #e85d04;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(249, 115, 22, 0.3);
}

.how-to-buy-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: block;
    text-align: center;
    margin-bottom: 1.5rem;
}

.how-to-buy-link:hover {
    text-decoration: underline;
}

.contact-section h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.contact-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.btn-contact {
    flex: 1;
    padding: 0.75rem;
    border-radius: 8px;
    text-decoration: none;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-phone {
    background: var(--primary-color);
    color: white;
}

.btn-whatsapp {
    background: #25D366;
    color: white;
}

.btn-contact:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.additional-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.action-icon {
    background: var(--light-bg);
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    border-radius: 8px;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
}

.action-icon:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* مسار التنقل */
.breadcrumb-section {
    background: var(--light-bg);
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--text-muted);
}
</style>
@endpush

@section('content')
<div class="car-detail-page">
    {{-- مسار التنقل (Breadcrumbs) --}}
    <div class="breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('site.home') }}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('site.cars.index') }}">السيارات الجديدة</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('site.cars.index', ['brand_ids' => [$car->brand_id]]) }}">
                            {{ $car->brand->name }}
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('site.cars.index', ['brand_ids' => [$car->brand_id], 'model_ids' => [$car->car_model_id]]) }}">
                            {{ $car->carModel->name }}
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        {{ $car->title }}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="container">
        <div class="row">
            {{-- الجزء الأيمن: معرض الصور والفيديو --}}
            <div class="col-lg-8">
                <div class="car-gallery">
                    {{-- الصورة الرئيسية --}}
                    @php
                        $mainImage = $car->getFirstMedia('car_main_image') ?: $car->getFirstMedia('car_images');
                        $allImages = $car->getMedia('car_images');
                        if ($mainImage && !$allImages->contains('id', $mainImage->id)) {
                            $allImages = $allImages->prepend($mainImage);
                        }
                    @endphp

                    <div class="main-image-container">
                        <img
                            src="{{ $mainImage ? $mainImage->getUrl('large') : asset('images/no-car-image.jpg') }}"
                            alt="{{ $car->title }}"
                            class="main-car-image"
                            id="mainCarImage"
                        >

                        {{-- أيقونة التكبير --}}
                        <div class="zoom-icon" onclick="openImageModal()">
                            <i class="fas fa-search-plus"></i>
                        </div>
                    </div>

                    {{-- الصور المصغرة --}}
                    @if($allImages->count() > 1)
                    <div class="car-thumbnails">
                        @foreach($allImages as $index => $image)
                        <div class="thumbnail-item {{ $index === 0 ? 'active' : '' }}"
                             onclick="changeMainImage('{{ $image->getUrl('large') }}', this)">
                            <img src="{{ $image->getUrl('medium') }}" alt="صورة {{ $index + 1 }}">
                        </div>
                        @endforeach

                        {{-- فيديو إذا كان متوفراً --}}
                        @if($car->video_url)
                        <div class="thumbnail-item video-thumbnail" onclick="openVideoModal()">
                            <div class="d-flex align-items-center justify-content-center h-100 bg-dark text-white">
                                <i class="fas fa-play-circle fa-2x"></i>
                            </div>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>

            {{-- الجزء الأيسر: معلومات الشراء الأساسية --}}
            <div class="col-lg-4">
                <div class="purchase-info">
                    {{-- عنوان السيارة --}}
                    <h1 class="car-title">{{ $car->title }}</h1>
                    <p class="car-subtitle">
                        {{ $car->brand->name }} - {{ $car->carModel->name }} - {{ $car->manufacturingYear->year }}
                    </p>

                    {{-- قسم السعر --}}
                    <div class="price-section">
                        @if($car->offer_price && $car->offer_start_date <= now() && $car->offer_end_date >= now())
                            <div class="d-flex align-items-center">
                                <span class="original-price">{{ format_currency($car->price) }}</span>
                                <span class="current-price">{{ format_currency($car->offer_price) }}</span>
                            </div>
                            <div class="offer-badge">
                                <i class="fas fa-tag me-1"></i>
                                وفر {{ format_currency($car->price - $car->offer_price) }}
                            </div>
                        @else
                            <div class="current-price">{{ format_currency($car->price) }}</div>
                        @endif
                        <small class="text-muted">السعر شامل الضريبة</small>
                    </div>

                    {{-- أزرار الإجراءات الرئيسية --}}
                    <div class="action-buttons">
                        <button class="btn btn-purchase" onclick="openPurchaseModal()">
                            <i class="fas fa-shopping-cart me-2"></i>
                            اطلبها الآن
                        </button>

                        <a href="#" class="how-to-buy-link" onclick="openHowToBuyModal()">
                            <i class="fas fa-question-circle me-1"></i>
                            كيف أشتريها أونلاين؟
                        </a>
                    </div>

                    {{-- قسم المساعدة --}}
                    <div class="contact-section">
                        <h6>هل تحتاج مساعدة؟</h6>
                        <div class="contact-buttons">
                            <a href="tel:{{ setting('main_phone', '+966501234567') }}" class="btn-contact btn-phone">
                                <i class="fas fa-phone me-1"></i>
                                اتصل بنا
                            </a>
                            <a href="https://wa.me/{{ str_replace(['+', ' ', '-'], '', setting('whatsapp_number', setting('main_phone', '+966501234567'))) }}"
                               target="_blank" class="btn-contact btn-whatsapp">
                                <i class="fab fa-whatsapp me-1"></i>
                                واتساب
                            </a>
                        </div>
                    </div>

                    {{-- أزرار الإجراءات الإضافية --}}
                    <div class="additional-actions">
                        <a href="#" class="action-icon" title="أضف للمفضلة" onclick="toggleFavorite({{ $car->id }})">
                            <i class="fas fa-heart"></i>
                        </a>
                        <a href="#" class="action-icon" title="شارك الإعلان" onclick="shareProduct()">
                            <i class="fas fa-share-alt"></i>
                        </a>
                        <a href="#" class="action-icon" title="أضف للمقارنة" onclick="addToCompare({{ $car->id }})">
                            <i class="fas fa-balance-scale"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- أقسام المحتوى السفلية --}}
    <div class="container mt-4">
        {{-- نبذة عن السيارة --}}
        @if($car->description)
        <div class="content-section">
            <h2 class="section-title">
                <i class="fas fa-info-circle me-2"></i>
                نبذة عن {{ $car->title }}
            </h2>
            <div class="description-content">
                {!! $car->description !!}
            </div>
        </div>
        @endif

        {{-- المواصفات الفنية --}}
        <div class="content-section">
            <h2 class="section-title">
                <i class="fas fa-cogs me-2"></i>
                مواصفات السيارة
            </h2>
            <div class="specifications-grid">
                {{-- المحرك والأداء --}}
                @if($car->engine_capacity || $car->transmissionType || $car->fuelType)
                <div class="spec-item">
                    <div class="spec-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="spec-content">
                        <h6>المحرك والأداء</h6>
                        @if($car->engine_capacity)
                            <p>سعة المحرك: {{ format_number($car->engine_capacity) }} سي سي</p>
                        @endif
                        @if($car->transmissionType)
                            <p>ناقل الحركة: {{ $car->transmissionType->name }}</p>
                        @endif
                        @if($car->fuelType)
                            <p>نوع الوقود: {{ $car->fuelType->name }}</p>
                        @endif
                    </div>
                </div>
                @endif

                {{-- الأبعاد والتصميم --}}
                @if($car->doors_count || $car->seats_count || $car->bodyType)
                <div class="spec-item">
                    <div class="spec-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <div class="spec-content">
                        <h6>الأبعاد والتصميم</h6>
                        @if($car->doors_count)
                            <p>عدد الأبواب: {{ $car->doors_count }}</p>
                        @endif
                        @if($car->seats_count)
                            <p>عدد المقاعد: {{ $car->seats_count }}</p>
                        @endif
                        @if($car->bodyType)
                            <p>نوع الهيكل: {{ $car->bodyType->name }}</p>
                        @endif
                    </div>
                </div>
                @endif

                {{-- معلومات عامة --}}
                <div class="spec-item">
                    <div class="spec-icon">
                        <i class="fas fa-info"></i>
                    </div>
                    <div class="spec-content">
                        <h6>معلومات عامة</h6>
                        <p>الماركة: {{ $car->brand->name }}</p>
                        <p>الموديل: {{ $car->carModel->name }}</p>
                        <p>سنة الصنع: {{ $car->manufacturingYear->year }}</p>
                    </div>
                </div>

                {{-- الألوان --}}
                <div class="spec-item">
                    <div class="spec-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="spec-content">
                        <h6>الألوان</h6>
                        <p>اللون الخارجي: {{ $car->mainColor->name }}</p>
                        @if($car->interiorColor)
                            <p>لون المقصورة: {{ $car->interiorColor->name }}</p>
                        @endif
                    </div>
                </div>

                {{-- معلومات إضافية --}}
                @if($car->vin || $car->mileage)
                <div class="spec-item">
                    <div class="spec-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="spec-content">
                        <h6>معلومات إضافية</h6>
                        @if($car->vin)
                            <p>رقم الهيكل: {{ $car->vin }}</p>
                        @endif
                        @if($car->mileage)
                            <p>المسافة المقطوعة: {{ format_number($car->mileage) }} {{ $car->mileage_unit ?? 'كم' }}</p>
                        @endif
                        <p>الحالة: {{ $car->condition === 'new' ? 'جديدة' : 'مستعملة' }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        {{-- الميزات والكماليات --}}
        @if($car->features->count() > 0)
        <div class="content-section">
            <h2 class="section-title">
                <i class="fas fa-star me-2"></i>
                الميزات والكماليات
            </h2>
            <div class="features-grid">
                @php
                    $groupedFeatures = $car->features->groupBy('category.name');
                @endphp
                @foreach($groupedFeatures as $categoryName => $features)
                <div class="feature-category">
                    <h6>
                        <i class="fas fa-check-circle me-2"></i>
                        {{ $categoryName }}
                    </h6>
                    <ul class="feature-list">
                        @foreach($features as $feature)
                        <li class="feature-item">
                            <i class="fas fa-check feature-check"></i>
                            <span>{{ $feature->name }}</span>
                        </li>
                        @endforeach
                    </ul>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        {{-- خدمات ما بعد البيع --}}
        <div class="after-sales-service">
            <h4>
                <i class="fas fa-tools me-2"></i>
                خدمات ما بعد البيع المجانية بقيمة 500 ريال
            </h4>
            <div class="service-value">
                <i class="fas fa-gift me-2"></i>
                قيمة الخدمات: 500 ر.س مجاناً
            </div>
            <p>{{ $afterSalesServiceText }}</p>
            <div class="row mt-3">
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check me-2"></i> توصيل مجاني</li>
                        <li><i class="fas fa-check me-2"></i> غسيل مجاني أول شهر</li>
                        <li><i class="fas fa-check me-2"></i> فحص دوري مجاني</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check me-2"></i> صيانة أولية مجانية</li>
                        <li><i class="fas fa-check me-2"></i> استشارة فنية مجانية</li>
                        <li><i class="fas fa-check me-2"></i> ضمان شامل</li>
                    </ul>
                </div>
            </div>
        </div>

        {{-- زر تحميل المواصفات --}}
        <div class="text-center mb-4">
            <a href="#" class="btn btn-outline-primary btn-lg" onclick="downloadSpecifications()">
                <i class="fas fa-download me-2"></i>
                تحميل مواصفات السيارة (PDF)
            </a>
        </div>

        {{-- السيارات المشابهة --}}
        @if($similarCars->count() > 0)
        <div class="content-section">
            <h2 class="section-title">
                <i class="fas fa-cars me-2"></i>
                سيارات مشابهة قد تعجبك
            </h2>
            <div class="similar-cars-grid">
                @foreach($similarCars as $similarCar)
                <a href="{{ route('site.cars.show', $similarCar->id) }}" class="similar-car-card">
                    @if($similarCar->getFirstMediaUrl('car_images') || $similarCar->getFirstMediaUrl('car_main_image'))
                        <img src="{{ $similarCar->getFirstMediaUrl('car_main_image') ?: $similarCar->getFirstMediaUrl('car_images') }}"
                             alt="{{ $similarCar->title }}" class="similar-car-image">
                    @else
                        <div class="similar-car-image d-flex align-items-center justify-content-center bg-light">
                            <i class="fas fa-car fa-3x text-muted"></i>
                        </div>
                    @endif
                    <div class="similar-car-info">
                        <h6 class="similar-car-title">{{ $similarCar->title }}</h6>
                        <p class="text-muted small mb-2">
                            {{ $similarCar->brand->name }} {{ $similarCar->carModel->name }} • {{ $similarCar->manufacturingYear->year }}
                        </p>
                        <div class="similar-car-price">{{ format_currency($similarCar->price) }}</div>
                    </div>
                </a>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>

@endsection

@push('styles')
<style>
/* أنماط إضافية لصفحة تفاصيل السيارة */

/* أقسام المحتوى */
.content-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

/* المواصفات */
.specifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.spec-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--light-bg);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.spec-icon {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-left: 1rem;
}

.spec-content h6 {
    margin: 0;
    font-weight: 600;
    color: var(--text-color);
}

.spec-content p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* الميزات */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-category {
    background: var(--light-bg);
    border-radius: 8px;
    padding: 1.5rem;
}

.feature-category h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.feature-item:last-child {
    border-bottom: none;
}

.feature-check {
    color: var(--success-color);
    margin-left: 0.75rem;
    font-size: 1.1rem;
}

/* خدمات ما بعد البيع */
.after-sales-service {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.after-sales-service h4 {
    color: white;
    margin-bottom: 1rem;
}

.service-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

/* السيارات المشابهة */
.similar-cars-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.similar-car-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.similar-car-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.similar-car-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.similar-car-info {
    padding: 1.5rem;
}

.similar-car-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.similar-car-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--success-color);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .car-detail-page {
        padding: 1rem 0;
    }

    .purchase-info {
        position: static;
        margin-top: 2rem;
    }

    .contact-buttons {
        flex-direction: column;
    }

    .additional-actions {
        justify-content: center;
        gap: 1rem;
    }

    .specifications-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .similar-cars-grid {
        grid-template-columns: 1fr;
    }
}

/* Modal للصور */
.image-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
}

.modal-content {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 700px;
    max-height: 80%;
    object-fit: contain;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover {
    color: #bbb;
}
</style>
@endpush

@push('scripts')
<script>
// تغيير الصورة الرئيسية
function changeMainImage(imageUrl, thumbnailElement) {
    document.getElementById('mainCarImage').src = imageUrl;

    // إزالة الكلاس النشط من جميع الصور المصغرة
    document.querySelectorAll('.thumbnail-item').forEach(item => {
        item.classList.remove('active');
    });

    // إضافة الكلاس النشط للصورة المحددة
    thumbnailElement.classList.add('active');
}

// فتح modal للصور
function openImageModal() {
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    const mainImg = document.getElementById('mainCarImage');

    modal.style.display = 'block';
    modalImg.src = mainImg.src;
}

// إغلاق modal للصور
function closeImageModal() {
    document.getElementById('imageModal').style.display = 'none';
}

// فتح modal للفيديو
function openVideoModal() {
    // يمكن تنفيذ هذا لاحقاً
    alert('سيتم فتح الفيديو قريباً');
}

// إضافة للمفضلة
function toggleFavorite(carId) {
    // يمكن تنفيذ هذا لاحقاً مع AJAX
    alert('تم إضافة السيارة للمفضلة');
}

// مشاركة المنتج
function shareProduct() {
    if (navigator.share) {
        navigator.share({
            title: document.title,
            url: window.location.href
        });
    } else {
        // نسخ الرابط للحافظة
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('تم نسخ رابط السيارة');
        });
    }
}

// إضافة للمقارنة
function addToCompare(carId) {
    // يمكن تنفيذ هذا لاحقاً
    alert('تم إضافة السيارة للمقارنة');
}

// فتح modal الشراء
function openPurchaseModal() {
    // يمكن تنفيذ هذا لاحقاً
    alert('سيتم فتح نموذج الطلب قريباً');
}

// فتح modal كيفية الشراء
function openHowToBuyModal() {
    // يمكن تنفيذ هذا لاحقاً
    alert('سيتم عرض دليل الشراء قريباً');
}

// تحميل المواصفات
function downloadSpecifications() {
    // يمكن تنفيذ هذا لاحقاً
    alert('سيتم تحميل ملف المواصفات قريباً');
}

// إغلاق modal عند النقر خارجها
window.onclick = function(event) {
    const modal = document.getElementById('imageModal');
    if (event.target === modal) {
        closeImageModal();
    }
}

// إغلاق modal بمفتاح Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeImageModal();
    }
});
</script>
@endpush

{{-- Modal للصور --}}
<div id="imageModal" class="image-modal">
    <span class="close-modal" onclick="closeImageModal()">&times;</span>
    <img class="modal-content" id="modalImage">
</div>
