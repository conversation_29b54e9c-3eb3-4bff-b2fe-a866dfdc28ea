<?php

namespace Modules\CarCatalog\Http\Controllers\Admin;

use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Modules\CarCatalog\Http\Requests\Admin\StoreCarModelRequest;
use Modules\CarCatalog\Http\Requests\Admin\UpdateCarModelRequest;
use Modules\CarCatalog\Models\Brand;
use Modules\CarCatalog\Models\CarModel;
use Modules\Core\Http\Controllers\BaseController;

/**
 * وحدة تحكم إدارة موديلات السيارات.
 *
 * تتعامل هذه الوحدة مع عمليات CRUD لموديلات السيارات في لوحة تحكم الإدارة
 */
class CarModelController extends BaseController
{
    /**
     * عرض قائمة موديلات السيارات.
     *
     * @param Request $request طلب HTTP
     *
     * @return View
     */
    public function index(Request $request): View
    {
        $query = CarModel::with('brand')->withCount('cars');

        // تطبيق فلتر البحث بالاسم إذا تم تقديمه
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->input('search') . '%');
        }

        // تطبيق فلتر الماركة إذا تم تقديمه
        if ($request->filled('brand_id')) {
            $query->where('brand_id', $request->input('brand_id'));
        }

        // تطبيق فلتر الحالة إذا تم تقديمه
        if ($request->has('status') && $request->input('status') !== '') {
            $query->where('status', $request->input('status'));
        }

        // ترتيب النتائج
        $query->orderBy('brand_id')->orderBy('name');

        // الحصول على الموديلات مع الترقيم
        $models = $query->paginate(15)->withQueryString();

        // الحصول على قائمة الماركات للفلترة
        $brands = Brand::where('status', 1)->orderBy('name')->pluck('name', 'id');

        return view('car_catalog::admin.models.index', compact('models', 'brands'));
    }

    /**
     * عرض نموذج إضافة موديل سيارة جديد.
     *
     * @return View
     */
    public function create(): View
    {
        // الحصول على الماركات النشطة فقط
        $brands = Brand::where('status', 1)->orderBy('name')->pluck('name', 'id');

        return view('car_catalog::admin.models.create', compact('brands'));
    }

    /**
     * تخزين موديل سيارة جديد.
     *
     * @param StoreCarModelRequest $request طلب تخزين موديل
     *
     * @return RedirectResponse
     */
    public function store(StoreCarModelRequest $request): RedirectResponse
    {
        // إنشاء الموديل باستخدام البيانات المتحقق منها
        CarModel::create($request->validated());

        return redirect()->route('admin.models.index')->with('success', 'تمت إضافة الموديل بنجاح.');
    }

    /**
     * عرض نموذج تعديل موديل سيارة موجود.
     *
     * @param CarModel $model الموديل المراد تعديله
     *
     * @return View
     */
    public function edit(CarModel $model): View
    {
        // الحصول على الماركات النشطة فقط
        $brands = Brand::where('status', 1)->orderBy('name')->pluck('name', 'id');

        return view('car_catalog::admin.models.edit', compact('model', 'brands'));
    }

    /**
     * تحديث موديل سيارة موجود.
     *
     * @param UpdateCarModelRequest $request طلب تحديث موديل
     * @param CarModel $model الموديل المراد تحديثه
     *
     * @return RedirectResponse
     */
    public function update(UpdateCarModelRequest $request, CarModel $model): RedirectResponse
    {
        // تحديث الموديل باستخدام البيانات المتحقق منها
        $model->update($request->validated());

        return redirect()->route('admin.models.index')->with('success', 'تم تحديث الموديل بنجاح.');
    }

    /**
     * حذف موديل سيارة.
     *
     * @param CarModel $model الموديل المراد حذفه
     *
     * @return RedirectResponse
     */
    public function destroy(CarModel $model): RedirectResponse
    {
        // التحقق من عدم وجود سيارات مرتبطة بالموديل
        if ($model->cars()->exists()) {
            return back()->with('error', 'لا يمكن حذف الموديل لوجود سيارات مرتبطة به. يمكنك تعطيله بدلاً من ذلك.');
        }

        // حذف الموديل (حذف ناعم)
        $model->delete();

        return redirect()->route('admin.models.index')->with('success', 'تم حذف الموديل بنجاح.');
    }
}
