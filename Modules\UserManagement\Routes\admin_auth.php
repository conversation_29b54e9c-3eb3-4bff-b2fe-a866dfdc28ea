<?php

use Illuminate\Support\Facades\Route;
use Modules\UserManagement\Http\Controllers\Admin\AuthenticatedSessionController;
use Modules\UserManagement\Http\Controllers\Admin\NewPasswordController;
use Modules\UserManagement\Http\Controllers\Admin\PasswordResetLinkController;

/*
|--------------------------------------------------------------------------
| مسارات مصادقة لوحة التحكم الإدارية
|--------------------------------------------------------------------------
|
| هنا يتم تعريف مسارات المصادقة الخاصة بلوحة التحكم الإدارية
| مثل تسجيل الدخول، تسجيل الخروج، إعادة تعيين كلمة المرور
|
*/

Route::prefix(config('modules.AppConfig.admin_prefix', 'admin').'/auth')->name('admin.')->group(function () {
    // مسارات تسجيل الدخول
    Route::get('login', [AuthenticatedSessionController::class, 'create'])
        ->middleware('guest:web')
        ->name('login');

    Route::post('login', [AuthenticatedSessionController::class, 'store'])
        ->middleware('guest:web')
        ->name('login.store');

    // مسار تسجيل الخروج
    Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
        ->middleware('auth:web')
        ->name('logout');

    // مسارات إعادة تعيين كلمة المرور
    Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])
        ->middleware('guest:web')
        ->name('password.request');

    Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])
        ->middleware('guest:web')
        ->name('password.email');

    Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])
        ->middleware('guest:web')
        ->name('password.reset');

    Route::post('reset-password', [NewPasswordController::class, 'store'])
        ->middleware('guest:web')
        ->name('password.update');
});
