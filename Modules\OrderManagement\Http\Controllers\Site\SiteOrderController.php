<?php

namespace Modules\OrderManagement\Http\Controllers\Site;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Modules\CarCatalog\Models\Car;
use Modules\OrderManagement\Models\Order;
use Modules\OrderManagement\Http\Requests\CashOrderStep1Request;
use Modules\OrderManagement\Http\Requests\CashOrderStep2Request;
use Modules\OrderManagement\Http\Requests\CashOrderStep3Request;
use Modules\OrderManagement\Http\Requests\CashOrderStep4Request;
use Modules\OrderManagement\Http\Requests\FinanceOrderStep1Request;
use Modules\OrderManagement\Http\Requests\FinanceOrderStep2Request;
use Modules\OrderManagement\Http\Requests\FinanceOrderStep3Request;
use Modules\OrderManagement\Http\Requests\FinanceOrderStep4Request;
use Modules\OrderManagement\Http\Requests\Site\FinanceOrderFinalRequest;
use Modules\OrderManagement\Services\OrderProcessingService;
use Modules\OrderManagement\Services\DocumentUploadService;
use Modules\OrderManagement\Services\PaymentGatewayService;
use Modules\OrderManagement\Notifications\OrderCreatedNotification;
use Modules\OrderManagement\Notifications\NewOrderAdminNotification;
use App\Models\User;
use Exception;

/**
 * Site Order Controller - يدير عملية شراء السيارات (كاش وتمويل) في الموقع العام
 *
 * يخدم هذا Controller عمليات شراء السيارة متعددة المراحل في الموقع العام
 * بناءً على UIUX-FR.md و REQ-FR.md
 *
 * عملية الشراء كاش (SITE-BUY-CASH-STEPX-001) - MOD-ORDER-MGMT-FEAT-003:
 * 1. البيانات الشخصية
 * 2. تفاصيل الحجز والدفع
 * 3. رفع المستندات
 * 4. المراجعة والتأكيد
 *
 * عملية طلب التمويل (SITE-BUY-FINANCE-STEPX-001) - MOD-ORDER-MGMT-FEAT-004:
 * 1. البيانات الشخصية
 * 2. معلومات التمويل
 * 3. رفع المستندات
 * 4. المراجعة والتأكيد
 */
class SiteOrderController extends Controller
{
    /**
     * إنشاء instance جديد من Controller
     * تطبيق middleware auth للتأكد من تسجيل دخول المستخدم
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * الخطوة 1: عرض صفحة البيانات الشخصية
     *
     * @param Request $request
     * @param int $carId معرف السيارة المختارة
     * @return View
     */
    public function showCashOrderStep1PersonalDetails(Request $request, $carId): View
    {
        // جلب تفاصيل السيارة مع العلاقات المطلوبة
        $car = Car::with([
            'brand',
            'carModel',
            'manufacturingYear',
            'mainColor',
            'media'
        ])
        ->where('is_active', true)
        ->where('is_sold', false)
        ->findOrFail($carId);

        // جلب بيانات المستخدم الحالي
        $user = Auth::user();

        // تحضير بيانات المستخدم للملء التلقائي
        $userData = [
            'full_name' => $user->first_name . ' ' . $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'national_id' => $user->national_id ?? '',
            'date_of_birth' => $user->date_of_birth ?? '',
            'nationality_id' => $user->nationality_id ?? '',
            'address' => $user->address ?? '',
            'city' => $user->city ?? '',
        ];

        // حفظ معرف السيارة في الجلسة للخطوات التالية
        Session::put('cash_order_car_id', $carId);
        Session::put('cash_order_step', 1);

        return view('ordermanagement::site.buy_cash.step1_personal', compact('car', 'userData'));
    }

    /**
     * الخطوة 2: عرض صفحة تفاصيل الحجز والدفع
     *
     * @param Request $request
     * @return View
     */
    public function showCashOrderStep2BookingPayment(Request $request): View
    {
        // التحقق من وجود معرف السيارة في الجلسة
        $carId = Session::get('cash_order_car_id');
        if (!$carId) {
            return redirect()->route('site.cars.index')
                ->with('error', 'يرجى اختيار سيارة أولاً');
        }

        // جلب تفاصيل السيارة
        $car = Car::with([
            'brand',
            'carModel',
            'manufacturingYear',
            'mainColor',
            'media'
        ])->findOrFail($carId);

        // حساب مبلغ الحجز (افتراضياً 10% من سعر السيارة أو حد أدنى)
        $reservationAmount = max($car->price * 0.10, 5000); // 10% أو 5000 ريال كحد أدنى
        $remainingAmount = $car->price - $reservationAmount;

        // جلب بيانات المستخدم
        $user = Auth::user();

        // تحديث خطوة الجلسة
        Session::put('cash_order_step', 2);
        Session::put('cash_order_reservation_amount', $reservationAmount);

        return view('ordermanagement::site.buy_cash.step2_booking_payment', compact(
            'car',
            'user',
            'reservationAmount',
            'remainingAmount'
        ));
    }

    /**
     * الخطوة 3: عرض صفحة رفع المستندات
     *
     * @param Request $request
     * @return View
     */
    public function showCashOrderStep3Documents(Request $request): View
    {
        // التحقق من وجود معرف السيارة في الجلسة
        $carId = Session::get('cash_order_car_id');
        if (!$carId) {
            return redirect()->route('site.cars.index')
                ->with('error', 'يرجى اختيار سيارة أولاً');
        }

        // جلب تفاصيل السيارة
        $car = Car::with([
            'brand',
            'carModel',
            'manufacturingYear',
            'mainColor',
            'media'
        ])->findOrFail($carId);

        // جلب بيانات المستخدم
        $user = Auth::user();

        // جلب مبلغ الحجز من الجلسة
        $reservationAmount = Session::get('cash_order_reservation_amount', 0);

        // تحديث خطوة الجلسة
        Session::put('cash_order_step', 3);

        return view('ordermanagement::site.buy_cash.step3_documents', compact(
            'car',
            'user',
            'reservationAmount'
        ));
    }

    /**
     * الخطوة 4: عرض صفحة المراجعة والتأكيد
     *
     * @param Request $request
     * @return View
     */
    public function showCashOrderStep4ReviewConfirm(Request $request): View
    {
        // التحقق من وجود معرف السيارة في الجلسة
        $carId = Session::get('cash_order_car_id');
        if (!$carId) {
            return redirect()->route('site.cars.index')
                ->with('error', 'يرجى اختيار سيارة أولاً');
        }

        // جلب تفاصيل السيارة
        $car = Car::with([
            'brand',
            'carModel',
            'manufacturingYear',
            'mainColor',
            'bodyType',
            'transmissionType',
            'fuelType',
            'media'
        ])->findOrFail($carId);

        // جلب بيانات المستخدم
        $user = Auth::user();

        // جلب البيانات المحفوظة في الجلسة من الخطوات السابقة
        $personalData = Session::get('cash_order_personal_data', []);
        $paymentData = Session::get('cash_order_payment_data', []);
        $documentsData = Session::get('cash_order_documents_data', []);
        $reservationAmount = Session::get('cash_order_reservation_amount', 0);

        // حساب المبلغ المتبقي
        $remainingAmount = $car->price - $reservationAmount;

        // تحديث خطوة الجلسة
        Session::put('cash_order_step', 4);

        return view('ordermanagement::site.buy_cash.step4_review_confirm', compact(
            'car',
            'user',
            'personalData',
            'paymentData',
            'documentsData',
            'reservationAmount',
            'remainingAmount'
        ));
    }

    // ==========================================
    // دوال عملية طلب التمويل (FINANCE ORDER)
    // ==========================================

    /**
     * عرض الخطوة الأولى لطلب التمويل - البيانات الشخصية
     *
     * @param Request $request
     * @param int $carId
     * @return View
     */
    public function showFinanceOrderStep1PersonalDetails(Request $request, int $carId): View
    {
        // التحقق من وجود السيارة وحالتها
        $car = Car::with([
            'brand',
            'carModel',
            'manufacturingYear',
            'mainColor',
            'bodyType',
            'transmissionType',
            'fuelType',
            'media'
        ])->where('is_active', true)
        ->where('is_sold', false)
        ->findOrFail($carId);

        // جلب بيانات المستخدم الحالي
        $user = Auth::user();

        // تحضير بيانات المستخدم للملء التلقائي
        $userData = [
            'full_name' => $user->first_name . ' ' . $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'national_id' => $user->national_id ?? '',
            'date_of_birth' => $user->date_of_birth ?? '',
            'nationality_id' => $user->nationality_id ?? '',
            'address' => $user->address ?? '',
            'city' => $user->city ?? '',
        ];

        // حفظ معرف السيارة في الجلسة للخطوات التالية
        Session::put('finance_order_car_id', $carId);
        Session::put('finance_order_step', 1);

        return view('ordermanagement::site.buy_finance.step1_personal', compact('car', 'userData'));
    }

    /**
     * عرض الخطوة الثانية لطلب التمويل - معلومات التمويل
     *
     * @return View
     */
    public function showFinanceOrderStep2FinanceInfo(): View
    {
        // التحقق من وجود السيارة في الجلسة
        if (!Session::has('finance_order_car_id')) {
            return redirect()->route('site.cars.index')
                            ->with('error', 'يجب اختيار السيارة أولاً');
        }

        // جلب السيارة
        $car = Car::with([
            'brand',
            'carModel',
            'manufacturingYear',
            'mainColor',
            'bodyType',
            'transmissionType',
            'fuelType',
            'media'
        ])->findOrFail(Session::get('finance_order_car_id'));

        // جلب البيانات المحفوظة من الخطوة السابقة
        $personalData = Session::get('finance_order_personal_data', []);

        // تحديث خطوة الجلسة
        Session::put('finance_order_step', 2);

        return view('ordermanagement::site.buy_finance.step2_finance_info', compact('car', 'personalData'));
    }

    /**
     * عرض الخطوة الثالثة لطلب التمويل - رفع المستندات
     *
     * @return View
     */
    public function showFinanceOrderStep3Documents(): View
    {
        // التحقق من وجود البيانات المطلوبة في الجلسة
        if (!Session::has('finance_order_car_id') || !Session::has('finance_order_personal_data') || !Session::has('finance_order_finance_data')) {
            return redirect()->route('site.cars.index')
                            ->with('error', 'يجب إكمال الخطوات السابقة أولاً');
        }

        // جلب السيارة
        $car = Car::with([
            'brand',
            'carModel',
            'manufacturingYear',
            'mainColor',
            'bodyType',
            'transmissionType',
            'fuelType',
            'media'
        ])->findOrFail(Session::get('finance_order_car_id'));

        // جلب البيانات المحفوظة من الخطوات السابقة
        $personalData = Session::get('finance_order_personal_data', []);
        $financeData = Session::get('finance_order_finance_data', []);

        // تحديث خطوة الجلسة
        Session::put('finance_order_step', 3);

        return view('ordermanagement::site.buy_finance.step3_documents', compact('car', 'personalData', 'financeData'));
    }

    /**
     * عرض الخطوة الرابعة لطلب التمويل - المراجعة والتأكيد
     *
     * @return View
     */
    public function showFinanceOrderStep4ReviewConfirm(): View
    {
        // التحقق من وجود جميع البيانات المطلوبة في الجلسة
        if (!Session::has('finance_order_car_id') ||
            !Session::has('finance_order_personal_data') ||
            !Session::has('finance_order_finance_data') ||
            !Session::has('finance_order_documents_data')) {
            return redirect()->route('site.cars.index')
                            ->with('error', 'يجب إكمال جميع الخطوات السابقة أولاً');
        }

        // جلب السيارة
        $car = Car::with([
            'brand',
            'carModel',
            'manufacturingYear',
            'mainColor',
            'bodyType',
            'transmissionType',
            'fuelType',
            'media'
        ])->findOrFail(Session::get('finance_order_car_id'));

        // جلب بيانات المستخدم
        $user = Auth::user();

        // جلب البيانات المحفوظة في الجلسة من الخطوات السابقة
        $personalData = Session::get('finance_order_personal_data', []);
        $financeData = Session::get('finance_order_finance_data', []);
        $documentsData = Session::get('finance_order_documents_data', []);

        // تحديث خطوة الجلسة
        Session::put('finance_order_step', 4);

        return view('ordermanagement::site.buy_finance.step4_review_confirm', compact(
            'car',
            'user',
            'personalData',
            'financeData',
            'documentsData'
        ));
    }

    /**
     * دالة مساعدة للحصول على ملخص السيارة المختارة
     *
     * @return array|null
     */
    protected function getSelectedCarSummary(): ?array
    {
        $carId = Session::get('cash_order_car_id');
        if (!$carId) {
            return null;
        }

        $car = Car::with(['brand', 'carModel', 'manufacturingYear', 'mainColor', 'media'])
            ->find($carId);

        if (!$car) {
            return null;
        }

        return [
            'id' => $car->id,
            'title' => $car->title,
            'brand_name' => $car->brand->name ?? '',
            'model_name' => $car->carModel->name ?? '',
            'year' => $car->manufacturingYear->year ?? '',
            'color' => $car->mainColor->name ?? '',
            'price' => $car->price,
            'currency' => $car->currency,
            'main_image' => $car->getFirstMediaUrl('car_main_image') ?: $car->getFirstMediaUrl('car_images'),
        ];
    }

    /**
     * دالة مساعدة للتحقق من صحة خطوة معينة
     *
     * @param int $step
     * @return bool
     */
    protected function isStepValid(int $step): bool
    {
        $currentStep = Session::get('cash_order_step', 0);
        $carId = Session::get('cash_order_car_id');

        // التحقق من وجود معرف السيارة
        if (!$carId) {
            return false;
        }

        // التحقق من أن الخطوة المطلوبة لا تتجاوز الخطوة الحالية + 1
        return $step <= ($currentStep + 1);
    }

    /**
     * دالة مساعدة لحساب مبلغ الحجز
     *
     * @param float $carPrice
     * @return float
     */
    protected function calculateReservationAmount(float $carPrice): float
    {
        // حساب 10% من سعر السيارة مع حد أدنى 5000 ريال
        return max($carPrice * 0.10, 5000);
    }

    /**
     * جمع جميع بيانات طلب التمويل من الجلسة
     *
     * يجمع البيانات من جميع خطوات طلب التمويل ويحضرها لإنشاء الطلب
     */
    private function collectFinanceOrderDataFromSession(): ?array
    {
        $carId = session('finance_order_car_id');
        $personalData = session('finance_order_personal_data');
        $financeData = session('finance_order_finance_data');
        $documentsData = session('finance_order_documents_data');
        $confirmationData = session('finance_order_confirmation_data');

        if (!$carId || !$personalData || !$financeData || !$documentsData) {
            return null;
        }

        // حساب المبلغ المطلوب للتمويل
        $car = Car::find($carId);
        $downPaymentAmount = $financeData['down_payment_amount'] ?? 0;
        $requestedAmount = $car ? ($car->price - $downPaymentAmount) : 0;

        return [
            'car_id' => $carId,
            'personal_data' => $personalData,
            'finance_data' => array_merge($financeData, [
                'requested_amount' => $requestedAmount,
                'car_price' => $car ? $car->price : 0
            ]),
            'documents_data' => $documentsData,
            'confirmation_data' => $confirmationData ?? [],
            'order_type' => 'finance_application',
            'user_id' => auth()->id()
        ];
    }

    /**
     * التحقق من صحة بيانات جلسة طلب التمويل
     *
     * يتأكد من وجود جميع البيانات المطلوبة في الجلسة
     */
    private function validateFinanceOrderSessionData(): bool
    {
        $requiredSessionKeys = [
            'finance_order_car_id',
            'finance_order_personal_data',
            'finance_order_finance_data',
            'finance_order_documents_data'
        ];

        foreach ($requiredSessionKeys as $key) {
            if (!session()->has($key) || empty(session($key))) {
                return false;
            }
        }

        // التحقق من صحة البيانات الشخصية
        $personalData = session('finance_order_personal_data');
        $requiredPersonalFields = ['full_name', 'email', 'phone_number', 'national_id'];

        foreach ($requiredPersonalFields as $field) {
            if (empty($personalData[$field])) {
                return false;
            }
        }

        // التحقق من صحة بيانات التمويل
        $financeData = session('finance_order_finance_data');
        $requiredFinanceFields = ['down_payment_amount', 'monthly_income', 'employment_type'];

        foreach ($requiredFinanceFields as $field) {
            if (empty($financeData[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * التحقق من توفر السيارة لطلب التمويل
     */
    private function isCarAvailableForFinancing(Car $car): bool
    {
        return $car->is_active &&
               !$car->is_sold &&
               in_array($car->status, ['available', 'reserved']);
    }

    /**
     * الحصول على ملخص بيانات الجلسة للتسجيل
     */
    private function getSessionDataSummary(): array
    {
        return [
            'has_car_id' => session()->has('finance_order_car_id'),
            'has_personal_data' => session()->has('finance_order_personal_data'),
            'has_finance_data' => session()->has('finance_order_finance_data'),
            'has_documents_data' => session()->has('finance_order_documents_data'),
            'current_step' => session('finance_order_step', 0)
        ];
    }

    /**
     * رفع مستندات طلب التمويل
     *
     * يقوم برفع جميع المستندات المطلوبة لطلب التمويل من الملفات المؤقتة
     * ويربطها بالطلب في قاعدة البيانات
     */
    private function uploadFinanceOrderDocuments(Order $order): void
    {
        try {
            $tempDocuments = session('finance_order_temp_documents', []);
            $documentService = new DocumentUploadService();
            $uploadedCount = 0;

            // رفع المستندات الأساسية المطلوبة
            $basicDocuments = [
                'national_id_front' => 'صورة الهوية الوطنية (الوجه الأمامي)',
                'national_id_back' => 'صورة الهوية الوطنية (الوجه الخلفي)',
                'driving_license' => 'صورة رخصة القيادة'
            ];

            foreach ($basicDocuments as $docType => $description) {
                if (isset($tempDocuments[$docType])) {
                    if ($this->uploadSingleDocument($order, $tempDocuments[$docType], $docType, $description, $documentService)) {
                        $uploadedCount++;
                    }
                }
            }

            // رفع مستندات التمويل الإضافية المطلوبة
            $financeDocuments = [
                'salary_certificate' => 'شهادة راتب',
                'bank_statement' => 'كشف حساب بنكي'
            ];

            foreach ($financeDocuments as $docType => $description) {
                if (isset($tempDocuments[$docType])) {
                    if ($this->uploadSingleDocument($order, $tempDocuments[$docType], $docType, $description, $documentService)) {
                        $uploadedCount++;
                    }
                }
            }

            // رفع المستندات الإضافية (اختيارية)
            if (isset($tempDocuments['additional_documents']) && is_array($tempDocuments['additional_documents'])) {
                $descriptions = session('finance_order_documents_data.additional_documents_descriptions', []);

                foreach ($tempDocuments['additional_documents'] as $index => $tempPath) {
                    $description = $descriptions[$index] ?? "مستند إضافي " . ($index + 1);
                    if ($this->uploadSingleDocument($order, $tempPath, 'additional_documents', $description, $documentService)) {
                        $uploadedCount++;
                    }
                }
            }

            // تنظيف بيانات الملفات المؤقتة من الجلسة
            session()->forget('finance_order_temp_documents');

            Log::info('تم رفع مستندات طلب التمويل بنجاح', [
                'order_id' => $order->id,
                'uploaded_documents_count' => $uploadedCount
            ]);

        } catch (Exception $e) {
            Log::error('خطأ في رفع مستندات طلب التمويل', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // في حالة فشل رفع المستندات، لا نريد إيقاف العملية بالكامل
            // لكن نسجل الخطأ للمراجعة اللاحقة
        }
    }

    /**
     * رفع مستند واحد من الملفات المؤقتة
     */
    private function uploadSingleDocument(Order $order, string $tempPath, string $docType, string $description, DocumentUploadService $documentService): bool
    {
        try {
            $filePath = storage_path('app/' . $tempPath);

            if (!file_exists($filePath)) {
                Log::warning('ملف مؤقت غير موجود', [
                    'order_id' => $order->id,
                    'doc_type' => $docType,
                    'file_path' => $filePath
                ]);
                return false;
            }

            $uploadedFile = new UploadedFile(
                $filePath,
                basename($tempPath),
                mime_content_type($filePath),
                null,
                true
            );

            $documentService->uploadDocument($order, $uploadedFile, $docType, $description);

            // حذف الملف المؤقت بعد الرفع الناجح
            unlink($filePath);

            return true;

        } catch (Exception $e) {
            Log::error('خطأ في رفع مستند واحد', [
                'order_id' => $order->id,
                'doc_type' => $docType,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * دالة مساعدة لتنظيف بيانات جلسة طلب التمويل
     *
     * تقوم بحذف جميع البيانات المتعلقة بطلب التمويل من الجلسة
     * وتنظيف الملفات المؤقتة إذا وجدت
     */
    protected function clearFinanceOrderSession(): void
    {
        try {
            // تنظيف الملفات المؤقتة قبل حذف بيانات الجلسة
            $this->cleanupTemporaryFinanceDocuments();

            // حذف جميع بيانات طلب التمويل من الجلسة
            Session::forget([
                'finance_order_car_id',
                'finance_order_step',
                'finance_order_personal_data',
                'finance_order_finance_data',
                'finance_order_documents_data',
                'finance_order_temp_documents',
                'finance_order_confirmation_data'
            ]);

            Log::info('تم تنظيف جلسة طلب التمويل', [
                'user_id' => auth()->id()
            ]);

        } catch (Exception $e) {
            Log::error('خطأ في تنظيف جلسة طلب التمويل', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * تنظيف الملفات المؤقتة لطلب التمويل
     */
    private function cleanupTemporaryFinanceDocuments(): void
    {
        try {
            $tempDocuments = session('finance_order_temp_documents', []);

            if (empty($tempDocuments)) {
                return;
            }

            $deletedCount = 0;

            // حذف المستندات الأساسية المؤقتة
            $basicDocuments = ['national_id_front', 'national_id_back', 'driving_license', 'salary_certificate', 'bank_statement'];

            foreach ($basicDocuments as $docType) {
                if (isset($tempDocuments[$docType])) {
                    $filePath = storage_path('app/' . $tempDocuments[$docType]);
                    if (file_exists($filePath)) {
                        unlink($filePath);
                        $deletedCount++;
                    }
                }
            }

            // حذف المستندات الإضافية المؤقتة
            if (isset($tempDocuments['additional_documents']) && is_array($tempDocuments['additional_documents'])) {
                foreach ($tempDocuments['additional_documents'] as $tempPath) {
                    $filePath = storage_path('app/' . $tempPath);
                    if (file_exists($filePath)) {
                        unlink($filePath);
                        $deletedCount++;
                    }
                }
            }

            if ($deletedCount > 0) {
                Log::info('تم حذف الملفات المؤقتة لطلب التمويل', [
                    'user_id' => auth()->id(),
                    'deleted_files_count' => $deletedCount
                ]);
            }

        } catch (Exception $e) {
            Log::error('خطأ في تنظيف الملفات المؤقتة لطلب التمويل', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * إلغاء عملية طلب التمويل وتنظيف الجلسة
     *
     * يتم استدعاؤها عندما يقرر المستخدم إلغاء عملية طلب التمويل
     * في أي مرحلة من المراحل
     */
    public function cancelFinanceOrder()
    {
        try {
            $carId = session('finance_order_car_id');
            $currentStep = session('finance_order_step', 0);

            // تسجيل عملية الإلغاء للمراجعة
            Log::info('تم إلغاء عملية طلب التمويل من قبل المستخدم', [
                'user_id' => auth()->id(),
                'car_id' => $carId,
                'cancelled_at_step' => $currentStep
            ]);

            // تنظيف جميع بيانات الجلسة والملفات المؤقتة
            $this->clearFinanceOrderSession();

            // توجيه المستخدم مع رسالة مناسبة
            if ($carId) {
                return redirect()->route('site.cars.show', $carId)
                    ->with('info', 'تم إلغاء عملية طلب التمويل. يمكنك البدء من جديد في أي وقت');
            }

            return redirect()->route('site.cars.index')
                ->with('info', 'تم إلغاء عملية طلب التمويل');

        } catch (Exception $e) {
            Log::error('خطأ في إلغاء عملية طلب التمويل', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->route('site.cars.index')
                ->with('warning', 'تم إلغاء العملية');
        }
    }

    /**
     * دالة مساعدة لتنظيف بيانات الجلسة عند إلغاء العملية
     *
     * @return void
     */
    protected function clearOrderSession(): void
    {
        Session::forget([
            'cash_order_car_id',
            'cash_order_step',
            'cash_order_personal_data',
            'cash_order_payment_data',
            'cash_order_documents_data',
            'cash_order_reservation_amount'
        ]);
    }

    /**
     * معالجة بيانات الخطوة الأولى - البيانات الشخصية
     */
    public function processCashOrderStep1(CashOrderStep1Request $request)
    {
        try {
            // التحقق من وجود السيارة في الجلسة
            if (!session()->has('cash_order_car_id')) {
                return redirect()->route('site.cars.index')
                                ->with('error', 'يجب اختيار السيارة أولاً');
            }

            // حفظ البيانات الشخصية في الجلسة
            $personalData = $request->validated();
            session(['cash_order_personal_data' => $personalData]);
            session(['cash_order_step' => 2]);

            // تحديث بيانات المستخدم إذا لزم الأمر
            $this->updateUserProfileIfNeeded($personalData);

            return redirect()->route('site.order.cash.step2')
                            ->with('success', 'تم حفظ البيانات الشخصية بنجاح');

        } catch (Exception $e) {
            Log::error('خطأ في معالجة الخطوة الأولى', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                        ->with('error', 'حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * معالجة بيانات الخطوة الثانية - الحجز والدفع
     */
    public function processCashOrderStep2(CashOrderStep2Request $request)
    {
        try {
            $paymentData = $request->validated();

            // حساب المبلغ المتبقي
            $carPrice = $paymentData['car_price'];
            $reservationAmount = $paymentData['reservation_amount'];
            $remainingAmount = $carPrice - $reservationAmount;

            // إضافة المبلغ المتبقي للبيانات
            $paymentData['remaining_amount'] = $remainingAmount;

            // حفظ بيانات الدفع في الجلسة
            session(['cash_order_payment_data' => $paymentData]);
            session(['cash_order_reservation_amount' => $reservationAmount]);
            session(['cash_order_step' => 3]);

            return redirect()->route('site.order.cash.step3')
                            ->with('success', 'تم حفظ بيانات الحجز والدفع بنجاح');

        } catch (Exception $e) {
            Log::error('خطأ في معالجة الخطوة الثانية', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                        ->with('error', 'حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * معالجة بيانات الخطوة الثالثة - المستندات
     */
    public function processCashOrderStep3(CashOrderStep3Request $request)
    {
        try {
            // حفظ معلومات المستندات في الجلسة (ليس الملفات نفسها)
            $documentsData = [
                'national_id_front_uploaded' => $request->hasFile('national_id_front'),
                'national_id_back_uploaded' => $request->hasFile('national_id_back'),
                'driving_license_uploaded' => $request->hasFile('driving_license'),
                'additional_documents_count' => $request->hasFile('additional_documents') ?
                    count($request->file('additional_documents')) : 0,
                'documents_authenticity_confirmed' => $request->input('documents_authenticity_confirmed'),
                'data_processing_consent' => $request->input('data_processing_consent'),
                'additional_documents_descriptions' => $request->input('additional_documents_descriptions', [])
            ];

            // حفظ الملفات مؤقتاً في الجلسة (سيتم نقلها عند إنشاء الطلب)
            $tempDocuments = [];

            if ($request->hasFile('national_id_front')) {
                $tempDocuments['national_id_front'] = $this->storeTemporaryFile($request->file('national_id_front'));
            }

            if ($request->hasFile('national_id_back')) {
                $tempDocuments['national_id_back'] = $this->storeTemporaryFile($request->file('national_id_back'));
            }

            if ($request->hasFile('driving_license')) {
                $tempDocuments['driving_license'] = $this->storeTemporaryFile($request->file('driving_license'));
            }

            if ($request->hasFile('additional_documents')) {
                $tempDocuments['additional_documents'] = [];
                foreach ($request->file('additional_documents') as $file) {
                    $tempDocuments['additional_documents'][] = $this->storeTemporaryFile($file);
                }
            }

            session(['cash_order_documents_data' => $documentsData]);
            session(['cash_order_temp_documents' => $tempDocuments]);
            session(['cash_order_step' => 4]);

            return redirect()->route('site.order.cash.step4')
                            ->with('success', 'تم رفع المستندات بنجاح');

        } catch (Exception $e) {
            Log::error('خطأ في معالجة الخطوة الثالثة', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                        ->with('error', 'حدث خطأ في رفع المستندات. يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * معالجة الخطوة الرابعة والتأكيد النهائي
     */
    public function processCashOrderStep4(CashOrderStep4Request $request)
    {
        try {
            // حفظ بيانات التأكيد النهائي
            $confirmationData = $request->validated();
            session(['cash_order_confirmation_data' => $confirmationData]);

            // إنشاء الطلب النهائي
            return $this->storeCashOrder();

        } catch (Exception $e) {
            Log::error('خطأ في معالجة الخطوة الرابعة', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                        ->with('error', 'حدث خطأ في التأكيد النهائي. يرجى المحاولة مرة أخرى');
        }
    }

    // ==========================================
    // دوال معالجة بيانات طلب التمويل
    // ==========================================

    /**
     * معالجة بيانات الخطوة الأولى لطلب التمويل - البيانات الشخصية
     */
    public function processFinanceOrderStep1(FinanceOrderStep1Request $request)
    {
        try {
            // التحقق من وجود السيارة في الجلسة
            if (!session()->has('finance_order_car_id')) {
                return redirect()->route('site.cars.index')
                                ->with('error', 'يجب اختيار السيارة أولاً');
            }

            // حفظ البيانات الشخصية في الجلسة
            $personalData = $request->validated();
            session(['finance_order_personal_data' => $personalData]);
            session(['finance_order_step' => 2]);

            // تحديث بيانات المستخدم إذا لزم الأمر
            $this->updateUserProfileIfNeeded($personalData);

            return redirect()->route('site.order.finance.step2')
                            ->with('success', 'تم حفظ البيانات الشخصية بنجاح');

        } catch (Exception $e) {
            Log::error('خطأ في معالجة الخطوة الأولى لطلب التمويل', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                        ->with('error', 'حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * معالجة بيانات الخطوة الثانية لطلب التمويل - معلومات التمويل
     */
    public function processFinanceOrderStep2(FinanceOrderStep2Request $request)
    {
        try {
            $financeData = $request->validated();

            // الحصول على معرف السيارة من الجلسة
            $carId = session('finance_order_car_id');
            if (!$carId) {
                return redirect()->route('site.cars.index')
                                ->with('error', 'يجب اختيار السيارة أولاً');
            }

            // جلب تفاصيل السيارة لحساب مبلغ التمويل
            $car = Car::findOrFail($carId);

            // حساب تفاصيل التمويل
            $financeCalculations = $this->calculateFinanceDetails($car, $financeData);

            // إضافة الحسابات إلى بيانات التمويل
            $financeData = array_merge($financeData, $financeCalculations);

            // التحقق من أهلية التمويل الأساسية
            $eligibilityCheck = $this->checkBasicFinanceEligibility($financeData);
            if (!$eligibilityCheck['eligible']) {
                return back()->withInput()
                            ->with('error', $eligibilityCheck['message']);
            }

            // حفظ بيانات التمويل في الجلسة
            session(['finance_order_finance_data' => $financeData]);
            session(['finance_order_step' => 3]);

            Log::info('تم حفظ معلومات التمويل للخطوة الثانية', [
                'user_id' => auth()->id(),
                'car_id' => $carId,
                'requested_amount' => $financeCalculations['requested_amount']
            ]);

            return redirect()->route('site.order.finance.step3')
                            ->with('success', 'تم حفظ معلومات التمويل بنجاح');

        } catch (Exception $e) {
            Log::error('خطأ في معالجة الخطوة الثانية لطلب التمويل', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                        ->with('error', 'حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * حساب تفاصيل التمويل
     */
    private function calculateFinanceDetails(Car $car, array $financeData): array
    {
        $carPrice = $car->price;
        $downPaymentAmount = $financeData['down_payment_amount'];
        $requestedAmount = $carPrice - $downPaymentAmount;

        // حساب نسبة الدفعة الأولى
        $downPaymentPercentage = ($downPaymentAmount / $carPrice) * 100;

        // حساب نسبة الدين إلى الدخل (DTI)
        $monthlyIncome = $financeData['monthly_income'];
        $monthlyObligations = $financeData['monthly_obligations'] ?? 0;
        $estimatedMonthlyPayment = $this->estimateMonthlyPayment($requestedAmount);
        $totalMonthlyObligations = $monthlyObligations + $estimatedMonthlyPayment;
        $debtToIncomeRatio = ($totalMonthlyObligations / $monthlyIncome) * 100;

        return [
            'car_price' => $carPrice,
            'requested_amount' => $requestedAmount,
            'down_payment_percentage' => round($downPaymentPercentage, 2),
            'estimated_monthly_payment' => $estimatedMonthlyPayment,
            'debt_to_income_ratio' => round($debtToIncomeRatio, 2),
            'total_monthly_obligations' => $totalMonthlyObligations
        ];
    }

    /**
     * تقدير الدفعة الشهرية (تقدير مبدئي)
     */
    private function estimateMonthlyPayment(float $amount): float
    {
        // تقدير مبدئي بناءً على معدل فائدة افتراضي وفترة سداد
        $interestRate = 0.08; // 8% سنوياً
        $years = 5; // 5 سنوات
        $monthlyRate = $interestRate / 12;
        $numberOfPayments = $years * 12;

        if ($monthlyRate == 0) {
            return $amount / $numberOfPayments;
        }

        return $amount * ($monthlyRate * pow(1 + $monthlyRate, $numberOfPayments)) /
               (pow(1 + $monthlyRate, $numberOfPayments) - 1);
    }

    /**
     * فحص الأهلية الأساسية للتمويل
     */
    private function checkBasicFinanceEligibility(array $financeData): array
    {
        // فحص نسبة الدين إلى الدخل
        if ($financeData['debt_to_income_ratio'] > 50) {
            return [
                'eligible' => false,
                'message' => 'نسبة الالتزامات الشهرية إلى الدخل عالية جداً. يجب ألا تتجاوز 50%'
            ];
        }

        // فحص الحد الأدنى للدخل
        if ($financeData['monthly_income'] < 3000) {
            return [
                'eligible' => false,
                'message' => 'الدخل الشهري أقل من الحد الأدنى المطلوب (3000 ريال)'
            ];
        }

        // فحص الحد الأدنى للدفعة الأولى (10%)
        if ($financeData['down_payment_percentage'] < 10) {
            return [
                'eligible' => false,
                'message' => 'الدفعة الأولى يجب أن تكون 10% على الأقل من قيمة السيارة'
            ];
        }

        return ['eligible' => true, 'message' => ''];
    }

    /**
     * معالجة بيانات الخطوة الثالثة لطلب التمويل - المستندات
     */
    public function processFinanceOrderStep3(FinanceOrderStep3Request $request)
    {
        try {
            // حفظ معلومات المستندات في الجلسة (ليس الملفات نفسها)
            $documentsData = [
                'national_id_front_uploaded' => $request->hasFile('national_id_front'),
                'national_id_back_uploaded' => $request->hasFile('national_id_back'),
                'driving_license_uploaded' => $request->hasFile('driving_license'),
                'salary_certificate_uploaded' => $request->hasFile('salary_certificate'),
                'bank_statement_uploaded' => $request->hasFile('bank_statement'),
                'additional_documents_count' => $request->hasFile('additional_documents') ?
                    count($request->file('additional_documents')) : 0,
                'documents_authenticity_confirmed' => $request->input('documents_authenticity_confirmed'),
                'data_processing_consent' => $request->input('data_processing_consent'),
                'additional_documents_descriptions' => $request->input('additional_documents_descriptions', [])
            ];

            // حفظ الملفات مؤقتاً في الجلسة (سيتم نقلها عند إنشاء الطلب)
            $tempDocuments = [];

            // المستندات الأساسية
            $basicDocuments = ['national_id_front', 'national_id_back', 'driving_license'];
            foreach ($basicDocuments as $docType) {
                if ($request->hasFile($docType)) {
                    $tempDocuments[$docType] = $this->storeTemporaryFile($request->file($docType));
                }
            }

            // مستندات التمويل الإضافية
            $financeDocuments = ['salary_certificate', 'bank_statement'];
            foreach ($financeDocuments as $docType) {
                if ($request->hasFile($docType)) {
                    $tempDocuments[$docType] = $this->storeTemporaryFile($request->file($docType));
                }
            }

            // المستندات الإضافية
            if ($request->hasFile('additional_documents')) {
                $tempDocuments['additional_documents'] = [];
                foreach ($request->file('additional_documents') as $file) {
                    $tempDocuments['additional_documents'][] = $this->storeTemporaryFile($file);
                }
            }

            session(['finance_order_documents_data' => $documentsData]);
            session(['finance_order_temp_documents' => $tempDocuments]);
            session(['finance_order_step' => 4]);

            return redirect()->route('site.order.finance.step4')
                            ->with('success', 'تم رفع المستندات بنجاح');

        } catch (Exception $e) {
            Log::error('خطأ في معالجة الخطوة الثالثة لطلب التمويل', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                        ->with('error', 'حدث خطأ في رفع المستندات. يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * معالجة الخطوة الرابعة والتأكيد النهائي لطلب التمويل
     */
    public function processFinanceOrderStep4(FinanceOrderStep4Request $request)
    {
        try {
            // حفظ بيانات التأكيد النهائي
            $confirmationData = $request->validated();
            session(['finance_order_confirmation_data' => $confirmationData]);

            // توجيه المستخدم لصفحة التأكيد النهائي مع FormRequest
            return redirect()->route('site.order.finance.step4')
                            ->with('success', 'تم حفظ بيانات التأكيد. يرجى مراجعة جميع البيانات والموافقة النهائية');

        } catch (Exception $e) {
            Log::error('خطأ في معالجة الخطوة الرابعة لطلب التمويل', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                        ->with('error', 'حدث خطأ في التأكيد النهائي. يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * إنشاء طلب التمويل النهائي
     *
     * يقوم بمعالجة جميع بيانات طلب التمويل وإنشاء الطلب في قاعدة البيانات
     * بناءً على MOD-ORDER-MGMT-FEAT-004 في REQ-FR.md
     */
    public function storeFinanceOrder(FinanceOrderFinalRequest $request)
    {
        try {
            // الحصول على البيانات المؤكدة من FormRequest
            $confirmationData = $request->validated();

            // جمع جميع البيانات من الجلسة
            $orderData = $this->collectFinanceOrderDataFromSession();

            if (!$orderData) {
                Log::error('فشل في جمع بيانات طلب التمويل من الجلسة', [
                    'user_id' => auth()->id()
                ]);

                return redirect()->route('site.cars.index')
                                ->with('error', 'بيانات طلب التمويل غير مكتملة');
            }

            // إضافة بيانات التأكيد النهائية إلى بيانات الطلب
            $orderData['confirmation_data'] = $confirmationData;

            // التحقق من صحة السيارة وتوفرها
            $car = Car::findOrFail($orderData['car_id']);
            if (!$this->isCarAvailableForFinancing($car)) {
                Log::warning('محاولة طلب تمويل لسيارة غير متوفرة', [
                    'user_id' => auth()->id(),
                    'car_id' => $car->id,
                    'car_status' => $car->status
                ]);

                return redirect()->route('site.cars.show', $car->id)
                                ->with('error', 'السيارة المختارة غير متوفرة لطلب التمويل حالياً');
            }

            // إنشاء طلب التمويل باستخدام OrderProcessingService
            $orderService = new OrderProcessingService();
            $order = $orderService->createFinanceOrder($orderData);

            // رفع المستندات باستخدام DocumentUploadService
            $this->uploadFinanceOrderDocuments($order);

            // إرسال الإشعارات للعميل والإدارة
            $this->sendOrderNotifications($order);

            // تسجيل نجاح العملية
            Log::info('تم إنشاء طلب تمويل جديد بنجاح', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'user_id' => auth()->id(),
                'car_id' => $car->id,
                'requested_amount' => $orderData['finance_data']['requested_amount'] ?? 'غير محدد'
            ]);

            // تنظيف الجلسة
            $this->clearFinanceOrderSession();

            return redirect()->route('site.order.success', $order->id)
                            ->with('success', 'تم تقديم طلب التمويل بنجاح. سيتم مراجعته والتواصل معك قريباً');

        } catch (Exception $e) {
            Log::error('خطأ في إنشاء طلب التمويل النهائي', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'حدث خطأ في إنشاء طلب التمويل. يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * إنشاء الطلب النهائي
     */
    public function storeCashOrder()
    {
        try {
            // جمع جميع البيانات من الجلسة
            $orderData = $this->collectOrderDataFromSession();

            if (!$orderData) {
                return redirect()->route('site.cars.index')
                                ->with('error', 'بيانات الطلب غير مكتملة');
            }

            // إنشاء الطلب باستخدام OrderProcessingService
            $orderService = new OrderProcessingService();
            $order = $orderService->createCashOrder($orderData);

            // رفع المستندات باستخدام DocumentUploadService
            $this->uploadOrderDocuments($order);

            // إرسال الإشعارات
            $this->sendOrderNotifications($order);

            // تنظيف الجلسة
            $this->clearOrderSession();

            // توجيه المستخدم بناءً على طريقة الدفع
            if ($orderData['payment_method'] === 'online_payment') {
                return $this->redirectToPaymentGateway($order);
            } else {
                return redirect()->route('site.order.success', $order->id)
                                ->with('success', 'تم تقديم طلبك بنجاح. سيتم التواصل معك قريباً');
            }

        } catch (Exception $e) {
            Log::error('خطأ في إنشاء الطلب النهائي', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'حدث خطأ في إنشاء الطلب. يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * جمع جميع بيانات الطلب من الجلسة
     */
    private function collectOrderDataFromSession(): ?array
    {
        $carId = session('cash_order_car_id');
        $personalData = session('cash_order_personal_data');
        $paymentData = session('cash_order_payment_data');
        $documentsData = session('cash_order_documents_data');
        $reservationAmount = session('cash_order_reservation_amount');

        if (!$carId || !$personalData || !$paymentData || !$documentsData || !$reservationAmount) {
            return null;
        }

        return [
            'car_id' => $carId,
            'personal_data' => $personalData,
            'payment_data' => $paymentData,
            'documents_data' => $documentsData,
            'reservation_amount' => $reservationAmount,
            'payment_method' => $paymentData['payment_method'] ?? 'showroom_payment'
        ];
    }

    /**
     * تحديث بيانات المستخدم إذا لزم الأمر
     */
    private function updateUserProfileIfNeeded(array $personalData): void
    {
        $user = auth()->user();
        $updateData = [];

        // تحديث رقم الجوال إذا تغير
        if ($user->phone_number !== $personalData['phone_number']) {
            $updateData['phone_number'] = $personalData['phone_number'];
        }

        // تحديث البريد الإلكتروني إذا تغير
        if ($user->email !== $personalData['email']) {
            $updateData['email'] = $personalData['email'];
        }

        if (!empty($updateData)) {
            $user->update($updateData);
        }
    }

    /**
     * تخزين ملف مؤقت
     */
    private function storeTemporaryFile(UploadedFile $file): string
    {
        $fileName = time() . '_' . $file->getClientOriginalName();
        $path = $file->storeAs('temp/order_documents', $fileName, 'local');

        return $path;
    }

    /**
     * رفع مستندات الطلب
     */
    private function uploadOrderDocuments(Order $order): void
    {
        try {
            $tempDocuments = session('cash_order_temp_documents', []);
            $documentService = new DocumentUploadService();

            // رفع المستندات الأساسية
            $basicDocuments = ['national_id_front', 'national_id_back', 'driving_license'];

            foreach ($basicDocuments as $docType) {
                if (isset($tempDocuments[$docType])) {
                    $filePath = storage_path('app/' . $tempDocuments[$docType]);
                    if (file_exists($filePath)) {
                        $uploadedFile = new UploadedFile(
                            $filePath,
                            basename($tempDocuments[$docType]),
                            mime_content_type($filePath),
                            null,
                            true
                        );

                        $documentService->uploadDocument($order, $uploadedFile, $docType);

                        // حذف الملف المؤقت
                        unlink($filePath);
                    }
                }
            }

            // رفع المستندات الإضافية
            if (isset($tempDocuments['additional_documents']) && is_array($tempDocuments['additional_documents'])) {
                $descriptions = session('cash_order_documents_data.additional_documents_descriptions', []);

                foreach ($tempDocuments['additional_documents'] as $index => $tempPath) {
                    $filePath = storage_path('app/' . $tempPath);
                    if (file_exists($filePath)) {
                        $uploadedFile = new UploadedFile(
                            $filePath,
                            basename($tempPath),
                            mime_content_type($filePath),
                            null,
                            true
                        );

                        $description = $descriptions[$index] ?? "مستند إضافي " . ($index + 1);
                        $documentService->uploadDocument($order, $uploadedFile, 'additional_documents', $description);

                        // حذف الملف المؤقت
                        unlink($filePath);
                    }
                }
            }

            // تنظيف بيانات الملفات المؤقتة من الجلسة
            session()->forget('cash_order_temp_documents');

        } catch (Exception $e) {
            Log::error('خطأ في رفع مستندات الطلب', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * إرسال الإشعارات المتعلقة بالطلب
     *
     * يرسل إشعارات مخصصة حسب نوع الطلب (كاش أو تمويل)
     */
    private function sendOrderNotifications(Order $order): void
    {
        try {
            // إرسال إشعار للعميل
            $order->user->notify(new OrderCreatedNotification($order));

            // إرسال إشعار للإدارة العامة
            $adminUsers = User::role('Super Admin')->get();
            foreach ($adminUsers as $admin) {
                $admin->notify(new NewOrderAdminNotification($order));
            }

            // إرسال إشعار إضافي لقسم التمويل إذا كان طلب تمويل
            if ($order->order_type === 'finance_application') {
                $this->sendFinanceTeamNotification($order);
            }

            Log::info('تم إرسال إشعارات الطلب بنجاح', [
                'order_id' => $order->id,
                'order_type' => $order->order_type,
                'user_id' => $order->user_id
            ]);

        } catch (Exception $e) {
            Log::error('خطأ في إرسال إشعارات الطلب', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * إرسال إشعار خاص لفريق التمويل
     */
    private function sendFinanceTeamNotification(Order $order): void
    {
        try {
            // البحث عن المستخدمين المخولين لمراجعة طلبات التمويل
            $financeTeamUsers = User::whereHas('roles', function($query) {
                $query->where('name', 'finance_manager')
                      ->orWhere('name', 'finance_analyst');
            })->get();

            // إذا لم يوجد فريق تمويل محدد، إرسال للموظفين
            if ($financeTeamUsers->isEmpty()) {
                $financeTeamUsers = User::role('Employee')->get();
            }

            // إرسال إشعار لكل عضو في فريق التمويل
            foreach ($financeTeamUsers as $financeUser) {
                $financeUser->notify(new NewOrderAdminNotification($order));
            }

            Log::info('تم إرسال إشعار لفريق التمويل', [
                'order_id' => $order->id,
                'finance_team_count' => $financeTeamUsers->count()
            ]);

        } catch (Exception $e) {
            Log::error('خطأ في إرسال إشعار لفريق التمويل', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * توجيه المستخدم لبوابة الدفع
     */
    private function redirectToPaymentGateway(Order $order)
    {
        try {
            // تحديد بوابة الدفع من بيانات الجلسة
            $paymentData = session('cash_order_payment_data');
            $gateway = config('payment.default_gateway', 'test');

            // إنشاء خدمة الدفع مع البوابة المحددة
            $paymentService = new PaymentGatewayService($gateway);
            $paymentUrl = $paymentService->createPaymentSession($order);

            // تسجيل نجاح إنشاء جلسة الدفع
            Log::info('تم توجيه المستخدم لبوابة الدفع بنجاح', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'gateway' => $gateway,
                'user_id' => auth()->id()
            ]);

            return redirect($paymentUrl);

        } catch (Exception $e) {
            Log::error('خطأ في توجيه المستخدم لبوابة الدفع', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('site.order.payment.error', $order->id)
                            ->with('error', 'حدث خطأ في عملية الدفع. يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * صفحة نجاح الدفع
     */
    public function paymentSuccess(Order $order)
    {
        try {
            // التحقق من أن المستخدم يملك هذا الطلب
            if ($order->user_id !== auth()->id()) {
                Log::warning('محاولة وصول غير مصرح بها لصفحة نجاح الدفع', [
                    'order_id' => $order->id,
                    'order_owner' => $order->user_id,
                    'current_user' => auth()->id()
                ]);
                abort(403);
            }

            // التحقق من أن الدفع لم يتم معالجته مسبقاً
            if ($order->payment_status === 'completed') {
                Log::info('محاولة معالجة دفع مكتمل مسبقاً', [
                    'order_id' => $order->id,
                    'current_status' => $order->payment_status
                ]);

                return redirect()->route('site.order.success', $order->id)
                                ->with('info', 'تم الدفع مسبقاً لهذا الطلب');
            }

            // معالجة نجاح الدفع
            $orderService = new OrderProcessingService();
            $paymentResponse = [
                'transaction_id' => request('transaction_id', 'TXN_' . time()),
                'payment_method' => request('payment_method', 'online_payment'),
                'amount' => request('amount', $order->reservation_amount),
                'currency' => 'SAR',
                'gateway' => request('gateway', 'test'),
                'processed_at' => now()->toISOString(),
                'success_url_accessed' => true
            ];

            $success = $orderService->updateOrderPaymentStatus($order, 'completed', $paymentResponse);

            if ($success) {
                // تنظيف بيانات الجلسة
                $this->clearOrderSession();

                Log::info('تم معالجة نجاح الدفع بنجاح', [
                    'order_id' => $order->id,
                    'transaction_id' => $paymentResponse['transaction_id'],
                    'user_id' => auth()->id()
                ]);

                return redirect()->route('site.order.success', $order->id)
                                ->with('success', 'تم الدفع بنجاح! تم تأكيد حجز السيارة');
            } else {
                throw new Exception('فشل في تحديث حالة الدفع');
            }

        } catch (Exception $e) {
            Log::error('خطأ في معالجة نجاح الدفع', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return redirect()->route('site.order.payment.error', $order->id)
                            ->with('error', 'حدث خطأ في تأكيد الدفع. يرجى التواصل مع الدعم الفني');
        }
    }

    /**
     * صفحة إلغاء الدفع
     */
    public function paymentCancel(Order $order)
    {
        if ($order->user_id !== auth()->id()) {
            abort(403);
        }

        return redirect()->route('site.order.details', $order->id)
                        ->with('warning', 'تم إلغاء عملية الدفع. يمكنك المحاولة مرة أخرى');
    }

    /**
     * صفحة خطأ الدفع
     */
    public function paymentError(Order $order)
    {
        if ($order->user_id !== auth()->id()) {
            abort(403);
        }

        return redirect()->route('site.order.details', $order->id)
                        ->with('error', 'حدث خطأ في عملية الدفع. يرجى المحاولة مرة أخرى');
    }

    /**
     * صفحة اختبار الدفع (للتطوير)
     */
    public function paymentTest(Order $order)
    {
        if ($order->user_id !== auth()->id()) {
            abort(403);
        }

        // محاكاة نجاح الدفع للاختبار
        return view('ordermanagement::site.payment.test', compact('order'));
    }

    /**
     * صفحة نجاح الطلب
     */
    public function orderSuccess(Order $order)
    {
        if ($order->user_id !== auth()->id()) {
            abort(403);
        }

        return view('ordermanagement::site.order.success', compact('order'));
    }

    /**
     * صفحة تفاصيل الطلب
     */
    public function orderDetails(Order $order)
    {
        if ($order->user_id !== auth()->id()) {
            abort(403);
        }

        $order->load(['car', 'user']);

        return view('ordermanagement::site.order.details', compact('order'));
    }

    /**
     * إلغاء عملية الطلب وتنظيف الجلسة
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancelOrder()
    {
        $this->clearOrderSession();

        return redirect()->route('site.cars.index')
            ->with('info', 'تم إلغاء عملية الطلب');
    }

    /**
     * رفع مستند واحد عبر AJAX
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadDocumentAjax(Request $request): JsonResponse
    {
        try {
            // التحقق من صحة البيانات
            $request->validate([
                'order_id' => 'required|integer|exists:orders,id',
                'document_type' => 'required|string|in:national_id_front,national_id_back,driving_license,salary_certificate,bank_statement,additional_documents',
                'file' => 'required|file|mimes:jpeg,png,jpg,webp,pdf|max:2048',
                'description' => 'nullable|string|max:255'
            ]);

            // التحقق من ملكية الطلب للمستخدم الحالي
            $order = Order::where('id', $request->order_id)
                          ->where('user_id', auth()->id())
                          ->firstOrFail();

            // رفع المستند باستخدام DocumentUploadService
            $documentService = new DocumentUploadService();
            $success = $documentService->uploadDocument(
                $order,
                $request->file('file'),
                $request->document_type,
                $request->description
            );

            if ($success) {
                // الحصول على معلومات المستند المرفوع
                $mediaItem = $order->getMedia($request->document_type)->last();

                Log::info('تم رفع مستند جديد للطلب عبر AJAX', [
                    'order_id' => $order->id,
                    'document_type' => $request->document_type,
                    'file_name' => $request->file('file')->getClientOriginalName(),
                    'user_id' => auth()->id()
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'تم رفع المستند بنجاح',
                    'document' => [
                        'id' => $mediaItem->id,
                        'name' => $mediaItem->name,
                        'file_name' => $mediaItem->file_name,
                        'size' => $mediaItem->size,
                        'mime_type' => $mediaItem->mime_type,
                        'collection_name' => $mediaItem->collection_name,
                        'created_at' => $mediaItem->created_at->format('Y-m-d H:i:s'),
                        'preview_url' => route('site.order.document.preview', [$order->id, $mediaItem->id]),
                        'download_url' => route('site.order.document.download', [$order->id, $mediaItem->id])
                    ]
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'فشل في رفع المستند'
            ], 500);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $e->errors()
            ], 422);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'الطلب غير موجود أو غير مصرح لك بالوصول إليه'
            ], 404);

        } catch (Exception $e) {
            Log::error('خطأ في رفع المستند عبر AJAX', [
                'order_id' => $request->order_id ?? 'غير محدد',
                'document_type' => $request->document_type ?? 'غير محدد',
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء رفع المستند. يرجى المحاولة مرة أخرى'
            ], 500);
        }
    }

    /**
     * حذف مستند مرفوع عبر AJAX
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteDocumentAjax(Request $request): JsonResponse
    {
        try {
            // التحقق من صحة البيانات
            $request->validate([
                'order_id' => 'required|integer|exists:orders,id',
                'media_id' => 'required|integer'
            ]);

            // التحقق من ملكية الطلب للمستخدم الحالي
            $order = Order::where('id', $request->order_id)
                          ->where('user_id', auth()->id())
                          ->firstOrFail();

            // البحث عن المستند
            $mediaItem = $order->getMedia()->where('id', $request->media_id)->first();

            if (!$mediaItem) {
                return response()->json([
                    'success' => false,
                    'message' => 'المستند غير موجود'
                ], 404);
            }

            // حذف المستند
            $documentType = $mediaItem->collection_name;
            $fileName = $mediaItem->file_name;

            $mediaItem->delete();

            Log::info('تم حذف مستند من الطلب عبر AJAX', [
                'order_id' => $order->id,
                'media_id' => $request->media_id,
                'document_type' => $documentType,
                'file_name' => $fileName,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم حذف المستند بنجاح'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $e->errors()
            ], 422);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'الطلب غير موجود أو غير مصرح لك بالوصول إليه'
            ], 404);

        } catch (Exception $e) {
            Log::error('خطأ في حذف المستند عبر AJAX', [
                'order_id' => $request->order_id ?? 'غير محدد',
                'media_id' => $request->media_id ?? 'غير محدد',
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف المستند. يرجى المحاولة مرة أخرى'
            ], 500);
        }
    }

    /**
     * معاينة مستند مرفوع
     *
     * @param Order $order
     * @param int $mediaId
     * @return Response
     */
    public function previewDocument(Order $order, int $mediaId)
    {
        try {
            // التحقق من ملكية الطلب للمستخدم الحالي
            if ($order->user_id !== auth()->id()) {
                abort(403, 'غير مصرح لك بالوصول لهذا المستند');
            }

            // البحث عن المستند
            $mediaItem = $order->getMedia()->where('id', $mediaId)->first();

            if (!$mediaItem) {
                abort(404, 'المستند غير موجود');
            }

            // التحقق من نوع الملف للمعاينة
            $mimeType = $mediaItem->mime_type;
            $allowedPreviewTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'application/pdf'];

            if (!in_array($mimeType, $allowedPreviewTypes)) {
                abort(415, 'نوع الملف غير مدعوم للمعاينة');
            }

            // إرجاع الملف للمعاينة
            $filePath = $mediaItem->getPath();

            if (!file_exists($filePath)) {
                abort(404, 'الملف غير موجود على الخادم');
            }

            Log::info('تم عرض مستند للطلب', [
                'order_id' => $order->id,
                'media_id' => $mediaId,
                'document_type' => $mediaItem->collection_name,
                'user_id' => auth()->id()
            ]);

            return response()->file($filePath, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . $mediaItem->file_name . '"'
            ]);

        } catch (Exception $e) {
            Log::error('خطأ في معاينة المستند', [
                'order_id' => $order->id,
                'media_id' => $mediaId,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            abort(500, 'حدث خطأ أثناء معاينة المستند');
        }
    }

    /**
     * تحميل مستند مرفوع
     *
     * @param Order $order
     * @param int $mediaId
     * @return Response
     */
    public function downloadDocument(Order $order, int $mediaId)
    {
        try {
            // التحقق من ملكية الطلب للمستخدم الحالي
            if ($order->user_id !== auth()->id()) {
                abort(403, 'غير مصرح لك بالوصول لهذا المستند');
            }

            // البحث عن المستند
            $mediaItem = $order->getMedia()->where('id', $mediaId)->first();

            if (!$mediaItem) {
                abort(404, 'المستند غير موجود');
            }

            // إرجاع الملف للتحميل
            $filePath = $mediaItem->getPath();

            if (!file_exists($filePath)) {
                abort(404, 'الملف غير موجود على الخادم');
            }

            Log::info('تم تحميل مستند للطلب', [
                'order_id' => $order->id,
                'media_id' => $mediaId,
                'document_type' => $mediaItem->collection_name,
                'file_name' => $mediaItem->file_name,
                'user_id' => auth()->id()
            ]);

            return response()->download($filePath, $mediaItem->file_name, [
                'Content-Type' => $mediaItem->mime_type
            ]);

        } catch (Exception $e) {
            Log::error('خطأ في تحميل المستند', [
                'order_id' => $order->id,
                'media_id' => $mediaId,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            abort(500, 'حدث خطأ أثناء تحميل المستند');
        }
    }

    /**
     * الحصول على قائمة مستندات الطلب عبر AJAX
     *
     * @param Order $order
     * @return JsonResponse
     */
    public function getOrderDocuments(Order $order): JsonResponse
    {
        try {
            // التحقق من ملكية الطلب للمستخدم الحالي
            if ($order->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بالوصول لهذا الطلب'
                ], 403);
            }

            // جلب جميع مستندات الطلب
            $documents = [];
            $mediaItems = $order->getMedia();

            foreach ($mediaItems as $mediaItem) {
                $documents[] = [
                    'id' => $mediaItem->id,
                    'name' => $mediaItem->name,
                    'file_name' => $mediaItem->file_name,
                    'size' => $mediaItem->size,
                    'mime_type' => $mediaItem->mime_type,
                    'collection_name' => $mediaItem->collection_name,
                    'created_at' => $mediaItem->created_at->format('Y-m-d H:i:s'),
                    'preview_url' => route('site.order.document.preview', [$order->id, $mediaItem->id]),
                    'download_url' => route('site.order.document.download', [$order->id, $mediaItem->id])
                ];
            }

            return response()->json([
                'success' => true,
                'documents' => $documents,
                'total_count' => count($documents)
            ]);

        } catch (Exception $e) {
            Log::error('خطأ في جلب مستندات الطلب', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب المستندات'
            ], 500);
        }
    }
}
