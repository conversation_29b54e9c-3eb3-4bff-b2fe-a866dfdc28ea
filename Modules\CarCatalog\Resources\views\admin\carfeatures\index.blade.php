@extends('dashboard::layouts.admin_layout')

@section('title', 'إدارة ميزات السيارات')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h4 class="fw-bold mb-0" style="color: var(--primary-color);">إدارة ميزات السيارات</h4>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb brand-breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="brand-link">لوحة التحكم</a>
                    </li>
                    <li class="breadcrumb-item">إدارة السيارات</li>
                    <li class="breadcrumb-item active" aria-current="page">ميزات السيارات</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.car-features.create') }}" class="btn btn-brand-primary">
                <i class="fas fa-plus me-1"></i> إضافة ميزة سيارة جديدة
            </a>
        </div>
    </div>

    {{-- عرض رسائل النجاح والخطأ --}}
    @if(session('success'))
        <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {{-- نموذج البحث والفلترة --}}
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.car-features.index') }}" class="mb-0">
                <div class="row gx-2 gy-2 align-items-center">
                    <div class="col-md-3">
                        <input type="text" name="search" class="form-control" placeholder="بحث بالاسم..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-2">
                        <select name="category_id" class="form-select">
                            <option value="">كل الفئات</option>
                            @foreach($categories as $id => $name)
                                <option value="{{ $id }}" {{ request('category_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-select">
                            <option value="">كل الحالات</option>
                            <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>نشط</option>
                            <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="is_filterable" class="form-select">
                            <option value="">قابلية التصفية</option>
                            <option value="1" {{ request('is_filterable') == '1' ? 'selected' : '' }}>قابلة للتصفية</option>
                            <option value="0" {{ request('is_filterable') === '0' ? 'selected' : '' }}>غير قابلة للتصفية</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-brand-info w-100">بحث</button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ route('admin.car-features.index') }}" class="btn btn-brand-secondary w-100">إعادة تعيين</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- جدول ميزات السيارات --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">قائمة ميزات السيارات</h5>
        </div>
        <div class="card-body table-responsive p-0">
            <table class="table table-hover recent-activity-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الميزة</th>
                        <th>الفئة</th>
                        <th>الحالة</th>
                        <th>قابلة للتصفية</th>
                        <th>ميزة مميزة</th>
                        <th>ترتيب العرض</th>
                        <th>عدد السيارات المرتبطة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($carFeatures as $carFeature)
                        <tr>
                            <td>{{ $carFeature->id }}</td>
                            <td>
                                <div>
                                    <strong>{{ $carFeature->name }}</strong>
                                    @if($carFeature->description)
                                        <br><small class="text-muted">{{ Str::limit($carFeature->description, 50) }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @if($carFeature->category)
                                    <span class="badge bg-info">{{ $carFeature->category->name }}</span>
                                @else
                                    <span class="text-muted">غير محدد</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge rounded-pill bg-{{ $carFeature->status ? 'success' : 'danger' }}">
                                    {{ $carFeature->status ? 'نشط' : 'غير نشط' }}
                                </span>
                            </td>
                            <td>
                                <span class="badge rounded-pill bg-{{ $carFeature->is_filterable ? 'primary' : 'secondary' }}">
                                    {{ $carFeature->is_filterable ? 'نعم' : 'لا' }}
                                </span>
                            </td>
                            <td>
                                <span class="badge rounded-pill bg-{{ $carFeature->is_highlighted ? 'warning' : 'secondary' }}">
                                    {{ $carFeature->is_highlighted ? 'نعم' : 'لا' }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ $carFeature->display_order }}</span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $carFeature->cars_count }}</span>
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('admin.car-features.show', $carFeature) }}" class="btn btn-sm btn-info me-1 action-btn view" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.car-features.edit', $carFeature) }}" class="btn btn-sm btn-warning me-1 action-btn edit" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($carFeature->cars_count == 0)
                                        <form action="{{ route('admin.car-features.destroy', $carFeature) }}" method="POST" class="d-inline"
                                              onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف ميزة السيارة هذه؟');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger action-btn delete" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    @else
                                        <button type="button" class="btn btn-sm btn-secondary action-btn" title="لا يمكن الحذف - مرتبط بسيارات" disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-cogs fa-3x mb-3"></i>
                                    <p>لا توجد ميزات سيارات مسجلة</p>
                                    <a href="{{ route('admin.car-features.create') }}" class="btn btn-primary">
                                        إضافة ميزة سيارة جديدة
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

    </div>

    {{-- ترقيم الصفحات --}}
    <div class="mt-3">
        {{ $carFeatures->appends(request()->query())->links('pagination::bootstrap-5') }}
    </div>
</div>
@endsection
