# Payment Gateway Integration - OrderManagement Module

## نظرة عامة

تم تنفيذ التكامل مع بوابات الدفع لمعالجة دفع مبلغ الحجز أونلاين بناءً على **TASK-ID: PH03-TASK-029** و **MOD-ORDER-MGMT-FEAT-005** في REQ-FR.md.

## الميزات المنفذة

### 1. PaymentGatewayService المحسن
- **الموقع**: `Modules/OrderManagement/Services/PaymentGatewayService.php`
- **الميزات**:
  - دعم بوابات دفع متعددة (Test, Moyasar, PayTabs, HyperPay)
  - نظام تسجيل شامل لجميع عمليات الدفع
  - معالجة أخطاء محسنة
  - دعم بوابة اختبار للتطوير
  - التحقق من التواقيع الرقمية

### 2. Payment API Controller
- **الموقع**: `Modules/OrderManagement/Http/Controllers/Api/PaymentController.php`
- **الوظائف**:
  - معالجة webhooks من بوابات الدفع
  - الحصول على حالة الدفع
  - إعادة محاولة الدفع
  - التحقق من صحة البيانات المرسلة

### 3. ملف إعدادات الدفع
- **الموقع**: `config/payment.php`
- **المحتوى**:
  - إعدادات بوابات الدفع المختلفة
  - إعدادات webhooks
  - إعدادات التسجيل
  - إعدادات الأمان

### 4. صفحة اختبار الدفع
- **الموقع**: `Modules/OrderManagement/Resources/views/site/payment/test.blade.php`
- **الميزات**:
  - محاكاة عملية الدفع للاختبار
  - اختيار نتيجة الدفع (نجح/فشل/معلق)
  - واجهة مستخدم تفاعلية

### 5. Webhook Verification Middleware
- **الموقع**: `Modules/OrderManagement/Http/Middleware/VerifyPaymentWebhook.php`
- **الوظائف**:
  - التحقق من IP المرسل
  - التحقق من التوقيع الرقمي
  - التحقق من صحة البيانات

## إعدادات البيئة

تم إضافة المتغيرات التالية لملف `.env.example`:

```env
# Payment Gateway Settings
PAYMENT_DEFAULT_GATEWAY=test
PAYMENT_CURRENCY=SAR
PAYMENT_TIMEOUT=30
PAYMENT_RETRY_ATTEMPTS=3

# Test Gateway (للتطوير)
PAYMENT_TEST_ENABLED=true
PAYMENT_TEST_API_KEY=test_api_key
PAYMENT_TEST_SECRET_KEY=test_secret_key
PAYMENT_TEST_WEBHOOK_SECRET=test_webhook_secret

# Moyasar Gateway
PAYMENT_MOYASAR_ENABLED=false
PAYMENT_MOYASAR_API_KEY=
PAYMENT_MOYASAR_SECRET_KEY=
PAYMENT_MOYASAR_WEBHOOK_SECRET=

# PayTabs Gateway
PAYMENT_PAYTABS_ENABLED=false
PAYMENT_PAYTABS_API_KEY=
PAYMENT_PAYTABS_SECRET_KEY=
PAYMENT_PAYTABS_WEBHOOK_SECRET=

# HyperPay Gateway
PAYMENT_HYPERPAY_ENABLED=false
PAYMENT_HYPERPAY_API_KEY=
PAYMENT_HYPERPAY_SECRET_KEY=
PAYMENT_HYPERPAY_WEBHOOK_SECRET=

# Webhook Settings
PAYMENT_WEBHOOK_VERIFY_SIGNATURE=true
PAYMENT_WEBHOOK_ALLOWED_IPS=

# Payment Logging
PAYMENT_LOGGING_ENABLED=true
PAYMENT_LOGGING_LEVEL=info
```

## API Routes

### Webhook Route
```
POST /api/payment/webhook
```
- يستقبل webhooks من بوابات الدفع
- لا يتطلب مصادقة
- يتم التحقق من التوقيع والـ IP

### Payment Status
```
GET /api/payment/status/{order}
```
- يتطلب مصادقة (Sanctum)
- يعيد حالة الدفع للطلب

### Retry Payment
```
POST /api/payment/retry/{order}
```
- يتطلب مصادقة (Sanctum)
- ينشئ جلسة دفع جديدة

## استخدام النظام

### 1. إنشاء جلسة دفع

```php
use Modules\OrderManagement\Services\PaymentGatewayService;

// استخدام البوابة الافتراضية
$paymentService = new PaymentGatewayService();

// أو تحديد بوابة معينة
$paymentService = new PaymentGatewayService('moyasar');

$paymentUrl = $paymentService->createPaymentSession($order);
```

### 2. معالجة استجابة الدفع

```php
$response = $paymentService->processPaymentResponse($webhookData);

if ($response['success']) {
    // تم الدفع بنجاح
    $order = $response['order'];
} else {
    // فشل الدفع
    $errorMessage = $response['message'];
}
```

### 3. الحصول على معلومات البوابة

```php
$gatewayInfo = $paymentService->getGatewayInfo();
$supportedMethods = $gatewayInfo['supported_methods'];
```

## تدفق العمل

1. **إنشاء الطلب**: العميل يكمل عملية الطلب
2. **توجيه للدفع**: النظام ينشئ جلسة دفع ويوجه العميل
3. **معالجة الدفع**: العميل يدفع في بوابة الدفع
4. **Webhook**: بوابة الدفع ترسل webhook للنظام
5. **تحديث الحالة**: النظام يحدث حالة الطلب
6. **إشعار العميل**: العميل يحصل على تأكيد

## الأمان

- التحقق من التوقيع الرقمي لجميع webhooks
- تقييد IPs المسموح لها بإرسال webhooks
- تشفير البيانات الحساسة
- تسجيل شامل لجميع العمليات

## الاختبار

### بوابة الاختبار
- تستخدم للتطوير والاختبار
- لا تتطلب اتصال بالإنترنت
- تحاكي جميع حالات الدفع

### صفحة الاختبار
- متاحة في: `/site/order/payment/test/{order}`
- تسمح باختيار نتيجة الدفع
- تحاكي تجربة المستخدم الكاملة

## التسجيل

جميع عمليات الدفع يتم تسجيلها مع التفاصيل التالية:
- معرف الطلب ورقم الطلب
- بوابة الدفع المستخدمة
- حالة العملية
- تفاصيل الخطأ (إن وجد)
- الطابع الزمني

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع ملفات التسجيل في `storage/logs`
2. تحقق من إعدادات البيئة
3. تأكد من صحة مفاتيح API
4. اختبر باستخدام بوابة الاختبار أولاً
