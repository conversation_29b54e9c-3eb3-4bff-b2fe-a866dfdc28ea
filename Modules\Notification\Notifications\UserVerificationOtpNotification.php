<?php

namespace Modules\Notification\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

/**
 * إشعار رمز التحقق OTP للمستخدمين الجدد
 *
 * يتم إرسال هذا الإشعار عند تسجيل مستخدم جديد أو تغيير رقم الجوال
 * لإرسال رمز OTP للتحقق من ملكية رقم الجوال
 */
class UserVerificationOtpNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * رمز OTP المراد إرساله
     *
     * @var string
     */
    public $otpCode;

    /**
     * إنشاء نسخة جديدة من الإشعار
     *
     * @param string $otpCode
     * @return void
     */
    public function __construct(string $otpCode)
    {
        $this->otpCode = $otpCode;
    }

    /**
     * الحصول على قنوات توصيل الإشعار
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        // في بيئة التطوير، سنستخدم قناة log
        // في بيئة الإنتاج، يجب استخدام قناة SMS
        if (config('app.env') === 'production') {
            return ['sms']; // سيتم تنفيذ قناة SMS لاحقاً
        }
        
        return ['log', 'database'];
    }

    /**
     * الحصول على تمثيل الـ log للإشعار
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toLog($notifiable)
    {
        return [
            'message' => "رمز التحقق OTP للمستخدم {$notifiable->phone_number}: {$this->otpCode}",
            'phone_number' => $notifiable->phone_number,
            'otp_code' => $this->otpCode,
            'user_id' => $notifiable->id
        ];
    }

    /**
     * الحصول على تمثيل قاعدة البيانات للإشعار
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toDatabase($notifiable)
    {
        return [
            'title' => 'رمز التحقق',
            'message' => "تم إرسال رمز التحقق إلى رقم الجوال {$notifiable->phone_number}",
            'type' => 'otp_verification',
            'phone_number' => $notifiable->phone_number,
            'user_id' => $notifiable->id,
            'icon' => 'fas fa-mobile-alt'
        ];
    }

    /**
     * الحصول على تمثيل المصفوفة للإشعار
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'title' => 'رمز التحقق',
            'message' => "تم إرسال رمز التحقق إلى رقم الجوال {$notifiable->phone_number}",
            'type' => 'otp_verification',
            'phone_number' => $notifiable->phone_number,
            'user_id' => $notifiable->id,
            'icon' => 'fas fa-mobile-alt'
        ];
    }

    /**
     * الحصول على تمثيل SMS للإشعار (للتنفيذ المستقبلي)
     *
     * @param mixed $notifiable
     * @return string
     */
    public function toSms($notifiable)
    {
        $appName = config('app.name', 'MotorLine');
        return "رمز التحقق الخاص بك في {$appName}: {$this->otpCode}. صالح لمدة 10 دقائق.";
    }
}
