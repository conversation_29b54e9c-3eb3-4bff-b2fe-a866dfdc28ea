<?php

namespace Modules\UserManagement\Notifications;

use Illuminate\Auth\Notifications\ResetPassword as ResetPasswordNotification;
use Illuminate\Notifications\Messages\MailMessage;

/**
 * إشعار إعادة تعيين كلمة المرور المخصص
 *
 * يرسل هذا الإشعار رابط إعادة تعيين كلمة المرور للمستخدم
 * مع رسائل باللغة العربية ومحتوى مخصص للموقع
 */
class ResetPasswordNotification extends ResetPasswordNotification
{
    /**
     * إنشاء رسالة البريد الإلكتروني
     *
     * @param mixed $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        $url = $this->resetUrl($notifiable);

        return (new MailMessage)
            ->subject('إعادة تعيين كلمة المرور - ' . config('app.name'))
            ->greeting('مرحباً ' . $notifiable->first_name . '!')
            ->line('تلقيت هذا البريد الإلكتروني لأنه تم طلب إعادة تعيين كلمة المرور لحسابك.')
            ->action('إعادة تعيين كلمة المرور', $url)
            ->line('رابط إعادة تعيين كلمة المرور سينتهي خلال ' . config('auth.passwords.users.expire') . ' دقيقة.')
            ->line('إذا لم تطلب إعادة تعيين كلمة المرور، فلا حاجة لاتخاذ أي إجراء.')
            ->salutation('مع تحيات فريق ' . config('app.name'));
    }

    /**
     * الحصول على رابط إعادة تعيين كلمة المرور
     *
     * @param mixed $notifiable
     * @return string
     */
    protected function resetUrl($notifiable): string
    {
        return url(route('site.auth.password.reset.form', [
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ], false));
    }
}
