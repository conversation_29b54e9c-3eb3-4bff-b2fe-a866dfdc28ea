<?php

namespace Modules\Core\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * BaseRequest Class
 *
 * يوفر هذا الصنف الأساسي وظائف مشتركة لجميع طلبات النماذج في النظام
 * ويتضمن معالجة موحدة لأخطاء التحقق من الصحة للطلبات التي تتوقع استجابة JSON
 */
class BaseRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مصرح له بتنفيذ هذا الطلب
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب
     *
     * @return array
     */
    public function rules()
    {
        return [];
    }

    /**
     * معالجة فشل التحقق من صحة البيانات
     *
     * إذا كان الطلب يتوقع استجابة JSON، سيتم إرجاع استجابة خطأ بتنسيق JSON
     * وإلا سيتم استخدام السلوك الافتراضي
     *
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        if ($this->expectsJson()) {
            throw new HttpResponseException(
                response()->json([
                    'success' => false,
                    'message' => 'Validation errors occurred.',
                    'errors' => $validator->errors(),
                ], 422)
            );
        }

        parent::failedValidation($validator);
    }
}
