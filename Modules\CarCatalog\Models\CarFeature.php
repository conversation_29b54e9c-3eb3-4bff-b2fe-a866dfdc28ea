<?php

namespace Modules\CarCatalog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * CarFeature Model.
 *
 * يمثل هذا النموذج جدول ميزات السيارات في النظام
 *
 * @property int $id
 * @property int $category_id معرّف فئة الميزة
 * @property string $name اسم الميزة
 * @property string|null $description وصف الميزة
 * @property int|null $icon_id معرّف أيقونة الميزة
 * @property bool $is_filterable قابلة للتصفية في البحث المتقدم
 * @property bool $is_highlighted ميزة مميزة تظهر في الصفحة الرئيسية
 * @property bool $status حالة الميزة (نشطة/غير نشطة)
 * @property int $display_order ترتيب العرض
 * @property \Illuminate\Support\Carbon|null $created_at تاريخ الإنشاء
 * @property \Illuminate\Support\Carbon|null $updated_at تاريخ التحديث
 * @property \Modules\CarCatalog\Models\FeatureCategory $category علاقة فئة الميزة
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\CarCatalog\Models\Car[] $cars علاقة السيارات
 */
class CarFeature extends Model
{
    use HasFactory;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي.
     *
     * @var array
     */
    protected $fillable = [
        'category_id',
        'name',
        'description',
        'is_filterable',
        'is_highlighted',
        'status',
        'display_order',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة.
     *
     * @var array
     */
    protected $casts = [
        'is_filterable'  => 'boolean',
        'is_highlighted' => 'boolean',
        'status'         => 'boolean',
        'display_order'  => 'integer',
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime',
    ];

    /**
     * علاقة فئة الميزة التي تنتمي إليها هذه الميزة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo(FeatureCategory::class, 'category_id');
    }

    /**
     * علاقة السيارات المرتبطة بهذه الميزة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function cars()
    {
        return $this->belongsToMany(Car::class, 'car_car_feature', 'feature_id', 'car_id')
            ->withPivot('value')
            ->withTimestamps();
    }

    /**
     * مصنع النموذج.
     *
     * @return \Modules\CarCatalog\Database\factories\CarFeatureFactory
     */
    protected static function newFactory()
    {
        return \Modules\CarCatalog\Database\factories\CarFeatureFactory::new();
    }
}
