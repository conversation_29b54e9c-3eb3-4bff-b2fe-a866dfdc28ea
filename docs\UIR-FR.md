**START OF `UIR-FR.md`**
---

## UIR-FR.md - خارطة طريق تنفيذ واجهات المستخدم (نسخة نهائية معتمدة ذاتيًا)

**تاريخ الإنشاء:** (تاريخ اليوم)
**إصدار المستند:** 1.0 (نهائي معتمد ذاتيًا)

### مقدمة

**معرف القسم:** `UIR-INTRO-001`

الغرض من هذا المستند هو تقديم خارطة طريق مفصلة ومنهجية لتنفيذ واجهات المستخدم (UI) لمنصة معرض السيارات الإلكترونية المتكاملة. تستند هذه الخارطة إلى تصميمات الواجهات المحددة في `UIUX-FR.md` (النسخة النهائية المعتمدة ذاتيًا)، وتتكامل مع خطة المراحل التنفيذية الموضحة في `PPP-FR.md`. تركز هذه الخارطة بشكل خاص على كيفية وجدولة تنفيذ واجهات لوحة تحكم Dash المخصصة (باستخدام أصول HTML/CSS/JS المتوفرة وتحويلها إلى Blade)، واجهات الموقع العامة (Blade)، وشاشات تطبيق Flutter (إذا كان مطلوبًا)، لضمان تحقيق **[المخرجات النهائية الأربعة المتوقعة للمشروع بالكامل]** بجودة عالية وتجربة مستخدم ممتازة.

**المخرجات النهائية المتوقعة للمشروع بالكامل (المرجع الأساسي والنهائي):**
1.  **موقع إلكتروني فعال:** مرفوع على خادم عبر الإنترنت، جاهز لاستقبال الزوار بدون مشاكل.
2.  **لوحة تحكم احترافية (مخصصة من الصفر باستخدام أصول Dash HTML/CSS/JS):** (للإدارة و/أو العملاء) جاهزة للاستخدام، توفر تجربة مستخدم ممتازة، وتدعم اللغة العربية (RTL) بشكل كامل.
3.  **واجهة موقع (Frontend) جذابة وفعالة:** (باستخدام Blade إذا كانت مختلفة عن لوحة التحكم) جاهزة لاستقبال الزوار وتوفير تجربة مستخدم سلسة.
4.  **تطبيق موبايل (Flutter) كامل (إذا كان مطلوبًا):** جاهز للرفع على متاجر التطبيقات ويعمل بشكل صحيح ومتكامل.

### منهجية تنفيذ الواجهات

**(معرف القسم: `UIR-METHODOLOGY-001`)**

سيتم اتباع منهجية تطوير تكرارية ومتزايدة لتنفيذ واجهات المستخدم، مع التركيز على النقاط التالية:

1.  **البناء على أساس `UIUX-FR.md`:** جميع الواجهات سيتم بناؤها بناءً على الهياكل السلكية النصية التفصيلية، إرشادات الألوان، الخطوط، التباعد، والتفاعلات المحددة في `UIUX-FR.md`.
2.  **تخصيص لوحة تحكم Dash المخصصة:**
    *   **الأساس:** استخدام أصول `Dash/index.html`, `Dash/style.css`, `Dash/script.js` كنقطة انطلاق هيكلية ووظيفية.
    *   **التكامل مع Blade:** تحويل الأقسام الرئيسية من `Dash/index.html` إلى تخطيطات (`layouts`) ومكونات جزئية (`partials`) ضمن Laravel Blade (كما هو محدد في `STRU-LARAVEL-VIEWS-ADMIN-001`).
    *   **التخصيص المرئي:** تطبيق لوحة الألوان والخطوط المعتمدة (من `UIUX-COLORS-001`, `UIUX-TYPOGRAPHY-001`) على واجهات Dash، مع استخدام `dash_custom.css` لتجاوزات محدودة لـ `Dash/style.css` لضمان الحفاظ على الهوية البصرية للمشروع. يجب تقليل التعديلات المباشرة على ملفي `Dash/style.css` و `Dash/script.js` الأصليين إلى أقصى حد ممكن، وتوثيق أي تعديلات ضرورية بشكل كامل. يُفضل دائمًا استخدام `dash_custom.css` للتجاوزات و `dash_app.js` للمنطق الإضافي أو تكييف السلوك.
    *   **التفاعلات الديناميكية:** استخدام JavaScript مخصص (`dash_app.js`) لربط البيانات الديناميكية من Laravel بالرسوم البيانية والمكونات التفاعلية الأخرى، وتكييف سلوكيات `Dash/script.js` لتناسب البيئة الديناميكية (كما هو موضح في `UIUX-DASH-CUSTOMIZATION-001`).
3.  **واجهات Blade للموقع العام:** بناء واجهات متجاوبة وجذابة باستخدام Laravel Blade و CSS/JS مخصصين، مع الاستلهام الهيكلي من الصور المرجعية إذا كانت ذات صلة وتطبيق الهوية البصرية المحددة.
4.  **تطبيق Flutter:** بناء شاشات Flutter باتباع مبادئ Material Design وتكييفها مع الهوية البصرية للمشروع، وضمان تجربة مستخدم سلسة على الأجهزة المحمولة.
5.  **التكامل مع الـ Backend:** ضمان ربط جميع عناصر الواجهة بشكل صحيح مع وحدات التحكم (Controllers) ونقاط نهاية API في الـ Backend لجلب وعرض البيانات ومعالجة تفاعلات المستخدم.
6.  **الاختبار المستمر:** إجراء اختبارات للواجهة (يدوية وآلية باستخدام Laravel Dusk أو ما يعادله) بشكل مستمر خلال عملية التطوير للتحقق من المظهر، السلوك، التجاوب، والوصولية.
7.  **مراعاة اللغة العربية (RTL):** تصميم وتنفيذ جميع الواجهات مع دعم كامل لاتجاه النص من اليمين لليسار، وهذا ينطبق بشكل أساسي وحرج على لوحات تحكم Dash وواجهة الموقع العام والتطبيق.

### جدولة تنفيذ مكونات واجهة المستخدم (مرتبطة بالمراحل)

**(معرف القسم: `UIR-SCHEDULE-001`)**

#### **المرحلة 1: بناء الأساس (Backend Foundation) وتجهيز بيئة Dash المخصصة (`PH-01`)**

*   **الواجهات التي سيتم التركيز عليها (مبدئيًا):**
    *   `UIR-PH01-ITEM-001`: **صفحة تسجيل الدخول الأساسية للوحة تحكم الإدارة Dash** (واجهة Blade بسيطة مبنية على تصميم `DASH-ADMIN-LOGIN-001` من `UIUX-FR.md`).
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-USER-MGMT-FEAT-002` (وظيفة تسجيل الدخول للمديرين).
    *   `UIR-PH01-ITEM-002`: **الهيكل الأساسي للوحة تحكم الإدارة Dash (`admin_layout.blade.php`)** (بناءً على `Dash/index.html` مع ربط أولي لـ `Dash/style.css` و `Dash/script.js` كما هو موضح في `STRU-LARAVEL-VIEWS-ADMIN-001`). (يخدم `MOD-DASHBOARD-FEAT-001` جزئيًا).
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** لا يوجد تبعيات Backend مباشرة لهذه المرحلة، ولكنها أساس للمراحل اللاحقة.
    *   `UIR-PH01-ITEM-003`: **مكونات Blade أولية (فارغة أو ثابتة مؤقتًا) للشريط العلوي (`_topbar.blade.php`) والقائمة الجانبية (`_sidebar.blade.php`)** للوحة تحكم الإدارة Dash، مدمجة في `admin_layout.blade.php`.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** لا يوجد.

#### **المرحلة 2: بناء لوحة تحكم الإدارة Dash المخصصة (الأساسية) ووظائف الكتالوج (`PH-02`)**

*   **الواجهات التي سيتم التركيز عليها:**
    *   `UIR-PH02-ITEM-001`: **واجهة لوحة البيانات الرئيسية للإدارة (`DASH-ADMIN-HOME-001`)** - تنفيذ الهيكل البصري للبطاقات الإحصائية، وبناء وتكييف جميع الرسوم البيانية (المحددة في `UIUX-FR.md` ضمن `DASH-ADMIN-HOME-001`) لتعمل مبدئيًا ببيانات ثابتة/وهمية، ثم ربطها ببيانات ديناميكية حقيقية من `MOD-DASHBOARD-FEAT-002` و `TS-FR.md` عبر `dash_app.js`.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-DASHBOARD-FEAT-002` (لتوفير البيانات الديناميكية للرسوم البيانية)، `MOD-USER-MGMT` (لبيانات المستخدمين)، `MOD-CAR-CATALOG` (لبيانات السيارات الأولية).
    *   `UIR-PH02-ITEM-002`: **تطوير القائمة الجانبية الديناميكية (`_sidebar.blade.php`) والشريط العلوي (`_topbar.blade.php`)** للوحة تحكم الإدارة Dash بشكل كامل (مع عناصر قائمة بناءً على الأدوار والصلاحيات، وعرض الإشعارات).
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `spatie/laravel-permission`، `MOD-USER-MGMT` (لبيانات المستخدم)، `MOD-NOTIFICATION-FEAT-002` (لبيانات الإشعارات).
    *   `UIR-PH02-ITEM-003`: **واجهات CRUD كاملة (جداول عرض ونماذج) لإدارة البيانات الوصفية للسيارات** في لوحة التحكم Dash:
        *   `DASH-BRANDS-MGMT-001` (الماركات)
        *   `DASH-MODELS-MGMT-001` (الموديلات)
        *   `DASH-COLORS-MGMT-001` (الألوان)
        *   `DASH-YEARS-MGMT-001` (سنوات الصنع)
        *   `DASH-TRANSMISSIONS-MGMT-001` (أنواع ناقل الحركة)
        *   `DASH-FUELS-MGMT-001` (أنواع الوقود)
        *   (واجهة مشابهة لأنواع هياكل السيارات)
        *   `DASH-FEATURECATS-MGMT-001` (فئات الميزات)
        *   `DASH-CARFEATURES-MGMT-001` (ميزات السيارات)
        *   **الأولوية:** عالية.
        *   **الاعتماديات:** وظائف CRUD المقابلة في `MOD-CAR-CATALOG`.
    *   `UIR-PH02-ITEM-004`: **واجهة إضافة/تعديل السيارة (`DASH-CAR-CRUD-001`)** باستخدام Stepper (تكييف هيكل Stepper من `Dash/index.html` وملء كل خطوة بحقول Blade ديناميكية).
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-CAR-CATALOG-FEAT-018`, `spatie/laravel-medialibrary`.
    *   `UIR-PH02-ITEM-005`: **واجهة قائمة السيارات (`DASH-CAR-LIST-001`)** في لوحة التحكم Dash (جدول، بحث أساسي، فلترة بسيطة، ترقيم).
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** وظيفة عرض قائمة السيارات من `MOD-CAR-CATALOG`.
    *   `UIR-PH02-ITEM-006`: **واجهات إدارة الموظفين (`DASH-EMPLOYEES-LIST-001`) والأدوار والصلاحيات (`DASH-ROLES-PERMISSIONS-MGMT-001`)**.
        *   **الأولوية:** عالية.
        *   **الاعتماديات:** `MOD-USER-MGMT-FEAT-010`, `spatie/laravel-permission`.
    *   `UIR-PH02-ITEM-007`: **واجهة إعدادات النظام الأساسية (`DASH-SYSTEM-SETTINGS-001` - تبويب الإعدادات العامة و SEO)**.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `MOD-CORE-FEAT-003`.

#### **المرحلة 3: بناء الواجهة الخارجية للزوار (Public Visitor Interface - Blade) والوظائف الأساسية للعملاء (`PH-03`)**

*   **الواجهات التي سيتم التركيز عليها:**
    *   `UIR-PH03-ITEM-001`: **التخطيط العام للموقع (`SITE-LAYOUT-MAIN-001`)** والرأس والتذييل.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** لا يوجد.
    *   `UIR-PH03-ITEM-002`: **الصفحة الرئيسية للموقع (`SITE-HOME-001`)**.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-CMS-FEAT-002`, `MOD-CAR-CATALOG` (للسيارات المميزة), `MOD-PROMO-MGMT` (للعروض).
    *   `UIR-PH03-ITEM-003`: **صفحة قائمة السيارات (`SITE-CAR-LIST-001`)** مع الفلاتر المتقدمة.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-CAR-CATALOG-FEAT-001`, `MOD-CAR-CATALOG-FEAT-002`.
    *   `UIR-PH03-ITEM-004`: **صفحة تفاصيل السيارة (`SITE-CAR-DETAIL-001`)**.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-CAR-CATALOG-FEAT-003`.
    *   `UIR-PH03-ITEM-005`: **صفحات المصادقة للعملاء** (تسجيل الدخول `SITE-AUTH-LOGIN-001`, إنشاء حساب `SITE-AUTH-REGISTER-001`, تحقق OTP `SITE-AUTH-VERIFY-OTP-001`, استعادة كلمة المرور `SITE-AUTH-FORGOT-PASSWORD-001`, `SITE-AUTH-RESET-PASSWORD-001`).
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-USER-MGMT-FEAT-001`, `MOD-USER-MGMT-FEAT-001B`, `MOD-USER-MGMT-FEAT-002`, `MOD-USER-MGMT-FEAT-003`.
    *   `UIR-PH03-ITEM-006`: **صفحات عملية شراء السيارة كاش (`SITE-BUY-CASH-STEPX-001`)**.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-ORDER-MGMT-FEAT-003`, `MOD-ORDER-MGMT-FEAT-005`, `MOD-ORDER-MGMT-FEAT-006`.
    *   `UIR-PH03-ITEM-007`: **صفحات عملية طلب التمويل (`SITE-BUY-FINANCE-STEPX-001`)**.
        *   **الأولوية:** عالية.
        *   **الاعتماديات:** `MOD-ORDER-MGMT-FEAT-004`.
    *   `UIR-PH03-ITEM-008`: **الصفحات الثابتة الأساسية** (`SITE-STATIC-ABOUT-US-001`, `SITE-STATIC-PRIVACY-001`, `SITE-STATIC-TERMS-001`, `SITE-STATIC-CONTACT-US-001`, `SITE-STATIC-FAQ-001`).
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `MOD-CMS-FEAT-001`.
    *   `UIR-PH03-ITEM-009`: **صفحة "كيف أشتريها؟" (`SITE-HOW-TO-BUY-001`)**.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `MOD-ORDER-MGMT-FEAT-002`.
    *   `UIR-PH03-ITEM-010`: **صفحات عرض الخدمات (`SITE-SERVICES-LIST-001`) ونموذج طلبها**.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `MOD-SERVICE-MGMT-FEAT-001`, `FEAT-SERVICE-002`.
    *   `UIR-PH03-ITEM-011`: **صفحات عرض العروض (`SITE-PROMOTIONS-LIST-001`) وتفاصيلها (`SITE-PROMOTION-DETAIL-001`)**.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `MOD-PROMO-MGMT-FEAT-001`, `FEAT-PROMO-002`.
    *   `UIR-PH03-ITEM-012`: **صفحة مبيعات الشركات (`SITE-CORPORATE-SALES-001`) ونموذج الطلب**.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `MOD-CORP-SALES-FEAT-001`, `FEAT-CORP-002`.
    *   `UIR-PH03-ITEM-013`: **صفحات "اطلب سيارتك" (`SITE-REQUEST-CAR-STEPX-001`)**.
        *   **الأولوية:** عالية.
        *   **الاعتماديات:** `FEAT-REQCAR-001`, `MOD-CAR-CATALOG` (لبيانات الماركات والموديلات).

#### **المرحلة 4: بناء لوحة تحكم العملاء Dash المخصصة وإكمال لوحة تحكم الإدارة (`PH-04`)**

*   **الواجهات التي سيتم التركيز عليها:**
    *   `UIR-PH04-ITEM-001`: **الهيكل الأساسي للوحة تحكم العميل Dash (بما في ذلك `customer_layout.blade.php`, `_customer_sidebar.blade.php`, و `_topbar.blade.php` نسخة العميل)** بناءً على أصول Dash وتصميم `UIUX-FR.md`.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-USER-MGMT` (لتخصيص القائمة).
    *   `UIR-PH04-ITEM-002`: **الصفحة الرئيسية للوحة تحكم العميل (`DASH-CUSTOMER-HOME-001`)**.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-USER-MGMT-FEAT-004`.
    *   `UIR-PH04-ITEM-003`: **صفحة "طلباتي" (`DASH-CUSTOMER-ORDERS-001`) وتفاصيل الطلب (`DASH-CUSTOMER-ORDER-DETAIL-001`)** للعميل.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-USER-MGMT-FEAT-006`.
    *   `UIR-PH04-ITEM-004`: **صفحة "المفضلة" (`DASH-CUSTOMER-FAVORITES-001`)** للعميل.
        *   **الأولوية:** عالية.
        *   **الاعتماديات:** `MOD-CAR-CATALOG-FEAT-005B`.
    *   `UIR-PH04-ITEM-005`: **صفحة "الملف الشخصي" (`DASH-CUSTOMER-PROFILE-001`)** للعميل.
        *   **الأولوية:** عالية.
        *   **الاعتماديات:** `MOD-USER-MGMT-FEAT-005`.
    *   `UIR-PH04-ITEM-006`: **صفحات "رشح عميل", "المعاملات المالية", "الإشعارات"** في لوحة تحكم العميل.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `MOD-USER-MGMT-FEAT-008`, `MOD-USER-MGMT-FEAT-007`, `MOD-NOTIFICATION-FEAT-002`.
    *   `UIR-PH04-ITEM-007`: **واجهة إدارة الطلبات (كاش وتمويل) في لوحة تحكم الإدارة (`DASH-ORDERS-LIST-001`, `DASH-ORDER-DETAIL-001`)**.
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** `MOD-ORDER-MGMT-FEAT-007`, `FEAT-ORDER-008`.
    *   `UIR-PH04-ITEM-008`: **واجهة إدارة العملاء في لوحة تحكم الإدارة (`DASH-CUSTOMERS-LIST-001`, `DASH-CUSTOMER-DETAIL-001`)**.
        *   **الأولوية:** عالية.
        *   **الاعتماديات:** `MOD-USER-MGMT-FEAT-009`.
    *   `UIR-PH04-ITEM-009`: **واجهات إدارة المحتوى (الصفحات، البنرات) في لوحة التحكم Dash (`DASH-CMS-PAGES-LIST-001`, `DASH-CMS-BANNERS-LIST-001`)**.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `MOD-CMS-FEAT-001`, `MOD-CMS-FEAT-002`.
    *   `UIR-PH04-ITEM-010`: **واجهات إدارة الخدمات وفئاتها وطلباتها في لوحة التحكم Dash**.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `FEAT-SERVICE-003` إلى `FEAT-SERVICE-005`.
    *   `UIR-PH04-ITEM-011`: **واجهات إدارة العروض الترويجية في لوحة التحكم Dash**.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `FEAT-PROMO-003`.
    *   `UIR-PH04-ITEM-012`: **واجهات إدارة طلبات الشركات في لوحة التحكم Dash**.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `FEAT-CORP-003`.
    *   `UIR-PH04-ITEM-013`: **استكمال واجهة إعدادات النظام في لوحة التحكم Dash (`DASH-SYSTEM-SETTINGS-001` - باقي التبويبات)**.
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** `FEAT-ADMIN-003`.

#### **المرحلة 5: بناء تطبيق الموبايل (Mobile Application - Flutter) (إذا كان مطلوبًا) (`PH-05`)**

*   **الواجهات التي سيتم التركيز عليها:**
    *   `UIR-PH05-ITEM-001`: **الشاشات الأساسية للتطبيق** (البداية `FLUTTER-SPLASH-001`, المصادقة `FLUTTER-AUTH-*-001`, الرئيسية `FLUTTER-HOME-001`).
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** نقاط نهاية API المقابلة من `MOD-API`.
    *   `UIR-PH05-ITEM-002`: **شاشات كتالوج السيارات** (القائمة `FLUTTER-CAR-LIST-001`, الفلاتر `FLUTTER-CAR-FILTER-001`, التفاصيل `FLUTTER-CAR-DETAIL-001`).
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** نقاط نهاية API المقابلة من `MOD-API`.
    *   `UIR-PH05-ITEM-003`: **شاشات عمليات الشراء والطلبات** ("اطلب سيارتك" `FLUTTER-REQUEST-CAR-STEPX-001`, شراء كاش/تمويل `FLUTTER-BUY-*-STEPX-001`).
        *   **الأولوية:** حرجة.
        *   **الاعتماديات:** نقاط نهاية API المقابلة من `MOD-API`.
    *   `UIR-PH05-ITEM-004`: **شاشات لوحة تحكم العميل المصغرة** (طلباتي `FLUTTER-CUSTOMER-ORDERS-001`, المفضلة `FLUTTER-CUSTOMER-FAVORITES-001`, الملف الشخصي `FLUTTER-CUSTOMER-PROFILE-*-001`, الإشعارات `FLUTTER-NOTIFICATIONS-LIST-001`).
        *   **الأولوية:** عالية.
        *   **الاعتماديات:** نقاط نهاية API المقابلة من `MOD-API`.
    *   `UIR-PH05-ITEM-005`: **الشاشات الإضافية** (عرض الخدمات `FLUTTER-SERVICES-LIST-001`, العروض `FLUTTER-PROMOTIONS-LIST-001`, الصفحات الثابتة `FLUTTER-CMS-PAGE-001`, تواصل معنا `FLUTTER-CONTACT-US-001`).
        *   **الأولوية:** متوسطة.
        *   **الاعتماديات:** نقاط نهاية API المقابلة من `MOD-API`.

### نقاط المراجعة والتسليم المرحلي للواجهات

**(معرف القسم: `UIR-MILESTONES-001`)**

1.  **نهاية `PH-01`:** مراجعة الهيكل الأساسي للوحة تحكم الإدارة Dash وصفحة تسجيل الدخول.
2.  **نهاية `PH-02`:** مراجعة وظائف إدارة كتالوج السيارات الأساسية في لوحة تحكم الإدارة Dash، ولوحة البيانات الرئيسية. مراجعة اكتمال تكييف وظائف JavaScript الأساسية للوحة تحكم الإدارة (من `Dash/script.js` و `dash_app.js`)، بما في ذلك القائمة الجانبية، الرسوم البيانية، والـ Stepper.
3.  **نهاية `PH-03`:** مراجعة شاملة لواجهة الموقع العامة ووظائف العملاء الأساسية (التسجيل، تصفح السيارات، عمليات الشراء الأولية).
4.  **نهاية `PH-04`:** مراجعة شاملة للوحتَي تحكم الإدارة والعملاء بكامل وظائفهما.
5.  **نهاية `PH-05`:** مراجعة شاملة لتطبيق Flutter.

سيتم إجراء اختبارات قابلية الاستخدام (Usability Testing) واختبارات تجربة المستخدم (UX Testing) في نقاط رئيسية خلال هذه المراحل، خاصة بعد اكتمال أجزاء كبيرة من لوحات التحكم أو الموقع العام.

---
**END OF `UIR-FR.md`**
