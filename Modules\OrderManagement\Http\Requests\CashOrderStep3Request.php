<?php

namespace Modules\OrderManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * طلب التحقق من صحة بيانات الخطوة الثالثة لطلب شراء سيارة كاش
 * 
 * يتحقق من المستندات المرفوعة
 * بناءً على MOD-ORDER-MGMT-FEAT-003 في REQ-FR.md
 */
class CashOrderStep3Request extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return auth()->check() && session()->has('cash_order_car_id');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // صورة الهوية الوطنية - الوجه الأمامي
            'national_id_front' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB
            ],
            
            // صورة الهوية الوطنية - الوجه الخلفي
            'national_id_back' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB
            ],
            
            // رخصة القيادة
            'driving_license' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB
            ],
            
            // مستندات إضافية (اختيارية)
            'additional_documents' => [
                'nullable',
                'array',
                'max:3' // حد أقصى 3 مستندات إضافية
            ],
            
            'additional_documents.*' => [
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB لكل ملف
            ],
            
            // أوصاف المستندات الإضافية
            'additional_documents_descriptions' => [
                'nullable',
                'array'
            ],
            
            'additional_documents_descriptions.*' => [
                'nullable',
                'string',
                'max:255'
            ],
            
            // تأكيد صحة المستندات
            'documents_authenticity_confirmed' => [
                'required',
                'accepted'
            ],
            
            // الموافقة على معالجة البيانات الشخصية
            'data_processing_consent' => [
                'required',
                'accepted'
            ]
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'national_id_front.required' => 'صورة الهوية الوطنية (الوجه الأمامي) مطلوبة',
            'national_id_front.file' => 'يجب أن تكون صورة الهوية الوطنية (الوجه الأمامي) ملفاً صالحاً',
            'national_id_front.mimes' => 'صورة الهوية الوطنية (الوجه الأمامي) يجب أن تكون من نوع: jpeg, png, jpg, webp, pdf',
            'national_id_front.max' => 'حجم صورة الهوية الوطنية (الوجه الأمامي) يجب ألا يزيد عن 2 ميجابايت',
            
            'national_id_back.required' => 'صورة الهوية الوطنية (الوجه الخلفي) مطلوبة',
            'national_id_back.file' => 'يجب أن تكون صورة الهوية الوطنية (الوجه الخلفي) ملفاً صالحاً',
            'national_id_back.mimes' => 'صورة الهوية الوطنية (الوجه الخلفي) يجب أن تكون من نوع: jpeg, png, jpg, webp, pdf',
            'national_id_back.max' => 'حجم صورة الهوية الوطنية (الوجه الخلفي) يجب ألا يزيد عن 2 ميجابايت',
            
            'driving_license.required' => 'صورة رخصة القيادة مطلوبة',
            'driving_license.file' => 'يجب أن تكون رخصة القيادة ملفاً صالحاً',
            'driving_license.mimes' => 'رخصة القيادة يجب أن تكون من نوع: jpeg, png, jpg, webp, pdf',
            'driving_license.max' => 'حجم رخصة القيادة يجب ألا يزيد عن 2 ميجابايت',
            
            'additional_documents.array' => 'المستندات الإضافية يجب أن تكون مصفوفة',
            'additional_documents.max' => 'لا يمكن رفع أكثر من 3 مستندات إضافية',
            
            'additional_documents.*.file' => 'كل مستند إضافي يجب أن يكون ملفاً صالحاً',
            'additional_documents.*.mimes' => 'المستندات الإضافية يجب أن تكون من نوع: jpeg, png, jpg, webp, pdf',
            'additional_documents.*.max' => 'حجم كل مستند إضافي يجب ألا يزيد عن 2 ميجابايت',
            
            'additional_documents_descriptions.*.max' => 'وصف المستند يجب ألا يزيد عن 255 حرف',
            
            'documents_authenticity_confirmed.required' => 'يجب تأكيد صحة المستندات',
            'documents_authenticity_confirmed.accepted' => 'يجب تأكيد صحة المستندات',
            
            'data_processing_consent.required' => 'يجب الموافقة على معالجة البيانات الشخصية',
            'data_processing_consent.accepted' => 'يجب الموافقة على معالجة البيانات الشخصية'
        ];
    }

    /**
     * أسماء الحقول المخصصة للعرض في رسائل الخطأ
     */
    public function attributes(): array
    {
        return [
            'national_id_front' => 'صورة الهوية الوطنية (الوجه الأمامي)',
            'national_id_back' => 'صورة الهوية الوطنية (الوجه الخلفي)',
            'driving_license' => 'رخصة القيادة',
            'additional_documents' => 'المستندات الإضافية',
            'additional_documents_descriptions' => 'أوصاف المستندات الإضافية',
            'documents_authenticity_confirmed' => 'تأكيد صحة المستندات',
            'data_processing_consent' => 'الموافقة على معالجة البيانات الشخصية'
        ];
    }

    /**
     * التحقق من صحة الملفات المرفوعة
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من أن الملفات المرفوعة ليست فارغة أو تالفة
            $requiredFiles = ['national_id_front', 'national_id_back', 'driving_license'];
            
            foreach ($requiredFiles as $fileField) {
                $file = $this->file($fileField);
                if ($file && !$file->isValid()) {
                    $validator->errors()->add($fileField, "الملف المرفوع تالف أو غير صالح");
                }
            }
            
            // التحقق من المستندات الإضافية
            if ($this->hasFile('additional_documents')) {
                $additionalFiles = $this->file('additional_documents');
                foreach ($additionalFiles as $index => $file) {
                    if ($file && !$file->isValid()) {
                        $validator->errors()->add("additional_documents.{$index}", "المستند الإضافي رقم " . ($index + 1) . " تالف أو غير صالح");
                    }
                }
            }
        });
    }
}
