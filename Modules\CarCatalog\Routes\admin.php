<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['web', 'auth:web', 'verified', 'role:Super Admin|Employee', 'permission:manage_car_metadata'])
    ->prefix(config('modules.AppConfig.admin_prefix', 'admin')) // استخدام تكوين للبادئة الإدارية إذا كان متاحًا، وإلا استخدام 'admin' كقيمة افتراضية
    ->name('admin.')
    ->group(function () {
        // مسارات إدارة الماركات
        Route::resource('brands', \Modules\CarCatalog\Http\Controllers\Admin\BrandController::class);

        // مسارات إدارة الموديلات
        Route::resource('models', \Modules\CarCatalog\Http\Controllers\Admin\CarModelController::class);

        // مسارات إدارة الألوان
        Route::resource('colors', \Modules\CarCatalog\Http\Controllers\Admin\ColorController::class);

        // مسارات إدارة سنوات الصنع
        Route::resource('years', \Modules\CarCatalog\Http\Controllers\Admin\ManufacturingYearController::class);

        // مسارات إدارة أنواع ناقل الحركة
        Route::resource('transmission-types', \Modules\CarCatalog\Http\Controllers\Admin\TransmissionTypeController::class)->parameters(['transmission-types' => 'transmissiontype']);

        // مسارات إدارة أنواع الوقود
        Route::resource('fuel-types', \Modules\CarCatalog\Http\Controllers\Admin\FuelTypeController::class)->parameters(['fuel-types' => 'fueltype']);

        // مسارات إدارة أنواع هياكل السيارات
        Route::resource('body-types', \Modules\CarCatalog\Http\Controllers\Admin\BodyTypeController::class)->parameters(['body-types' => 'bodytype']);

        // مسارات إدارة فئات الميزات
        Route::resource('feature-categories', \Modules\CarCatalog\Http\Controllers\Admin\FeatureCategoryController::class)->parameters(['feature-categories' => 'featurecategory']);

        // مسارات إدارة ميزات السيارات
        Route::resource('car-features', \Modules\CarCatalog\Http\Controllers\Admin\CarFeatureController::class)->parameters(['car-features' => 'carfeature']);

        // مسار AJAX لجلب الموديلات حسب الماركة (يحتاجه في صفحات إنشاء وتعديل السيارات)
        Route::get('brands/{brand}/models', [\Modules\CarCatalog\Http\Controllers\Admin\CarController::class, 'getModelsByBrand'])
            ->name('brands.models.get');
    });

// مسارات إدارة السيارات (تتطلب صلاحية إدارة السيارات)
Route::middleware(['web', 'auth:web', 'verified', 'role:Super Admin|Employee', 'permission:manage_cars_admin'])
    ->prefix(config('modules.AppConfig.admin_prefix', 'admin'))
    ->name('admin.')
    ->group(function () {
        // مسارات إدارة السيارات
        Route::resource('cars', \Modules\CarCatalog\Http\Controllers\Admin\CarController::class);

        // صفحة تشخيص مشكلة الـ form submission
        Route::get('cars/debug', function() {
            \Illuminate\Support\Facades\Log::info('=== CARS DEBUG PAGE ACCESSED ===');
            \Illuminate\Support\Facades\Log::info('User: ' . (auth()->check() ? auth()->user()->email : 'Guest'));
            return view('car_catalog::admin.cars.debug');
        })->name('cars.debug');

        // مسار اختبار لتشخيص مشكلة الـ form submission
        Route::post('cars/test-submission', function(\Illuminate\Http\Request $request) {
            \Illuminate\Support\Facades\Log::info('=== TEST SUBMISSION ROUTE ===');
            \Illuminate\Support\Facades\Log::info('Method: ' . $request->method());
            \Illuminate\Support\Facades\Log::info('All Input: ', $request->all());
            \Illuminate\Support\Facades\Log::info('Headers: ', $request->headers->all());

            return response()->json([
                'success' => true,
                'message' => 'Test submission received successfully',
                'data' => $request->all()
            ]);
        })->name('cars.test-submission');
    });


