@extends('dashboard::layouts.admin_layout')

@section('title', 'عرض نوع هيكل السيارة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid">
    {{-- عنوان الصفحة --}}
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">عرض نوع هيكل السيارة: {{ $bodytype->name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.body-types.index') }}">أنواع هياكل السيارات</a></li>
                        <li class="breadcrumb-item active">عرض</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {{-- تفاصيل نوع هيكل السيارة --}}
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تفاصيل نوع هيكل السيارة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <th width="200">المعرف:</th>
                                    <td>{{ $bodytype->id }}</td>
                                </tr>
                                <tr>
                                    <th>اسم نوع هيكل السيارة:</th>
                                    <td>{{ $bodytype->name }}</td>
                                </tr>
                                <tr>
                                    <th>الحالة:</th>
                                    <td>
                                        <span class="badge rounded-pill bg-{{ $bodytype->status ? 'success' : 'danger' }}">
                                            {{ $bodytype->status ? 'نشط' : 'غير نشط' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>عدد السيارات المرتبطة:</th>
                                    <td>
                                        <span class="badge bg-info">{{ $bodytype->cars_count }}</span>
                                    </td>
                                </tr>
                                @if($bodytype->description)
                                <tr>
                                    <th>الوصف:</th>
                                    <td>{{ $bodytype->description }}</td>
                                </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            {{-- الإجراءات --}}
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">الإجراءات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.body-types.edit', $bodytype) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="{{ route('admin.body-types.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        @if($bodytype->cars_count == 0)
                            <form action="{{ route('admin.body-types.destroy', $bodytype) }}" 
                                  method="POST" 
                                  onsubmit="return confirm('هل أنت متأكد من حذف نوع هيكل السيارة هذا؟')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </form>
                        @else
                            <button type="button" class="btn btn-danger" disabled title="لا يمكن الحذف - مرتبط بسيارات">
                                <i class="fas fa-trash"></i> حذف (غير متاح)
                            </button>
                        @endif
                    </div>
                </div>
            </div>

            {{-- معلومات إضافية --}}
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">معلومات إضافية</h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <i class="fas fa-car fa-3x text-muted mb-3"></i>
                        <p class="text-muted">
                            نوع هيكل السيارة هذا {{ $bodytype->status ? 'نشط' : 'غير نشط' }} حاليًا
                            @if($bodytype->cars_count > 0)
                                ومرتبط بـ {{ $bodytype->cars_count }} سيارة
                            @else
                                وغير مرتبط بأي سيارات
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
