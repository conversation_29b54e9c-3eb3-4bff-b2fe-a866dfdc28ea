<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تحديث موديل سيارة موجود.
 *
 * يتحقق هذا الطلب من صحة بيانات تعديل موديل سيارة موجود
 */
class UpdateCarModelRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'brand_id' => 'required|exists:brands,id',
            'name'     => [
                'required',
                'string',
                'max:100',
                Rule::unique('car_models')
                    ->where('brand_id', $this->input('brand_id'))
                    ->ignore($this->model->id),
            ],
            'status' => 'required|boolean',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة للقواعد المحددة.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'brand_id.required' => 'الماركة مطلوبة',
            'brand_id.exists'   => 'الماركة المحددة غير موجودة',
            'name.required'     => 'اسم الموديل مطلوب',
            'name.string'       => 'اسم الموديل يجب أن يكون نصًا',
            'name.max'          => 'اسم الموديل يجب ألا يتجاوز 100 حرف',
            'name.unique'       => 'اسم الموديل موجود بالفعل لهذه الماركة',
            'status.required'   => 'حالة الموديل مطلوبة',
            'status.boolean'    => 'حالة الموديل يجب أن تكون صحيحة أو خاطئة',
        ];
    }
}
