/*
 * Site App CSS - ملف الأنماط الرئيسي للموقع العام
 * يحتوي على الأنماط المخصصة للموقع العام بما يتوافق مع الهوية البصرية
 */

/* استيراد الخطوط */
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* متغيرات إضافية للموقع العام */
:root {
    /* الألوان الأساسية - مطابقة لـ brand-identity.css */
    --primary-color: #0f172a;
    --secondary-color: #1e3a8a;
    --accent-color: #f97316;
    --light-bg: #f8fafc;
    --dark-bg: #020617;
    --text-color: #334155;
    --text-light: #f8fafc;
    --text-muted: #94a3b8;
    --border-color: #e5e7eb;
    --transition-speed: 0.3s;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* الخطوط */
    --font-family-arabic: 'IBM Plex Sans Arabic', sans-serif;
    --font-family-english: 'Roboto', sans-serif;

    /* أحجام الخطوط */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* الحدود */
    --border-width: 1px;
    --border-color: #e5e7eb;
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    /* الشفافية */
    --opacity-disabled: 0.6;
    --opacity-hover: 0.8;
}

/* إعدادات عامة */
* {
    font-family: var(--font-family-arabic);
}

body {
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-bg);
}

/* تحسينات للعناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

/* تحسينات للفقرات */
p {
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

/* تحسينات للروابط */
a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: all var(--transition-speed) ease;
}

a:hover {
    color: var(--accent-color);
}

/* تحسينات للقوائم */
ul, ol {
    margin-bottom: var(--spacing-md);
    padding-right: var(--spacing-lg);
}

li {
    margin-bottom: var(--spacing-xs);
}

/* تحسينات للصور */
img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-md);
}

/* أنماط مخصصة للموقع العام */

/* بطاقات السيارات */
.car-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-speed) ease;
    border: none;
}

.car-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.car-card-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.car-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-speed) ease;
    border-radius: 0;
}

.car-card:hover .car-card-image img {
    transform: scale(1.05);
}

.car-card-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: var(--accent-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.car-card-body {
    padding: var(--spacing-lg);
}

.car-card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.car-card-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
}

.car-card-price .original-price {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    text-decoration: line-through;
    margin-left: var(--spacing-sm);
}

.car-card-specs {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.car-spec {
    display: flex;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.car-spec i {
    margin-left: var(--spacing-xs);
    color: var(--secondary-color);
}

.car-card-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.favorite-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: var(--font-size-lg);
    transition: all var(--transition-speed) ease;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.favorite-btn:hover,
.favorite-btn.active {
    color: #e74c3c;
    background-color: rgba(231, 76, 60, 0.1);
}

/* أقسام الصفحة الرئيسية */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: var(--spacing-2xl) 0;
    margin-bottom: var(--spacing-2xl);
}

.hero-content {
    text-align: center;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: white;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

.hero-cta {
    display: inline-flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    justify-content: center;
}

/* أقسام المحتوى */
.content-section {
    padding: var(--spacing-2xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-muted);
    max-width: 600px;
    margin: 0 auto;
}

/* شريط الفلاتر */
.filters-bar {
    background: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
}

.filter-group {
    margin-bottom: var(--spacing-md);
}

.filter-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
    display: block;
}

/* نماذج البحث */
.search-form {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* أزرار مخصصة للموقع */
.btn-site-primary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    border: none;
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: all var(--transition-speed) ease;
}

.btn-site-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-site-outline {
    background: transparent;
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: all var(--transition-speed) ease;
}

.btn-site-outline:hover {
    background: var(--secondary-color);
    color: white;
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }

    .car-card-specs {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .car-card-actions {
        flex-direction: column;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }
}

/* تحسينات للطباعة */
@media print {
    .hero-section,
    .filters-bar,
    .btn,
    .car-card-actions {
        display: none !important;
    }

    .car-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }
}

/* تحسينات الوصولية */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسينات للتركيز */
*:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
