<?php

namespace Modules\Dashboard\Http\Controllers\Admin;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use Modules\Core\Http\Controllers\BaseController;
use Modules\Core\Models\Setting;
use Modules\Core\Services\SettingsService;

/**
 * متحكم إعدادات النظام
 *
 * يتعامل هذا المتحكم مع عرض وتحديث إعدادات النظام العامة و SEO
 */
class SystemSettingsController extends BaseController
{
    protected SettingsService $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * عرض صفحة إعدادات النظام
     */
    public function index(): View
    {
        // جلب جميع الإعدادات التي تنتمي لمجموعة general و seo
        $settings = Setting::whereIn('group_name', ['general', 'seo'])
            ->get()
            ->keyBy('key');

        return view('dashboard::admin.settings.system', compact('settings'));
    }

    /**
     * تحديث إعدادات النظام
     */
    public function update(Request $request): RedirectResponse
    {
        // التحقق من صحة المدخلات الأساسية
        $request->validate([
            'site_name' => 'required|string|max:255',
            'admin_email' => 'required|email|max:255',
            'main_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'default_currency' => 'nullable|string|max:10',
            'vat_percentage' => 'nullable|numeric|min:0|max:100',
            'default_booking_fee' => 'nullable|numeric|min:0',
            'after_sales_service_text' => 'nullable|string|max:1000',
            'seo_default_keywords' => 'nullable|string|max:500',
            'seo_default_description' => 'nullable|string|max:500',
            'ga_tracking_id' => 'nullable|string|max:50',
        ]);

        // جلب الإعدادات الحالية للمقارنة
        $currentSettings = Setting::whereIn('group_name', ['general', 'seo'])
            ->get()
            ->keyBy('key')
            ->map(function($setting) {
                return $setting->value;
            })
            ->toArray();

        $changedSettings = [];

        // التكرار على المدخلات وحفظ كل إعداد
        foreach ($request->except(['_token', '_method']) as $key => $value) {
            // تحديد المجموعة بناءً على مفتاح الإعداد
            $group = $this->determineSettingGroup($key);

            // تحديد نوع الحقل
            $type = $this->determineSettingType($key);

            // تحديد الاسم المعروض
            $displayName = $this->getDisplayName($key);

            // تتبع التغييرات للإشعارات
            if (isset($currentSettings[$key]) && $currentSettings[$key] != $value) {
                $changedSettings[$key] = [
                    'old_value' => $currentSettings[$key],
                    'new_value' => $value,
                    'display_name' => $displayName
                ];
            }

            // حفظ الإعداد
            $this->settingsService->set(
                $key,
                $value,
                $group,
                $displayName,
                $type
            );

            // مسح cache الإعداد
            Cache::forget("setting.{$key}");
        }

        // TODO: PH02-TASK-025 - إرسال إشعار عند تحديث إعدادات النظام الحرجة
        // if (!empty($changedSettings)) {
        //     $criticalSettings = ['site_name', 'admin_email', 'vat_percentage', 'default_currency'];
        //     $criticalChanges = array_intersect_key($changedSettings, array_flip($criticalSettings));
        //
        //     if (!empty($criticalChanges)) {
        //         NotificationService::send([
        //             'type' => 'system_settings_updated',
        //             'title' => 'تم تحديث إعدادات النظام الحرجة',
        //             'message' => 'تم تحديث إعدادات مهمة في النظام',
        //             'data' => [
        //                 'changed_settings' => $criticalChanges,
        //                 'updated_by' => auth()->user()->name,
        //                 'ip_address' => request()->ip()
        //             ],
        //             'recipients' => ['super_admin'],
        //             'channels' => ['database', 'email'],
        //             'priority' => 'high'
        //         ]);
        //     }
        // }

        return redirect()
            ->route('admin.settings.system.index')
            ->with('success', 'تم حفظ الإعدادات بنجاح');
    }

    /**
     * تحديد مجموعة الإعداد بناءً على المفتاح
     */
    private function determineSettingGroup(string $key): string
    {
        if (str_starts_with($key, 'seo_') || str_starts_with($key, 'ga_')) {
            return 'seo';
        }

        return 'general';
    }

    /**
     * تحديد نوع الإعداد بناءً على المفتاح
     */
    private function determineSettingType(string $key): string
    {
        $textareaFields = ['address', 'after_sales_service_text', 'seo_default_keywords', 'seo_default_description'];
        $numberFields = ['vat_percentage', 'default_booking_fee'];
        $emailFields = ['admin_email'];

        if (in_array($key, $textareaFields)) {
            return 'textarea';
        }

        if (in_array($key, $numberFields)) {
            return 'number';
        }

        if (in_array($key, $emailFields)) {
            return 'email';
        }

        return 'text';
    }

    /**
     * الحصول على الاسم المعروض للإعداد
     */
    private function getDisplayName(string $key): string
    {
        $displayNames = [
            'site_name' => 'اسم الموقع',
            'admin_email' => 'البريد الإلكتروني للإدارة',
            'main_phone' => 'رقم الهاتف الرئيسي',
            'address' => 'العنوان',
            'default_currency' => 'العملة الافتراضية',
            'vat_percentage' => 'نسبة ضريبة القيمة المضافة (%)',
            'default_booking_fee' => 'رسوم الحجز الافتراضية',
            'after_sales_service_text' => 'نص خدمة ما بعد البيع',
            'seo_default_keywords' => 'الكلمات المفتاحية الافتراضية',
            'seo_default_description' => 'الوصف الافتراضي للصفحات',
            'ga_tracking_id' => 'معرف Google Analytics',
        ];

        return $displayNames[$key] ?? $key;
    }
}
