@extends('dashboard::layouts.admin_layout')

@section('title', 'إدارة فئات الميزات')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h4 class="fw-bold mb-0" style="color: var(--primary-color);">إدارة فئات الميزات</h4>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb brand-breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="brand-link">لوحة التحكم</a>
                    </li>
                    <li class="breadcrumb-item">إدارة السيارات</li>
                    <li class="breadcrumb-item active" aria-current="page">فئات الميزات</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.feature-categories.create') }}" class="btn btn-brand-primary">
                <i class="fas fa-plus me-1"></i> إضافة فئة ميزة جديدة
            </a>
        </div>
    </div>

    {{-- عرض رسائل النجاح والخطأ --}}
    @if(session('success'))
        <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {{-- نموذج البحث والفلترة --}}
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.feature-categories.index') }}" class="mb-0">
                <div class="row gx-2 gy-2 align-items-center">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="بحث بالاسم..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">كل الحالات</option>
                            <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>نشط</option>
                            <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-brand-info w-100">بحث</button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ route('admin.feature-categories.index') }}" class="btn btn-brand-secondary w-100">إعادة تعيين</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- جدول فئات الميزات --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">قائمة فئات الميزات</h5>
        </div>
        <div class="card-body table-responsive p-0">
            <table class="table table-hover recent-activity-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم فئة الميزة</th>
                        <th>الحالة</th>
                        <th>ترتيب العرض</th>
                        <th>عدد الميزات المرتبطة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($featureCategories as $featureCategory)
                        <tr>
                            <td>{{ $featureCategory->id }}</td>
                            <td>
                                <div>
                                    <strong>{{ $featureCategory->name }}</strong>
                                    @if($featureCategory->description)
                                        <br><small class="text-muted">{{ Str::limit($featureCategory->description, 50) }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <span class="badge rounded-pill bg-{{ $featureCategory->status ? 'success' : 'danger' }}">
                                    {{ $featureCategory->status ? 'نشط' : 'غير نشط' }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ $featureCategory->display_order }}</span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $featureCategory->features_count }}</span>
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('admin.feature-categories.show', $featureCategory) }}" class="btn btn-sm btn-info me-1 action-btn view" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.feature-categories.edit', $featureCategory) }}" class="btn btn-sm btn-warning me-1 action-btn edit" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($featureCategory->features_count == 0)
                                        <form action="{{ route('admin.feature-categories.destroy', $featureCategory) }}" method="POST" class="d-inline"
                                              onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف فئة الميزة هذه؟');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger action-btn delete" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    @else
                                        <button type="button" class="btn btn-sm btn-secondary action-btn" title="لا يمكن الحذف - مرتبط بميزات" disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-list-alt fa-3x mb-3"></i>
                                    <p>لا توجد فئات ميزات مسجلة</p>
                                    <a href="{{ route('admin.feature-categories.create') }}" class="btn btn-primary">
                                        إضافة فئة ميزة جديدة
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

    </div>

    {{-- ترقيم الصفحات --}}
    <div class="mt-3">
        {{ $featureCategories->appends(request()->query())->links('pagination::bootstrap-5') }}
    </div>
</div>
@endsection
