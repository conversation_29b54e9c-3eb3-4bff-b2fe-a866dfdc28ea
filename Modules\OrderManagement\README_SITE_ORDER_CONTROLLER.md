# Site Order Controller Implementation - OrderManagement Module

## Overview
تم تنفيذ `SiteOrderController` لإدارة عملية شراء السيارات كاش متعددة المراحل في الموقع العام بناءً على المتطلبات المحددة في `UIUX-FR.md` و `REQ-FR.md`.

## Features Implemented

### 1. SiteOrderController Class
- **Location**: `Modules/OrderManagement/Http/Controllers/Site/SiteOrderController.php`
- **Purpose**: إدارة عملية شراء السيارة كاش عبر 4 خطوات متتالية
- **Security**: تطبيق `auth` middleware للتأكد من تسجيل دخول المستخدم

### 2. Controller Methods

#### Main Step Methods
1. **showCashOrderStep1PersonalDetails($request, $carId)**
   - عرض صفحة البيانات الشخصية
   - جلب تفاصيل السيارة مع العلاقات
   - ملء البيانات تلقائياً من ملف المستخدم
   - حفظ معرف السيارة في الجلسة

2. **showCashOrderStep2BookingPayment($request)**
   - عرض صفحة تفاصيل الحجز والدفع
   - حساب مبلغ الحجز (10% أو 5000 ريال كحد أدنى)
   - عرض خيارات طريقة الدفع

3. **showCashOrderStep3Documents($request)**
   - عرض صفحة رفع المستندات
   - رفع الهوية الوطنية (الوجهين)
   - رفع رخصة القيادة

4. **showCashOrderStep4ReviewConfirm($request)**
   - عرض صفحة المراجعة والتأكيد النهائي
   - ملخص شامل للطلب
   - التأكيد النهائي قبل الدفع

#### Helper Methods
- **getSelectedCarSummary()**: الحصول على ملخص السيارة المختارة
- **isStepValid($step)**: التحقق من صحة خطوة معينة
- **calculateReservationAmount($carPrice)**: حساب مبلغ الحجز
- **clearOrderSession()**: تنظيف بيانات الجلسة
- **cancelOrder()**: إلغاء عملية الطلب

### 3. Routes Configuration
تم إضافة Routes في `Modules/OrderManagement/Routes/web.php`:

```php
Route::prefix('site/order')->name('site.order.')->group(function() {
    Route::prefix('cash')->name('cash.')->group(function() {
        Route::get('step1/{car}', 'Site\SiteOrderController@showCashOrderStep1PersonalDetails')->name('step1');
        Route::get('step2', 'Site\SiteOrderController@showCashOrderStep2BookingPayment')->name('step2');
        Route::get('step3', 'Site\SiteOrderController@showCashOrderStep3Documents')->name('step3');
        Route::get('step4', 'Site\SiteOrderController@showCashOrderStep4ReviewConfirm')->name('step4');
        Route::post('cancel', 'Site\SiteOrderController@cancelOrder')->name('cancel');
    });
});
```

### 4. Blade Views Created

#### Step 1: Personal Details
- **File**: `step1_personal.blade.php`
- **Features**:
  - نموذج البيانات الشخصية مع الملء التلقائي
  - مؤشر التقدم (Stepper)
  - ملخص السيارة المختارة
  - التحقق من صحة البيانات

#### Step 2: Booking & Payment
- **File**: `step2_booking_payment.blade.php`
- **Features**:
  - عرض تفاصيل الأسعار
  - خيارات طريقة الدفع (إلكتروني/في المعرض)
  - ضمانات الأمان
  - شروط وأحكام الحجز

#### Step 3: Documents Upload
- **File**: `step3_documents.blade.php`
- **Features**:
  - رفع المستندات بـ Drag & Drop
  - التحقق من نوع وحجم الملفات
  - معاينة الملفات المرفوعة
  - إرشادات واضحة للمستندات المطلوبة

#### Step 4: Review & Confirm
- **File**: `step4_review_confirm.blade.php`
- **Features**:
  - ملخص شامل للطلب
  - إمكانية تعديل أي خطوة
  - ملخص الأسعار
  - التأكيد النهائي مع الشروط

### 5. Session Management
تم استخدام Laravel Session لحفظ بيانات العملية:
- `cash_order_car_id`: معرف السيارة المختارة
- `cash_order_step`: الخطوة الحالية
- `cash_order_reservation_amount`: مبلغ الحجز
- `cash_order_personal_data`: البيانات الشخصية
- `cash_order_payment_data`: بيانات الدفع
- `cash_order_documents_data`: بيانات المستندات

### 6. Security Features
- **Authentication**: تطبيق `auth` middleware على جميع الطرق
- **Validation**: التحقق من وجود السيارة وحالتها
- **Session Security**: التحقق من صحة بيانات الجلسة
- **File Upload Security**: التحقق من نوع وحجم الملفات

### 7. UI/UX Features
- **Responsive Design**: تصميم متجاوب لجميع الأجهزة
- **Progress Indicator**: مؤشر تقدم واضح للخطوات
- **Car Summary**: عرض ملخص السيارة في جميع الخطوات
- **Form Validation**: التحقق من صحة البيانات في الوقت الفعلي
- **File Upload UX**: واجهة سهلة لرفع الملفات
- **Arabic RTL**: دعم كامل للغة العربية

## Technical Implementation Details

### Data Flow
1. المستخدم يختار السيارة من صفحة التفاصيل
2. يتم توجيهه للخطوة الأولى مع معرف السيارة
3. في كل خطوة يتم حفظ البيانات في الجلسة
4. التحقق من صحة الخطوات والبيانات
5. عرض ملخص شامل في الخطوة الأخيرة
6. التأكيد النهائي وتوجيه للدفع

### Error Handling
- التحقق من وجود السيارة وحالتها
- التحقق من صحة بيانات الجلسة
- رسائل خطأ واضحة للمستخدم
- إعادة توجيه آمنة في حالة الأخطاء

### Performance Considerations
- استخدام Eager Loading لتحميل العلاقات
- تحسين استعلامات قاعدة البيانات
- تحميل الصور بكفاءة
- استخدام Session بدلاً من قاعدة البيانات للبيانات المؤقتة

## Next Steps
1. تنفيذ منطق معالجة الطلب النهائي
2. تكامل مع بوابة الدفع
3. إرسال الإشعارات والرسائل
4. تنفيذ FormRequests للتحقق من صحة البيانات
5. إضافة اختبارات الوحدة

## Dependencies
- `Modules\CarCatalog\Models\Car`: للوصول لبيانات السيارات
- `Modules\OrderManagement\Models\Order`: لحفظ الطلبات
- `Laravel Session`: لإدارة بيانات العملية
- `Laravel Auth`: للمصادقة والتحقق من المستخدم

## Route Examples
```
GET /site/order/cash/step1/{car_id} - الخطوة الأولى
GET /site/order/cash/step2 - الخطوة الثانية  
GET /site/order/cash/step3 - الخطوة الثالثة
GET /site/order/cash/step4 - الخطوة الرابعة
POST /site/order/cash/cancel - إلغاء الطلب
```

## Notes
- جميع الطرق تتطلب تسجيل دخول المستخدم
- يتم التحقق من حالة السيارة (متاحة وغير مباعة)
- البيانات محفوظة في الجلسة حتى التأكيد النهائي
- تصميم متجاوب ومتوافق مع الأجهزة المختلفة
