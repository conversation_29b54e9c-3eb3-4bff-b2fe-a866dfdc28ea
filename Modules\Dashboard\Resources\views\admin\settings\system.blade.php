@extends('dashboard::layouts.admin_layout')

@section('title', 'إعدادات النظام')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إعدادات النظام',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إعدادات النظام', 'active' => true]
        ]
    ])

    {{-- عرض رسائل النجاح --}}
    @if(session('success'))
        <div class="brand-alert brand-alert-success alert-dismissible fade show brand-fade-in" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {{-- عرض أخطاء التحقق --}}
    @if($errors->any())
        <div class="brand-alert brand-alert-danger alert-dismissible fade show brand-fade-in" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>يرجى تصحيح الأخطاء التالية:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="brand-card brand-fade-in">
        <div class="brand-card-header">
            <div class="d-flex align-items-center">
                <div class="brand-icon-wrapper me-3">
                    <i class="fas fa-sliders-h"></i>
                </div>
                <div>
                    <h5 class="mb-1 fw-bold">إعدادات النظام</h5>
                    <small class="opacity-75">قم بتكوين الإعدادات العامة وإعدادات SEO للموقع</small>
                </div>
            </div>
        </div>
        <div class="brand-card-body">
            {{-- نظام التبويبات --}}
            <ul class="nav nav-tabs border-0 mb-4" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active border-0 rounded-pill me-2 px-4" id="general-tab" data-bs-toggle="tab" data-bs-target="#general"
                            type="button" role="tab" aria-controls="general" aria-selected="true"
                            style="background-color: var(--secondary-color); color: white;">
                        <i class="fas fa-cog me-2"></i>الإعدادات العامة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link border-0 rounded-pill me-2 px-4" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial"
                            type="button" role="tab" aria-controls="financial" aria-selected="false"
                            style="background-color: var(--light-bg); color: var(--text-color);">
                        <i class="fas fa-dollar-sign me-2"></i>الإعدادات المالية
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link border-0 rounded-pill px-4" id="seo-tab" data-bs-toggle="tab" data-bs-target="#seo"
                            type="button" role="tab" aria-controls="seo" aria-selected="false"
                            style="background-color: var(--light-bg); color: var(--text-color);">
                        <i class="fas fa-search me-2"></i>إعدادات SEO
                    </button>
                </li>
            </ul>

            <form method="POST" action="{{ route('admin.settings.system.update') }}" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="tab-content mt-4" id="settingsTabsContent">
                    {{-- تبويب الإعدادات العامة --}}
                    <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                        {{-- قسم المعلومات الأساسية --}}
                        <div class="mb-5">
                            <div class="d-flex align-items-center mb-4">
                                <div class="brand-icon-wrapper brand-icon-accent me-3">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-1" style="color: var(--primary-color);">المعلومات الأساسية</h6>
                                    <small class="text-muted">الإعدادات الأساسية للموقع والتواصل</small>
                                </div>
                            </div>

                            <div class="row">
                                {{-- اسم الموقع --}}
                                <div class="col-lg-6 mb-4">
                                    <label for="site_name" class="brand-form-label">
                                        <i class="fas fa-globe me-1 text-primary"></i>
                                        اسم الموقع <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="brand-form-control @error('site_name') is-invalid @enderror"
                                           id="site_name" name="site_name"
                                           value="{{ old('site_name', $settings['site_name']->value ?? '') }}"
                                           placeholder="أدخل اسم الموقع" required>
                                    @error('site_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">الاسم الذي سيظهر في عنوان المتصفح وأعلى الموقع</div>
                                </div>

                                {{-- البريد الإلكتروني للإدارة --}}
                                <div class="col-lg-6 mb-4">
                                    <label for="admin_email" class="brand-form-label">
                                        <i class="fas fa-envelope me-1 text-primary"></i>
                                        البريد الإلكتروني للإدارة <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" class="brand-form-control @error('admin_email') is-invalid @enderror"
                                           id="admin_email" name="admin_email"
                                           value="{{ old('admin_email', $settings['admin_email']->value ?? '') }}"
                                           placeholder="<EMAIL>" required>
                                    @error('admin_email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">البريد الإلكتروني الذي ستصل إليه رسائل النظام</div>
                                </div>

                                {{-- رقم الهاتف الرئيسي --}}
                                <div class="col-lg-6 mb-4">
                                    <label for="main_phone" class="brand-form-label">
                                        <i class="fas fa-phone me-1 text-primary"></i>
                                        رقم الهاتف الرئيسي
                                    </label>
                                    <input type="text" class="brand-form-control @error('main_phone') is-invalid @enderror"
                                           id="main_phone" name="main_phone"
                                           value="{{ old('main_phone', $settings['main_phone']->value ?? '') }}"
                                           placeholder="+966501234567">
                                    @error('main_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">رقم الهاتف الذي سيظهر للعملاء للتواصل</div>
                                </div>

                                {{-- العنوان --}}
                                <div class="col-lg-6 mb-4">
                                    <label for="address" class="brand-form-label">
                                        <i class="fas fa-map-marker-alt me-1 text-primary"></i>
                                        العنوان
                                    </label>
                                    <textarea class="brand-form-control @error('address') is-invalid @enderror"
                                              id="address" name="address" rows="3"
                                              placeholder="أدخل عنوان المعرض">{{ old('address', $settings['address']->value ?? '') }}</textarea>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">العنوان الكامل للمعرض الذي سيظهر للعملاء</div>
                                </div>
                            </div>
                        </div>



                        {{-- قسم النصوص والمحتوى --}}
                        <div class="mb-5">
                            <div class="d-flex align-items-center mb-4">
                                <div class="brand-icon-wrapper brand-icon-accent me-3">
                                    <i class="fas fa-file-text"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-1" style="color: var(--primary-color);">النصوص والمحتوى</h6>
                                    <small class="text-muted">النصوص التي تظهر للعملاء</small>
                                </div>
                            </div>

                            <div class="row">
                                {{-- نص خدمة ما بعد البيع --}}
                                <div class="col-12 mb-4">
                                    <label for="after_sales_service_text" class="brand-form-label">
                                        <i class="fas fa-tools me-1 text-primary"></i>
                                        نص خدمة ما بعد البيع
                                    </label>
                                    <textarea class="brand-form-control @error('after_sales_service_text') is-invalid @enderror"
                                              id="after_sales_service_text" name="after_sales_service_text" rows="4"
                                              placeholder="أدخل نص خدمة ما بعد البيع">{{ old('after_sales_service_text', $settings['after_sales_service_text']->value ?? '') }}</textarea>
                                    @error('after_sales_service_text')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">النص الذي سيظهر للعملاء حول خدمات ما بعد البيع</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- تبويب الإعدادات المالية --}}
                    <div class="tab-pane fade" id="financial" role="tabpanel" aria-labelledby="financial-tab">
                        <div class="brand-alert brand-alert-info mb-5">
                            <i class="fas fa-coins me-2"></i>
                            <strong>الإعدادات المالية:</strong> قم بتكوين العملة والضرائب والرسوم المستخدمة في النظام.
                        </div>

                        {{-- قسم العملة والضرائب --}}
                        <div class="mb-5">
                            <div class="d-flex align-items-center mb-4">
                                <div class="brand-icon-wrapper brand-icon-accent me-3">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-1" style="color: var(--primary-color);">العملة والضرائب</h6>
                                    <small class="text-muted">إعدادات العملة الأساسية ونسب الضرائب</small>
                                </div>
                            </div>

                            <div class="row">
                                {{-- العملة الافتراضية --}}
                                <div class="col-lg-6 mb-4">
                                    <label for="default_currency" class="brand-form-label">
                                        <i class="fas fa-coins me-1 text-primary"></i>
                                        العملة الافتراضية
                                    </label>
                                    <input type="text" class="brand-form-control @error('default_currency') is-invalid @enderror"
                                           id="default_currency" name="default_currency"
                                           value="{{ old('default_currency', $settings['default_currency']->value ?? 'ر.س') }}"
                                           placeholder="ر.س">
                                    @error('default_currency')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        <i class="fas fa-info-circle text-info me-1"></i>
                                        رمز العملة المستخدم في عرض الأسعار في جميع أنحاء الموقع
                                    </div>
                                </div>

                                {{-- نسبة ضريبة القيمة المضافة --}}
                                <div class="col-lg-6 mb-4">
                                    <label for="vat_percentage" class="brand-form-label">
                                        <i class="fas fa-percentage me-1 text-primary"></i>
                                        نسبة ضريبة القيمة المضافة (%)
                                    </label>
                                    <input type="number" class="brand-form-control @error('vat_percentage') is-invalid @enderror"
                                           id="vat_percentage" name="vat_percentage"
                                           value="{{ old('vat_percentage', $settings['vat_percentage']->value ?? '15') }}"
                                           placeholder="15" min="0" max="100" step="0.01">
                                    @error('vat_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        <i class="fas fa-calculator text-success me-1"></i>
                                        النسبة المئوية لضريبة القيمة المضافة (في السعودية: 15%)
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- قسم الرسوم والمدفوعات --}}
                        <div class="mb-5">
                            <div class="d-flex align-items-center mb-4">
                                <div class="brand-icon-wrapper brand-icon-accent me-3">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-1" style="color: var(--primary-color);">الرسوم والمدفوعات</h6>
                                    <small class="text-muted">إعدادات رسوم الحجز والخدمات الإضافية</small>
                                </div>
                            </div>

                            <div class="row">
                                {{-- رسوم الحجز الافتراضية --}}
                                <div class="col-lg-6 mb-4">
                                    <label for="default_booking_fee" class="brand-form-label">
                                        <i class="fas fa-money-bill me-1 text-primary"></i>
                                        رسوم الحجز الافتراضية
                                    </label>
                                    <input type="number" class="brand-form-control @error('default_booking_fee') is-invalid @enderror"
                                           id="default_booking_fee" name="default_booking_fee"
                                           value="{{ old('default_booking_fee', $settings['default_booking_fee']->value ?? '1000') }}"
                                           placeholder="1000" min="0" step="0.01">
                                    @error('default_booking_fee')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        <i class="fas fa-hand-holding-usd text-warning me-1"></i>
                                        المبلغ المطلوب دفعه مقدماً لحجز السيارة
                                    </div>
                                </div>

                                {{-- حقل إضافي للتوسع المستقبلي --}}
                                <div class="col-lg-6 mb-4">
                                    <div class="brand-alert brand-alert-info">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        <strong>ملاحظة:</strong> يمكن إضافة المزيد من الإعدادات المالية هنا مستقبلاً مثل رسوم التحويل أو خصومات الكمية.
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- قسم معلومات إضافية --}}
                        <div class="mb-5">
                            <div class="brand-alert brand-alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تنبيه مهم:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>تأكد من صحة نسبة ضريبة القيمة المضافة حسب قوانين بلدك</li>
                                    <li>رسوم الحجز ستطبق على جميع السيارات ما لم يتم تخصيص رسوم مختلفة</li>
                                    <li>تغيير العملة قد يؤثر على عرض الأسعار الحالية</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {{-- تبويب إعدادات SEO --}}
                    <div class="tab-pane fade" id="seo" role="tabpanel" aria-labelledby="seo-tab">
                        <div class="brand-alert brand-alert-info mb-5">
                            <i class="fas fa-search me-2"></i>
                            <strong>نصائح SEO:</strong> هذه الإعدادات تساعد في تحسين ظهور موقعك في محركات البحث وزيادة عدد الزوار.
                        </div>

                        {{-- قسم الكلمات المفتاحية والوصف --}}
                        <div class="mb-5">
                            <div class="d-flex align-items-center mb-4">
                                <div class="brand-icon-wrapper brand-icon-accent me-3">
                                    <i class="fas fa-tags"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-1" style="color: var(--primary-color);">المحتوى والكلمات المفتاحية</h6>
                                    <small class="text-muted">تحسين المحتوى لمحركات البحث</small>
                                </div>
                            </div>

                            <div class="row">
                                {{-- الكلمات المفتاحية الافتراضية --}}
                                <div class="col-lg-6 mb-4">
                                    <label for="seo_default_keywords" class="brand-form-label">
                                        <i class="fas fa-tags me-1 text-primary"></i>
                                        الكلمات المفتاحية الافتراضية
                                    </label>
                                    <textarea class="brand-form-control @error('seo_default_keywords') is-invalid @enderror"
                                              id="seo_default_keywords" name="seo_default_keywords" rows="4"
                                              placeholder="سيارات، معرض سيارات، شراء سيارة، تمويل سيارات">{{ old('seo_default_keywords', $settings['seo_default_keywords']->value ?? '') }}</textarea>
                                    <div class="form-text">
                                        <i class="fas fa-lightbulb text-warning me-1"></i>
                                        افصل الكلمات المفتاحية بفواصل - استخدم كلمات مرتبطة بنشاطك التجاري
                                    </div>
                                    @error('seo_default_keywords')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                {{-- الوصف الافتراضي للصفحات --}}
                                <div class="col-lg-6 mb-4">
                                    <label for="seo_default_description" class="brand-form-label">
                                        <i class="fas fa-file-alt me-1 text-primary"></i>
                                        الوصف الافتراضي للصفحات
                                    </label>
                                    <textarea class="brand-form-control @error('seo_default_description') is-invalid @enderror"
                                              id="seo_default_description" name="seo_default_description" rows="4"
                                              placeholder="وصف موجز عن الموقع وخدماته">{{ old('seo_default_description', $settings['seo_default_description']->value ?? '') }}</textarea>
                                    <div class="form-text">
                                        <i class="fas fa-ruler text-info me-1"></i>
                                        يُنصح بأن يكون الوصف بين 150-160 حرف لأفضل ظهور في نتائج البحث
                                    </div>
                                    @error('seo_default_description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        {{-- قسم أدوات التتبع والتحليل --}}
                        <div class="mb-5">
                            <div class="d-flex align-items-center mb-4">
                                <div class="brand-icon-wrapper brand-icon-accent me-3">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-1" style="color: var(--primary-color);">أدوات التتبع والتحليل</h6>
                                    <small class="text-muted">تتبع زوار الموقع وتحليل الأداء</small>
                                </div>
                            </div>

                            <div class="row">
                                {{-- معرف Google Analytics --}}
                                <div class="col-lg-6 mb-4">
                                    <label for="ga_tracking_id" class="brand-form-label">
                                        <i class="fab fa-google me-1 text-primary"></i>
                                        معرف Google Analytics
                                    </label>
                                    <input type="text" class="brand-form-control @error('ga_tracking_id') is-invalid @enderror"
                                           id="ga_tracking_id" name="ga_tracking_id"
                                           value="{{ old('ga_tracking_id', $settings['ga_tracking_id']->value ?? '') }}"
                                           placeholder="G-XXXXXXXXXX">
                                    <div class="form-text">
                                        <i class="fas fa-chart-line text-success me-1"></i>
                                        مثال: G-XXXXXXXXXX أو UA-XXXXXXXXX-X - لتتبع زوار الموقع
                                    </div>
                                    @error('ga_tracking_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- أزرار الحفظ --}}
                <div class="mt-5 pt-4 border-top">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            تأكد من مراجعة جميع الإعدادات قبل الحفظ
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-brand-secondary" onclick="resetForm()">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-brand-primary btn-lg">
                                <i class="fas fa-save me-1"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* تحسينات خاصة بصفحة الإعدادات */
.nav-tabs .nav-link {
    transition: all var(--transition-speed) ease;
    font-weight: 500;
}

.nav-tabs .nav-link:hover {
    background-color: var(--secondary-color) !important;
    color: white !important;
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    background-color: var(--secondary-color) !important;
    color: white !important;
    border-color: var(--secondary-color) !important;
}

.tab-content {
    min-height: 400px;
}

.form-text {
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.form-text i {
    font-size: 0.8rem;
}
</style>
@endpush

@push('scripts')
<script>
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الحقول؟')) {
        document.querySelector('form').reset();
        showToast('تم إعادة تعيين النموذج', 'info');
    }
}

function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `brand-alert brand-alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle me-2"></i>
        ${message}
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// تحسين تجربة التبويبات
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');

    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            // تحديث أنماط التبويبات
            tabButtons.forEach(btn => {
                btn.style.backgroundColor = 'var(--light-bg)';
                btn.style.color = 'var(--text-color)';
            });

            e.target.style.backgroundColor = 'var(--secondary-color)';
            e.target.style.color = 'white';
        });
    });

    // تحسين تجربة النموذج
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    }
});
</script>
@endpush
