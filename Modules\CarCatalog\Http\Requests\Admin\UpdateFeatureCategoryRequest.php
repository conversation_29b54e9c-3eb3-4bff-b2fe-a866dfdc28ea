<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تحديث فئة ميزة موجودة.
 *
 * يتحقق هذا الطلب من صحة بيانات تعديل فئة ميزة موجودة
 */
class UpdateFeatureCategoryRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:100',
                Rule::unique('feature_categories')->ignore($this->featurecategory->id),
            ],
            'description'   => 'nullable|string|max:500',
            'status'        => 'required|boolean',
            'display_order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة لقواعد التحقق
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required'         => 'اسم فئة الميزة مطلوب.',
            'name.string'           => 'اسم فئة الميزة يجب أن يكون نص.',
            'name.max'              => 'اسم فئة الميزة يجب ألا يتجاوز 100 حرف.',
            'name.unique'           => 'اسم فئة الميزة موجود مسبقاً.',
            'description.string'    => 'وصف فئة الميزة يجب أن يكون نص.',
            'description.max'       => 'وصف فئة الميزة يجب ألا يتجاوز 500 حرف.',
            'status.required'       => 'حالة فئة الميزة مطلوبة.',
            'status.boolean'        => 'حالة فئة الميزة يجب أن تكون نشط أو غير نشط.',
            'display_order.integer' => 'ترتيب العرض يجب أن يكون رقم صحيح.',
            'display_order.min'     => 'ترتيب العرض يجب أن يكون أكبر من أو يساوي 0.',
        ];
    }

    /**
     * الحصول على أسماء الخصائص المخصصة للتحقق
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name'          => 'اسم فئة الميزة',
            'description'   => 'وصف فئة الميزة',
            'status'        => 'حالة فئة الميزة',
            'display_order' => 'ترتيب العرض',
        ];
    }
}
