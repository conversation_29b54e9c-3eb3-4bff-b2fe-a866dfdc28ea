# Order Management Module - Setup Documentation

## نظرة عامة

تم إنشاء موديول `OrderManagement` بنجاح لإدارة طلبات شراء السيارات (كاش أو تمويل) وفقاً للمواصفات المحددة في `TS-FR.md` (القسم `DB-TBL-012`).

## المكونات المنشأة

### 1. نموذج Order (`Modules/OrderManagement/Models/Order.php`)

#### الخصائص الرئيسية:
- **الحقول القابلة للتعبئة**: جميع الحقول المحددة في `TS-FR.md`
- **العلاقات**: مع User، Car، Nationality، والموظف المعين
- **Soft Deletes**: مفعل للحذف الناعم
- **Media Collections**: مكوّن لإدارة مستندات الطلب

#### العلاقات:
- `user()`: العميل الذي قدم الطلب (belongsTo)
- `car()`: السيارة المطلوبة (belongsTo)
- `assignedEmployee()`: الموظف المعين للطلب (belongsTo)
- `customerNationality()`: جنسية العميل وقت الطلب (belongsTo)

#### النطاقات (Scopes):
- `scopePendingReview()`: الطلبات المعلقة للمراجعة
- `scopeProcessing()`: الطلبات قيد المعالجة
- `scopeCompleted()`: الطلبات المكتملة
- `scopeCashOrders()`: الطلبات النقدية
- `scopeFinanceOrders()`: طلبات التمويل

#### الدوال المساعدة:
- `generateOrderNumber()`: توليد رقم طلب فريد
- `isFinanceOrder()`: التحقق من كون الطلب تمويلي
- `isCashOrder()`: التحقق من كون الطلب نقدي
- `getStatusInArabic()`: الحصول على حالة الطلب بالعربية

#### مجموعات الوسائط:
- `national_id_front`: صورة الهوية الأمامية
- `national_id_back`: صورة الهوية الخلفية
- `driving_license`: رخصة القيادة
- `salary_certificate`: شهادة الراتب (للتمويل)
- `bank_statement`: كشف حساب بنكي (للتمويل)
- `additional_documents`: مستندات إضافية

### 2. Migration (`2025_05_25_021552_create_orders_table.php`)

#### الأعمدة المنشأة:
- `id`: المفتاح الأساسي
- `user_id`: معرّف العميل (FK)
- `car_id`: معرّف السيارة (FK)
- `order_number`: رقم الطلب الفريد
- `order_type`: نوع الطلب (cash_reservation, finance_application, custom_car_request)
- `status`: حالة الطلب (default: pending_payment)
- `car_price_at_order`: سعر السيارة وقت الطلب
- `reservation_amount`: مبلغ الحجز
- `remaining_amount`: المبلغ المتبقي
- `payment_method`: طريقة الدفع
- `payment_status`: حالة الدفع
- `payment_transaction_id`: معرّف معاملة الدفع
- `customer_national_id`: رقم هوية العميل
- `customer_dob`: تاريخ ميلاد العميل
- `customer_nationality_id`: جنسية العميل (FK)
- `customer_address_details`: تفاصيل عنوان العميل
- `admin_notes`: ملاحظات إدارية
- `finance_details`: تفاصيل التمويل (JSON)
- `assigned_employee_id`: الموظف المعين (FK)
- `payment_gateway_response`: استجابة بوابة الدفع (JSON)
- `timestamps`: created_at, updated_at
- `deleted_at`: للحذف الناعم

#### المفاتيح الخارجية والفهارس:
- Foreign keys مع `users`, `cars`, `nationalities`
- Indexes على الحقول المهمة للبحث والفلترة
- Unique constraint على `order_number`

### 3. تحديث العلاقات في النماذج الأخرى

#### User Model:
- `orders()`: طلبات المستخدم (hasMany)
- `assignedOrders()`: الطلبات المعينة للموظف (hasMany)

#### Car Model:
- `orders()`: طلبات السيارة (hasMany)

## الاختبارات المنجزة

### 1. اختبار النموذج:
✅ تحميل النموذج بنجاح
✅ التحقق من اسم الجدول
✅ التحقق من الحقول القابلة للتعبئة

### 2. اختبار العلاقات:
✅ جميع علاقات Order تعمل بشكل صحيح
✅ علاقات User مع Orders تعمل بشكل صحيح
✅ علاقات Car مع Orders تعمل بشكل صحيح

### 3. اختبار الدوال المساعدة:
✅ توليد رقم الطلب يعمل بشكل صحيح
✅ فحص نوع الطلب يعمل بشكل صحيح
✅ ترجمة حالة الطلب للعربية تعمل بشكل صحيح

## الاستخدام

### إنشاء طلب جديد:
```php
use Modules\OrderManagement\Models\Order;

$order = Order::create([
    'user_id' => 1,
    'car_id' => 1,
    'order_number' => Order::generateOrderNumber(),
    'order_type' => 'cash_reservation',
    'car_price_at_order' => 120000.00,
    'reservation_amount' => 5000.00,
    // ... باقي الحقول
]);
```

### استخدام النطاقات:
```php
// الطلبات المعلقة للمراجعة
$pendingOrders = Order::pendingReview()->get();

// طلبات التمويل
$financeOrders = Order::financeOrders()->get();

// الطلبات النقدية
$cashOrders = Order::cashOrders()->get();
```

### الوصول للعلاقات:
```php
$order = Order::with(['user', 'car', 'assignedEmployee'])->first();
echo $order->user->full_name;
echo $order->car->brand->name;
echo $order->assignedEmployee->full_name ?? 'غير معين';
```

## الخطوات التالية

1. إنشاء Controllers لإدارة الطلبات
2. إنشاء Form Requests للتحقق من البيانات
3. إنشاء واجهات Blade لإدارة الطلبات
4. إنشاء API endpoints للتطبيق
5. إنشاء Notifications للطلبات
6. إنشاء Tests للموديول

## ملاحظات مهمة

- تم تطبيق جميع المواصفات من `TS-FR.md` بدقة
- النموذج جاهز للاستخدام مع `spatie/laravel-medialibrary`
- جميع العلاقات مكوّنة بشكل صحيح
- الفهارس مضافة للأداء الأمثل
- Soft Deletes مفعل للحفاظ على البيانات

---

**تاريخ الإنشاء**: 2025-05-25
**الحالة**: مكتمل ✅
**المطور**: Augment Agent
