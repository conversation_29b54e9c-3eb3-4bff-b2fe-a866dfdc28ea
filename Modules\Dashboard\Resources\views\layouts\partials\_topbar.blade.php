<!-- Top Navbar -->
<nav class="navbar top-navbar">
    <div class="container-fluid">
        <div class="d-flex">
            <button class="navbar-toggler me-2" id="sidebar-toggler" type="button">
                <i class="fas fa-bars"></i>
            </button>
            <form class="d-flex search-bar">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="بحث...">
                    <button class="btn" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>

        <div class="d-flex align-items-center">
            <div class="notification-bell mx-3">
                <a href="#" class="text-dark position-relative">
                    <i class="fas fa-bell fa-lg"></i>
                    <span class="notification-badge">5</span>
                </a>
            </div>

            <div class="dropdown user-dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="d-flex align-items-center">
                        <div class="me-2 d-none d-lg-block">
                            @auth
                                <div class="fw-bold">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</div>
                                <div class="small text-muted">{{ Auth::user()->getRoleNames()->first() ?? 'User' }}</div>
                            @else
                                <div class="fw-bold">Guest</div>
                                <div class="small text-muted">Not logged in</div>
                            @endauth
                        </div>
                        @auth
                            @if(Auth::user()->profile_photo_path)
                                <img src="{{ asset('storage/' . Auth::user()->profile_photo_path) }}" alt="profile image">
                            @else
                                <img src="/api/placeholder/40/40" alt="profile image">
                            @endif
                        @else
                            <i class="fas fa-user-circle fa-lg"></i>
                        @endauth
                    </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end shadow border-0" aria-labelledby="userDropdown">
                    <li><a class="dropdown-item" href="{{-- route('admin.profile.edit') --}}#"><i class="fas fa-user me-2 text-primary"></i> الملف
                            الشخصي</a></li>
                    <li><a class="dropdown-item" href="{{ route('admin.settings.system.index') }}"><i class="fas fa-cog me-2 text-secondary"></i>
                            الإعدادات</a></li>
                    <li>
                        <hr class="dropdown-divider">
                    </li>
                    <li>
                        <form method="POST" action="{{ route('admin.logout') }}" id="admin-topbar-logout-form" class="d-none">@csrf</form>
                        <a class="dropdown-item" href="#"
                           onclick="event.preventDefault(); if(document.getElementById('admin-topbar-logout-form')) { document.getElementById('admin-topbar-logout-form').submit(); }">
                            <i class="fas fa-sign-out-alt me-2 text-danger"></i> تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>
<!-- End Top Navbar -->
