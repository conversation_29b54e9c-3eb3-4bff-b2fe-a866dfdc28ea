<?php

namespace Modules\CarCatalog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

/**
 * FeatureCategory Model.
 *
 * يمثل هذا النموذج جدول فئات ميزات السيارات في النظام
 *
 * @property int $id
 * @property string $name اسم فئة الميزات
 * @property string|null $description وصف فئة الميزات
 * @property int|null $icon_id معرّف أيقونة الفئة
 * @property bool $status حالة فئة الميزات (نشطة/غير نشطة)
 * @property int $display_order ترتيب العرض
 * @property \Illuminate\Support\Carbon|null $created_at تاريخ الإنشاء
 * @property \Illuminate\Support\Carbon|null $updated_at تاريخ التحديث
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\CarCatalog\Models\CarFeature[] $features علاقة ميزات السيارات
 * @property \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection|\Spatie\MediaLibrary\MediaCollections\Models\Media[] $media مجموعة الوسائط
 */
class FeatureCategory extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use HasTranslations;
    use \App\Traits\HasStandardMediaConversions;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'status',
        'display_order',
    ];

    /**
     * الخصائص التي يمكن ترجمتها.
     *
     * @var array
     */
    public $translatable = [
        'name',
        'description',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة.
     *
     * @var array
     */
    protected $casts = [
        'status'        => 'boolean',
        'display_order' => 'integer',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
    ];

    /**
     * علاقة ميزات السيارات المرتبطة بهذه الفئة.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function features()
    {
        return $this->hasMany(CarFeature::class, 'category_id');
    }

    /**
     * تسجيل مجموعات الوسائط.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('feature_category_icons')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp']);
    }

    /**
     * تسجيل تحويلات الوسائط.
     *
     * @param mixed|null $media
     */
    public function registerMediaConversions($media = null): void
    {
        // استخدام التحويلات المعيارية للأيقونات
        $this->registerIconConversions($media);
    }

    /**
     * مصنع النموذج.
     *
     * @return \Modules\CarCatalog\Database\factories\FeatureCategoryFactory
     */
    protected static function newFactory()
    {
        return \Modules\CarCatalog\Database\factories\FeatureCategoryFactory::new();
    }
}
