<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('user_favorites', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->comment('معرّف المستخدم');
            $table->unsignedBigInteger('car_id')->comment('معرّف السيارة');
            $table->timestamps();

            // إنشاء الفهارس
            $table->index('user_id');
            $table->index('car_id');

            // إنشاء المفاتيح الخارجية
            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            $table->foreign('car_id')
                ->references('id')
                ->on('cars')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            // إنشاء فهرس مركب للفرادة (مستخدم واحد لا يمكنه إضافة نفس السيارة للمفضلة أكثر من مرة)
            $table->unique(['user_id', 'car_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('user_favorites');
    }
};
