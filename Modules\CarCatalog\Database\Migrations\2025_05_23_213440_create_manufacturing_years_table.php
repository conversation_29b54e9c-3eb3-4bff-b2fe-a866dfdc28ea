<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('manufacturing_years', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->year('year')->unique()->comment('سنة الصنع (4 أرقام)');
            $table->boolean('status')->default(true)->comment('نشطة/غير نشطة');

            // إنشاء الفهارس
            $table->index('year');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('manufacturing_years');
    }
};
