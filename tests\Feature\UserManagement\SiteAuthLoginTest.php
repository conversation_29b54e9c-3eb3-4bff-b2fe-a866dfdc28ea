<?php

namespace Tests\Feature\UserManagement;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Modules\UserManagement\Models\User;
use Spatie\Permission\Models\Role;

/**
 * اختبارات تسجيل الدخول للموقع العام
 */
class SiteAuthLoginTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // إنشاء دور العميل
        Role::create(['name' => 'Customer']);
    }

    /**
     * اختبار تسجيل دخول ناجح بالبريد الإلكتروني
     */
    public function test_successful_login_with_email()
    {
        // إنشاء مستخدم نشط
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'active'
        ]);
        $user->assignRole('Customer');

        // محاولة تسجيل الدخول
        $response = $this->post(route('site.auth.login.submit'), [
            'identifier' => '<EMAIL>',
            'password' => 'password123'
        ]);

        // التحقق من النجاح
        $response->assertRedirect(route('site.home'));
        $this->assertAuthenticatedAs($user);
        
        // التحقق من تحديث آخر تسجيل دخول
        $user->refresh();
        $this->assertNotNull($user->last_login_at);
    }

    /**
     * اختبار تسجيل دخول ناجح برقم الجوال
     */
    public function test_successful_login_with_phone()
    {
        // إنشاء مستخدم نشط
        $user = User::factory()->create([
            'phone_number' => '0512345678',
            'password' => Hash::make('password123'),
            'status' => 'active'
        ]);
        $user->assignRole('Customer');

        // محاولة تسجيل الدخول
        $response = $this->post(route('site.auth.login.submit'), [
            'identifier' => '0512345678',
            'password' => 'password123'
        ]);

        // التحقق من النجاح
        $response->assertRedirect(route('site.home'));
        $this->assertAuthenticatedAs($user);
    }

    /**
     * اختبار فشل تسجيل الدخول مع كلمة مرور خاطئة
     */
    public function test_login_fails_with_wrong_password()
    {
        // إنشاء مستخدم نشط
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'active'
        ]);
        $user->assignRole('Customer');

        // محاولة تسجيل الدخول بكلمة مرور خاطئة
        $response = $this->post(route('site.auth.login.submit'), [
            'identifier' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);

        // التحقق من الفشل
        $response->assertRedirect();
        $response->assertSessionHasErrors(['identifier']);
        $this->assertGuest();
    }

    /**
     * اختبار فشل تسجيل الدخول مع حساب غير نشط
     */
    public function test_login_fails_with_inactive_account()
    {
        // إنشاء مستخدم غير نشط
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'pending_verification'
        ]);
        $user->assignRole('Customer');

        // محاولة تسجيل الدخول
        $response = $this->post(route('site.auth.login.submit'), [
            'identifier' => '<EMAIL>',
            'password' => 'password123'
        ]);

        // التحقق من الفشل
        $response->assertRedirect();
        $response->assertSessionHasErrors(['identifier']);
        $this->assertGuest();
    }

    /**
     * اختبار فشل تسجيل الدخول مع مستخدم ليس لديه دور Customer
     */
    public function test_login_fails_without_customer_role()
    {
        // إنشاء دور آخر
        Role::create(['name' => 'Employee']);
        
        // إنشاء مستخدم بدور موظف
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'active'
        ]);
        $user->assignRole('Employee');

        // محاولة تسجيل الدخول
        $response = $this->post(route('site.auth.login.submit'), [
            'identifier' => '<EMAIL>',
            'password' => 'password123'
        ]);

        // التحقق من الفشل
        $response->assertRedirect();
        $response->assertSessionHasErrors(['identifier']);
        $this->assertGuest();
    }

    /**
     * اختبار فشل تسجيل الدخول مع معرف غير صالح
     */
    public function test_login_fails_with_invalid_identifier()
    {
        // محاولة تسجيل الدخول بمعرف غير صالح
        $response = $this->post(route('site.auth.login.submit'), [
            'identifier' => 'invalid-identifier',
            'password' => 'password123'
        ]);

        // التحقق من الفشل
        $response->assertRedirect();
        $response->assertSessionHasErrors(['identifier']);
        $this->assertGuest();
    }

    /**
     * اختبار التحقق من صحة البيانات المطلوبة
     */
    public function test_login_validation_required_fields()
    {
        // محاولة تسجيل الدخول بدون بيانات
        $response = $this->post(route('site.auth.login.submit'), []);

        // التحقق من أخطاء التحقق
        $response->assertSessionHasErrors(['identifier', 'password']);
        $this->assertGuest();
    }

    /**
     * اختبار خيار "تذكرني"
     */
    public function test_remember_me_functionality()
    {
        // إنشاء مستخدم نشط
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'active'
        ]);
        $user->assignRole('Customer');

        // محاولة تسجيل الدخول مع تذكرني
        $response = $this->post(route('site.auth.login.submit'), [
            'identifier' => '<EMAIL>',
            'password' => 'password123',
            'remember' => true
        ]);

        // التحقق من النجاح
        $response->assertRedirect(route('site.home'));
        $this->assertAuthenticatedAs($user);
    }
}
