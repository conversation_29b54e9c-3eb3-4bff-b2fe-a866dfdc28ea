<?php

namespace Modules\OrderManagement\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware للتحقق من صحة webhooks بوابات الدفع
 * 
 * يتولى التحقق من:
 * - IP المرسل
 * - التوقيع الرقمي
 * - صحة البيانات المرسلة
 */
class VerifyPaymentWebhook
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            // تسجيل محاولة الوصول
            Log::info('Payment webhook verification attempt', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'headers' => $request->headers->all(),
                'method' => $request->method(),
                'url' => $request->fullUrl()
            ]);

            // التحقق من طريقة الطلب
            if (!$request->isMethod('POST')) {
                Log::warning('Invalid webhook method', [
                    'method' => $request->method(),
                    'ip' => $request->ip()
                ]);
                
                return response()->json([
                    'error' => 'Method not allowed'
                ], 405);
            }

            // التحقق من IP المرسل
            if (!$this->verifySourceIP($request)) {
                Log::warning('Webhook from unauthorized IP', [
                    'ip' => $request->ip(),
                    'allowed_ips' => config('payment.webhook.allowed_ips')
                ]);
                
                return response()->json([
                    'error' => 'Unauthorized IP'
                ], 403);
            }

            // التحقق من التوقيع
            if (!$this->verifySignature($request)) {
                Log::warning('Invalid webhook signature', [
                    'ip' => $request->ip(),
                    'signature' => $request->header('X-Signature')
                ]);
                
                return response()->json([
                    'error' => 'Invalid signature'
                ], 403);
            }

            // التحقق من صحة البيانات الأساسية
            if (!$this->verifyPayload($request)) {
                Log::warning('Invalid webhook payload', [
                    'ip' => $request->ip(),
                    'payload' => $request->all()
                ]);
                
                return response()->json([
                    'error' => 'Invalid payload'
                ], 400);
            }

            Log::info('Webhook verification successful', [
                'ip' => $request->ip()
            ]);

            return $next($request);

        } catch (\Exception $e) {
            Log::error('Webhook verification error', [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Verification failed'
            ], 500);
        }
    }

    /**
     * التحقق من IP المرسل
     */
    private function verifySourceIP(Request $request): bool
    {
        $allowedIps = config('payment.webhook.allowed_ips');
        
        // إذا لم يتم تحديد IPs مسموحة، السماح للجميع
        if (empty($allowedIps)) {
            return true;
        }

        $allowedIpsArray = array_map('trim', explode(',', $allowedIps));
        $clientIP = $request->ip();

        // التحقق من IP المباشر
        if (in_array($clientIP, $allowedIpsArray)) {
            return true;
        }

        // التحقق من IPs خلف proxy
        $forwardedIps = $request->header('X-Forwarded-For');
        if ($forwardedIps) {
            $forwardedIpsArray = array_map('trim', explode(',', $forwardedIps));
            foreach ($forwardedIpsArray as $ip) {
                if (in_array($ip, $allowedIpsArray)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * التحقق من التوقيع الرقمي
     */
    private function verifySignature(Request $request): bool
    {
        // إذا كان التحقق من التوقيع معطل
        if (!config('payment.webhook.verify_signature', true)) {
            return true;
        }

        $signature = $request->header('X-Signature') 
                  ?? $request->header('X-Webhook-Signature')
                  ?? $request->header('Signature');

        if (!$signature) {
            return false;
        }

        // الحصول على المحتوى الخام
        $payload = $request->getContent();
        
        // تحديد البوابة من البيانات أو استخدام الافتراضية
        $gateway = $request->input('gateway') ?? config('payment.default_gateway', 'test');
        $secret = config("payment.gateways.{$gateway}.webhook_secret");

        if (!$secret) {
            Log::warning('No webhook secret configured for gateway', [
                'gateway' => $gateway
            ]);
            return false;
        }

        // حساب التوقيع المتوقع
        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        
        // مقارنة التواقيع بطريقة آمنة
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * التحقق من صحة البيانات المرسلة
     */
    private function verifyPayload(Request $request): bool
    {
        $data = $request->all();

        // التحقق من وجود الحقول الأساسية
        $requiredFields = ['order_id', 'status'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) && !isset($data['metadata'][$field])) {
                return false;
            }
        }

        // التحقق من صحة order_id
        $orderId = $data['order_id'] ?? $data['metadata']['order_id'] ?? null;
        if (!$orderId || !is_numeric($orderId)) {
            return false;
        }

        // التحقق من صحة status
        $status = $data['status'] ?? null;
        $validStatuses = ['success', 'failed', 'pending', 'completed', 'cancelled', 'error'];
        if (!$status || !in_array(strtolower($status), $validStatuses)) {
            return false;
        }

        return true;
    }

    /**
     * تسجيل محاولة webhook مشبوهة
     */
    private function logSuspiciousActivity(Request $request, string $reason): void
    {
        Log::warning('Suspicious webhook activity detected', [
            'reason' => $reason,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'headers' => $request->headers->all(),
            'payload' => $request->all(),
            'timestamp' => now()->toISOString()
        ]);
    }
}
