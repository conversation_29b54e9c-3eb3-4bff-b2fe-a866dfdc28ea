<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('brands', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name', 100)->unique()->comment('اسم الماركة');
            $table->unsignedBigInteger('logo_id')->nullable()->comment('معرّف شعار الماركة من جدول media');
            $table->text('description')->nullable()->comment('وصف مختصر للماركة');
            $table->boolean('status')->default(true)->comment('حالة الماركة (نشطة/غير نشطة)');
            $table->timestamps();
            $table->softDeletes();

            // إنشاء الفهارس
            $table->index('name');
            $table->index('logo_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('brands');
    }
};
