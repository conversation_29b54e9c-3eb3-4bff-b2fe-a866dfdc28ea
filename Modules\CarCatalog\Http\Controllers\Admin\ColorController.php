<?php

namespace Modules\CarCatalog\Http\Controllers\Admin;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\CarCatalog\Http\Requests\Admin\StoreColorRequest;
use Modules\CarCatalog\Http\Requests\Admin\UpdateColorRequest;
use Modules\CarCatalog\Models\Color;
use Modules\Core\Http\Controllers\BaseController;

/**
 * وحدة تحكم إدارة الألوان.
 *
 * تتعامل هذه الوحدة مع عمليات CRUD لألوان السيارات في لوحة تحكم الإدارة
 */
class ColorController extends BaseController
{
    /**
     * عرض قائمة الألوان.
     *
     * @param Request $request طلب HTTP
     *
     * @return View
     */
    public function index(Request $request): View
    {
        $query = Color::query()->withCount('cars');

        // تطبيق فلتر البحث إذا تم تقديمه
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->input('search') . '%');
        }

        // تطبيق فلتر الحالة إذا تم تقديمه
        if ($request->has('status') && $request->input('status') !== '') {
            $query->where('status', $request->input('status'));
        }

        // ترتيب النتائج وتطبيق الترقيم
        $colors = $query->orderBy('name')->paginate(15);

        return view('car_catalog::admin.colors.index', compact('colors'));
    }

    /**
     * عرض نموذج إضافة لون جديد.
     *
     * @return View
     */
    public function create(): View
    {
        return view('car_catalog::admin.colors.create');
    }

    /**
     * تخزين لون جديد.
     *
     * @param StoreColorRequest $request طلب تخزين لون
     *
     * @return RedirectResponse
     */
    public function store(StoreColorRequest $request): RedirectResponse
    {
        // إنشاء اللون باستخدام البيانات المتحقق منها
        Color::create($request->validated());

        return redirect()->route('admin.colors.index')->with('success', 'تمت إضافة اللون بنجاح.');
    }

    /**
     * عرض تفاصيل لون محدد.
     *
     * @param Color $color اللون المراد عرضه
     *
     * @return View
     */
    public function show(Color $color): View
    {
        $color->loadCount('cars');

        return view('car_catalog::admin.colors.show', compact('color'));
    }

    /**
     * عرض نموذج تعديل لون موجود.
     *
     * @param Color $color اللون المراد تعديله
     *
     * @return View
     */
    public function edit(Color $color): View
    {
        return view('car_catalog::admin.colors.edit', compact('color'));
    }

    /**
     * تحديث لون موجود.
     *
     * @param UpdateColorRequest $request طلب تحديث لون
     * @param Color $color اللون المراد تحديثه
     *
     * @return RedirectResponse
     */
    public function update(UpdateColorRequest $request, Color $color): RedirectResponse
    {
        // تحديث اللون باستخدام البيانات المتحقق منها
        $color->update($request->validated());

        return redirect()->route('admin.colors.index')->with('success', 'تم تعديل اللون بنجاح.');
    }

    /**
     * حذف لون محدد.
     *
     * @param Color $color اللون المراد حذفه
     *
     * @return RedirectResponse
     */
    public function destroy(Color $color): RedirectResponse
    {
        // التحقق من وجود سيارات مرتبطة بهذا اللون
        if ($color->cars()->count() > 0) {
            return redirect()->route('admin.colors.index')
                ->with('error', 'لا يمكن حذف هذا اللون لأنه مرتبط بسيارات موجودة.');
        }

        $color->delete();

        return redirect()->route('admin.colors.index')->with('success', 'تم حذف اللون بنجاح.');
    }
}
