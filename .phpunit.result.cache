{"version": 1, "defects": {"Tests\\Feature\\TransmissionTypeCrudTest::it_can_display_transmission_types_index": 8, "Tests\\Feature\\TransmissionTypeCrudTest::it_can_create_transmission_type": 8, "Tests\\Feature\\TransmissionTypeCrudTest::it_can_update_transmission_type": 8, "Tests\\Feature\\TransmissionTypeCrudTest::it_can_delete_transmission_type_without_cars": 8, "Tests\\Feature\\TransmissionTypeCrudTest::it_validates_required_fields": 8, "Tests\\Feature\\TransmissionTypeCrudTest::it_validates_unique_name": 8, "Tests\\Feature\\CarImageUploadTest::it_can_upload_car_images_successfully": 8, "Tests\\Feature\\CarImageUploadTest::it_can_update_car_images": 8, "Tests\\Feature\\CarImageUploadTest::it_validates_image_file_types": 8, "Tests\\Feature\\CarImageUploadTest::it_validates_image_file_size": 8, "Tests\\Feature\\SimpleCarImageTest::it_can_display_car_images_in_index": 1, "Tests\\Feature\\SimpleCarImageTest::it_can_get_car_image_urls": 1, "Tests\\Feature\\SimpleCarImageTest::it_can_access_car_create_page": 7, "Tests\\Feature\\SimpleCarImageTest::it_can_check_storage_configuration": 7, "Tests\\Feature\\UserRegistrationTest::test_registration_form_can_be_displayed": 8, "Tests\\Feature\\UserRegistrationTest::test_user_can_register_successfully": 8, "Tests\\Feature\\UserRegistrationTest::test_registration_fails_with_invalid_data": 8, "Tests\\Feature\\UserRegistrationTest::test_verify_otp_form_can_be_displayed": 8, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_successful_login_with_email": 8, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_successful_login_with_phone": 8, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_login_fails_with_wrong_password": 8, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_login_fails_with_inactive_account": 8, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_login_fails_without_customer_role": 8, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_login_fails_with_invalid_identifier": 8, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_login_validation_required_fields": 8, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_remember_me_functionality": 8}, "times": {"Tests\\Feature\\SimpleCarImageTest::it_can_display_car_images_in_index": 0.088, "Tests\\Feature\\SimpleCarImageTest::it_can_get_car_image_urls": 0.004, "Tests\\Feature\\SimpleCarImageTest::it_can_access_car_create_page": 0.046, "Tests\\Feature\\SimpleCarImageTest::it_has_proper_media_collections_configured": 0.004, "Tests\\Feature\\SimpleCarImageTest::it_can_check_storage_configuration": 0.002, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_successful_login_with_email": 0.159, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_successful_login_with_phone": 0.024, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_login_fails_with_wrong_password": 0.232, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_login_fails_with_inactive_account": 0.02, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_login_fails_without_customer_role": 0.02, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_login_fails_with_invalid_identifier": 0.006, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_login_validation_required_fields": 0.008, "Tests\\Feature\\UserManagement\\SiteAuthLoginTest::test_remember_me_functionality": 0.02}}