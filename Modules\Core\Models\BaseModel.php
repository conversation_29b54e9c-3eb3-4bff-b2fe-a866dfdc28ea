<?php

namespace Modules\Core\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * BaseModel Class
 *
 * يوفر هذا الصنف الأساسي وظائف مشتركة لجميع نماذج البيانات في النظام
 * ويتضمن خصائص الحذف الناعم وإنشاء النماذج الاختبارية
 */
class BaseModel extends Model
{
    use HasFactory;
    use SoftDeletes;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];
}
