<?php

namespace Modules\UserManagement\Http\Requests\Admin;

use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب إنشاء دور جديد
 *
 * يتحقق هذا الطلب من صحة بيانات إنشاء دور جديد
 */
class StoreRoleRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:50',
                'unique:roles,name',
            ],
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة للتحقق من صحة البيانات
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'اسم الدور مطلوب.',
            'name.string' => 'اسم الدور يجب أن يكون نصاً.',
            'name.max' => 'اسم الدور يجب ألا يتجاوز 50 حرفاً.',
            'name.unique' => 'اسم الدور موجود مسبقاً.',
            'permissions.array' => 'الصلاحيات يجب أن تكون مصفوفة.',
            'permissions.*.exists' => 'إحدى الصلاحيات المحددة غير موجودة.',
        ];
    }

    /**
     * الحصول على أسماء الخصائص المخصصة للتحقق من صحة البيانات
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => 'اسم الدور',
            'permissions' => 'الصلاحيات',
        ];
    }
}
