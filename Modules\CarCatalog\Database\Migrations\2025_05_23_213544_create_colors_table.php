<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('colors', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name', 50)->unique()->comment('اسم اللون');
            $table->string('hex_code', 7)->nullable()->unique()->comment('الكود الست عشري للون');
            $table->boolean('status')->default(true)->comment('نشط/غير نشط');

            // إنشاء الفهارس
            $table->index('name');
            $table->index('hex_code');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('colors');
    }
};
