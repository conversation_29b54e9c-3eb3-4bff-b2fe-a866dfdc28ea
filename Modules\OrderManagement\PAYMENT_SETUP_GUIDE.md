# دليل إعداد نظام الدفع - Payment Setup Guide

## خطوات الإعداد السريع

### 1. نسخ إعدادات البيئة
```bash
# نسخ المتغيرات من .env.example إلى .env
cp .env.example .env
```

### 2. تحديث ملف .env
أضف المتغيرات التالية لملف `.env`:

```env
# إعدادات الدفع الأساسية
PAYMENT_DEFAULT_GATEWAY=test
PAYMENT_CURRENCY=SAR
PAYMENT_TIMEOUT=30

# بوابة الاختبار (مفعلة افتراضياً)
PAYMENT_TEST_ENABLED=true
PAYMENT_TEST_API_KEY=test_api_key
PAYMENT_TEST_SECRET_KEY=test_secret_key
PAYMENT_TEST_WEBHOOK_SECRET=test_webhook_secret

# إعدادات Webhook
PAYMENT_WEBHOOK_VERIFY_SIGNATURE=true
PAYMENT_WEBHOOK_ALLOWED_IPS=

# التسجيل
PAYMENT_LOGGING_ENABLED=true
PAYMENT_LOGGING_LEVEL=info
```

### 3. تشغيل الخادم
```bash
php artisan serve
```

### 4. اختبار النظام

#### أ. إنشاء طلب جديد
1. انتقل إلى صفحة السيارات
2. اختر سيارة واضغط "احجز الآن"
3. أكمل خطوات الطلب
4. اختر "الدفع الإلكتروني"

#### ب. اختبار الدفع
1. ستتم إعادة توجيهك لصفحة اختبار الدفع
2. اختر نتيجة الدفع المرغوبة:
   - ✅ **نجح الدفع**: لاختبار الدفع الناجح
   - ❌ **فشل الدفع**: لاختبار فشل الدفع
   - ⏳ **معلق**: لاختبار الدفع المعلق
3. اضغط "معالجة الدفع"

#### ج. التحقق من النتائج
- **نجح الدفع**: ستتم إعادة التوجيه لصفحة النجاح
- **فشل الدفع**: ستتم إعادة التوجيه لصفحة الإلغاء
- **معلق**: ستتم إعادة التوجيه لصفحة تفاصيل الطلب

## إعداد بوابة دفع حقيقية

### Moyasar
```env
PAYMENT_MOYASAR_ENABLED=true
PAYMENT_MOYASAR_API_KEY=your_moyasar_api_key
PAYMENT_MOYASAR_SECRET_KEY=your_moyasar_secret_key
PAYMENT_MOYASAR_WEBHOOK_SECRET=your_webhook_secret
```

### PayTabs
```env
PAYMENT_PAYTABS_ENABLED=true
PAYMENT_PAYTABS_API_KEY=your_paytabs_api_key
PAYMENT_PAYTABS_SECRET_KEY=your_paytabs_secret_key
PAYMENT_PAYTABS_WEBHOOK_SECRET=your_webhook_secret
```

### HyperPay
```env
PAYMENT_HYPERPAY_ENABLED=true
PAYMENT_HYPERPAY_API_KEY=your_hyperpay_api_key
PAYMENT_HYPERPAY_SECRET_KEY=your_hyperpay_secret_key
PAYMENT_HYPERPAY_WEBHOOK_SECRET=your_webhook_secret
```

## URLs مهمة

### للعملاء
- **اختبار الدفع**: `/site/order/payment/test/{order_id}`
- **نجاح الدفع**: `/site/order/payment/success/{order_id}`
- **إلغاء الدفع**: `/site/order/payment/cancel/{order_id}`
- **خطأ الدفع**: `/site/order/payment/error/{order_id}`

### للـ API
- **Webhook**: `/api/payment/webhook`
- **حالة الدفع**: `/api/payment/status/{order_id}`
- **إعادة المحاولة**: `/api/payment/retry/{order_id}`

## استكشاف الأخطاء

### 1. خطأ "بوابة الدفع غير مفعلة"
**الحل**: تأكد من أن `PAYMENT_TEST_ENABLED=true` في ملف `.env`

### 2. خطأ في إنشاء جلسة الدفع
**الحل**: 
- تحقق من ملفات التسجيل في `storage/logs`
- تأكد من صحة مفاتيح API
- تحقق من اتصال الإنترنت (للبوابات الحقيقية)

### 3. Webhook لا يعمل
**الحل**:
- تأكد من أن `PAYMENT_WEBHOOK_VERIFY_SIGNATURE=false` للاختبار
- تحقق من IP المرسل إذا كان محدد في `PAYMENT_WEBHOOK_ALLOWED_IPS`
- راجع ملفات التسجيل

### 4. صفحة اختبار الدفع لا تظهر
**الحل**:
- تأكد من وجود الملف: `Modules/OrderManagement/Resources/views/site/payment/test.blade.php`
- تحقق من أن الطلب موجود ويخص المستخدم الحالي

## ملفات التسجيل

جميع عمليات الدفع يتم تسجيلها في:
- `storage/logs/laravel.log` (الافتراضي)
- أو الملف المحدد في `PAYMENT_LOGGING_CHANNEL`

### مثال على رسالة تسجيل:
```
[2024-01-15 10:30:45] local.INFO: Payment Activity: payment_session_created {"activity":"payment_session_created","gateway":"test","timestamp":"2024-01-15T10:30:45.000000Z","order_id":123,"payment_url":"http://localhost:8000/site/order/payment/test/123"}
```

## الدعم الفني

إذا واجهت أي مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من ملفات التسجيل
3. اختبر باستخدام بوابة الاختبار
4. تأكد من إعدادات البيئة

## ملاحظات مهمة

⚠️ **تحذير**: لا تستخدم مفاتيح الإنتاج في بيئة التطوير
✅ **نصيحة**: اختبر دائماً باستخدام بوابة الاختبار أولاً
🔒 **أمان**: احتفظ بمفاتيح API آمنة ولا تشاركها
📝 **تسجيل**: فعل التسجيل لمراقبة العمليات
