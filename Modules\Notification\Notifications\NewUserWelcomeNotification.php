<?php

namespace Modules\Notification\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\UserManagement\Models\User;
use Modules\Notification\Mail\WelcomeEmail;

/**
 * إشعار ترحيب للمستخدمين الجدد
 *
 * يتم إرسال هذا الإشعار عند تسجيل مستخدم جديد في النظام
 * يدعم قناتي البريد الإلكتروني وقاعدة البيانات
 */
class NewUserWelcomeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * المستخدم الذي سيتم إرسال الإشعار له
     *
     * @var \Modules\UserManagement\Models\User
     */
    public $user;

    /**
     * إنشاء نسخة جديدة من الإشعار
     *
     * @param \Modules\UserManagement\Models\User $user
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * الحصول على قنوات توصيل الإشعار
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * الحصول على تمثيل البريد الإلكتروني للإشعار
     *
     * @param mixed $notifiable
     * @return \Modules\Notification\Mail\WelcomeEmail
     */
    public function toMail($notifiable)
    {
        return (new WelcomeEmail($this->user))->to($notifiable->email);
    }

    /**
     * الحصول على تمثيل المصفوفة للإشعار
     * يستخدم لتخزين الإشعار في قاعدة البيانات
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'title' => 'ترحيب مستخدم جديد',
            'message' => 'مرحباً بك، ' . $this->user->first_name . '! شكراً لتسجيلك في ' . config('app.name') . '.',
            'action_url' => route('home'), // سيتم تحديثه لاحقًا إلى route('customer.dashboard') عند إنشائه
            'user_id' => $this->user->id,
            'icon' => 'fas fa-user-plus', // أيقونة Font Awesome لعرض الإشعار في لوحة التحكم
        ];
    }
}
