<?php

namespace Modules\Core\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

/**
 * BaseController Class
 *
 * يوفر هذا الصنف الأساسي وظائف مشتركة لجميع المتحكمات في النظام
 * ويتضمن دوال مساعدة للاستجابات الموحدة بتنسيق JSON
 */
class BaseController extends Controller
{
    /**
     * إرسال استجابة نجاح بتنسيق JSON
     *
     * @param  mixed  $data  البيانات المراد إرجاعها في الاستجابة
     * @param  string  $message  رسالة النجاح
     * @param  int  $statusCode  رمز حالة HTTP
     */
    protected function sendSuccessResponse($data = [], string $message = 'Operation successful.', int $statusCode = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $data,
            'message' => $message,
        ], $statusCode);
    }

    /**
     * إرسال استجابة خطأ بتنسيق JSON
     *
     * @param  string  $message  رسالة الخطأ
     * @param  array  $errors  تفاصيل الأخطاء (اختياري)
     * @param  int  $statusCode  رمز حالة HTTP
     */
    protected function sendErrorResponse(string $message, array $errors = [], int $statusCode = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (! empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode);
    }
}
