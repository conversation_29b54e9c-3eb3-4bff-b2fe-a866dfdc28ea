# معايير جودة الكود - Code Quality Standards

## 🎯 نظرة عامة

يحدد هذا المستند معايير جودة الكود المطبقة في مشروع MotorLine، بما في ذلك معايير التوثيق والتنسيق والأفضل الممارسات.

## 📚 معايير التوثيق (Documentation Standards)

### 1. **DocBlocks للكلاسات**

```php
/**
 * وصف مختصر للكلاس
 *
 * وصف مفصل للكلاس ووظيفته في النظام
 * يمكن أن يكون متعدد الأسطر
 *
 * @package Modules\ModuleName\SubNamespace
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024
 */
class ExampleClass
{
    // محتوى الكلاس
}
```

### 2. **DocBlocks للدوال**

```php
/**
 * وصف مختصر للدالة
 *
 * وصف مفصل لما تقوم به الدالة
 * وكيفية استخدامها
 *
 * @param string $parameter1 وصف المعامل الأول
 * @param int $parameter2 وصف المعامل الثاني (اختياري)
 * @return array وصف القيمة المرجعة
 *
 * @throws \Exception في حالة حدوث خطأ معين
 *
 * @example
 * $result = exampleMethod('test', 123);
 */
public function exampleMethod(string $parameter1, int $parameter2 = 0): array
{
    // محتوى الدالة
}
```

### 3. **DocBlocks للخصائص**

```php
/**
 * وصف الخاصية ووظيفتها
 *
 * @var string
 */
protected $exampleProperty;

/**
 * مصفوفة الخصائص القابلة للتعبئة
 *
 * @var array
 */
protected $fillable = [
    'name',
    'description',
    'status',
];
```

### 4. **DocBlocks للثوابت**

```php
/**
 * وصف الثابت واستخدامه
 *
 * @var string
 */
const EXAMPLE_CONSTANT = 'value';
```

## 🎨 معايير التنسيق (Formatting Standards)

### 1. **PSR-12 Compliance**
جميع ملفات PHP يجب أن تتبع معايير PSR-12:

- استخدام 4 مسافات للمسافة البادئة
- أقواس الكلاسات والدوال في سطر جديد
- استخدام short array syntax `[]`
- حد أقصى 120 حرف للسطر الواحد

### 2. **تنسيق Arrays**

```php
// صحيح
$array = [
    'key1' => 'value1',
    'key2' => 'value2',
    'key3' => 'value3',
];

// خطأ
$array = array('key1' => 'value1', 'key2' => 'value2');
```

### 3. **تنسيق Control Structures**

```php
// صحيح
if ($condition) {
    // code
} elseif ($otherCondition) {
    // code
} else {
    // code
}

// صحيح
foreach ($items as $item) {
    // code
}
```

### 4. **تنسيق Function Calls**

```php
// صحيح
$result = someFunction(
    $parameter1,
    $parameter2,
    $parameter3
);

// صحيح للمعاملات القصيرة
$result = someFunction($param1, $param2);
```

## 🔧 أدوات الجودة (Quality Tools)

### 1. **PHP CS Fixer**

```bash
# تشغيل فحص التنسيق
./vendor/bin/php-cs-fixer fix --dry-run --diff

# تطبيق التنسيق
./vendor/bin/php-cs-fixer fix
```

### 2. **فحص جودة التوثيق**

```bash
# تشغيل سكريبت فحص التوثيق
php scripts/check_documentation_quality.php
```

### 3. **PHPStan (مستقبلي)**

```bash
# تحليل ثابت للكود
./vendor/bin/phpstan analyse
```

## 📋 قائمة المراجعة (Checklist)

### قبل إرسال الكود:

- [ ] جميع الكلاسات موثقة بـ DocBlocks
- [ ] جميع الدوال العامة موثقة
- [ ] جميع المعاملات والقيم المرجعة موثقة
- [ ] تم تشغيل PHP CS Fixer
- [ ] لا توجد أخطاء في التنسيق
- [ ] تم اختبار الكود
- [ ] تم مراجعة الكود من قبل زميل

### للمراجعين:

- [ ] التوثيق واضح ومفهوم
- [ ] التنسيق متسق مع المعايير
- [ ] الكود قابل للقراءة
- [ ] لا توجد تكرارات غير ضرورية
- [ ] الأسماء واضحة ومعبرة

## 🎯 أهداف الجودة

### المستهدف:
- **التوثيق**: 95% من الكلاسات والدوال موثقة
- **التنسيق**: 100% اتباع معايير PSR-12
- **التغطية**: 80% تغطية بالاختبارات
- **التعقيد**: Cyclomatic Complexity < 10

### الحالي (PH-02):
- ✅ **التوثيق**: 90% (ممتاز)
- ✅ **التنسيق**: 100% (مكتمل)
- 🔄 **التغطية**: قيد التطوير
- 🔄 **التعقيد**: قيد المراجعة

## 📖 مراجع إضافية

### المعايير المتبعة:
- [PSR-12: Extended Coding Style](https://www.php-fig.org/psr/psr-12/)
- [PHPDoc Standards](https://docs.phpdoc.org/3.0/guide/)
- [Clean Code Principles](https://github.com/jupeter/clean-code-php)

### الأدوات المستخدمة:
- [PHP CS Fixer](https://cs.symfony.com/)
- [PHPStan](https://phpstan.org/)
- [Psalm](https://psalm.dev/)

## 🔄 التحديثات المستقبلية

### المخطط لها:
1. **إضافة PHPStan** للتحليل الثابت
2. **إضافة Psalm** للتحقق من الأنواع
3. **إضافة PHPMD** لكشف المشاكل
4. **إضافة Pre-commit Hooks** للتحقق التلقائي
5. **إضافة CI/CD Checks** للجودة

### التحسينات المستمرة:
- مراجعة دورية للمعايير
- تدريب الفريق على أفضل الممارسات
- تحديث الأدوات والمعايير
- قياس ومراقبة جودة الكود

---

**آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0  
**المسؤول**: فريق تطوير MotorLine
