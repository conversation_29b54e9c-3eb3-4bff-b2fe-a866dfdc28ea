<?php

namespace Modules\CarCatalog\Http\Controllers\Admin;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\CarCatalog\Http\Requests\Admin\StoreFeatureCategoryRequest;
use Modules\CarCatalog\Http\Requests\Admin\UpdateFeatureCategoryRequest;
use Modules\CarCatalog\Models\FeatureCategory;
use Modules\Core\Http\Controllers\BaseController;

/**
 * وحدة تحكم إدارة فئات الميزات.
 *
 * تتعامل هذه الوحدة مع عمليات CRUD لفئات الميزات في لوحة تحكم الإدارة
 */
class FeatureCategoryController extends BaseController
{
    /**
     * عرض قائمة فئات الميزات.
     *
     * @param Request $request طلب HTTP
     *
     * @return View
     */
    public function index(Request $request): View
    {
        $query = FeatureCategory::withCount('features');

        // تطبيق فلتر البحث بالاسم إذا تم تقديمه
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->input('search') . '%');
        }

        // تطبيق فلتر الحالة إذا تم تقديمه
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // ترتيب وترقيم النتائج
        $featureCategories = $query->orderBy('display_order')->orderBy('name')->paginate(10);

        return view('carcatalog::admin.featurecategories.index', compact('featureCategories'));
    }

    /**
     * عرض نموذج إنشاء فئة ميزة جديدة.
     *
     * @return View
     */
    public function create(): View
    {
        return view('carcatalog::admin.featurecategories.create');
    }

    /**
     * تخزين فئة ميزة جديدة.
     *
     * @param StoreFeatureCategoryRequest $request طلب تخزين فئة ميزة
     *
     * @return RedirectResponse
     */
    public function store(StoreFeatureCategoryRequest $request): RedirectResponse
    {
        // إنشاء فئة الميزة باستخدام البيانات المتحقق منها
        FeatureCategory::create($request->validated());

        return redirect()->route('admin.feature-categories.index')->with('success', 'تمت إضافة فئة الميزة بنجاح.');
    }

    /**
     * عرض تفاصيل فئة ميزة محددة.
     *
     * @param FeatureCategory $featurecategory فئة الميزة المراد عرضها
     *
     * @return View
     */
    public function show(FeatureCategory $featurecategory): View
    {
        // تحميل الميزات المرتبطة بفئة الميزة
        $featurecategory->loadCount('features');

        return view('carcatalog::admin.featurecategories.show', compact('featurecategory'));
    }

    /**
     * عرض نموذج تعديل فئة ميزة موجودة.
     *
     * @param FeatureCategory $featurecategory فئة الميزة المراد تعديلها
     *
     * @return View
     */
    public function edit(FeatureCategory $featurecategory): View
    {
        return view('carcatalog::admin.featurecategories.edit', compact('featurecategory'));
    }

    /**
     * تحديث فئة ميزة موجودة.
     *
     * @param UpdateFeatureCategoryRequest $request طلب تحديث فئة ميزة
     * @param FeatureCategory $featurecategory فئة الميزة المراد تحديثها
     *
     * @return RedirectResponse
     */
    public function update(UpdateFeatureCategoryRequest $request, FeatureCategory $featurecategory): RedirectResponse
    {
        // تحديث فئة الميزة باستخدام البيانات المتحقق منها
        $featurecategory->update($request->validated());

        return redirect()->route('admin.feature-categories.index')->with('success', 'تم تحديث فئة الميزة بنجاح.');
    }

    /**
     * حذف فئة ميزة محددة.
     *
     * @param FeatureCategory $featurecategory فئة الميزة المراد حذفها
     *
     * @return RedirectResponse
     */
    public function destroy(FeatureCategory $featurecategory): RedirectResponse
    {
        // التحقق من وجود ميزات مرتبطة بفئة الميزة
        if ($featurecategory->features()->count() > 0) {
            return redirect()->route('admin.feature-categories.index')
                ->with('error', 'لا يمكن حذف فئة الميزة لأنها مرتبطة بميزات أخرى.');
        }

        // حذف فئة الميزة
        $featurecategory->delete();

        return redirect()->route('admin.feature-categories.index')->with('success', 'تم حذف فئة الميزة بنجاح.');
    }
}
