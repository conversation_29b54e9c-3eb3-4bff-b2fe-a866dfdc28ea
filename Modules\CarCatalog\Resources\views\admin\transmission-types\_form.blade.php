{{-- حقل اسم نوع ناقل الحركة --}}
<div class="mb-3">
    <label for="name" class="form-label">اسم نوع ناقل الحركة <span class="text-danger">*</span></label>
    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" 
           value="{{ old('name', $transmissionType->name ?? '') }}" 
           maxlength="50" 
           required>
    @error('name')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">أدخل اسم نوع ناقل الحركة (حد أقصى 50 حرف)</div>
</div>

{{-- حقل الوصف --}}
<div class="mb-3">
    <label for="description" class="form-label">الوصف</label>
    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" 
              rows="3" maxlength="500">{{ old('description', $transmissionType->description ?? '') }}</textarea>
    @error('description')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">وصف اختياري لنوع ناقل الحركة (حد أقصى 500 حرف)</div>
</div>

{{-- حقل الحالة --}}
<div class="mb-3">
    <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
        <option value="">اختر الحالة</option>
        <option value="1" {{ old('status', $transmissionType->status ?? '') == '1' ? 'selected' : '' }}>نشط</option>
        <option value="0" {{ old('status', $transmissionType->status ?? '') === '0' ? 'selected' : '' }}>غير نشط</option>
    </select>
    @error('status')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">حدد حالة نوع ناقل الحركة</div>
</div>
