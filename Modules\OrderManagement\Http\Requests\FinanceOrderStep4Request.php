<?php

namespace Modules\OrderManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * طلب التحقق من صحة بيانات الخطوة الرابعة لطلب التمويل
 * 
 * يتحقق من المراجعة النهائية والتأكيد
 * بناءً على MOD-ORDER-MGMT-FEAT-004 في REQ-FR.md
 */
class FinanceOrderStep4Request extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return auth()->check() && 
               session()->has('finance_order_car_id') &&
               session()->has('finance_order_personal_data') &&
               session()->has('finance_order_finance_data') &&
               session()->has('finance_order_documents_data');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // تأكيد مراجعة جميع البيانات
            'data_review_confirmed' => [
                'required',
                'accepted'
            ],
            
            // الموافقة النهائية على شروط وأحكام التمويل
            'financing_terms_final_accepted' => [
                'required',
                'accepted'
            ],
            
            // الموافقة على سياسة الإلغاء والاسترداد
            'cancellation_policy_accepted' => [
                'required',
                'accepted'
            ],
            
            // تأكيد صحة جميع البيانات المدخلة
            'data_accuracy_confirmed' => [
                'required',
                'accepted'
            ],
            
            // الموافقة على التواصل للمتابعة
            'communication_consent' => [
                'required',
                'accepted'
            ],
            
            // الموافقة على فحص السجل الائتماني
            'credit_check_final_consent' => [
                'required',
                'accepted'
            ],
            
            // الموافقة على مشاركة البيانات مع جهات التمويل
            'data_sharing_financing_consent' => [
                'required',
                'accepted'
            ],
            
            // تأكيد فهم عملية التمويل
            'financing_process_understood' => [
                'required',
                'accepted'
            ],
            
            // ملاحظات نهائية (اختيارية)
            'final_notes' => [
                'nullable',
                'string',
                'max:500'
            ]
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'data_review_confirmed.required' => 'يجب تأكيد مراجعة جميع البيانات',
            'data_review_confirmed.accepted' => 'يجب تأكيد مراجعة جميع البيانات',
            
            'financing_terms_final_accepted.required' => 'يجب الموافقة على شروط وأحكام التمويل',
            'financing_terms_final_accepted.accepted' => 'يجب الموافقة على شروط وأحكام التمويل',
            
            'cancellation_policy_accepted.required' => 'يجب الموافقة على سياسة الإلغاء والاسترداد',
            'cancellation_policy_accepted.accepted' => 'يجب الموافقة على سياسة الإلغاء والاسترداد',
            
            'data_accuracy_confirmed.required' => 'يجب تأكيد صحة جميع البيانات المدخلة',
            'data_accuracy_confirmed.accepted' => 'يجب تأكيد صحة جميع البيانات المدخلة',
            
            'communication_consent.required' => 'يجب الموافقة على التواصل للمتابعة',
            'communication_consent.accepted' => 'يجب الموافقة على التواصل للمتابعة',
            
            'credit_check_final_consent.required' => 'يجب الموافقة على فحص السجل الائتماني',
            'credit_check_final_consent.accepted' => 'يجب الموافقة على فحص السجل الائتماني',
            
            'data_sharing_financing_consent.required' => 'يجب الموافقة على مشاركة البيانات مع جهات التمويل',
            'data_sharing_financing_consent.accepted' => 'يجب الموافقة على مشاركة البيانات مع جهات التمويل',
            
            'financing_process_understood.required' => 'يجب تأكيد فهم عملية التمويل',
            'financing_process_understood.accepted' => 'يجب تأكيد فهم عملية التمويل',
            
            'final_notes.max' => 'الملاحظات النهائية يجب ألا تزيد عن 500 حرف'
        ];
    }

    /**
     * أسماء الحقول المخصصة للعرض في رسائل الخطأ
     */
    public function attributes(): array
    {
        return [
            'data_review_confirmed' => 'تأكيد مراجعة البيانات',
            'financing_terms_final_accepted' => 'الموافقة على شروط التمويل',
            'cancellation_policy_accepted' => 'الموافقة على سياسة الإلغاء والاسترداد',
            'data_accuracy_confirmed' => 'تأكيد صحة البيانات',
            'communication_consent' => 'الموافقة على التواصل',
            'credit_check_final_consent' => 'الموافقة على فحص السجل الائتماني',
            'data_sharing_financing_consent' => 'الموافقة على مشاركة البيانات',
            'financing_process_understood' => 'تأكيد فهم عملية التمويل',
            'final_notes' => 'الملاحظات النهائية'
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'final_notes' => trim($this->final_notes ?? ''),
        ]);
    }

    /**
     * التحقق من اكتمال جميع الخطوات السابقة
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من وجود بيانات الخطوة الأولى
            $personalData = session('finance_order_personal_data');
            if (!$personalData || !is_array($personalData)) {
                $validator->errors()->add('step1', 'بيانات الخطوة الأولى غير مكتملة');
            }
            
            // التحقق من وجود بيانات الخطوة الثانية
            $financeData = session('finance_order_finance_data');
            if (!$financeData || !is_array($financeData)) {
                $validator->errors()->add('step2', 'بيانات الخطوة الثانية غير مكتملة');
            }
            
            // التحقق من وجود بيانات الخطوة الثالثة
            $documentsData = session('finance_order_documents_data');
            if (!$documentsData || !is_array($documentsData)) {
                $validator->errors()->add('step3', 'بيانات الخطوة الثالثة غير مكتملة');
            }
            
            // التحقق من وجود السيارة
            $carId = session('finance_order_car_id');
            if (!$carId) {
                $validator->errors()->add('car', 'لم يتم اختيار السيارة');
            }
            
            // التحقق من رفع المستندات المطلوبة
            if ($documentsData && is_array($documentsData)) {
                $requiredDocuments = [
                    'national_id_front_uploaded',
                    'national_id_back_uploaded', 
                    'driving_license_uploaded',
                    'salary_certificate_uploaded',
                    'bank_statement_uploaded'
                ];
                
                foreach ($requiredDocuments as $docField) {
                    if (!isset($documentsData[$docField]) || !$documentsData[$docField]) {
                        $validator->errors()->add('documents', 'لم يتم رفع جميع المستندات المطلوبة');
                        break;
                    }
                }
            }
            
            // التحقق من الحد الأدنى للدخل مقابل الالتزامات
            if ($financeData && is_array($financeData)) {
                $monthlyIncome = $financeData['monthly_income'] ?? 0;
                $monthlyObligations = $financeData['monthly_obligations'] ?? 0;
                $netIncome = $monthlyIncome - $monthlyObligations;
                
                // يجب أن يكون صافي الدخل 2000 ريال على الأقل
                if ($netIncome < 2000) {
                    $validator->errors()->add('income_validation', 'صافي الدخل (الدخل - الالتزامات) يجب أن يكون 2000 ريال على الأقل');
                }
            }
        });
    }

    /**
     * الحصول على البيانات المجمعة من جميع الخطوات
     */
    public function getCompleteOrderData(): array
    {
        return [
            'personal_data' => session('finance_order_personal_data', []),
            'finance_data' => session('finance_order_finance_data', []),
            'documents_data' => session('finance_order_documents_data', []),
            'confirmation_data' => $this->validated(),
            'car_id' => session('finance_order_car_id'),
            'order_type' => 'finance_application'
        ];
    }
}
