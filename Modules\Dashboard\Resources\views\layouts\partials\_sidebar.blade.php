<!-- Sidebar -->
<div class="sidebar" id="sidebar">
    <button class="mobile-sidebar-close d-lg-none" id="mobile-sidebar-close">
        <i class="fas fa-times"></i>
    </button>

    <div class="sidebar-header py-4">
        <h4 class="text-center text-white fw-bold mb-0">معرض السيارات</h4>
        <p class="text-center text-white small mb-0">لوحة تحكم الإدارة</p>
        <!-- شعار مصغر للعرض عند تصغير القائمة -->
        <div class="sidebar-logo-mini">
            <i class="fas fa-car-alt"></i>
        </div>
    </div>

    <ul class="nav flex-column mt-3">
        {{-- الرئيسية - تظهر فقط للمستخدمين الذين لديهم صلاحية الوصول للوحة التحكم --}}
        @can('access_admin_dashboard')
        <li class="nav-item">
            <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                <i class="fas fa-tachometer-alt"></i>
                <span>الرئيسية</span>
            </a>
        </li>
        @endcan

        {{-- إدارة السيارات - قائمة فرعية --}}
        @php
            $carSubmenuActive = request()->routeIs('admin.cars.*') ||
                              request()->routeIs('admin.brands.*') ||
                              request()->routeIs('admin.models.*') ||
                              request()->routeIs('admin.colors.*') ||
                              request()->routeIs('admin.years.*') ||
                              request()->routeIs('admin.transmission-types.*') ||
                              request()->routeIs('admin.fuel-types.*') ||
                              request()->routeIs('admin.body-types.*') ||
                              request()->routeIs('admin.feature-categories.*') ||
                              request()->routeIs('admin.car-features.*');
        @endphp
        @canany(['view_cars_admin', 'manage_cars_admin', 'manage_car_metadata'])
        <li class="nav-item">
            <a href="#cars-submenu" class="nav-link {{ $carSubmenuActive ? '' : 'collapsed' }}"
               data-bs-toggle="collapse"
               aria-expanded="{{ $carSubmenuActive ? 'true' : 'false' }}">
                <i class="fas fa-car-alt"></i>
                <span>إدارة السيارات</span>
                <i class="fas fa-chevron-down chevron"></i>
            </a>
            <div class="collapse {{ $carSubmenuActive ? 'show' : '' }}" id="cars-submenu">
                <ul class="nav flex-column submenu">
                    @can('view_cars_admin')
                    <li class="nav-item">
                        <a href="{{ route('admin.cars.index') }}" class="nav-link {{ request()->routeIs('admin.cars.index') ? 'active' : '' }}">
                            <i class="fas fa-list"></i>
                            <span>عرض جميع السيارات</span>
                        </a>
                    </li>
                    @endcan

                    @can('manage_cars_admin')
                    <li class="nav-item">
                        <a href="{{ route('admin.cars.create') }}" class="nav-link {{ request()->routeIs('admin.cars.create') ? 'active' : '' }}">
                            <i class="fas fa-plus"></i>
                            <span>إضافة سيارة جديدة</span>
                        </a>
                    </li>
                    @endcan

                    @can('manage_car_metadata')
                    <li class="nav-item">
                        <a href="{{ route('admin.brands.index') }}" class="nav-link {{ request()->routeIs('admin.brands.*') ? 'active' : '' }}">
                            <i class="fas fa-copyright"></i>
                            <span>الماركات</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('admin.models.index') }}" class="nav-link {{ request()->routeIs('admin.models.*') ? 'active' : '' }}">
                            <i class="fas fa-car-side"></i>
                            <span>الموديلات</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('admin.colors.index') }}" class="nav-link {{ request()->routeIs('admin.colors.*') ? 'active' : '' }}">
                            <i class="fas fa-palette"></i>
                            <span>الألوان</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('admin.years.index') }}" class="nav-link {{ request()->routeIs('admin.years.*') ? 'active' : '' }}">
                            <i class="fas fa-calendar-alt"></i>
                            <span>سنوات الصنع</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('admin.transmission-types.index') }}" class="nav-link {{ request()->routeIs('admin.transmission-types.*') ? 'active' : '' }}">
                            <i class="fas fa-cog"></i>
                            <span>أنواع ناقل الحركة</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('admin.fuel-types.index') }}" class="nav-link {{ request()->routeIs('admin.fuel-types.*') ? 'active' : '' }}">
                            <i class="fas fa-gas-pump"></i>
                            <span>أنواع الوقود</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('admin.body-types.index') }}" class="nav-link {{ request()->routeIs('admin.body-types.*') ? 'active' : '' }}">
                            <i class="fas fa-car"></i>
                            <span>أنواع الهيكل</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('admin.feature-categories.index') }}" class="nav-link {{ request()->routeIs('admin.feature-categories.*') ? 'active' : '' }}">
                            <i class="fas fa-layer-group"></i>
                            <span>فئات الميزات</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('admin.car-features.index') }}" class="nav-link {{ request()->routeIs('admin.car-features.*') ? 'active' : '' }}">
                            <i class="fas fa-list-ul"></i>
                            <span>ميزات السيارات</span>
                        </a>
                    </li>
                    @endcan
                </ul>
            </div>
        </li>
        @endcanany

        {{-- إدارة الطلبات - قائمة فرعية --}}
        @php
            $ordersSubmenuActive = request()->routeIs('admin.orders.*');
        @endphp
        @canany(['view_orders_admin', 'manage_orders_admin'])
        <li class="nav-item">
            <a href="#orders-submenu" class="nav-link {{ $ordersSubmenuActive ? '' : 'collapsed' }}"
               data-bs-toggle="collapse"
               aria-expanded="{{ $ordersSubmenuActive ? 'true' : 'false' }}">
                <i class="fas fa-shopping-cart"></i>
                <span>إدارة الطلبات</span>
                <i class="fas fa-chevron-down chevron"></i>
            </a>
            <div class="collapse {{ $ordersSubmenuActive ? 'show' : '' }}" id="orders-submenu">
                <ul class="nav flex-column submenu">
                    @can('view_orders_admin')
                    <li class="nav-item">
                        <a href="{{-- route('admin.orders.index') --}}#" class="nav-link {{ request()->routeIs('admin.orders.index') || request()->routeIs('admin.orders.show') ? 'active' : '' }}">
                            <i class="fas fa-clipboard-list"></i>
                            <span>جميع الطلبات</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{-- route('admin.orders.new') --}}#" class="nav-link {{ request()->routeIs('admin.orders.new') ? 'active' : '' }}">
                            <i class="fas fa-plus-circle"></i>
                            <span>طلبات جديدة</span>
                            {{-- عدد الطلبات الجديدة سيتم استرجاعه من قاعدة البيانات --}}
                            @php
                                // $newOrdersCount = App\Models\Order::where('status', 'new')->count();
                                $newOrdersCount = 5; // قيمة مؤقتة للعرض
                            @endphp
                            @if($newOrdersCount > 0)
                                <span class="badge rounded-pill bg-danger ms-2">{{ $newOrdersCount }}</span>
                            @endif
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{-- route('admin.orders.processing') --}}#" class="nav-link {{ request()->routeIs('admin.orders.processing') ? 'active' : '' }}">
                            <i class="fas fa-sync-alt"></i>
                            <span>قيد المعالجة</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{-- route('admin.orders.completed') --}}#" class="nav-link {{ request()->routeIs('admin.orders.completed') ? 'active' : '' }}">
                            <i class="fas fa-check-circle"></i>
                            <span>مكتملة</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{-- route('admin.orders.cancelled') --}}#" class="nav-link {{ request()->routeIs('admin.orders.cancelled') ? 'active' : '' }}">
                            <i class="fas fa-times-circle"></i>
                            <span>ملغاة</span>
                        </a>
                    </li>
                    @endcan

                    @can('manage_orders_admin')
                    <li class="nav-item">
                        <a href="{{-- route('admin.orders.create') --}}#" class="nav-link {{ request()->routeIs('admin.orders.create') ? 'active' : '' }}">
                            <i class="fas fa-file-invoice"></i>
                            <span>إضافة طلب يدويًا</span>
                        </a>
                    </li>
                    @endcan
                </ul>
            </div>
        </li>
        @endcanany

        {{-- إدارة العملاء --}}
        @can('view_customers_admin')
        <li class="nav-item">
            <a href="{{-- route('admin.customers.index') --}}#" class="nav-link {{ request()->routeIs('admin.customers.*') ? 'active' : '' }}">
                <i class="fas fa-users"></i>
                <span>إدارة العملاء</span>
            </a>
        </li>
        @endcan

        {{-- إدارة الموظفين - قائمة فرعية --}}
        @php
            $employeesSubmenuActive = request()->routeIs('admin.employees.*') || request()->routeIs('admin.roles.*');
        @endphp
        @canany(['manage_employees_admin', 'manage_roles_permissions'])
        <li class="nav-item">
            <a href="#employees-submenu" class="nav-link {{ $employeesSubmenuActive ? '' : 'collapsed' }}"
               data-bs-toggle="collapse"
               aria-expanded="{{ $employeesSubmenuActive ? 'true' : 'false' }}">
                <i class="fas fa-user-tie"></i>
                <span>إدارة الموظفين</span>
                <i class="fas fa-chevron-down chevron"></i>
            </a>
            <div class="collapse {{ $employeesSubmenuActive ? 'show' : '' }}" id="employees-submenu">
                <ul class="nav flex-column submenu">
                    @can('manage_employees_admin')
                    <li class="nav-item">
                        <a href="{{-- route('admin.employees.index') --}}#" class="nav-link {{ request()->routeIs('admin.employees.*') ? 'active' : '' }}">
                            <i class="fas fa-users-cog"></i>
                            <span>قائمة الموظفين</span>
                        </a>
                    </li>
                    @endcan

                    @can('manage_roles_permissions')
                    <li class="nav-item">
                        <a href="{{ route('admin.roles.index') }}" class="nav-link {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}">
                            <i class="fas fa-user-shield"></i>
                            <span>الأدوار والصلاحيات</span>
                        </a>
                    </li>
                    @endcan
                </ul>
            </div>
        </li>
        @endcanany

        {{-- إدارة المحتوى - قائمة فرعية --}}
        @php
            $cmsSubmenuActive = request()->routeIs('admin.cms.*');
        @endphp
        @can('manage_cms')
        <li class="nav-item">
            <a href="#cms-submenu" class="nav-link {{ $cmsSubmenuActive ? '' : 'collapsed' }}"
               data-bs-toggle="collapse"
               aria-expanded="{{ $cmsSubmenuActive ? 'true' : 'false' }}">
                <i class="fas fa-file-alt"></i>
                <span>إدارة المحتوى</span>
                <i class="fas fa-chevron-down chevron"></i>
            </a>
            <div class="collapse {{ $cmsSubmenuActive ? 'show' : '' }}" id="cms-submenu">
                <ul class="nav flex-column submenu">
                    <li class="nav-item">
                        <a href="{{-- route('admin.cms.pages.index') --}}#" class="nav-link {{ request()->routeIs('admin.cms.pages.*') ? 'active' : '' }}">
                            <i class="fas fa-file-alt"></i>
                            <span>الصفحات الثابتة</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{-- route('admin.cms.banners.index') --}}#" class="nav-link {{ request()->routeIs('admin.cms.banners.*') ? 'active' : '' }}">
                            <i class="fas fa-images"></i>
                            <span>بنرات الصفحة الرئيسية</span>
                        </a>
                    </li>
                </ul>
            </div>
        </li>
        @endcan

        {{-- الإعدادات العامة - قائمة فرعية --}}
        @php
            $settingsSubmenuActive = request()->routeIs('admin.settings.*');
        @endphp
        @can('manage_system_settings')
        <li class="nav-item">
            <a href="#settings-submenu" class="nav-link {{ $settingsSubmenuActive ? '' : 'collapsed' }}"
               data-bs-toggle="collapse"
               aria-expanded="{{ $settingsSubmenuActive ? 'true' : 'false' }}">
                <i class="fas fa-cog"></i>
                <span>الإعدادات العامة</span>
                <i class="fas fa-chevron-down chevron"></i>
            </a>
            <div class="collapse {{ $settingsSubmenuActive ? 'show' : '' }}" id="settings-submenu">
                <ul class="nav flex-column submenu">
                    <li class="nav-item">
                        <a href="{{ route('admin.settings.system.index') }}" class="nav-link {{ request()->routeIs('admin.settings.system.*') ? 'active' : '' }}">
                            <i class="fas fa-sliders-h"></i>
                            <span>إعدادات النظام</span>
                        </a>
                    </li>
                </ul>
            </div>
        </li>
        @endcan
    </ul>
</div>
<!-- End Sidebar -->