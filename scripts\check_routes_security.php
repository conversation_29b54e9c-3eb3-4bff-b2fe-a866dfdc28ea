<?php

/**
 * Route Security Checker Script
 * 
 * هذا السكريبت يتحقق من أمان المسارات الإدارية
 * ويتأكد من تطبيق وسائط الحماية المناسبة
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;

class RouteSecurityChecker
{
    private array $requiredMiddleware = [
        'web',
        'auth:web', 
        'verified',
        'role:Super Admin|Employee'
    ];

    private array $adminRoutePatterns = [
        '/admin',
        '/admin/*'
    ];

    private array $expectedPermissions = [
        'access_admin_dashboard',
        'manage_cars_admin',
        'manage_car_metadata', 
        'manage_roles_permissions',
        'manage_system_settings',
        'manage_employees_admin'
    ];

    public function checkRoutes(): array
    {
        $results = [
            'secure_routes' => [],
            'insecure_routes' => [],
            'missing_permissions' => [],
            'summary' => []
        ];

        // جلب جميع المسارات
        $routes = Route::getRoutes();

        foreach ($routes as $route) {
            $uri = $route->uri();
            $methods = $route->methods();
            $middleware = $route->middleware();
            
            // التحقق من المسارات الإدارية فقط
            if ($this->isAdminRoute($uri)) {
                $securityCheck = $this->checkRouteSecurity($route);
                
                if ($securityCheck['is_secure']) {
                    $results['secure_routes'][] = [
                        'uri' => $uri,
                        'methods' => $methods,
                        'middleware' => $middleware,
                        'controller' => $route->getActionName()
                    ];
                } else {
                    $results['insecure_routes'][] = [
                        'uri' => $uri,
                        'methods' => $methods,
                        'middleware' => $middleware,
                        'controller' => $route->getActionName(),
                        'issues' => $securityCheck['issues']
                    ];
                }
            }
        }

        $results['summary'] = [
            'total_admin_routes' => count($results['secure_routes']) + count($results['insecure_routes']),
            'secure_routes_count' => count($results['secure_routes']),
            'insecure_routes_count' => count($results['insecure_routes']),
            'security_percentage' => $this->calculateSecurityPercentage($results)
        ];

        return $results;
    }

    private function isAdminRoute(string $uri): bool
    {
        return str_starts_with($uri, 'admin') || str_contains($uri, '/admin');
    }

    private function checkRouteSecurity($route): array
    {
        $middleware = $route->middleware();
        $issues = [];
        
        // التحقق من وجود middleware الأساسي
        foreach ($this->requiredMiddleware as $required) {
            if (!in_array($required, $middleware)) {
                $issues[] = "Missing required middleware: {$required}";
            }
        }

        // التحقق من مسارات المصادقة (استثناءات)
        if (str_contains($route->uri(), '/auth/')) {
            // مسارات المصادقة لها قواعد مختلفة
            if (str_contains($route->uri(), 'login') || str_contains($route->uri(), 'register')) {
                if (!in_array('guest:web', $middleware)) {
                    $issues[] = "Auth routes should use guest:web middleware";
                }
            }
        }

        return [
            'is_secure' => empty($issues),
            'issues' => $issues
        ];
    }

    private function calculateSecurityPercentage(array $results): float
    {
        $total = $results['summary']['total_admin_routes'] ?? 0;
        $secure = $results['summary']['secure_routes_count'] ?? 0;
        
        return $total > 0 ? round(($secure / $total) * 100, 2) : 0;
    }

    public function generateReport(array $results): string
    {
        $report = "# Route Security Report\n\n";
        $report .= "Generated: " . date('Y-m-d H:i:s') . "\n\n";
        
        $report .= "## Summary\n";
        $report .= "- Total Admin Routes: {$results['summary']['total_admin_routes']}\n";
        $report .= "- Secure Routes: {$results['summary']['secure_routes_count']}\n";
        $report .= "- Insecure Routes: {$results['summary']['insecure_routes_count']}\n";
        $report .= "- Security Percentage: {$results['summary']['security_percentage']}%\n\n";

        if (!empty($results['insecure_routes'])) {
            $report .= "## ⚠️ Insecure Routes\n\n";
            foreach ($results['insecure_routes'] as $route) {
                $report .= "### {$route['uri']}\n";
                $report .= "- Methods: " . implode(', ', $route['methods']) . "\n";
                $report .= "- Controller: {$route['controller']}\n";
                $report .= "- Issues:\n";
                foreach ($route['issues'] as $issue) {
                    $report .= "  - {$issue}\n";
                }
                $report .= "\n";
            }
        }

        if (!empty($results['secure_routes'])) {
            $report .= "## ✅ Secure Routes\n\n";
            foreach ($results['secure_routes'] as $route) {
                $report .= "- {$route['uri']} ({$route['controller']})\n";
            }
        }

        return $report;
    }
}

// تشغيل الفحص
if (php_sapi_name() === 'cli') {
    echo "🔍 Checking route security...\n";
    
    $checker = new RouteSecurityChecker();
    $results = $checker->checkRoutes();
    $report = $checker->generateReport($results);
    
    // حفظ التقرير
    file_put_contents(__DIR__ . '/../docs/route_security_report.md', $report);
    
    echo "✅ Security check completed!\n";
    echo "📊 Security Percentage: {$results['summary']['security_percentage']}%\n";
    echo "📄 Report saved to: docs/route_security_report.md\n";
    
    if ($results['summary']['insecure_routes_count'] > 0) {
        echo "⚠️  Found {$results['summary']['insecure_routes_count']} insecure routes!\n";
        exit(1);
    } else {
        echo "🎉 All routes are secure!\n";
        exit(0);
    }
}
