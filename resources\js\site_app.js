/**
 * Site App JavaScript - ملف الجافا سكريبت الرئيسي للموقع العام
 * يحتوي على الوظائف والتفاعلات المخصصة للموقع العام
 */

// إعدادات عامة
const SiteApp = {
    // إعدادات الـ CSRF
    csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
    
    // إعدادات الـ API
    apiBaseUrl: '/api/v1',
    
    // إعدادات التحميل
    loadingOverlay: null,
    
    // إعدادات التنبيهات
    alertTimeout: 5000,
    
    // تهيئة التطبيق
    init() {
        this.setupCSRF();
        this.setupLoadingOverlay();
        this.setupScrollToTop();
        this.setupAlerts();
        this.setupForms();
        this.setupCarCards();
        this.setupSearch();
        this.setupFavorites();
        this.setupModals();
        this.setupTooltips();
        
        console.log('Site App initialized successfully');
    },
    
    // إعداد CSRF Token
    setupCSRF() {
        if (this.csrfToken) {
            // إعداد CSRF للـ AJAX requests
            const headers = {
                'X-CSRF-TOKEN': this.csrfToken,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };
            
            // إعداد Axios إذا كان متوفراً
            if (typeof axios !== 'undefined') {
                axios.defaults.headers.common['X-CSRF-TOKEN'] = this.csrfToken;
            }
            
            // إعداد Fetch API
            window.fetchWithCSRF = (url, options = {}) => {
                return fetch(url, {
                    ...options,
                    headers: {
                        ...headers,
                        ...options.headers
                    }
                });
            };
        }
    },
    
    // إعداد شاشة التحميل
    setupLoadingOverlay() {
        this.loadingOverlay = document.getElementById('loadingOverlay');
    },
    
    // إظهار شاشة التحميل
    showLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.style.display = 'flex';
        }
    },
    
    // إخفاء شاشة التحميل
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.style.display = 'none';
        }
    },
    
    // إعداد زر العودة للأعلى
    setupScrollToTop() {
        const scrollToTopBtn = document.getElementById('scrollToTop');
        
        if (scrollToTopBtn) {
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    scrollToTopBtn.classList.add('show');
                } else {
                    scrollToTopBtn.classList.remove('show');
                }
            });
            
            scrollToTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    },
    
    // إعداد التنبيهات
    setupAlerts() {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        
        alerts.forEach(alert => {
            // إخفاء التنبيه تلقائياً
            setTimeout(() => {
                if (alert && alert.parentNode) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, this.alertTimeout);
        });
    },
    
    // إعداد النماذج
    setupForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                const submitBtn = form.querySelector('button[type="submit"]');
                
                if (submitBtn && !submitBtn.disabled) {
                    // تعطيل الزر ومنع الإرسال المتكرر
                    submitBtn.disabled = true;
                    
                    // تغيير نص الزر
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                    
                    // إعادة تفعيل الزر بعد فترة (في حالة فشل الإرسال)
                    setTimeout(() => {
                        if (submitBtn.disabled) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = originalText;
                        }
                    }, 10000);
                }
            });
        });
    },
    
    // إعداد بطاقات السيارات
    setupCarCards() {
        const carCards = document.querySelectorAll('.car-card');
        
        carCards.forEach(card => {
            // تأثير التحويم
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    },
    
    // إعداد البحث
    setupSearch() {
        const searchModal = document.getElementById('searchModal');
        const brandSelect = document.getElementById('search_brand');
        const modelSelect = document.getElementById('search_model');
        
        if (brandSelect && modelSelect) {
            // تحميل الماركات عند فتح المودال
            searchModal?.addEventListener('shown.bs.modal', () => {
                this.loadBrands();
            });
            
            // تحديث الموديلات عند تغيير الماركة
            brandSelect.addEventListener('change', () => {
                const brandId = brandSelect.value;
                if (brandId) {
                    this.loadModels(brandId);
                } else {
                    modelSelect.innerHTML = '<option value="">اختر الموديل</option>';
                }
            });
        }
    },
    
    // تحميل الماركات
    async loadBrands() {
        try {
            const response = await fetch('/api/brands');
            const brands = await response.json();
            
            const brandSelect = document.getElementById('search_brand');
            if (brandSelect) {
                brandSelect.innerHTML = '<option value="">اختر الماركة</option>';
                
                brands.forEach(brand => {
                    const option = document.createElement('option');
                    option.value = brand.id;
                    option.textContent = brand.name;
                    brandSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading brands:', error);
        }
    },
    
    // تحميل الموديلات
    async loadModels(brandId) {
        try {
            const response = await fetch(`/api/brands/${brandId}/models`);
            const models = await response.json();
            
            const modelSelect = document.getElementById('search_model');
            if (modelSelect) {
                modelSelect.innerHTML = '<option value="">اختر الموديل</option>';
                
                models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name;
                    modelSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading models:', error);
        }
    },
    
    // إعداد المفضلة
    setupFavorites() {
        const favoriteButtons = document.querySelectorAll('.favorite-btn');
        
        favoriteButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                const carId = btn.dataset.carId;
                if (carId) {
                    this.toggleFavorite(carId, btn);
                }
            });
        });
    },
    
    // تبديل حالة المفضلة
    async toggleFavorite(carId, button) {
        try {
            this.showLoading();
            
            const response = await fetchWithCSRF(`/api/favorites/toggle`, {
                method: 'POST',
                body: JSON.stringify({ car_id: carId })
            });
            
            const result = await response.json();
            
            if (response.ok) {
                // تحديث حالة الزر
                if (result.is_favorite) {
                    button.classList.add('active');
                    button.innerHTML = '<i class="fas fa-heart"></i>';
                } else {
                    button.classList.remove('active');
                    button.innerHTML = '<i class="far fa-heart"></i>';
                }
                
                // تحديث عداد المفضلة
                this.updateFavoritesCount();
                
                // إظهار رسالة نجاح
                this.showToast(result.message, 'success');
            } else {
                throw new Error(result.message || 'حدث خطأ غير متوقع');
            }
        } catch (error) {
            console.error('Error toggling favorite:', error);
            this.showToast('حدث خطأ أثناء إضافة/إزالة المفضلة', 'error');
        } finally {
            this.hideLoading();
        }
    },
    
    // تحديث عداد المفضلة
    async updateFavoritesCount() {
        try {
            const response = await fetch('/api/favorites/count');
            const result = await response.json();
            
            const countElement = document.querySelector('.favorites-count');
            if (countElement) {
                countElement.textContent = result.count;
            }
        } catch (error) {
            console.error('Error updating favorites count:', error);
        }
    },
    
    // إعداد المودالات
    setupModals() {
        // إعداد مودال البحث
        const searchModal = document.getElementById('searchModal');
        if (searchModal) {
            searchModal.addEventListener('hidden.bs.modal', () => {
                // إعادة تعيين النموذج
                const form = searchModal.querySelector('form');
                if (form) {
                    form.reset();
                }
            });
        }
    },
    
    // إعداد التلميحات
    setupTooltips() {
        // تفعيل Bootstrap Tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(tooltipTriggerEl => {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // إظهار رسالة Toast
    showToast(message, type = 'success') {
        // إنشاء عنصر Toast
        const toastContainer = document.getElementById('toast-container') || this.createToastContainer();
        
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // إزالة Toast بعد إخفائه
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    },
    
    // إنشاء حاوية Toast
    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    },
    
    // دوال مساعدة
    utils: {
        // تنسيق الأرقام
        formatNumber(number) {
            return new Intl.NumberFormat('ar-SA').format(number);
        },
        
        // تنسيق العملة
        formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(amount);
        },
        
        // تنسيق التاريخ
        formatDate(date) {
            return new Intl.DateTimeFormat('ar-SA').format(new Date(date));
        },
        
        // التحقق من صحة البريد الإلكتروني
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        
        // التحقق من صحة رقم الجوال السعودي
        isValidSaudiPhone(phone) {
            const phoneRegex = /^(05|5)[0-9]{8}$/;
            return phoneRegex.test(phone.replace(/\s+/g, ''));
        }
    }
};

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    SiteApp.init();
});

// تصدير SiteApp للاستخدام العام
window.SiteApp = SiteApp;
