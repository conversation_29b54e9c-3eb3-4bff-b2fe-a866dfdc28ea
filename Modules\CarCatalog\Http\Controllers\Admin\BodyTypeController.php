<?php

namespace Modules\CarCatalog\Http\Controllers\Admin;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\CarCatalog\Http\Requests\Admin\StoreBodyTypeRequest;
use Modules\CarCatalog\Http\Requests\Admin\UpdateBodyTypeRequest;
use Modules\CarCatalog\Models\BodyType;
use Modules\Core\Http\Controllers\BaseController;

/**
 * وحدة تحكم إدارة أنواع هياكل السيارات.
 *
 * تتعامل هذه الوحدة مع عمليات CRUD لأنواع هياكل السيارات في لوحة تحكم الإدارة
 */
class BodyTypeController extends BaseController
{
    /**
     * عرض قائمة أنواع هياكل السيارات.
     *
     * @param Request $request طلب HTTP
     *
     * @return View
     */
    public function index(Request $request): View
    {
        $query = BodyType::withCount('cars');

        // تطبيق فلتر البحث بالاسم إذا تم تقديمه
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->input('search') . '%');
        }

        // تطبيق فلتر الحالة إذا تم تقديمه
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // ترتيب وترقيم النتائج
        $bodyTypes = $query->orderBy('name')->paginate(10);

        return view('carcatalog::admin.bodytypes.index', compact('bodyTypes'));
    }

    /**
     * عرض نموذج إنشاء نوع هيكل سيارة جديد.
     *
     * @return View
     */
    public function create(): View
    {
        return view('carcatalog::admin.bodytypes.create');
    }

    /**
     * تخزين نوع هيكل سيارة جديد.
     *
     * @param StoreBodyTypeRequest $request طلب تخزين نوع هيكل سيارة
     *
     * @return RedirectResponse
     */
    public function store(StoreBodyTypeRequest $request): RedirectResponse
    {
        // إنشاء نوع هيكل السيارة باستخدام البيانات المتحقق منها
        BodyType::create($request->validated());

        return redirect()->route('admin.body-types.index')->with('success', 'تمت إضافة نوع هيكل السيارة بنجاح.');
    }

    /**
     * عرض تفاصيل نوع هيكل سيارة محدد.
     *
     * @param BodyType $bodytype نوع هيكل السيارة المراد عرضه
     *
     * @return View
     */
    public function show(BodyType $bodytype): View
    {
        // تحميل عدد السيارات المرتبطة
        $bodytype->loadCount('cars');

        return view('carcatalog::admin.bodytypes.show', compact('bodytype'));
    }

    /**
     * عرض نموذج تعديل نوع هيكل سيارة موجود.
     *
     * @param BodyType $bodytype نوع هيكل السيارة المراد تعديله
     *
     * @return View
     */
    public function edit(BodyType $bodytype): View
    {
        return view('carcatalog::admin.bodytypes.edit', compact('bodytype'));
    }

    /**
     * تحديث نوع هيكل سيارة موجود.
     *
     * @param UpdateBodyTypeRequest $request طلب تحديث نوع هيكل سيارة
     * @param BodyType $bodytype نوع هيكل السيارة المراد تحديثه
     *
     * @return RedirectResponse
     */
    public function update(UpdateBodyTypeRequest $request, BodyType $bodytype): RedirectResponse
    {
        // تحديث نوع هيكل السيارة باستخدام البيانات المتحقق منها
        $bodytype->update($request->validated());

        return redirect()->route('admin.body-types.index')->with('success', 'تم تعديل نوع هيكل السيارة بنجاح.');
    }

    /**
     * حذف نوع هيكل سيارة موجود.
     *
     * @param BodyType $bodytype نوع هيكل السيارة المراد حذفه
     *
     * @return RedirectResponse
     */
    public function destroy(BodyType $bodytype): RedirectResponse
    {
        // التحقق من عدم وجود سيارات مرتبطة بنوع هيكل السيارة
        if ($bodytype->cars()->count() > 0) {
            return redirect()->route('admin.body-types.index')
                ->with('error', 'لا يمكن حذف نوع هيكل السيارة لأنه مرتبط بسيارات موجودة.');
        }

        // حذف نوع هيكل السيارة
        $bodytype->delete();

        return redirect()->route('admin.body-types.index')->with('success', 'تم حذف نوع هيكل السيارة بنجاح.');
    }
}
