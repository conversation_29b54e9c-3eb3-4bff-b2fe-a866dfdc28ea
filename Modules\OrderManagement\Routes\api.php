<?php

use Illuminate\Http\Request;
use Modules\OrderManagement\Http\Controllers\Api\PaymentController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/ordermanagement', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| Payment API Routes
|--------------------------------------------------------------------------
|
| Routes للتعامل مع webhooks وطلبات API المتعلقة بالدفع
|
*/

Route::prefix('payment')->name('api.payment.')->group(function() {

    // Webhook من بوابات الدفع (بدون middleware للمصادقة)
    Route::post('webhook', [PaymentController::class, 'webhook'])
        ->name('webhook');

    // Routes تتطلب مصادقة
    Route::middleware('auth:sanctum')->group(function() {

        // الحصول على حالة الدفع
        Route::get('status/{order}', [PaymentController::class, 'getPaymentStatus'])
            ->name('status');

        // إعادة محاولة الدفع
        Route::post('retry/{order}', [PaymentController::class, 'retryPayment'])
            ->name('retry');
    });
});