<?php

namespace Modules\OrderManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * طلب التحقق من صحة بيانات الخطوة الرابعة لطلب شراء سيارة كاش
 * 
 * يتحقق من المراجعة النهائية والتأكيد
 * بناءً على MOD-ORDER-MGMT-FEAT-003 في REQ-FR.md
 */
class CashOrderStep4Request extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return auth()->check() && 
               session()->has('cash_order_car_id') &&
               session()->has('cash_order_personal_data') &&
               session()->has('cash_order_payment_data') &&
               session()->has('cash_order_documents_data');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // تأكيد مراجعة جميع البيانات
            'data_review_confirmed' => [
                'required',
                'accepted'
            ],
            
            // الموافقة النهائية على الشروط والأحكام
            'final_terms_accepted' => [
                'required',
                'accepted'
            ],
            
            // الموافقة على سياسة الإلغاء والاسترداد
            'cancellation_policy_accepted' => [
                'required',
                'accepted'
            ],
            
            // تأكيد صحة جميع البيانات المدخلة
            'data_accuracy_confirmed' => [
                'required',
                'accepted'
            ],
            
            // الموافقة على التواصل للمتابعة
            'communication_consent' => [
                'required',
                'accepted'
            ],
            
            // ملاحظات نهائية (اختيارية)
            'final_notes' => [
                'nullable',
                'string',
                'max:500'
            ]
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'data_review_confirmed.required' => 'يجب تأكيد مراجعة جميع البيانات',
            'data_review_confirmed.accepted' => 'يجب تأكيد مراجعة جميع البيانات',
            
            'final_terms_accepted.required' => 'يجب الموافقة على الشروط والأحكام النهائية',
            'final_terms_accepted.accepted' => 'يجب الموافقة على الشروط والأحكام النهائية',
            
            'cancellation_policy_accepted.required' => 'يجب الموافقة على سياسة الإلغاء والاسترداد',
            'cancellation_policy_accepted.accepted' => 'يجب الموافقة على سياسة الإلغاء والاسترداد',
            
            'data_accuracy_confirmed.required' => 'يجب تأكيد صحة جميع البيانات المدخلة',
            'data_accuracy_confirmed.accepted' => 'يجب تأكيد صحة جميع البيانات المدخلة',
            
            'communication_consent.required' => 'يجب الموافقة على التواصل للمتابعة',
            'communication_consent.accepted' => 'يجب الموافقة على التواصل للمتابعة',
            
            'final_notes.max' => 'الملاحظات النهائية يجب ألا تزيد عن 500 حرف'
        ];
    }

    /**
     * أسماء الحقول المخصصة للعرض في رسائل الخطأ
     */
    public function attributes(): array
    {
        return [
            'data_review_confirmed' => 'تأكيد مراجعة البيانات',
            'final_terms_accepted' => 'الموافقة على الشروط والأحكام النهائية',
            'cancellation_policy_accepted' => 'الموافقة على سياسة الإلغاء والاسترداد',
            'data_accuracy_confirmed' => 'تأكيد صحة البيانات',
            'communication_consent' => 'الموافقة على التواصل',
            'final_notes' => 'الملاحظات النهائية'
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'final_notes' => trim($this->final_notes ?? ''),
        ]);
    }

    /**
     * التحقق من اكتمال جميع الخطوات السابقة
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من وجود بيانات الخطوة الأولى
            $personalData = session('cash_order_personal_data');
            if (!$personalData || !is_array($personalData)) {
                $validator->errors()->add('step1', 'بيانات الخطوة الأولى غير مكتملة');
            }
            
            // التحقق من وجود بيانات الخطوة الثانية
            $paymentData = session('cash_order_payment_data');
            if (!$paymentData || !is_array($paymentData)) {
                $validator->errors()->add('step2', 'بيانات الخطوة الثانية غير مكتملة');
            }
            
            // التحقق من وجود بيانات الخطوة الثالثة
            $documentsData = session('cash_order_documents_data');
            if (!$documentsData || !is_array($documentsData)) {
                $validator->errors()->add('step3', 'بيانات الخطوة الثالثة غير مكتملة');
            }
            
            // التحقق من وجود معرف السيارة
            $carId = session('cash_order_car_id');
            if (!$carId) {
                $validator->errors()->add('car', 'لم يتم اختيار السيارة');
            }
            
            // التحقق من صحة مبلغ الحجز
            $reservationAmount = session('cash_order_reservation_amount');
            if (!$reservationAmount || $reservationAmount <= 0) {
                $validator->errors()->add('reservation', 'مبلغ الحجز غير صحيح');
            }
        });
    }
}
