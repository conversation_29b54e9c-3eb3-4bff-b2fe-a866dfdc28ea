@extends('dashboard::layouts.admin_layout')

@section('title', 'تفاصيل الدور: ' . $role->name)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
<style>
/* تحسينات خاصة بصفحة عرض تفاصيل الدور */
.info-list-horizontal {
    padding: 0;
}

.info-item-horizontal {
    border-bottom: 1px solid #f1f5f9;
    padding-bottom: 0.75rem;
}

.info-item-horizontal:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.info-label-horizontal {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

.info-value-horizontal {
    font-size: 0.95rem;
    color: var(--text-color);
    font-weight: 500;
}

.permission-group-display {
    background: rgba(30, 58, 138, 0.02);
    border-radius: var(--border-radius);
    padding: 1rem;
    border: 1px solid rgba(30, 58, 138, 0.1);
}

.permission-display-item {
    transition: all 0.2s ease;
    border-radius: var(--border-radius) !important;
}

.permission-display-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.group-header {
    border-bottom: 2px solid rgba(30, 58, 138, 0.1);
    padding-bottom: 0.75rem;
}

/* تحسين الجدول */
.brand-table tbody tr:hover {
    background-color: rgba(30, 58, 138, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسين الأيقونات */
.brand-icon-primary {
    background: var(--secondary-color);
}

/* تأثيرات الحركة */
.brand-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

.brand-slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين responsive */
@media (max-width: 768px) {
    .info-item {
        text-align: center;
    }

    .permission-group-display {
        margin-bottom: 1rem;
    }

    .brand-card-header .d-flex {
        flex-direction: column;
        text-align: center;
    }

    .brand-icon-wrapper {
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'تفاصيل الدور: ' . $role->name,
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة المستخدمين', 'url' => null],
            ['name' => 'الأدوار', 'url' => route('admin.roles.index')],
            ['name' => $role->name, 'active' => true]
        ],
        'actions' => view('usermanagement::admin.roles._show_actions', compact('role'))->render()
    ])

    <div class="row">
        {{-- معلومات الدور --}}
        <div class="col-md-4 mb-4">
            <div class="brand-card brand-fade-in">
                <div class="brand-card-header">
                    <div class="d-flex align-items-center">
                        <div class="brand-icon-wrapper me-3">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <h5 class="mb-0 fw-bold">معلومات الدور</h5>
                    </div>
                </div>
                <div class="brand-card-body">
                    <div class="info-list-horizontal">
                        <div class="info-item-horizontal mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="info-label-horizontal">اسم الدور:</span>
                                <span class="info-value-horizontal fw-bold">{{ $role->name }}</span>
                            </div>
                        </div>
                        <div class="info-item-horizontal mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="info-label-horizontal">نوع الحماية:</span>
                                <span class="brand-status-badge brand-status-info">{{ $role->guard_name }}</span>
                            </div>
                        </div>
                        <div class="info-item-horizontal mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="info-label-horizontal">تاريخ الإنشاء:</span>
                                <span class="info-value-horizontal">{{ $role->created_at ? $role->created_at->format('d/m/Y H:i') : '-' }}</span>
                            </div>
                        </div>
                        <div class="info-item-horizontal mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="info-label-horizontal">آخر تحديث:</span>
                                <span class="info-value-horizontal">{{ $role->updated_at ? $role->updated_at->format('d/m/Y H:i') : '-' }}</span>
                            </div>
                        </div>
                        <div class="info-item-horizontal mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="info-label-horizontal">عدد المستخدمين:</span>
                                @if($role->users->count() > 0)
                                    <span class="brand-status-badge brand-status-primary">{{ $role->users->count() }}</span>
                                @else
                                    <span class="brand-status-badge brand-status-warning">0</span>
                                @endif
                            </div>
                        </div>
                        <div class="info-item-horizontal mb-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="info-label-horizontal">عدد الصلاحيات:</span>
                                @if($role->permissions->count() > 0)
                                    <span class="brand-status-badge brand-status-success">{{ $role->permissions->count() }}</span>
                                @else
                                    <span class="brand-status-badge brand-status-danger">0</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- الصلاحيات المعينة --}}
        <div class="col-md-8 mb-4">
            <div class="brand-card brand-fade-in">
                <div class="brand-card-header">
                    <div class="d-flex align-items-center">
                        <div class="brand-icon-wrapper me-3">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">الصلاحيات المعينة</h5>
                            <small class="opacity-75">{{ $role->permissions->count() }} صلاحية معينة لهذا الدور</small>
                        </div>
                    </div>
                </div>
                <div class="brand-card-body">
                    @if($role->permissions->count() > 0)
                        <div class="row">
                            @php
                                $groupedPermissions = $role->permissions->groupBy(function($item) {
                                    return explode('_', $item->name)[0] ?? 'general';
                                });
                            @endphp
                            @foreach($groupedPermissions as $group => $permissions)
                                @php
                                    $groupTranslation = get_permission_group_translation($group);
                                @endphp
                                <div class="col-md-6 mb-4">
                                    <div class="permission-group-display">
                                        <div class="group-header mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="brand-icon-wrapper brand-icon-success me-2" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                                    <i class="fas fa-{{ $groupTranslation['icon'] }}"></i>
                                                </div>
                                                <h6 class="mb-0 fw-bold" style="color: var(--secondary-color);">
                                                    {{ $groupTranslation['name'] }}
                                                </h6>
                                            </div>
                                        </div>
                                        <div class="permissions-list">
                                            @foreach($permissions as $permission)
                                                @php
                                                    $permissionTranslation = get_permission_translation($permission->name);
                                                @endphp
                                                <div class="permission-display-item mb-3 p-3 border rounded" style="background-color: rgba(16, 185, 129, 0.05); border-color: var(--success-color) !important;">
                                                    <div class="d-flex align-items-start">
                                                        <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                                                        <div class="flex-grow-1">
                                                            <div class="fw-bold text-dark mb-1">{{ $permissionTranslation['name'] }}</div>
                                                            <small class="text-muted">{{ $permissionTranslation['description'] }}</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="brand-icon-wrapper brand-icon-warning mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h5 class="text-muted mb-2">لا توجد صلاحيات معينة</h5>
                            <p class="text-muted mb-3">لم يتم تعيين أي صلاحيات لهذا الدور بعد.</p>
                            @can('manage_roles_permissions')
                            <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-brand-primary">
                                <i class="fas fa-plus me-1"></i>إضافة صلاحيات
                            </a>
                            @endcan
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    {{-- المستخدمون المرتبطون بالدور --}}
    @if($role->users->count() > 0)
    <div class="brand-card brand-slide-up mt-4">
        <div class="brand-card-header">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="brand-icon-wrapper brand-icon-success me-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">المستخدمون المرتبطون بهذا الدور</h5>
                        <small class="opacity-75">{{ $role->users->count() }} مستخدم لديه هذا الدور</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="brand-card-body p-0">
            <div class="table-responsive">
                <table class="table brand-table mb-0">
                    <thead>
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">الاسم</th>
                            <th scope="col">البريد الإلكتروني</th>
                            <th scope="col">رقم الجوال</th>
                            <th scope="col">الحالة</th>
                            <th scope="col">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($role->users as $user)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="brand-icon-wrapper brand-icon-primary me-2" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ $user->first_name }} {{ $user->last_name }}</div>
                                            <small class="text-muted">مستخدم</small>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ $user->email }}</td>
                                <td>{{ $user->phone_number ?? '-' }}</td>
                                <td>
                                    @if($user->status === 'active')
                                        <span class="brand-status-badge brand-status-success">نشط</span>
                                    @else
                                        <span class="brand-status-badge brand-status-warning">غير نشط</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <button class="brand-action-btn brand-action-view me-1" title="عرض المستخدم">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="brand-action-btn brand-action-edit" title="تعديل المستخدم">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
