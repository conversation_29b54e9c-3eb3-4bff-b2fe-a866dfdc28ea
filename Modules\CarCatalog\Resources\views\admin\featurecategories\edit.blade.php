@extends('dashboard::layouts.admin_layout')

@section('title', 'تعديل فئة الميزة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid">
    {{-- عنوان الصفحة --}}
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">تعديل فئة الميزة: {{ $featurecategory->name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item">إدارة السيارات</li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.feature-categories.index') }}">فئات الميزات</a></li>
                        <li class="breadcrumb-item active">تعديل</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    {{-- نموذج تعديل فئة الميزة --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">بيانات فئة الميزة</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.feature-categories.update', $featurecategory->id) }}" method="POST">
                @csrf
                @method('PUT')
                
                @include('carcatalog::admin.featurecategories._form', ['featureCategory' => $featurecategory])
                
                <div class="mt-4 d-flex justify-content-end">
                    <button type="submit" class="btn btn-success me-2">تحديث فئة الميزة</button>
                    <a href="{{ route('admin.feature-categories.index') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
