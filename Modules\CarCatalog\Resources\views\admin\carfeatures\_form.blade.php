{{-- حقل اسم الميزة --}}
<div class="mb-3">
    <label for="name" class="form-label">اسم الميزة <span class="text-danger">*</span></label>
    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" 
           value="{{ old('name', $carFeature->name ?? '') }}" 
           maxlength="100" 
           required>
    @error('name')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">أدخل اسم الميزة (حد أقصى 100 حرف)</div>
</div>

{{-- حقل فئة الميزة --}}
<div class="mb-3">
    <label for="category_id" class="form-label">فئة الميزة</label>
    <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id">
        <option value="">اختر فئة الميزة (اختياري)</option>
        @foreach($categories as $id => $name)
            <option value="{{ $id }}" {{ old('category_id', $carFeature->category_id ?? '') == $id ? 'selected' : '' }}>
                {{ $name }}
            </option>
        @endforeach
    </select>
    @error('category_id')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">اختر فئة الميزة (اختياري)</div>
</div>

{{-- حقل وصف الميزة --}}
<div class="mb-3">
    <label for="description" class="form-label">وصف الميزة</label>
    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" 
              rows="3" maxlength="500">{{ old('description', $carFeature->description ?? '') }}</textarea>
    @error('description')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">أدخل وصف الميزة (اختياري - حد أقصى 500 حرف)</div>
</div>

{{-- حقل قابلية التصفية --}}
<div class="mb-3">
    <label for="is_filterable" class="form-label">قابلة للتصفية <span class="text-danger">*</span></label>
    <select class="form-select @error('is_filterable') is-invalid @enderror" id="is_filterable" name="is_filterable" required>
        <option value="">اختر قابلية التصفية</option>
        <option value="1" {{ old('is_filterable', $carFeature->is_filterable ?? '') == '1' ? 'selected' : '' }}>نعم - قابلة للتصفية</option>
        <option value="0" {{ old('is_filterable', $carFeature->is_filterable ?? '') === '0' ? 'selected' : '' }}>لا - غير قابلة للتصفية</option>
    </select>
    @error('is_filterable')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">حدد ما إذا كانت هذه الميزة قابلة للتصفية في البحث المتقدم</div>
</div>

{{-- حقل الميزة المميزة --}}
<div class="mb-3">
    <label for="is_highlighted" class="form-label">ميزة مميزة <span class="text-danger">*</span></label>
    <select class="form-select @error('is_highlighted') is-invalid @enderror" id="is_highlighted" name="is_highlighted" required>
        <option value="">اختر نوع الميزة</option>
        <option value="1" {{ old('is_highlighted', $carFeature->is_highlighted ?? '') == '1' ? 'selected' : '' }}>نعم - ميزة مميزة</option>
        <option value="0" {{ old('is_highlighted', $carFeature->is_highlighted ?? '') === '0' ? 'selected' : '' }}>لا - ميزة عادية</option>
    </select>
    @error('is_highlighted')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">حدد ما إذا كانت هذه الميزة مميزة وتظهر في الصفحة الرئيسية</div>
</div>

{{-- حقل ترتيب العرض --}}
<div class="mb-3">
    <label for="display_order" class="form-label">ترتيب العرض</label>
    <input type="number" class="form-control @error('display_order') is-invalid @enderror" id="display_order" name="display_order" 
           value="{{ old('display_order', $carFeature->display_order ?? 0) }}" 
           min="0">
    @error('display_order')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">أدخل ترتيب العرض (اختياري - الافتراضي 0)</div>
</div>

{{-- حقل حالة الميزة --}}
<div class="mb-3">
    <label for="status" class="form-label">حالة الميزة <span class="text-danger">*</span></label>
    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
        <option value="">اختر الحالة</option>
        <option value="1" {{ old('status', $carFeature->status ?? '') == '1' ? 'selected' : '' }}>نشط</option>
        <option value="0" {{ old('status', $carFeature->status ?? '') === '0' ? 'selected' : '' }}>غير نشط</option>
    </select>
    @error('status')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">اختر حالة الميزة</div>
</div>
