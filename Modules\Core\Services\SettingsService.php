<?php

namespace Modules\Core\Services;

use Illuminate\Support\Facades\Cache;
use Modules\Core\Models\Setting;

/**
 * خدمة إدارة إعدادات النظام
 *
 * توفر هذه الخدمة واجهة برمجية للتعامل مع إعدادات النظام
 * مع دعم التخزين المؤقت (Caching) لتحسين الأداء
 */
class SettingsService
{
    /**
     * الحصول على قيمة إعداد معين
     *
     * @param  string  $key  مفتاح الإعداد
     * @param  mixed  $default  القيمة الافتراضية إذا لم يوجد الإعداد
     * @return mixed قيمة الإعداد أو القيمة الافتراضية
     */
    public function get(string $key, $default = null)
    {
        return Cache::rememberForever("setting.{$key}", function () use ($key, $default) {
            $setting = Setting::where('key', $key)->first();

            return $setting ? $setting->value : $default;
        });
    }

    /**
     * تعيين قيمة إعداد معين
     *
     * @param  string  $key  مفتاح الإعداد
     * @param  mixed  $value  قيمة الإعداد
     * @param  string|null  $group  مجموعة الإعداد
     * @param  string|null  $displayName  الاسم المعروض في واجهة الإدارة
     * @param  string  $type  نوع الإعداد
     * @param  array|null  $options  خيارات إضافية للنوع
     * @param  bool  $isTranslatable  هل قيمة الإعداد قابلة للترجمة؟
     * @return Setting نموذج الإعداد بعد التحديث
     */
    public function set(
        string $key,
        $value,
        ?string $group = null,
        ?string $displayName = null,
        string $type = 'text',
        ?array $options = null,
        bool $isTranslatable = false
    ): Setting {
        $setting = Setting::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'group_name' => $group,
                'display_name' => $displayName,
                'type' => $type,
                'options' => $options,
                'is_translatable' => $isTranslatable,
            ]
        );

        // مسح التخزين المؤقت لهذا الإعداد
        Cache::forget("setting.{$key}");

        return $setting;
    }

    /**
     * حذف إعداد معين
     *
     * @param  string  $key  مفتاح الإعداد
     * @return bool نجاح العملية
     */
    public function delete(string $key): bool
    {
        $result = Setting::where('key', $key)->delete();

        // مسح التخزين المؤقت لهذا الإعداد
        Cache::forget("setting.{$key}");

        return (bool) $result;
    }

    /**
     * الحصول على جميع الإعدادات مجمعة حسب المجموعة
     *
     * @return array الإعدادات مجمعة حسب المجموعة
     */
    public function getAllGrouped(): array
    {
        return Cache::rememberForever('settings.all.grouped', function () {
            $settings = Setting::all();
            $grouped = [];

            foreach ($settings as $setting) {
                $group = $setting->group_name ?? 'general';
                $grouped[$group][] = $setting;
            }

            return $grouped;
        });
    }

    /**
     * مسح التخزين المؤقت لجميع الإعدادات
     */
    public function clearCache(): void
    {
        Cache::forget('settings.all.grouped');

        $settings = Setting::all();
        foreach ($settings as $setting) {
            Cache::forget("setting.{$setting->key}");
        }
    }
}
