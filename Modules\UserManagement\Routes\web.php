<?php

use Illuminate\Support\Facades\Route;
use Modules\UserManagement\Http\Controllers\Site\SiteAuthController;

/*
|--------------------------------------------------------------------------
| Web Routes - Site Authentication
|--------------------------------------------------------------------------
|
| هنا يتم تعريف مسارات المصادقة الخاصة بالموقع العام
| مثل التسجيل، تسجيل الدخول، التحقق من OTP، واستعادة كلمة المرور
|
*/

// مسارات المصادقة للموقع العام
Route::prefix('auth')->name('site.auth.')->group(function () {

    // مسارات عرض النماذج (للزوار فقط)
    Route::middleware('guest:web')->group(function () {
        // صفحة نموذج التسجيل
        Route::get('register', [SiteAuthController::class, 'showRegisterForm'])
            ->name('register.form');

        // صفحة نموذج تسجيل الدخول
        Route::get('login', [SiteAuthController::class, 'showLoginForm'])
            ->name('login.form');

        // صفحة نموذج طلب استعادة كلمة المرور
        Route::get('forgot-password', [SiteAuthController::class, 'showForgotPasswordForm'])
            ->name('password.request.form');

        // صفحة نموذج إعادة تعيين كلمة المرور
        Route::get('reset-password/{token}', [SiteAuthController::class, 'showResetPasswordForm'])
            ->name('password.reset.form');
    });

    // صفحة التحقق من OTP (متاحة للمستخدمين غير المتحققين)
    Route::get('verify-otp', [SiteAuthController::class, 'showVerifyOtpForm'])
        ->name('verify.otp.form');

    // مسارات معالجة النماذج
    Route::post('register', [SiteAuthController::class, 'register'])
        ->middleware('guest:web')
        ->name('register.submit');

    Route::post('verify-otp', [SiteAuthController::class, 'verifyOtp'])
        ->name('verify.otp.submit');

    // مسار تسجيل الدخول مع حماية من brute force attacks
    Route::post('login', [SiteAuthController::class, 'login'])
        ->middleware('guest:web', 'throttle:5,1') // 5 محاولات كل دقيقة
        ->name('login.submit');

    // مسارات استعادة كلمة المرور
    Route::post('forgot-password', [SiteAuthController::class, 'sendResetLinkEmail'])
        ->middleware('guest:web', 'throttle:5,1') // 5 محاولات كل دقيقة
        ->name('password.email.submit');

    Route::post('reset-password', [SiteAuthController::class, 'resetPassword'])
        ->middleware('guest:web')
        ->name('password.update.submit');
});

Route::prefix('usermanagement')->group(function () {
    Route::get('/', 'UserManagementController@index');
});
