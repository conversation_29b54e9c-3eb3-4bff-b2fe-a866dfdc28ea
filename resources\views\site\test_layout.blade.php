{{--
    صفحة اختبار للتخطيط الرئيسي للموقع العام
    يمكن استخدامها للتحقق من عمل التخطيط والأنماط
--}}

@extends('site.layouts.site_layout')

@section('title', 'اختبار التخطيط - موتور لاين')

@section('meta_description', 'صفحة اختبار للتحقق من عمل التخطيط الرئيسي للموقع العام')

@section('content')
<div class="container">
    {{-- قسم الترحيب --}}
    <div class="hero-section">
        <div class="hero-content">
            <h1 class="hero-title">مرحباً بك في موتور لاين</h1>
            <p class="hero-subtitle">وجهتك الأولى لشراء السيارات الجديدة بأفضل الأسعار</p>
            <div class="hero-cta">
                <a href="#" class="btn btn-site-primary btn-lg">
                    <i class="fas fa-car me-2"></i>
                    تصفح السيارات
                </a>
                <a href="#" class="btn btn-site-outline btn-lg">
                    <i class="fas fa-phone me-2"></i>
                    اتصل بنا
                </a>
            </div>
        </div>
    </div>

    {{-- قسم اختبار البطاقات --}}
    <div class="content-section">
        <div class="section-header">
            <h2 class="section-title">أحدث السيارات</h2>
            <p class="section-subtitle">اكتشف مجموعتنا المتنوعة من السيارات الجديدة</p>
        </div>

        <div class="row">
            {{-- بطاقة سيارة تجريبية 1 --}}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="car-card">
                    <div class="car-card-image">
                        <img src="https://via.placeholder.com/400x200/007bff/ffffff?text=سيارة+1" alt="سيارة تجريبية">
                        <div class="car-card-badge">جديد</div>
                    </div>
                    <div class="car-card-body">
                        <h5 class="car-card-title">تويوتا كامري 2024</h5>
                        <div class="car-card-price">
                            120,000 ريال
                            <span class="original-price">130,000 ريال</span>
                        </div>
                        <div class="car-card-specs">
                            <div class="car-spec">
                                <i class="fas fa-calendar"></i>
                                2024
                            </div>
                            <div class="car-spec">
                                <i class="fas fa-gas-pump"></i>
                                بنزين
                            </div>
                            <div class="car-spec">
                                <i class="fas fa-cogs"></i>
                                أوتوماتيك
                            </div>
                        </div>
                        <div class="car-card-actions">
                            <a href="#" class="btn btn-brand-primary flex-fill">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </a>
                            <button class="favorite-btn" data-car-id="1">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {{-- بطاقة سيارة تجريبية 2 --}}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="car-card">
                    <div class="car-card-image">
                        <img src="https://via.placeholder.com/400x200/28a745/ffffff?text=سيارة+2" alt="سيارة تجريبية">
                        <div class="car-card-badge">عرض خاص</div>
                    </div>
                    <div class="car-card-body">
                        <h5 class="car-card-title">هوندا أكورد 2024</h5>
                        <div class="car-card-price">
                            95,000 ريال
                        </div>
                        <div class="car-card-specs">
                            <div class="car-spec">
                                <i class="fas fa-calendar"></i>
                                2024
                            </div>
                            <div class="car-spec">
                                <i class="fas fa-gas-pump"></i>
                                هايبرد
                            </div>
                            <div class="car-spec">
                                <i class="fas fa-cogs"></i>
                                CVT
                            </div>
                        </div>
                        <div class="car-card-actions">
                            <a href="#" class="btn btn-brand-primary flex-fill">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </a>
                            <button class="favorite-btn active" data-car-id="2">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {{-- بطاقة سيارة تجريبية 3 --}}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="car-card">
                    <div class="car-card-image">
                        <img src="https://via.placeholder.com/400x200/dc3545/ffffff?text=سيارة+3" alt="سيارة تجريبية">
                    </div>
                    <div class="car-card-body">
                        <h5 class="car-card-title">نيسان التيما 2024</h5>
                        <div class="car-card-price">
                            85,000 ريال
                        </div>
                        <div class="car-card-specs">
                            <div class="car-spec">
                                <i class="fas fa-calendar"></i>
                                2024
                            </div>
                            <div class="car-spec">
                                <i class="fas fa-gas-pump"></i>
                                بنزين
                            </div>
                            <div class="car-spec">
                                <i class="fas fa-cogs"></i>
                                أوتوماتيك
                            </div>
                        </div>
                        <div class="car-card-actions">
                            <a href="#" class="btn btn-brand-primary flex-fill">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </a>
                            <button class="favorite-btn" data-car-id="3">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- قسم اختبار النماذج --}}
    <div class="content-section">
        <div class="section-header">
            <h2 class="section-title">نموذج اختبار</h2>
            <p class="section-subtitle">اختبار النماذج والتفاعلات</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="search-form">
                    <h4 class="mb-4">ابحث عن سيارتك المثالية</h4>
                    <form>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="test_brand" class="brand-form-label">الماركة</label>
                                <select id="test_brand" class="form-select brand-form-control">
                                    <option value="">اختر الماركة</option>
                                    <option value="toyota">تويوتا</option>
                                    <option value="honda">هوندا</option>
                                    <option value="nissan">نيسان</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="test_model" class="brand-form-label">الموديل</label>
                                <select id="test_model" class="form-select brand-form-control">
                                    <option value="">اختر الموديل</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="test_year" class="brand-form-label">سنة الصنع</label>
                                <select id="test_year" class="form-select brand-form-control">
                                    <option value="">اختر السنة</option>
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                    <option value="2022">2022</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="test_price" class="brand-form-label">نطاق السعر</label>
                                <select id="test_price" class="form-select brand-form-control">
                                    <option value="">اختر النطاق</option>
                                    <option value="0-50000">أقل من 50,000 ريال</option>
                                    <option value="50000-100000">50,000 - 100,000 ريال</option>
                                    <option value="100000+">أكثر من 100,000 ريال</option>
                                </select>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-site-primary btn-lg">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                            <button type="button" class="btn btn-site-outline btn-lg ms-2" onclick="SiteApp.showToast('تم اختبار التنبيه بنجاح!', 'success')">
                                <i class="fas fa-bell me-2"></i>
                                اختبار التنبيه
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {{-- قسم اختبار الأزرار والعناصر --}}
    <div class="content-section">
        <div class="section-header">
            <h2 class="section-title">اختبار العناصر</h2>
        </div>

        <div class="row">
            <div class="col-md-6 mb-4">
                <h5>الأزرار</h5>
                <div class="d-flex gap-2 flex-wrap">
                    <button class="btn btn-brand-primary">زر أساسي</button>
                    <button class="btn btn-brand-secondary">زر ثانوي</button>
                    <button class="btn btn-brand-success">زر نجاح</button>
                    <button class="btn btn-brand-danger">زر خطر</button>
                    <button class="btn btn-brand-warning">زر تحذير</button>
                    <button class="btn btn-brand-info">زر معلومات</button>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <h5>التنبيهات</h5>
                <div class="alert brand-alert brand-alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    تم حفظ البيانات بنجاح!
                </div>
                <div class="alert brand-alert brand-alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومة مهمة للمستخدم.
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // اختبار JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        console.log('صفحة اختبار التخطيط تم تحميلها بنجاح');
        
        // اختبار تغيير الماركة
        const brandSelect = document.getElementById('test_brand');
        const modelSelect = document.getElementById('test_model');
        
        if (brandSelect && modelSelect) {
            brandSelect.addEventListener('change', function() {
                const brand = this.value;
                modelSelect.innerHTML = '<option value="">اختر الموديل</option>';
                
                if (brand === 'toyota') {
                    modelSelect.innerHTML += '<option value="camry">كامري</option>';
                    modelSelect.innerHTML += '<option value="corolla">كورولا</option>';
                } else if (brand === 'honda') {
                    modelSelect.innerHTML += '<option value="accord">أكورد</option>';
                    modelSelect.innerHTML += '<option value="civic">سيفيك</option>';
                } else if (brand === 'nissan') {
                    modelSelect.innerHTML += '<option value="altima">التيما</option>';
                    modelSelect.innerHTML += '<option value="sentra">سنترا</option>';
                }
            });
        }
    });
</script>
@endpush
