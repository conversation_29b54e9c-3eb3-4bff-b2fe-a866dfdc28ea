@extends('dashboard::layouts.admin_layout')

@section('title', 'تعديل الموديل: ' . $model->name)

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    <div class="mb-3">
        <h4 class="fw-bold mb-0">تعديل الموديل: {{ $model->name }}</h4>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.models.index') }}">إدارة الموديلات</a></li>
                <li class="breadcrumb-item active" aria-current="page">تعديل</li>
            </ol>
        </nav>
    </div>

    {{-- نموذج تعديل موديل --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">بيانات الموديل</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.models.update', $model->id) }}" method="POST">
                @csrf
                @method('PUT')
                
                @include('car_catalog::admin.models._form', ['model' => $model])
                
                <div class="mt-4 d-flex justify-content-end">
                    <button type="submit" class="btn btn-success me-2">تحديث الموديل</button>
                    <a href="{{ route('admin.models.index') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
