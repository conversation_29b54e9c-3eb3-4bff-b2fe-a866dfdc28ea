<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تحديث سنة صنع موجودة.
 *
 * يتحقق هذا الطلب من صحة بيانات تعديل سنة صنع موجودة
 */
class UpdateManufacturingYearRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'year' => [
                'required',
                'integer',
                'digits:4',
                Rule::unique('manufacturing_years')->ignore($this->year->id),
                'min:1900',
                'max:' . (date('Y') + 2),
            ],
            'status' => 'required|boolean',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة لقواعد التحقق
     *
     * @return array
     */
    public function messages()
    {
        return [
            'year.required'   => 'سنة الصنع مطلوبة.',
            'year.integer'    => 'سنة الصنع يجب أن تكون رقمًا صحيحًا.',
            'year.digits'     => 'سنة الصنع يجب أن تكون مكونة من 4 أرقام.',
            'year.unique'     => 'سنة الصنع موجودة مسبقًا.',
            'year.min'        => 'سنة الصنع يجب أن تكون 1900 أو أكثر.',
            'year.max'        => 'سنة الصنع يجب أن تكون ' . (date('Y') + 2) . ' أو أقل.',
            'status.required' => 'حالة سنة الصنع مطلوبة.',
            'status.boolean'  => 'حالة سنة الصنع يجب أن تكون نشطة أو غير نشطة.',
        ];
    }

    /**
     * الحصول على أسماء الخصائص المخصصة للتحقق
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'year'   => 'سنة الصنع',
            'status' => 'الحالة',
        ];
    }
}
