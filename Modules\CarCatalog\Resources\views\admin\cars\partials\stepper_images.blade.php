<div id="images-part" class="content" role="tabpanel" aria-labelledby="images-part-trigger">
    <h6 class="mb-4 text-primary">
        <i class="fas fa-images me-2"></i>
        صور وفيديو السيارة
    </h6>

    <!-- الصور الموجودة (في حالة التعديل) -->
    @if(isset($isEdit) && isset($car) && $car->getMedia('car_images')->count() > 0)
        <div class="row mb-4">
            <div class="col-12">
                <h6 class="text-secondary mb-3">
                    <i class="fas fa-image me-2"></i>
                    الصور الحالية
                </h6>
                <div class="row" id="existing-images">
                    @foreach($car->getMedia('car_images') as $media)
                        <div class="col-md-3 mb-3 image-card">
                            <div class="card">
                                @php
                                    $imageUrl = $media->getUrl('medium') ?: $media->getUrl();
                                @endphp
                                <img src="{{ $imageUrl }}" class="card-img-top"
                                     style="height: 150px; object-fit: cover;" alt="صورة السيارة">
                                <div class="card-body p-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">{{ $media->name }}</small>
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-image-btn"
                                                data-media-id="{{ $media->id }}" title="حذف الصورة">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    @if($car->getFirstMedia('car_main_image') && $car->getFirstMedia('car_main_image')->id == $media->id)
                                        <div class="mt-1">
                                            <span class="badge bg-success">صورة رئيسية</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        <hr class="my-4">
    @endif

    <!-- رفع صور جديدة -->
    <div class="row">
        <div class="col-12">
            <h6 class="text-secondary mb-3">
                <i class="fas fa-upload me-2"></i>
                {{ isset($isEdit) ? 'إضافة صور جديدة' : 'رفع صور السيارة' }}
            </h6>

            <div class="mb-3">
                <label for="car_images" class="form-label">
                    اختر صور السيارة
                    @if(!isset($isEdit))
                        <span class="text-danger">*</span>
                    @endif
                </label>
                <input type="file" class="form-control @error('car_images') is-invalid @enderror @error('car_images.*') is-invalid @enderror"
                       id="car_images" name="car_images[]" multiple accept="image/*"
                       {{ !isset($isEdit) ? 'required' : '' }}>
                <div class="form-text">
                    <i class="fas fa-info-circle me-1"></i>
                    يمكنك اختيار حتى 10 صور. الأنواع المدعومة: JPG, PNG, WEBP. الحد الأقصى لحجم الصورة: 5 ميجابايت.
                </div>
                @error('car_images')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                @error('car_images.*')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- معاينة الصور الجديدة -->
            <div class="row" id="{{ isset($isEdit) ? 'new-image-preview' : 'image-preview' }}">
                <!-- سيتم ملء هذا القسم بـ JavaScript عند اختيار الصور -->
            </div>
        </div>
    </div>

    <!-- رابط الفيديو -->
    <div class="row mt-4">
        <div class="col-12">
            <h6 class="text-secondary mb-3">
                <i class="fas fa-video me-2"></i>
                فيديو السيارة (اختياري)
            </h6>

            <div class="mb-3">
                <label for="video_url" class="form-label">رابط الفيديو</label>
                <input type="url" class="form-control @error('video_url') is-invalid @enderror"
                       id="video_url" name="video_url"
                       value="{{ old('video_url', isset($car) ? $car->video_url : '') }}"
                       placeholder="https://www.youtube.com/watch?v=...">
                <div class="form-text">
                    <i class="fas fa-info-circle me-1"></i>
                    يمكنك إضافة رابط فيديو من YouTube أو أي منصة أخرى لعرض السيارة
                </div>
                @error('video_url')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- معاينة الفيديو -->
            <div id="video-preview" class="mt-3" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">معاينة الفيديو</h6>
                    </div>
                    <div class="card-body">
                        <div class="ratio ratio-16x9">
                            <iframe id="video-iframe" src="" allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4 d-flex justify-content-between">
        <button type="button" class="btn btn-secondary" onclick="stepper.previous()">
            <i class="fas fa-arrow-right me-1"></i>
            السابق: الميزات
        </button>
        <button type="button" class="btn btn-primary" onclick="stepper.next()">
            التالي: السعر والحالة
            <i class="fas fa-arrow-left ms-1"></i>
        </button>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة معاينة الفيديو
    const videoInput = document.getElementById('video_url');
    const videoPreview = document.getElementById('video-preview');
    const videoIframe = document.getElementById('video-iframe');

    if (videoInput && videoPreview && videoIframe) {
        videoInput.addEventListener('input', function() {
            const url = this.value.trim();

            if (url) {
                // تحويل رابط YouTube إلى رابط embed
                let embedUrl = '';

                if (url.includes('youtube.com/watch?v=')) {
                    const videoId = url.split('v=')[1].split('&')[0];
                    embedUrl = `https://www.youtube.com/embed/${videoId}`;
                } else if (url.includes('youtu.be/')) {
                    const videoId = url.split('youtu.be/')[1].split('?')[0];
                    embedUrl = `https://www.youtube.com/embed/${videoId}`;
                } else if (url.includes('vimeo.com/')) {
                    const videoId = url.split('vimeo.com/')[1];
                    embedUrl = `https://player.vimeo.com/video/${videoId}`;
                } else {
                    // للروابط الأخرى، نحاول استخدامها مباشرة
                    embedUrl = url;
                }

                if (embedUrl) {
                    videoIframe.src = embedUrl;
                    videoPreview.style.display = 'block';
                } else {
                    videoPreview.style.display = 'none';
                }
            } else {
                videoPreview.style.display = 'none';
            }
        });

        // تشغيل المعاينة إذا كان هناك رابط موجود
        if (videoInput.value) {
            videoInput.dispatchEvent(new Event('input'));
        }
    }
});
</script>
