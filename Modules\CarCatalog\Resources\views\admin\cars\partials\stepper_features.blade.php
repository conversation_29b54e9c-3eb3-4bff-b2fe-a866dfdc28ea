<div id="features-part" class="content" role="tabpanel" aria-labelledby="features-part-trigger">
    <h6 class="mb-4 text-primary">
        <i class="fas fa-list-ul me-2"></i>
        ميزات السيارة
    </h6>

    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                اختر الميزات المتوفرة في هذه السيارة. يمكنك اختيار عدة ميزات من كل فئة.
            </div>
        </div>
    </div>

    @if($featureCategories->count() > 0)
        <div class="row">
            @foreach($featureCategories as $category)
                @if($category->features->count() > 0)
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0 text-dark">
                                    <i class="fas fa-tag me-2"></i>
                                    {{ $category->name }}
                                </h6>
                                @if($category->description)
                                    <small class="text-muted">{{ $category->description }}</small>
                                @endif
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    @foreach($category->features as $feature)
                                        <div class="col-12 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       name="features[]" value="{{ $feature->id }}"
                                                       id="feature_{{ $feature->id }}"
                                                       {{ in_array($feature->id, old('features', isset($selectedFeatures) ? $selectedFeatures : [])) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="feature_{{ $feature->id }}">
                                                    {{ $feature->name }}
                                                    @if($feature->description)
                                                        <small class="text-muted d-block">{{ $feature->description }}</small>
                                                    @endif
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <!-- أزرار التحكم السريع للفئة -->
                                <div class="mt-3 pt-3 border-top">
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2"
                                            onclick="selectAllInCategory('{{ $category->id }}')">
                                        <i class="fas fa-check-square me-1"></i>
                                        تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary"
                                            onclick="deselectAllInCategory('{{ $category->id }}')">
                                        <i class="fas fa-square me-1"></i>
                                        إلغاء التحديد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
    @else
        <div class="row">
            <div class="col-12">
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لا توجد ميزات متاحة حالياً. يمكنك إضافة الميزات من قسم إدارة الميزات.
                </div>
            </div>
        </div>
    @endif

    @error('features')
        <div class="alert alert-danger mt-3">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ $message }}
        </div>
    @enderror

    <div class="mt-4 d-flex justify-content-between">
        <button type="button" class="btn btn-secondary" onclick="stepper.previous()">
            <i class="fas fa-arrow-right me-1"></i>
            السابق: المواصفات الفنية
        </button>
        <button type="button" class="btn btn-primary" onclick="stepper.next()">
            التالي: الصور والفيديو
            <i class="fas fa-arrow-left ms-1"></i>
        </button>
    </div>
</div>

<script>
// دوال التحكم السريع في الميزات
function selectAllInCategory(categoryId) {
    const categoryCard = document.querySelector(`[data-category-id="${categoryId}"]`);
    if (categoryCard) {
        const checkboxes = categoryCard.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = true);
    }
}

function deselectAllInCategory(categoryId) {
    const categoryCard = document.querySelector(`[data-category-id="${categoryId}"]`);
    if (categoryCard) {
        const checkboxes = categoryCard.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
    }
}

// إضافة معرف الفئة لكل بطاقة
document.addEventListener('DOMContentLoaded', function() {
    @foreach($featureCategories as $category)
        const categoryCard{{ $category->id }} = document.querySelector('.card:has(#feature_{{ $category->features->first()->id ?? '' }})');
        if (categoryCard{{ $category->id }}) {
            categoryCard{{ $category->id }}.setAttribute('data-category-id', '{{ $category->id }}');
        }
    @endforeach
});
</script>
