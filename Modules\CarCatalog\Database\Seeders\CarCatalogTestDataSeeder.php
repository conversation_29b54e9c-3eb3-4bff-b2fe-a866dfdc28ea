<?php

namespace Modules\CarCatalog\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\CarCatalog\Models\BodyType;
use Modules\CarCatalog\Models\Brand;
use Modules\CarCatalog\Models\Car;
use Modules\CarCatalog\Models\CarFeature;
use Modules\CarCatalog\Models\CarModel;
use Modules\CarCatalog\Models\Color;
use Modules\CarCatalog\Models\FeatureCategory;
use Modules\CarCatalog\Models\FuelType;
use Modules\CarCatalog\Models\ManufacturingYear;
use Modules\CarCatalog\Models\TransmissionType;

/**
 * CarCatalogTestDataSeeder.
 *
 * هذا الـ Seeder مسؤول عن إنشاء بيانات وهمية لاختبار لوحة البيانات
 */
class CarCatalogTestDataSeeder extends Seeder
{
    /**
     * تشغيل عملية إضافة البيانات.
     */
    public function run()
    {
        // إنشاء الماركات
        $brands = $this->createBrands();

        // إنشاء الموديلات
        $models = $this->createModels($brands);

        // إنشاء سنوات الصنع
        $years = $this->createManufacturingYears();

        // إنشاء الألوان
        $colors = $this->createColors();

        // إنشاء أنواع ناقل الحركة
        $transmissions = $this->createTransmissionTypes();

        // إنشاء أنواع الوقود
        $fuelTypes = $this->createFuelTypes();

        // إنشاء أنواع الهياكل
        $bodyTypes = $this->createBodyTypes();

        // إنشاء فئات الميزات
        $featureCategories = $this->createFeatureCategories();

        // إنشاء الميزات
        $features = $this->createCarFeatures($featureCategories);

        // إنشاء السيارات
        $this->createCars($brands, $models, $years, $colors, $transmissions, $fuelTypes, $bodyTypes, $features);
    }

    /**
     * إنشاء الماركات.
     */
    private function createBrands()
    {
        $brandsData = [
            ['name' => 'تويوتا', 'description' => 'شركة يابانية رائدة في صناعة السيارات', 'status' => true],
            ['name' => 'هوندا', 'description' => 'شركة يابانية متخصصة في السيارات والدراجات النارية', 'status' => true],
            ['name' => 'نيسان', 'description' => 'شركة يابانية عريقة في صناعة السيارات', 'status' => true],
            ['name' => 'هيونداي', 'description' => 'شركة كورية جنوبية رائدة في صناعة السيارات', 'status' => true],
            ['name' => 'كيا', 'description' => 'شركة كورية جنوبية متخصصة في السيارات', 'status' => true],
            ['name' => 'مرسيدس بنز', 'description' => 'شركة ألمانية فاخرة في صناعة السيارات', 'status' => true],
            ['name' => 'بي إم دبليو', 'description' => 'شركة ألمانية متخصصة في السيارات الفاخرة', 'status' => true],
            ['name' => 'أودي', 'description' => 'شركة ألمانية فاخرة تابعة لمجموعة فولكس واجن', 'status' => true],
            ['name' => 'فولكس واجن', 'description' => 'شركة ألمانية عريقة في صناعة السيارات', 'status' => true],
            ['name' => 'فورد', 'description' => 'شركة أمريكية رائدة في صناعة السيارات', 'status' => true],
            ['name' => 'شيفروليه', 'description' => 'شركة أمريكية تابعة لجنرال موتورز', 'status' => true],
            ['name' => 'لكزس', 'description' => 'العلامة التجارية الفاخرة لشركة تويوتا', 'status' => true],
            ['name' => 'إنفينيتي', 'description' => 'العلامة التجارية الفاخرة لشركة نيسان', 'status' => true],
            ['name' => 'جينيسيس', 'description' => 'العلامة التجارية الفاخرة لشركة هيونداي', 'status' => true],
            ['name' => 'مازدا', 'description' => 'شركة يابانية متخصصة في السيارات', 'status' => true],
            ['name' => 'سوبارو', 'description' => 'شركة يابانية متخصصة في السيارات ذات الدفع الرباعي', 'status' => true],
            ['name' => 'ميتسوبيشي', 'description' => 'شركة يابانية متنوعة تشمل صناعة السيارات', 'status' => true],
            ['name' => 'جيب', 'description' => 'علامة تجارية أمريكية متخصصة في سيارات الدفع الرباعي', 'status' => true],
            ['name' => 'لاند روفر', 'description' => 'علامة تجارية بريطانية فاخرة للسيارات الرياضية', 'status' => true],
            ['name' => 'جاكوار', 'description' => 'علامة تجارية بريطانية فاخرة للسيارات الرياضية', 'status' => true],
        ];

        $brands = [];
        foreach ($brandsData as $brandData) {
            $brand = Brand::where('name->en', $brandData['name'])->first();
            if (!$brand) {
                $brand = Brand::create($brandData);
            }
            $brands[] = $brand;
        }

        return $brands;
    }

    /**
     * إنشاء الموديلات.
     *
     * @param mixed $brands
     */
    private function createModels($brands)
    {
        $modelsData = [
            'تويوتا'      => ['كامري', 'كورولا', 'لاند كروزر', 'برادو', 'يارس', 'أفالون', 'هايلاندر', 'راف 4', 'سيكويا', 'تاكوما'],
            'هوندا'       => ['أكورد', 'سيفيك', 'سي آر في', 'بايلوت', 'أوديسي', 'باسبورت', 'ريدج لاين', 'إنسايت'],
            'نيسان'       => ['التيما', 'سنترا', 'باترول', 'إكس تريل', 'مورانو', 'أرمادا', 'فيرسا', 'روج', 'باثفايندر'],
            'هيونداي'     => ['سوناتا', 'إلنترا', 'توسان', 'سانتا في', 'أكسنت', 'فيلوستر', 'باليسايد', 'كونا', 'فينيو'],
            'كيا'         => ['أوبتيما', 'سيراتو', 'سورينتو', 'سبورتاج', 'ريو', 'فورتي', 'تيلورايد', 'نيرو', 'ستينجر'],
            'مرسيدس بنز'  => ['C-Class', 'E-Class', 'S-Class', 'GLC', 'GLE', 'GLS', 'A-Class', 'CLA', 'GLB', 'G-Class'],
            'بي إم دبليو' => ['3 Series', '5 Series', 'X3', 'X5', '1 Series', '7 Series', 'X1', 'X7', 'Z4', 'i3'],
            'أودي'        => ['A4', 'A6', 'Q5', 'Q7', 'A3', 'A8', 'Q3', 'Q8', 'TT', 'e-tron'],
            'فولكس واجن'  => ['جيتا', 'باسات', 'تيجوان', 'أطلس', 'جولف', 'أرتيون', 'تاوس', 'آي دي 4'],
            'فورد'        => ['فيوجن', 'فوكس', 'إكسبلورر', 'إكسبيديشن', 'إف-150', 'إيدج', 'إسكيب', 'برونكو'],
            'شيفروليه'    => ['مالبيو', 'كروز', 'إكوينوكس', 'تاهو', 'سيلفرادو', 'تريفرس', 'بليزر', 'كامارو'],
            'لكزس'        => ['ES', 'IS', 'LS', 'RX', 'GX', 'LX', 'NX', 'UX', 'LC', 'RC'],
            'إنفينيتي'    => ['Q50', 'Q60', 'QX50', 'QX60', 'QX80', 'Q70'],
            'جينيسيس'     => ['G70', 'G80', 'G90', 'GV70', 'GV80'],
            'مازدا'       => ['مازدا 3', 'مازدا 6', 'CX-3', 'CX-5', 'CX-9', 'CX-30', 'MX-5'],
            'سوبارو'      => ['إمبريزا', 'ليجاسي', 'أوتباك', 'فورستر', 'أسنت', 'كروستريك'],
            'ميتسوبيشي'   => ['لانسر', 'أوتلاندر', 'إكليبس كروس', 'ميراج'],
            'جيب'         => ['رانجلر', 'جراند شيروكي', 'شيروكي', 'كومباس', 'رينيجيد'],
            'لاند روفر'   => ['ديسكفري', 'رينج روفر', 'رينج روفر سبورت', 'إيفوك', 'فيلار', 'ديفندر'],
            'جاكوار'      => ['XE', 'XF', 'XJ', 'F-PACE', 'E-PACE', 'I-PACE'],
        ];

        $models = [];
        foreach ($brands as $brand) {
            $brandName = is_array($brand->name) ? $brand->name['en'] : $brand->name;
            if (isset($modelsData[$brandName])) {
                foreach ($modelsData[$brandName] as $modelName) {
                    $existingModel = CarModel::where('brand_id', $brand->id)
                        ->where('name->en', $modelName)
                        ->first();

                    if (!$existingModel) {
                        $existingModel = CarModel::create([
                            'brand_id' => $brand->id,
                            'name'     => $modelName,
                            'status'   => true,
                        ]);
                    }
                    $models[] = $existingModel;
                }
            }
        }

        return $models;
    }

    /**
     * إنشاء سنوات الصنع.
     */
    private function createManufacturingYears()
    {
        $years = [];
        // إنشاء سنوات من 2015 إلى 2025 لتوفير تنوع أكبر
        for ($year = 2015; $year <= 2025; $year++) {
            $years[] = ManufacturingYear::firstOrCreate(['year' => $year], ['status' => true]);
        }

        return $years;
    }

    /**
     * إنشاء الألوان.
     */
    private function createColors()
    {
        $colorsData = [
            ['name' => 'أبيض', 'hex_code' => '#FFFFFF'],
            ['name' => 'أسود', 'hex_code' => '#000000'],
            ['name' => 'فضي', 'hex_code' => '#C0C0C0'],
            ['name' => 'رمادي', 'hex_code' => '#808080'],
            ['name' => 'أحمر', 'hex_code' => '#FF0000'],
            ['name' => 'أزرق', 'hex_code' => '#0000FF'],
            ['name' => 'أخضر', 'hex_code' => '#008000'],
            ['name' => 'بني', 'hex_code' => '#8B4513'],
            ['name' => 'ذهبي', 'hex_code' => '#FFD700'],
            ['name' => 'برونزي', 'hex_code' => '#CD7F32'],
            ['name' => 'أزرق داكن', 'hex_code' => '#000080'],
            ['name' => 'أحمر داكن', 'hex_code' => '#8B0000'],
            ['name' => 'رمادي داكن', 'hex_code' => '#2F4F4F'],
            ['name' => 'أبيض لؤلؤي', 'hex_code' => '#F8F8FF'],
            ['name' => 'أسود لامع', 'hex_code' => '#1C1C1C'],
            ['name' => 'فضي معدني', 'hex_code' => '#B8B8B8'],
            ['name' => 'أزرق معدني', 'hex_code' => '#4682B4'],
            ['name' => 'أحمر معدني', 'hex_code' => '#DC143C'],
        ];

        $colors = [];
        foreach ($colorsData as $colorData) {
            $color = Color::where('name->en', $colorData['name'])->first();
            if (!$color) {
                $color = Color::create(array_merge($colorData, ['status' => true]));
            }
            $colors[] = $color;
        }

        return $colors;
    }

    /**
     * إنشاء أنواع ناقل الحركة.
     */
    private function createTransmissionTypes()
    {
        $transmissionsData = [
            ['name' => 'أوتوماتيك', 'description' => 'ناقل حركة أوتوماتيكي تقليدي'],
            ['name' => 'يدوي', 'description' => 'ناقل حركة يدوي'],
            ['name' => 'CVT', 'description' => 'ناقل حركة متغير باستمرار'],
            ['name' => 'أوتوماتيك 8 سرعات', 'description' => 'ناقل حركة أوتوماتيكي 8 سرعات'],
            ['name' => 'أوتوماتيك 9 سرعات', 'description' => 'ناقل حركة أوتوماتيكي 9 سرعات'],
            ['name' => 'أوتوماتيك 10 سرعات', 'description' => 'ناقل حركة أوتوماتيكي 10 سرعات'],
            ['name' => 'يدوي 6 سرعات', 'description' => 'ناقل حركة يدوي 6 سرعات'],
        ];

        $transmissions = [];
        foreach ($transmissionsData as $transmissionData) {
            $transmission = TransmissionType::where('name', $transmissionData['name'])->first();
            if (!$transmission) {
                $transmission = TransmissionType::create(array_merge($transmissionData, ['status' => true]));
            }
            $transmissions[] = $transmission;
        }

        return $transmissions;
    }

    /**
     * إنشاء أنواع الوقود.
     */
    private function createFuelTypes()
    {
        $fuelTypesData = [
            ['name' => 'بنزين', 'description' => 'محرك بنزين تقليدي'],
            ['name' => 'ديزل', 'description' => 'محرك ديزل'],
            ['name' => 'هجين', 'description' => 'محرك هجين (بنزين + كهربائي)'],
            ['name' => 'كهربائي', 'description' => 'محرك كهربائي بالكامل'],
            ['name' => 'هجين قابل للشحن', 'description' => 'محرك هجين قابل للشحن من الخارج'],
            ['name' => 'بنزين توربو', 'description' => 'محرك بنزين مع شاحن توربيني'],
            ['name' => 'ديزل توربو', 'description' => 'محرك ديزل مع شاحن توربيني'],
        ];

        $fuelTypes = [];
        foreach ($fuelTypesData as $fuelTypeData) {
            $fuelType = FuelType::where('name->en', $fuelTypeData['name'])->first();
            if (!$fuelType) {
                $fuelType = FuelType::create(array_merge($fuelTypeData, ['status' => true]));
            }
            $fuelTypes[] = $fuelType;
        }

        return $fuelTypes;
    }

    /**
     * إنشاء أنواع الهياكل.
     */
    private function createBodyTypes()
    {
        $bodyTypesData = [
            ['name' => 'سيدان', 'description' => 'سيارة سيدان تقليدية بأربعة أبواب'],
            ['name' => 'دفع رباعي', 'description' => 'سيارة دفع رباعي (SUV)'],
            ['name' => 'هاتشباك', 'description' => 'سيارة هاتشباك صغيرة'],
            ['name' => 'كوبيه', 'description' => 'سيارة كوبيه رياضية بابين'],
            ['name' => 'كروس أوفر', 'description' => 'سيارة كروس أوفر متوسطة الحجم'],
            ['name' => 'واجن', 'description' => 'سيارة واجن عائلية'],
            ['name' => 'كونفرتيبل', 'description' => 'سيارة قابلة للتحويل (مكشوفة)'],
            ['name' => 'بيك أب', 'description' => 'شاحنة صغيرة (بيك أب)'],
            ['name' => 'فان', 'description' => 'سيارة فان متعددة الاستخدامات'],
            ['name' => 'مينيفان', 'description' => 'سيارة مينيفان عائلية'],
        ];

        $bodyTypes = [];
        foreach ($bodyTypesData as $bodyTypeData) {
            $bodyType = BodyType::where('name->en', $bodyTypeData['name'])->first();
            if (!$bodyType) {
                $bodyType = BodyType::create(array_merge($bodyTypeData, ['status' => true]));
            }
            $bodyTypes[] = $bodyType;
        }

        return $bodyTypes;
    }

    /**
     * إنشاء فئات الميزات.
     */
    private function createFeatureCategories()
    {
        $categoriesData = [
            ['name' => 'الأمان', 'description' => 'ميزات الأمان والحماية'],
            ['name' => 'الراحة', 'description' => 'ميزات الراحة والرفاهية'],
            ['name' => 'التكنولوجيا', 'description' => 'ميزات التكنولوجيا والترفيه'],
            ['name' => 'الأداء', 'description' => 'ميزات الأداء والقيادة'],
            ['name' => 'التصميم الخارجي', 'description' => 'ميزات التصميم الخارجي'],
            ['name' => 'التصميم الداخلي', 'description' => 'ميزات التصميم الداخلي'],
        ];

        $categories = [];
        foreach ($categoriesData as $categoryData) {
            $category = FeatureCategory::where('name->en', $categoryData['name'])->first();
            if (!$category) {
                $category = FeatureCategory::create(array_merge($categoryData, ['status' => true]));
            }
            $categories[] = $category;
        }

        return $categories;
    }

    /**
     * إنشاء الميزات.
     *
     * @param mixed $categories
     */
    private function createCarFeatures($categories)
    {
        $featuresData = [
            'الأمان' => [
                'وسائد هوائية أمامية', 'وسائد هوائية جانبية', 'وسائد هوائية ستائرية',
                'نظام ABS', 'نظام ESP', 'نظام التحكم في الجر',
                'كاميرا خلفية', 'حساسات ركن خلفية', 'حساسات ركن أمامية',
                'نظام مراقبة النقطة العمياء', 'نظام تحذير مغادرة المسار',
                'نظام الفرملة الطارئة', 'نظام مراقبة ضغط الإطارات',
                'نظام التحكم في السرعة التكيفي', 'نظام تحذير التصادم الأمامي',
            ],
            'الراحة' => [
                'مقاعد جلدية', 'مقاعد قماشية', 'مقاعد مدفأة',
                'مقاعد مبردة', 'مقاعد كهربائية', 'ذاكرة المقاعد',
                'تكييف أوتوماتيك', 'تكييف مناطق متعددة', 'فتحة سقف',
                'نوافذ كهربائية', 'مرايا كهربائية', 'مرايا مدفأة',
                'مقود كهربائي', 'مقود مدفأ', 'مقود جلدي',
                'إضاءة LED داخلية', 'إضاءة محيطية',
            ],
            'التكنولوجيا' => [
                'شاشة لمس 7 بوصة', 'شاشة لمس 8 بوصة', 'شاشة لمس 10 بوصة',
                'بلوتوث', 'Apple CarPlay', 'Android Auto',
                'نظام ملاحة GPS', 'نظام صوتي متقدم', 'نظام صوتي بريميوم',
                'شاحن لاسلكي', 'منافذ USB متعددة', 'نظام تشغيل عن بعد',
                'تطبيق الهاتف الذكي', 'نظام المعلومات والترفيه',
            ],
            'الأداء' => [
                'محرك توربو', 'نظام الدفع الرباعي', 'نظام الدفع الأمامي',
                'ناقل حركة رياضي', 'أوضاع قيادة متعددة', 'نظام التعليق الرياضي',
                'نظام التعليق التكيفي', 'فرامل رياضية', 'عجلات سبائكية',
                'إطارات عالية الأداء', 'نظام العادم الرياضي',
            ],
            'التصميم الخارجي' => [
                'مصابيح LED أمامية', 'مصابيح LED خلفية', 'مصابيح ضباب',
                'مصابيح نهارية LED', 'مرايا بلون الهيكل', 'مقابض أبواب بلون الهيكل',
                'شبكة أمامية كروم', 'حماية سفلية', 'قضبان سقف',
                'جناح خلفي', 'عجلات سبائكية 17 بوصة', 'عجلات سبائكية 18 بوصة',
                'عجلات سبائكية 19 بوصة', 'زجاج مظلل',
            ],
            'التصميم الداخلي' => [
                'لوحة عدادات رقمية', 'لوحة عدادات تناظرية', 'عداد سرعة رقمي',
                'شاشة معلومات متعددة', 'إضاءة داخلية LED', 'إضاءة محيطية ملونة',
                'تطعيمات خشبية', 'تطعيمات كربونية', 'تطعيمات معدنية',
                'مساحة تخزين واسعة', 'حاملات أكواب متعددة', 'جيوب تخزين',
            ],
        ];

        $features = [];
        foreach ($categories as $category) {
            $categoryName = is_array($category->name) ? $category->name['en'] : $category->name;
            if (isset($featuresData[$categoryName])) {
                foreach ($featuresData[$categoryName] as $featureName) {
                    $feature = CarFeature::where('name', $featureName)
                        ->where('category_id', $category->id)
                        ->first();

                    if (!$feature) {
                        $feature = CarFeature::create([
                            'name'        => $featureName,
                            'category_id' => $category->id,
                            'status'      => true,
                        ]);
                    }
                    $features[] = $feature;
                }
            }
        }

        return $features;
    }

    /**
     * إنشاء السيارات.
     *
     * @param mixed $brands
     * @param mixed $models
     * @param mixed $years
     * @param mixed $colors
     * @param mixed $transmissions
     * @param mixed $fuelTypes
     * @param mixed $bodyTypes
     * @param mixed $features
     */
    private function createCars($brands, $models, $years, $colors, $transmissions, $fuelTypes, $bodyTypes, $features)
    {
        // إنشاء 50 سيارة بدلاً من 20 لتوفير بيانات أكثر
        for ($i = 1; $i <= 50; $i++) {
            $brand = $brands[array_rand($brands)];
            $brandModels = collect($models)->where('brand_id', $brand->id);

            // التأكد من وجود موديلات للماركة
            if ($brandModels->isEmpty()) {
                continue;
            }

            $model = $brandModels->random();
            $year = $years[array_rand($years)];
            $color = $colors[array_rand($colors)];
            $transmission = $transmissions[array_rand($transmissions)];
            $fuelType = $fuelTypes[array_rand($fuelTypes)];
            $bodyType = $bodyTypes[array_rand($bodyTypes)];

            // تحديد الأسعار بناءً على الماركة والسنة
            $basePrice = $this->getBasePriceByBrand($brand->name);
            $yearFactor = ($year->year - 2015) / 10; // عامل السنة
            $price = $basePrice + ($basePrice * $yearFactor * 0.1) + rand(-20000, 50000);
            $price = max($price, 30000); // الحد الأدنى للسعر

            // تحديد الحالة والمسافة المقطوعة
            $condition = $year->year >= 2023 ? 'new' : (['new', 'used'][array_rand(['new', 'used'])]);
            $mileage = $condition === 'new' ? rand(0, 500) : rand(1000, 150000);

            // تحديد سعة المحرك بناءً على نوع الهيكل
            $engineCapacity = $this->getEngineCapacityByBodyType($bodyType->name);

            // تحديد عدد الأبواب والمقاعد بناءً على نوع الهيكل
            [$doorsCount, $seatsCount] = $this->getDoorsAndSeatsByBodyType($bodyType->name);

            // إنشاء وصف أكثر تفصيلاً
            $brandName = is_array($brand->name) ? $brand->name['en'] : $brand->name;
            $modelName = is_array($model->name) ? $model->name['en'] : $model->name;
            $fuelTypeName = is_array($fuelType->name) ? $fuelType->name['en'] : $fuelType->name;
            $transmissionName = is_array($transmission->name) ? $transmission->name['en'] : $transmission->name;

            $description = $this->generateCarDescription($brandName, $modelName, $year->year, $fuelTypeName, $transmissionName);
            $carTitle = $brandName . ' ' . $modelName . ' ' . $year->year;

            // التحقق من عدم وجود سيارة مماثلة
            $existingCar = Car::where('title', $carTitle)
                ->where('brand_id', $brand->id)
                ->where('car_model_id', $model->id)
                ->where('manufacturing_year_id', $year->id)
                ->first();

            if ($existingCar) {
                continue; // تخطي إذا كانت السيارة موجودة
            }

            $car = Car::create([
                'title'                 => $carTitle,
                'description'           => $description,
                'brand_id'              => $brand->id,
                'car_model_id'          => $model->id,
                'manufacturing_year_id' => $year->id,
                'body_type_id'          => $bodyType->id,
                'transmission_type_id'  => $transmission->id,
                'fuel_type_id'          => $fuelType->id,
                'main_color_id'         => $color->id,
                'interior_color_id'     => rand(0, 1) ? $colors[array_rand($colors)]->id : null,
                'price'                 => $price,
                'currency'              => 'SAR',
                'mileage'               => $mileage,
                'mileage_unit'          => 'كم',
                'engine_capacity'       => $engineCapacity,
                'doors_count'           => $doorsCount,
                'seats_count'           => $seatsCount,
                'condition'             => $condition,
                'is_featured'           => rand(0, 10) > 7, // 30% احتمال أن تكون مميزة
                'is_sold'               => rand(0, 10) > 8, // 20% احتمال أن تكون مباعة
                'is_active'             => true,
                'created_by'            => 1,
                'created_at'            => now()->subDays(rand(1, 90)), // تواريخ إنشاء متنوعة
            ]);

            // ربط الميزات بناءً على فئة السيارة
            $this->attachFeaturesBasedOnCarCategory($car, $features, $brandName, $year->year);
        }
    }

    /**
     * تحديد السعر الأساسي بناءً على الماركة.
     *
     * @param mixed $brandName
     */
    private function getBasePriceByBrand($brandName)
    {
        $luxuryBrands = ['مرسيدس بنز', 'بي إم دبليو', 'أودي', 'لكزس', 'إنفينيتي', 'جينيسيس', 'جاكوار', 'لاند روفر'];
        $premiumBrands = ['تويوتا', 'هوندا', 'نيسان', 'مازدا', 'سوبارو'];
        $economyBrands = ['هيونداي', 'كيا', 'ميتسوبيشي'];

        if (in_array($brandName, $luxuryBrands)) {
            return rand(150000, 400000);
        }
        if (in_array($brandName, $premiumBrands)) {
            return rand(80000, 200000);
        }

        return rand(50000, 120000);
    }

    /**
     * تحديد سعة المحرك بناءً على نوع الهيكل.
     *
     * @param mixed $bodyTypeName
     */
    private function getEngineCapacityByBodyType($bodyTypeName)
    {
        switch ($bodyTypeName) {
            case 'دفع رباعي':
            case 'بيك أب':
                return rand(2500, 5000);
            case 'سيدان':
            case 'واجن':
                return rand(1600, 3500);
            case 'هاتشباك':
                return rand(1000, 2000);
            case 'كوبيه':
                return rand(2000, 4000);
            case 'كروس أوفر':
                return rand(1800, 3000);
            default:
                return rand(1500, 2500);
        }
    }

    /**
     * تحديد عدد الأبواب والمقاعد بناءً على نوع الهيكل.
     *
     * @param mixed $bodyTypeName
     */
    private function getDoorsAndSeatsByBodyType($bodyTypeName)
    {
        switch ($bodyTypeName) {
            case 'كوبيه':
            case 'كونفرتيبل':
                return [2, rand(2, 4)];
            case 'بيك أب':
                return [rand(2, 4), rand(2, 5)];
            case 'فان':
            case 'مينيفان':
                return [4, rand(7, 8)];
            case 'هاتشباك':
                return [rand(3, 5), rand(4, 5)];
            default:
                return [4, rand(5, 7)];
        }
    }

    /**
     * إنشاء وصف تفصيلي للسيارة.
     *
     * @param mixed $brandName
     * @param mixed $modelName
     * @param mixed $year
     * @param mixed $fuelType
     * @param mixed $transmission
     */
    private function generateCarDescription($brandName, $modelName, $year, $fuelType, $transmission)
    {
        $descriptions = [
            "سيارة {$brandName} {$modelName} موديل {$year} بمحرك {$fuelType} وناقل حركة {$transmission}. سيارة في حالة ممتازة مع صيانة دورية منتظمة.",
            "تتميز سيارة {$brandName} {$modelName} {$year} بالأداء المتميز والتصميم العصري. مزودة بمحرك {$fuelType} وناقل حركة {$transmission}.",
            "{$brandName} {$modelName} {$year} - سيارة موثوقة ومريحة مع تقنيات حديثة. محرك {$fuelType} اقتصادي وناقل حركة {$transmission} سلس.",
            "سيارة {$brandName} {$modelName} موديل {$year} بمواصفات عالية ومحرك {$fuelType} قوي. ناقل الحركة {$transmission} يوفر قيادة مريحة.",
            "استمتع بالقيادة مع {$brandName} {$modelName} {$year}. مزودة بمحرك {$fuelType} متطور وناقل حركة {$transmission} متقدم.",
        ];

        return $descriptions[array_rand($descriptions)];
    }

    /**
     * ربط الميزات بناءً على فئة السيارة.
     *
     * @param mixed $car
     * @param mixed $features
     * @param mixed $brandName
     * @param mixed $year
     */
    private function attachFeaturesBasedOnCarCategory($car, $features, $brandName, $year)
    {
        $luxuryBrands = ['مرسيدس بنز', 'بي إم دبليو', 'أودي', 'لكزس', 'إنفينيتي', 'جينيسيس', 'جاكوار', 'لاند روفر'];
        $isLuxury = in_array($brandName, $luxuryBrands);
        $isNew = $year >= 2022;

        // تحديد عدد الميزات بناءً على فئة السيارة
        if ($isLuxury && $isNew) {
            $featureCount = rand(8, 15); // سيارات فاخرة حديثة
        } elseif ($isLuxury) {
            $featureCount = rand(6, 12); // سيارات فاخرة قديمة
        } elseif ($isNew) {
            $featureCount = rand(5, 10); // سيارات حديثة عادية
        } else {
            $featureCount = rand(3, 8); // سيارات قديمة عادية
        }

        $randomFeatures = collect($features)->random(min($featureCount, count($features)));
        $car->features()->attach($randomFeatures->pluck('id'));
    }
}
