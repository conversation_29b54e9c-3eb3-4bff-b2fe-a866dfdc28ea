/**
 * مثال على استخدام API رفع المستندات
 * 
 * هذا الملف يحتوي على أمثلة JavaScript لاستخدام API رفع المستندات
 * الجديد في OrderManagement module
 */

class DocumentUploadAPI {
    constructor() {
        this.baseUrl = '/site/order/document';
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    }

    /**
     * رفع مستند واحد عبر AJAX
     */
    async uploadDocument(orderId, documentType, file, description = null) {
        try {
            const formData = new FormData();
            formData.append('order_id', orderId);
            formData.append('document_type', documentType);
            formData.append('file', file);
            
            if (description) {
                formData.append('description', description);
            }

            const response = await fetch(`${this.baseUrl}/upload`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken
                }
            });

            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'حدث خطأ أثناء رفع المستند');
            }

            return data;

        } catch (error) {
            console.error('خطأ في رفع المستند:', error);
            throw error;
        }
    }

    /**
     * حذف مستند
     */
    async deleteDocument(orderId, mediaId) {
        try {
            const response = await fetch(`${this.baseUrl}/delete`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken
                },
                body: JSON.stringify({
                    order_id: orderId,
                    media_id: mediaId
                })
            });

            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'حدث خطأ أثناء حذف المستند');
            }

            return data;

        } catch (error) {
            console.error('خطأ في حذف المستند:', error);
            throw error;
        }
    }

    /**
     * الحصول على قائمة مستندات الطلب
     */
    async getOrderDocuments(orderId) {
        try {
            const response = await fetch(`${this.baseUrl}/list/${orderId}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'حدث خطأ أثناء جلب المستندات');
            }

            return data;

        } catch (error) {
            console.error('خطأ في جلب المستندات:', error);
            throw error;
        }
    }

    /**
     * الحصول على رابط معاينة المستند
     */
    getPreviewUrl(orderId, mediaId) {
        return `${this.baseUrl}/preview/${orderId}/${mediaId}`;
    }

    /**
     * الحصول على رابط تحميل المستند
     */
    getDownloadUrl(orderId, mediaId) {
        return `${this.baseUrl}/download/${orderId}/${mediaId}`;
    }

    /**
     * التحقق من صحة الملف قبل الرفع
     */
    validateFile(file) {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'application/pdf'];
        const maxSize = 2 * 1024 * 1024; // 2MB

        if (!allowedTypes.includes(file.type)) {
            throw new Error('نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, JPG, WEBP, PDF');
        }

        if (file.size > maxSize) {
            throw new Error('حجم الملف يجب ألا يزيد عن 2 ميجابايت');
        }

        return true;
    }
}

// مثال على الاستخدام
document.addEventListener('DOMContentLoaded', function() {
    const documentAPI = new DocumentUploadAPI();
    
    // مثال: رفع مستند عند اختيار ملف
    const fileInput = document.getElementById('document-file-input');
    const orderId = document.getElementById('order-id')?.value;
    const documentType = 'national_id_front';

    if (fileInput) {
        fileInput.addEventListener('change', async function(event) {
            const file = event.target.files[0];
            
            if (!file) return;

            try {
                // التحقق من صحة الملف
                documentAPI.validateFile(file);

                // عرض مؤشر التحميل
                showLoadingIndicator();

                // رفع المستند
                const result = await documentAPI.uploadDocument(orderId, documentType, file);
                
                if (result.success) {
                    showSuccessMessage('تم رفع المستند بنجاح');
                    updateDocumentsList(result.document);
                }

            } catch (error) {
                showErrorMessage(error.message);
            } finally {
                hideLoadingIndicator();
            }
        });
    }

    // مثال: حذف مستند
    document.addEventListener('click', async function(event) {
        if (event.target.classList.contains('delete-document-btn')) {
            const mediaId = event.target.dataset.mediaId;
            
            if (!confirm('هل أنت متأكد من حذف هذا المستند؟')) {
                return;
            }

            try {
                const result = await documentAPI.deleteDocument(orderId, mediaId);
                
                if (result.success) {
                    showSuccessMessage('تم حذف المستند بنجاح');
                    event.target.closest('.document-item').remove();
                }

            } catch (error) {
                showErrorMessage(error.message);
            }
        }
    });

    // مثال: تحديث قائمة المستندات
    async function updateDocumentsList(newDocument = null) {
        try {
            const result = await documentAPI.getOrderDocuments(orderId);
            
            if (result.success) {
                renderDocumentsList(result.documents);
            }

        } catch (error) {
            console.error('خطأ في تحديث قائمة المستندات:', error);
        }
    }

    // دوال مساعدة للواجهة
    function showLoadingIndicator() {
        const loader = document.getElementById('loading-indicator');
        if (loader) loader.style.display = 'block';
    }

    function hideLoadingIndicator() {
        const loader = document.getElementById('loading-indicator');
        if (loader) loader.style.display = 'none';
    }

    function showSuccessMessage(message) {
        // يمكن استخدام مكتبة notifications مثل toastr
        alert(message); // مؤقت
    }

    function showErrorMessage(message) {
        // يمكن استخدام مكتبة notifications مثل toastr
        alert('خطأ: ' + message); // مؤقت
    }

    function renderDocumentsList(documents) {
        const container = document.getElementById('documents-list');
        if (!container) return;

        container.innerHTML = '';

        documents.forEach(doc => {
            const docElement = createDocumentElement(doc);
            container.appendChild(docElement);
        });
    }

    function createDocumentElement(document) {
        const div = document.createElement('div');
        div.className = 'document-item';
        div.innerHTML = `
            <div class="document-info">
                <span class="document-name">${document.name}</span>
                <span class="document-size">${formatFileSize(document.size)}</span>
            </div>
            <div class="document-actions">
                <a href="${document.preview_url}" target="_blank" class="btn btn-sm btn-info">معاينة</a>
                <a href="${document.download_url}" class="btn btn-sm btn-success">تحميل</a>
                <button type="button" class="btn btn-sm btn-danger delete-document-btn" 
                        data-media-id="${document.id}">حذف</button>
            </div>
        `;
        return div;
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});

// تصدير الكلاس للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DocumentUploadAPI;
}
