# دليل استخدام Spatie Media Library في مشروع MotorLine

## نظرة عامة

تم تكوين مكتبة `spatie/laravel-medialibrary` في المشروع لإدارة الملفات والصور بطريقة فعالة ومتسقة. هذا الدليل يوضح التكوينات والاستخدامات المختلفة.

## التكوينات الأساسية

### ملف التكوين: `config/media-library.php`

- **القرص المستخدم**: `public` (يمكن الوصول إليه عبر الويب)
- **الحد الأقصى لحجم الملف**: 20MB
- **معالجة التحويلات**: فورية في البيئة المحلية، في الطابور في الإنتاج
- **محرك الصور**: GD (افتراضي)

### متغيرات البيئة (.env)

```env
MEDIA_DISK=public
MEDIA_QUEUE=media
QUEUE_CONVERSIONS_BY_DEFAULT=false
QUEUE_CONVERSIONS_AFTER_DB_COMMIT=true
IMAGE_DRIVER=gd
```

## المجموعات المستخدمة (Collections)

### 1. مجموعات السيارات
- **`car_images`**: صور السيارات المتعددة
- **`car_thumbnail`**: الصورة الرئيسية للسيارة (ملف واحد)
- **`car_documents`**: مستندات السيارة (PDF, صور)

### 2. مجموعات الماركات
- **`brand_logos`**: شعارات الماركات (ملف واحد)

### 3. مجموعات أنواع الهياكل
- **`body_type_icons`**: أيقونات أنواع هياكل السيارات (ملف واحد)

### 4. مجموعات فئات الميزات
- **`feature_category_icons`**: أيقونات فئات الميزات (ملف واحد)

## التحويلات المعيارية (Conversions)

تم إنشاء `HasStandardMediaConversions` trait لتوحيد التحويلات:

### تحويلات صور السيارات
- **`thumb`**: 200×150 بكسل (للجداول)
- **`medium`**: 600×450 بكسل (للمعاينة)
- **`large`**: 1200×900 بكسل (للعرض الكامل)
- **`print`**: 1920×1440 بكسل (للطباعة)

### تحويلات شعارات الماركات
- **`thumb`**: 50×50 بكسل (للقوائم المنسدلة)
- **`medium`**: 100×100 بكسل (للجداول)
- **`large`**: 200×200 بكسل (للعرض التفصيلي)

### تحويلات الأيقونات
- **`thumb`**: 32×32 بكسل
- **`medium`**: 64×64 بكسل
- **`large`**: 128×128 بكسل

## استخدام Trait في النماذج

```php
use App\Traits\HasStandardMediaConversions;

class Car extends BaseModel implements HasMedia
{
    use HasStandardMediaConversions;
    
    public function registerMediaConversions($media = null): void
    {
        $this->registerCarImageConversions($media);
    }
}
```

## أمثلة الاستخدام

### إضافة صورة
```php
$car = Car::find(1);
$car->addMediaFromRequest('image')
    ->toMediaCollection('car_images');
```

### الحصول على URL الصورة
```php
// الصورة الأصلية
$imageUrl = $car->getFirstMediaUrl('car_images');

// الصورة المصغرة
$thumbUrl = $car->getFirstMediaUrl('car_images', 'thumb');

// باستخدام helper method من Trait
$thumbUrl = $car->getThumbUrl('car_images', 'thumb', '/images/no-image.png');
```

### التحقق من وجود صور
```php
if ($car->hasMediaInCollection('car_images')) {
    // عرض الصور
}
```

### عرض الصور في Blade
```php
@if($car->getMedia('car_images')->count() > 0)
    @foreach($car->getMedia('car_images') as $media)
        <img src="{{ $media->getUrl('thumb') }}" alt="صورة السيارة">
    @endforeach
@endif
```

## أنواع الملفات المدعومة

### الصور
- JPEG (.jpg, .jpeg)
- PNG (.png)
- WebP (.webp)
- SVG (.svg) - للأيقونات والشعارات

### المستندات
- PDF (.pdf)
- صور (JPEG, PNG) - للمستندات الممسوحة ضوئياً

## نصائح الأداء

1. **استخدام التحويلات المناسبة**: استخدم `thumb` للجداول، `medium` للمعاينة، `large` للعرض الكامل
2. **تحسين الصور**: جميع التحويلات تتضمن تحسين تلقائي للصور
3. **المعالجة في الطابور**: في الإنتاج، فعّل `QUEUE_CONVERSIONS_BY_DEFAULT=true`

## استكشاف الأخطاء

### مشاكل شائعة
1. **صور لا تظهر**: تأكد من تشغيل `php artisan storage:link`
2. **تحويلات لا تعمل**: تحقق من إعدادات GD أو ImageMagick
3. **ملفات كبيرة**: راجع `max_file_size` في التكوين

### أوامر مفيدة
```bash
# ربط مجلد التخزين
php artisan storage:link

# مسح cache الوسائط
php artisan media-library:clean

# إعادة إنتاج التحويلات
php artisan media-library:regenerate
```

## الأمان

- جميع الملفات تُخزن في `storage/app/public`
- التحقق من أنواع الملفات عبر `acceptsMimeTypes()`
- حد أقصى لحجم الملفات (20MB)
- تحسين تلقائي للصور لتقليل الحجم

## التطوير المستقبلي

- إمكانية إضافة تحويلات WebP للأداء الأفضل
- دعم الصور المتجاوبة (Responsive Images)
- تكامل مع CDN للملفات الكبيرة
