{{--
    الخطوة 1: البيانات الشخصية - عملية طلب التمويل

    يعرض هذا الـ view نموذج لجمع البيانات الشخصية للعميل لطلب التمويل
    بناءً على UIUX-FR.md (SITE-BUY-FINANCE-STEPX-001) و REQ-FR.md (MOD-ORDER-MGMT-FEAT-004)
--}}

@extends('site.layouts.site_layout')

@section('title', 'طلب التمويل - البيانات الشخصية')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">

            {{-- عنوان الصفحة ومؤشر التقدم --}}
            <div class="text-center mb-4">
                <h2 class="mb-3">طلب تمويل السيارة</h2>

                {{-- مؤشر التقدم (Stepper) --}}
                <div class="progress-stepper mb-4">
                    <div class="step active">
                        <div class="step-number">1</div>
                        <div class="step-title">البيانات الشخصية</div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-title">معلومات التمويل</div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">المستندات</div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-title">المراجعة والتأكيد</div>
                    </div>
                </div>
            </div>

            <div class="row">
                {{-- ملخص السيارة المختارة --}}
                <div class="col-lg-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                السيارة المختارة
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($car->getFirstMediaUrl('car_main_image') || $car->getFirstMediaUrl('car_images'))
                                <img src="{{ $car->getFirstMediaUrl('car_main_image') ?: $car->getFirstMediaUrl('car_images') }}"
                                     alt="{{ $car->title }}"
                                     class="img-fluid rounded mb-3">
                            @endif

                            <h6 class="fw-bold">{{ $car->title }}</h6>
                            <p class="text-muted mb-2">
                                {{ $car->brand->name ?? '' }} - {{ $car->carModel->name ?? '' }}
                            </p>
                            <p class="text-muted mb-2">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $car->manufacturingYear->year ?? '' }}
                            </p>
                            <p class="text-muted mb-3">
                                <i class="fas fa-palette me-1"></i>
                                {{ $car->mainColor->name ?? '' }}
                            </p>

                            <div class="price-section">
                                <h4 class="text-success fw-bold">
                                    {{ number_format($car->price, 0) }} {{ $car->currency }}
                                </h4>
                                @if($car->offer_price && $car->offer_price < $car->price)
                                    <small class="text-decoration-line-through text-muted">
                                        {{ number_format($car->offer_price, 0) }} {{ $car->currency }}
                                    </small>
                                @endif
                            </div>

                            {{-- معلومات التمويل الأولية --}}
                            <div class="mt-3 p-3 bg-light rounded">
                                <h6 class="text-success mb-2">
                                    <i class="fas fa-percentage me-1"></i>
                                    طلب تمويل
                                </h6>
                                <small class="text-muted">
                                    سيتم تحديد تفاصيل التمويل في الخطوة التالية بناءً على دخلك والدفعة الأولى المرغوبة
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- نموذج البيانات الشخصية --}}
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                البيانات الشخصية
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('site.order.finance.step1.process') }}" method="POST" id="personalDataForm">
                                @csrf

                                <div class="row">
                                    {{-- الاسم الكامل --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">
                                            الاسم الكامل <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control @error('full_name') is-invalid @enderror"
                                               id="full_name"
                                               name="full_name"
                                               value="{{ old('full_name', $userData['full_name'] ?? '') }}"
                                               required>
                                        <div class="form-text">كما هو مكتوب في الهوية الوطنية</div>
                                        @error('full_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    {{-- رقم الهوية --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="national_id" class="form-label">
                                            رقم الهوية/الإقامة <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control @error('national_id') is-invalid @enderror"
                                               id="national_id"
                                               name="national_id"
                                               value="{{ old('national_id', $userData['national_id'] ?? '') }}"
                                               pattern="[0-9]{10}"
                                               maxlength="10"
                                               required>
                                        <div class="form-text">10 أرقام</div>
                                        @error('national_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="row">
                                    {{-- تاريخ الميلاد --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="date_of_birth" class="form-label">
                                            تاريخ الميلاد <span class="text-danger">*</span>
                                        </label>
                                        <input type="date"
                                               class="form-control @error('date_of_birth') is-invalid @enderror"
                                               id="date_of_birth"
                                               name="date_of_birth"
                                               value="{{ old('date_of_birth', $userData['date_of_birth'] ?? '') }}"
                                               required>
                                        <div class="form-text">يجب أن يكون العمر فوق 18 سنة</div>
                                        @error('date_of_birth')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    {{-- رقم الجوال --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            رقم الجوال <span class="text-danger">*</span>
                                        </label>
                                        <input type="tel"
                                               class="form-control @error('phone') is-invalid @enderror"
                                               id="phone"
                                               name="phone"
                                               value="{{ old('phone', $userData['phone'] ?? '') }}"
                                               pattern="[0-9]{10}"
                                               required>
                                        <div class="form-text">مثال: 0501234567</div>
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="row">
                                    {{-- البريد الإلكتروني --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            البريد الإلكتروني <span class="text-danger">*</span>
                                        </label>
                                        <input type="email"
                                               class="form-control @error('email') is-invalid @enderror"
                                               id="email"
                                               name="email"
                                               value="{{ old('email', $userData['email'] ?? '') }}"
                                               required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    {{-- الجنسية --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="nationality_id" class="form-label">
                                            الجنسية <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select @error('nationality_id') is-invalid @enderror"
                                                id="nationality_id"
                                                name="nationality_id"
                                                required>
                                            <option value="">اختر الجنسية</option>
                                            <option value="1" {{ old('nationality_id', $userData['nationality_id'] ?? '') == '1' ? 'selected' : '' }}>سعودي</option>
                                            <option value="2" {{ old('nationality_id', $userData['nationality_id'] ?? '') == '2' ? 'selected' : '' }}>مقيم</option>
                                        </select>
                                        @error('nationality_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="row">
                                    {{-- المدينة --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="city" class="form-label">
                                            المدينة <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select @error('city') is-invalid @enderror"
                                                id="city"
                                                name="city"
                                                required>
                                            <option value="">اختر المدينة</option>
                                            <option value="الرياض" {{ old('city', $userData['city'] ?? '') == 'الرياض' ? 'selected' : '' }}>الرياض</option>
                                            <option value="جدة" {{ old('city', $userData['city'] ?? '') == 'جدة' ? 'selected' : '' }}>جدة</option>
                                            <option value="الدمام" {{ old('city', $userData['city'] ?? '') == 'الدمام' ? 'selected' : '' }}>الدمام</option>
                                            <option value="مكة المكرمة" {{ old('city', $userData['city'] ?? '') == 'مكة المكرمة' ? 'selected' : '' }}>مكة المكرمة</option>
                                            <option value="المدينة المنورة" {{ old('city', $userData['city'] ?? '') == 'المدينة المنورة' ? 'selected' : '' }}>المدينة المنورة</option>
                                            <option value="الطائف" {{ old('city', $userData['city'] ?? '') == 'الطائف' ? 'selected' : '' }}>الطائف</option>
                                            <option value="الخبر" {{ old('city', $userData['city'] ?? '') == 'الخبر' ? 'selected' : '' }}>الخبر</option>
                                            <option value="القطيف" {{ old('city', $userData['city'] ?? '') == 'القطيف' ? 'selected' : '' }}>القطيف</option>
                                        </select>
                                        @error('city')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    {{-- الحالة الاجتماعية --}}
                                    <div class="col-md-6 mb-3">
                                        <label for="marital_status" class="form-label">
                                            الحالة الاجتماعية <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select @error('marital_status') is-invalid @enderror"
                                                id="marital_status"
                                                name="marital_status"
                                                required>
                                            <option value="">اختر الحالة الاجتماعية</option>
                                            <option value="single" {{ old('marital_status') == 'single' ? 'selected' : '' }}>أعزب</option>
                                            <option value="married" {{ old('marital_status') == 'married' ? 'selected' : '' }}>متزوج</option>
                                            <option value="divorced" {{ old('marital_status') == 'divorced' ? 'selected' : '' }}>مطلق</option>
                                            <option value="widowed" {{ old('marital_status') == 'widowed' ? 'selected' : '' }}>أرمل</option>
                                        </select>
                                        @error('marital_status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                {{-- العنوان التفصيلي --}}
                                <div class="mb-3">
                                    <label for="address" class="form-label">
                                        العنوان التفصيلي <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control @error('address') is-invalid @enderror"
                                              id="address"
                                              name="address"
                                              rows="3"
                                              required>{{ old('address', $userData['address'] ?? '') }}</textarea>
                                    <div class="form-text">الحي، الشارع، رقم المبنى</div>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                {{-- معلومات إضافية للتمويل --}}
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات مهمة
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li>تأكد من صحة جميع البيانات المدخلة</li>
                                        <li>ستحتاج لرفع مستندات إضافية في الخطوات التالية</li>
                                        <li>سيتم التواصل معك لاستكمال إجراءات التمويل</li>
                                        <li>مدة دراسة طلب التمويل من 3-5 أيام عمل</li>
                                    </ul>
                                </div>

                                {{-- أزرار التنقل --}}
                                <div class="d-flex justify-content-between mt-4">
                                    <a href="{{ route('site.cars.show', $car->id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة للسيارة
                                    </a>

                                    <button type="submit" class="btn btn-success">
                                        التالي: معلومات التمويل
                                        <i class="fas fa-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress-stepper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.5;
}

.step.active {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.step.active .step-number {
    background-color: #198754;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    text-align: center;
}

.price-section {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة تاريخ الميلاد (العمر فوق 18)
    const dateOfBirthInput = document.getElementById('date_of_birth');

    if (dateOfBirthInput) {
        dateOfBirthInput.addEventListener('change', function() {
            const birthDate = new Date(this.value);
            const today = new Date();
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }

            if (age < 18) {
                this.setCustomValidity('يجب أن يكون العمر 18 سنة أو أكثر');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // التحقق من صحة رقم الهوية
    const nationalIdInput = document.getElementById('national_id');

    if (nationalIdInput) {
        nationalIdInput.addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');

            if (this.value.length !== 10) {
                this.setCustomValidity('رقم الهوية يجب أن يكون 10 أرقام');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // التحقق من صحة رقم الجوال
    const phoneInput = document.getElementById('phone');

    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');

            if (this.value.length !== 10 || !this.value.startsWith('05')) {
                this.setCustomValidity('رقم الجوال يجب أن يبدأ بـ 05 ويكون 10 أرقام');
            } else {
                this.setCustomValidity('');
            }
        });
    }
});
</script>
@endpush
