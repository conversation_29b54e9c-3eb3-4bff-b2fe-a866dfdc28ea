<?php

namespace Modules\CarCatalog\Http\Controllers\Site;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\View\View;
use Modules\CarCatalog\Models\Car;

/**
 * وحدة تحكم مقارنة السيارات في الموقع العام.
 *
 * تتعامل هذه الوحدة مع إدارة سلة مقارنة السيارات المؤقتة في الجلسة
 * وتوفر واجهات لإضافة وإزالة السيارات من المقارنة وعرض صفحة المقارنة
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024
 */
class CompareController extends Controller
{
    /**
     * الحد الأقصى لعدد السيارات في سلة المقارنة.
     */
    const MAX_COMPARE_ITEMS = 4;

    /**
     * الحد الأدنى لعدد السيارات في سلة المقارنة.
     */
    const MIN_COMPARE_ITEMS = 2;

    /**
     * مفتاح تخزين سلة المقارنة في الجلسة.
     */
    const SESSION_KEY = 'car_compare_list';

    /**
     * إضافة سيارة إلى سلة المقارنة.
     *
     * يضيف السيارة إلى سلة المقارنة المخزنة في الجلسة إذا لم تكن موجودة
     * ولم يتجاوز العدد الحد الأقصى المسموح
     *
     * @param Car $car السيارة المراد إضافتها للمقارنة (Route Model Binding)
     * @return JsonResponse استجابة JSON تحتوي على حالة العملية
     */
    public function add(Car $car): JsonResponse
    {
        // التحقق من أن السيارة متاحة (غير مباعة ونشطة)
        if ($car->is_sold || !$car->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'هذه السيارة غير متاحة حالياً'
            ], 400);
        }

        // استرجاع سلة المقارنة الحالية من الجلسة
        $compareList = session()->get(self::SESSION_KEY, []);

        // التحقق من أن السيارة ليست موجودة بالفعل
        if (in_array($car->id, $compareList)) {
            return response()->json([
                'success' => false,
                'message' => 'هذه السيارة موجودة بالفعل في قائمة المقارنة'
            ], 400);
        }

        // التحقق من عدم تجاوز الحد الأقصى
        if (count($compareList) >= self::MAX_COMPARE_ITEMS) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن إضافة أكثر من ' . self::MAX_COMPARE_ITEMS . ' سيارات للمقارنة'
            ], 400);
        }

        // إضافة السيارة إلى السلة
        $compareList[] = $car->id;

        // تحديث الجلسة
        session()->put(self::SESSION_KEY, $compareList);

        return response()->json([
            'success' => true,
            'message' => 'تم إضافة السيارة إلى قائمة المقارنة بنجاح',
            'compare_count' => count($compareList),
            'can_compare' => count($compareList) >= self::MIN_COMPARE_ITEMS
        ]);
    }

    /**
     * إزالة سيارة من سلة المقارنة.
     *
     * يزيل السيارة من سلة المقارنة المخزنة في الجلسة
     *
     * @param Car $car السيارة المراد إزالتها من المقارنة (Route Model Binding)
     * @return JsonResponse استجابة JSON تحتوي على حالة العملية
     */
    public function remove(Car $car): JsonResponse
    {
        // استرجاع سلة المقارنة الحالية من الجلسة
        $compareList = session()->get(self::SESSION_KEY, []);

        // البحث عن السيارة وإزالتها
        $key = array_search($car->id, $compareList);
        if ($key !== false) {
            unset($compareList[$key]);
            $compareList = array_values($compareList); // إعادة ترقيم المصفوفة
        }

        // تحديث الجلسة
        session()->put(self::SESSION_KEY, $compareList);

        return response()->json([
            'success' => true,
            'message' => 'تم إزالة السيارة من قائمة المقارنة بنجاح',
            'compare_count' => count($compareList),
            'can_compare' => count($compareList) >= self::MIN_COMPARE_ITEMS
        ]);
    }

    /**
     * مسح سلة المقارنة بالكامل.
     *
     * @return JsonResponse استجابة JSON تحتوي على حالة العملية
     */
    public function clear(): JsonResponse
    {
        // مسح سلة المقارنة من الجلسة
        session()->forget(self::SESSION_KEY);

        return response()->json([
            'success' => true,
            'message' => 'تم مسح قائمة المقارنة بنجاح',
            'compare_count' => 0,
            'can_compare' => false
        ]);
    }

    /**
     * عرض صفحة مقارنة السيارات.
     *
     * يجلب السيارات من سلة المقارنة ويعرضها في صفحة المقارنة
     *
     * @return View عرض صفحة المقارنة
     */
    public function index(): View
    {
        // استرجاع سلة المقارنة من الجلسة
        $compareList = session()->get(self::SESSION_KEY, []);

        // التحقق من وجود سيارات للمقارنة
        if (count($compareList) < self::MIN_COMPARE_ITEMS) {
            return view('site.cars.compare', [
                'cars' => collect(),
                'message' => 'يجب إضافة سيارتين على الأقل للمقارنة'
            ]);
        }

        // جلب بيانات السيارات مع العلاقات المطلوبة
        $cars = Car::with([
            'media',
            'brand',
            'carModel',
            'manufacturingYear',
            'bodyType',
            'transmissionType',
            'fuelType',
            'mainColor',
            'interiorColor',
            'features.category'
        ])
        ->whereIn('id', $compareList)
        ->where('is_sold', false)
        ->where('is_active', true)
        ->get();

        // ترتيب السيارات حسب ترتيبها في سلة المقارنة
        $orderedCars = collect();
        foreach ($compareList as $carId) {
            $car = $cars->firstWhere('id', $carId);
            if ($car) {
                $orderedCars->push($car);
            }
        }

        return view('site.cars.compare', [
            'cars' => $orderedCars,
            'message' => null
        ]);
    }

    /**
     * الحصول على عدد السيارات في سلة المقارنة.
     *
     * @return JsonResponse استجابة JSON تحتوي على عدد السيارات في المقارنة
     */
    public function count(): JsonResponse
    {
        $compareList = session()->get(self::SESSION_KEY, []);

        return response()->json([
            'success' => true,
            'compare_count' => count($compareList),
            'can_compare' => count($compareList) >= self::MIN_COMPARE_ITEMS,
            'max_items' => self::MAX_COMPARE_ITEMS,
            'min_items' => self::MIN_COMPARE_ITEMS
        ]);
    }

    /**
     * التحقق من حالة سيارة معينة في سلة المقارنة.
     *
     * @param Car $car السيارة المراد التحقق من حالتها
     * @return JsonResponse استجابة JSON تحتوي على حالة السيارة في المقارنة
     */
    public function status(Car $car): JsonResponse
    {
        $compareList = session()->get(self::SESSION_KEY, []);
        $isInCompare = in_array($car->id, $compareList);

        return response()->json([
            'success' => true,
            'is_in_compare' => $isInCompare,
            'compare_count' => count($compareList),
            'can_compare' => count($compareList) >= self::MIN_COMPARE_ITEMS
        ]);
    }

    /**
     * الحصول على قائمة معرفات السيارات في سلة المقارنة.
     *
     * @return JsonResponse استجابة JSON تحتوي على قائمة معرفات السيارات
     */
    public function list(): JsonResponse
    {
        $compareList = session()->get(self::SESSION_KEY, []);

        return response()->json([
            'success' => true,
            'car_ids' => $compareList,
            'compare_count' => count($compareList),
            'can_compare' => count($compareList) >= self::MIN_COMPARE_ITEMS
        ]);
    }
}
