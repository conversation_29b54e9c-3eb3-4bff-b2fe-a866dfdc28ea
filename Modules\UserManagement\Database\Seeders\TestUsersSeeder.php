<?php

namespace Modules\UserManagement\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\UserManagement\Models\User;
use Spatie\Permission\Models\Role;

/**
 * TestUsersSeeder
 *
 * هذا الـ Seeder مسؤول عن إنشاء مستخدمين وهميين لاختبار النظام
 */
class TestUsersSeeder extends Seeder
{
    /**
     * تشغيل عملية إضافة البيانات
     *
     * @return void
     */
    public function run()
    {
        // إنشاء مستخدمين موظفين
        $this->createEmployees();

        // إنشاء مستخدمين عملاء
        $this->createCustomers();
    }

    /**
     * إنشاء مستخدمين موظفين
     */
    private function createEmployees()
    {
        $employeeRole = Role::firstOrCreate(['name' => 'Employee']);

        $employees = [
            [
                'first_name' => 'أحمد',
                'last_name' => 'محمد',
                'email' => '<EMAIL>',
                'phone_number' => '0501234567',
            ],
            [
                'first_name' => 'فاطمة',
                'last_name' => 'علي',
                'email' => '<EMAIL>',
                'phone_number' => '0501234568',
            ],
            [
                'first_name' => 'محمد',
                'last_name' => 'السعيد',
                'email' => '<EMAIL>',
                'phone_number' => '0501234569',
            ],
            [
                'first_name' => 'نورا',
                'last_name' => 'الأحمد',
                'email' => '<EMAIL>',
                'phone_number' => '0501234570',
            ],
            [
                'first_name' => 'خالد',
                'last_name' => 'العتيبي',
                'email' => '<EMAIL>',
                'phone_number' => '0501234571',
            ],
        ];

        foreach ($employees as $employeeData) {
            $createdDaysAgo = rand(30, 180);
            $updatedDaysAgo = rand(1, min($createdDaysAgo, 30));

            $user = User::firstOrCreate(
                ['email' => $employeeData['email']],
                array_merge($employeeData, [
                    'password' => bcrypt('password'),
                    'status' => 'active',
                    'email_verified_at' => now(),
                    'phone_verified_at' => now(),
                    'created_at' => now()->subDays($createdDaysAgo),
                    'updated_at' => now()->subDays($updatedDaysAgo),
                ])
            );

            $user->assignRole($employeeRole);
        }
    }

    /**
     * إنشاء مستخدمين عملاء
     */
    private function createCustomers()
    {
        $customerRole = Role::firstOrCreate(['name' => 'Customer']);

        $firstNames = [
            'عبدالله', 'محمد', 'أحمد', 'علي', 'عبدالرحمن', 'يوسف', 'عمر', 'خالد',
            'فاطمة', 'عائشة', 'خديجة', 'زينب', 'مريم', 'نورا', 'سارة', 'هند',
            'عبدالعزيز', 'سعد', 'فهد', 'ناصر', 'سلطان', 'بندر', 'تركي', 'راشد'
        ];

        $lastNames = [
            'الأحمد', 'المحمد', 'العلي', 'السعيد', 'العتيبي', 'القحطاني', 'الغامدي',
            'الزهراني', 'الشهري', 'العمري', 'الحربي', 'المطيري', 'الدوسري', 'الخالد',
            'السلمان', 'الفهد', 'الناصر', 'الراشد', 'البراك', 'الصالح'
        ];

        // إنشاء 30 عميل وهمي
        for ($i = 1; $i <= 30; $i++) {
            $firstName = $firstNames[array_rand($firstNames)];
            $lastName = $lastNames[array_rand($lastNames)];
            $email = 'customer' . $i . '@test.com';

            $createdDaysAgo = rand(30, 365);
            $updatedDaysAgo = rand(1, min($createdDaysAgo, 30));

            $user = User::firstOrCreate(
                ['email' => $email],
                [
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'password' => bcrypt('password'),
                    'phone_number' => '05' . rand(10000000, 99999999),
                    'status' => ['active', 'inactive'][array_rand(['active', 'inactive'])],
                    'email_verified_at' => rand(0, 1) ? now() : null,
                    'phone_verified_at' => rand(0, 1) ? now() : null,
                    'created_at' => now()->subDays($createdDaysAgo), // تواريخ متنوعة خلال السنة الماضية
                    'updated_at' => now()->subDays($updatedDaysAgo),
                ]
            );

            if (!$user->hasRole($customerRole)) {
                $user->assignRole($customerRole);
            }
        }

        // إنشاء عملاء جدد هذا الشهر (لإحصائيات لوحة البيانات)
        for ($i = 31; $i <= 45; $i++) {
            $firstName = $firstNames[array_rand($firstNames)];
            $lastName = $lastNames[array_rand($lastNames)];
            $email = 'newcustomer' . $i . '@test.com';

            $createdDaysAgo = rand(5, 30);
            $updatedDaysAgo = rand(1, min($createdDaysAgo, 7));

            $user = User::firstOrCreate(
                ['email' => $email],
                [
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'password' => bcrypt('password'),
                    'phone_number' => '05' . rand(10000000, 99999999),
                    'status' => 'active',
                    'email_verified_at' => now(),
                    'phone_verified_at' => now(),
                    'created_at' => now()->subDays($createdDaysAgo), // خلال الشهر الحالي
                    'updated_at' => now()->subDays($updatedDaysAgo),
                ]
            );

            if (!$user->hasRole($customerRole)) {
                $user->assignRole($customerRole);
            }
        }
    }
}
