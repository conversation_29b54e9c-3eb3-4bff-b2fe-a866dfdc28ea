<?php

namespace Modules\UserManagement\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Modules\UserManagement\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\UserManagement\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone_number' => '05' . $this->faker->numerify('########'),
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'password' => Hash::make('password'),
            'status' => 'active',
            'can_refer_customer' => false,
            'last_login_at' => null,
            'address_line1' => $this->faker->address(),
            'city' => $this->faker->city(),
            'national_id' => $this->faker->numerify('##########'),
            'date_of_birth' => $this->faker->date('Y-m-d', '2000-01-01'),
            'nationality_id' => null, // سيتم تعيينه حسب الحاجة
            'otp_code' => null,
            'otp_expires_at' => null,
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
            'phone_verified_at' => null,
            'status' => 'pending_verification',
        ]);
    }

    /**
     * Indicate that the user is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the user is banned.
     */
    public function banned(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'banned',
        ]);
    }

    /**
     * Indicate that the user can refer customers.
     */
    public function canReferCustomers(): static
    {
        return $this->state(fn (array $attributes) => [
            'can_refer_customer' => true,
        ]);
    }

    /**
     * Set a specific email for the user.
     */
    public function withEmail(string $email): static
    {
        return $this->state(fn (array $attributes) => [
            'email' => $email,
        ]);
    }

    /**
     * Set a specific phone number for the user.
     */
    public function withPhone(string $phone): static
    {
        return $this->state(fn (array $attributes) => [
            'phone_number' => $phone,
        ]);
    }

    /**
     * Set a specific password for the user.
     */
    public function withPassword(string $password): static
    {
        return $this->state(fn (array $attributes) => [
            'password' => Hash::make($password),
        ]);
    }

    /**
     * Set a specific status for the user.
     */
    public function withStatus(string $status): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => $status,
        ]);
    }
}
