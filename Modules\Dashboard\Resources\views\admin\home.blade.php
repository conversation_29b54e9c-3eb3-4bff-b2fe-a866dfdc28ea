@extends('dashboard::layouts.admin_layout')

@section('title', 'لوحة التحكم الرئيسية')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'لوحة التحكم الرئيسية',
        'breadcrumbs' => [
            ['name' => 'الرئيسية', 'active' => true]
        ]
    ])

    @if(isset($lastUpdated))
        <div class="mb-3">
            <small class="text-muted">آخر تحديث: {{ $lastUpdated->format('Y-m-d H:i') }}</small>
        </div>
    @endif

    @if(isset($error))
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ $error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Statistics Cards - First Row -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="dashboard-card stat-card p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-1">طلبات جديدة اليوم</h6>
                        <h3 class="fw-bold mb-0">{{ $newOrdersTodayCount ?? 0 }}</h3>
                        <small class="text-info"><i class="fas fa-info-circle"></i> بيانات ديناميكية</small>
                    </div>
                    <div class="icon bg-success-subtle text-success rounded-circle">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="dashboard-card stat-card p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-1">طلبات التمويل المعلقة</h6>
                        <h3 class="fw-bold mb-0">{{ $pendingFinanceRequestsCount ?? 0 }}</h3>
                        <small class="text-info"><i class="fas fa-info-circle"></i> بيانات ديناميكية</small>
                    </div>
                    <div class="icon bg-purple-subtle text-purple rounded-circle">
                        <i class="fas fa-money-check-alt"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="dashboard-card stat-card p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-1">عدد السيارات المتاحة</h6>
                        <h3 class="fw-bold mb-0">{{ $availableCarsCount ?? 0 }}</h3>
                        <small class="text-success"><i class="fas fa-check-circle"></i> بيانات حقيقية</small>
                    </div>
                    <div class="icon bg-info-subtle text-info rounded-circle">
                        <i class="fas fa-car"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="dashboard-card stat-card p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-1">عملاء جدد هذا الشهر</h6>
                        <h3 class="fw-bold mb-0">{{ $newCustomersThisMonthCount ?? 0 }}</h3>
                        <small class="text-success"><i class="fas fa-check-circle"></i> بيانات حقيقية</small>
                    </div>
                    <div class="icon bg-warning-subtle text-warning rounded-circle">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards - Second Row -->
    <div class="row mb-4">
        <div class="col-lg-6 col-md-6 mb-3">
            <div class="dashboard-card stat-card p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-1">مبيعات الشهر</h6>
                        <h3 class="fw-bold mb-0">{{ number_format($totalSalesThisMonth['amount'] ?? 0) }} ر.س</h3>
                        @if(isset($totalSalesThisMonth['change_percentage']))
                            <small class="{{ $totalSalesThisMonth['is_increase'] ? 'text-success' : 'text-danger' }}">
                                <i class="fas fa-arrow-{{ $totalSalesThisMonth['is_increase'] ? 'up' : 'down' }}"></i>
                                {{ abs($totalSalesThisMonth['change_percentage']) }}% من الشهر السابق
                            </small>
                        @else
                            <small class="text-info"><i class="fas fa-info-circle"></i> بيانات ديناميكية</small>
                        @endif
                    </div>
                    <div class="icon bg-indigo-subtle text-indigo rounded-circle">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-md-6 mb-3">
            <div class="dashboard-card stat-card p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-1">إجمالي المبيعات السنوية</h6>
                        <h3 class="fw-bold mb-0">{{ number_format($totalSalesThisYear['amount'] ?? 0) }} ر.س</h3>
                        @if(isset($totalSalesThisYear['change_percentage']))
                            <small class="{{ $totalSalesThisYear['is_increase'] ? 'text-success' : 'text-danger' }}">
                                <i class="fas fa-arrow-{{ $totalSalesThisYear['is_increase'] ? 'up' : 'down' }}"></i>
                                {{ abs($totalSalesThisYear['change_percentage']) }}% من العام السابق
                            </small>
                        @else
                            <small class="text-info"><i class="fas fa-info-circle"></i> بيانات ديناميكية</small>
                        @endif
                    </div>
                    <div class="icon bg-primary-subtle text-primary rounded-circle">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">تقرير المبيعات</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                            id="salesReportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            الفترة: آخر 6 أشهر
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="salesReportDropdown">
                            <li><a class="dropdown-item" href="#">هذا الأسبوع</a></li>
                            <li><a class="dropdown-item" href="#">هذا الشهر</a></li>
                            <li><a class="dropdown-item" href="#">آخر 3 أشهر</a></li>
                            <li><a class="dropdown-item" href="#">آخر 6 أشهر</a></li>
                            <li><a class="dropdown-item" href="#">هذا العام</a></li>
                        </ul>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-3">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">أفضل العلامات التجارية مبيعًا</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                            id="brandsReportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            الفترة: هذا الشهر
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="brandsReportDropdown">
                            <li><a class="dropdown-item" href="#">هذا الأسبوع</a></li>
                            <li><a class="dropdown-item" href="#">هذا الشهر</a></li>
                            <li><a class="dropdown-item" href="#">آخر 3 أشهر</a></li>
                            <li><a class="dropdown-item" href="#">هذا العام</a></li>
                        </ul>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="brandsPieChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">أفضل السيارات مبيعًا</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                            id="carsSalesDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            الفترة: هذا الشهر
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="carsSalesDropdown">
                            <li><a class="dropdown-item" href="#">هذا الأسبوع</a></li>
                            <li><a class="dropdown-item" href="#">هذا الشهر</a></li>
                            <li><a class="dropdown-item" href="#">آخر 3 أشهر</a></li>
                        </ul>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="topCarsChart"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-3">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">توزيع المبيعات حسب الفئة</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                            id="categoryDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            الفترة: آخر 6 أشهر
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="categoryDropdown">
                            <li><a class="dropdown-item" href="#">آخر 3 أشهر</a></li>
                            <li><a class="dropdown-item" href="#">آخر 6 أشهر</a></li>
                            <li><a class="dropdown-item" href="#">هذا العام</a></li>
                        </ul>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="categoriesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- More Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-3">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">حالة طلبات التمويل</h5>
                </div>
                <div class="chart-container">
                    <canvas id="financingStatusChart"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-3">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">تسجيل العملاء الجدد (شهريًا)</h5>
                </div>
                <div class="chart-container">
                    <canvas id="newCustomersChart"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-3">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">نسب طرق الدفع</h5>
                </div>
                <div class="chart-container">
                    <canvas id="paymentMethodsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Latest Cars Added Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">أحدث السيارات المضافة</h5>
                    <a href="{{ route('admin.cars.create') }}" class="btn btn-sm btn-primary"><i class="fas fa-plus me-1"></i> إضافة سيارة جديدة</a>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover recent-activity-table">
                        <thead>
                            <tr>
                                <th scope="col">الصورة</th>
                                <th scope="col">اسم السيارة</th>
                                <th scope="col">السعر</th>
                                <th scope="col">تاريخ الإضافة</th>
                                <th scope="col">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($latestCars ?? [] as $car)
                                <tr>
                                    <td>
                                        <img src="{{ $car['image_url'] ?? asset('vendor/dash/images/placeholder-car.jpg') }}"
                                             alt="صورة السيارة"
                                             class="img-fluid rounded"
                                             style="max-width: 60px; height: 40px; object-fit: cover;">
                                    </td>
                                    <td>{{ $car['brand_name'] }} - {{ $car['model_name'] }} - {{ $car['title'] }}</td>
                                    <td>{{ number_format($car['price']) }} {{ $car['currency'] }}</td>
                                    <td>{{ \Carbon\Carbon::parse($car['created_at'])->format('d/m/Y') }}</td>
                                    <td>
                                        <div class="d-flex">
                                            <a href="{{ route('admin.cars.show', $car['id']) }}" class="action-btn view me-1" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.cars.edit', $car['id']) }}" class="action-btn edit me-1" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-4">
                                        <i class="fas fa-car fa-2x mb-2"></i>
                                        <br>لا توجد سيارات مضافة حديثاً
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities & Alerts -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">آخر النشاطات</h5>
                    <a href="#" class="btn btn-sm btn-link text-primary">عرض الكل</a>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover recent-activity-table">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">النشاط</th>
                                <th scope="col">العميل</th>
                                <th scope="col">التاريخ</th>
                                <th scope="col">الحالة</th>
                                <th scope="col">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($recentActivities ?? [] as $activity)
                                <tr>
                                    <td>{{ $activity['id'] }}</td>
                                    <td>{{ $activity['description'] }}</td>
                                    <td>{{ $activity['user_name'] }}</td>
                                    <td>{{ \Carbon\Carbon::parse($activity['created_at'])->format('d/m/Y H:i') }}</td>
                                    <td>
                                        <span class="status-badge status-{{ $activity['status_class'] ?? 'default' }}">
                                            {{ $activity['status_label'] ?? $activity['status'] }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <button class="action-btn view me-1" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            @if($activity['status'] === 'completed')
                                                <button class="action-btn view" title="الفاتورة">
                                                    <i class="fas fa-file-invoice"></i>
                                                </button>
                                            @else
                                                <button class="action-btn edit" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        <i class="fas fa-history fa-2x mb-2"></i>
                                        <br>لا توجد نشاطات حديثة
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-3">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">التنبيهات المهمة</h5>
                </div>
                <div class="alert-list">
                    @forelse($alerts ?? [] as $alert)
                        <div class="alert alert-{{ $alert['type'] ?? 'info' }} d-flex align-items-center mb-2">
                            @switch($alert['type'] ?? 'info')
                                @case('warning')
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    @break
                                @case('danger')
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    @break
                                @case('success')
                                    <i class="fas fa-check-circle me-2"></i>
                                    @break
                                @default
                                    <i class="fas fa-info-circle me-2"></i>
                            @endswitch
                            <div class="flex-grow-1">
                                <strong>{{ $alert['title'] }}</strong>
                                <br><small>{{ $alert['message'] }}</small>
                                @if(isset($alert['action_url']) && $alert['action_url'] !== '#')
                                    <br><a href="{{ $alert['action_url'] }}" class="btn btn-sm btn-outline-primary mt-1">عرض التفاصيل</a>
                                @endif
                            </div>
                        </div>
                    @empty
                        <div class="alert alert-success d-flex align-items-center mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            <div>
                                <strong>لا توجد تنبيهات</strong>
                                <br><small>جميع الأمور تسير بشكل طبيعي</small>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title fw-bold mb-0">مؤشرات الأداء</h5>
                </div>
                <div class="row">
                    @if(isset($performanceMetrics['conversion_rate']))
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <h6 class="metric-title">{{ $performanceMetrics['conversion_rate']['label'] }}</h6>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-success"
                                         role="progressbar"
                                         style="width: {{ $performanceMetrics['conversion_rate']['value'] }}%"
                                         aria-valuenow="{{ $performanceMetrics['conversion_rate']['value'] }}"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">
                                    {{ $performanceMetrics['conversion_rate']['value'] }}%
                                    @if(isset($performanceMetrics['conversion_rate']['target']))
                                        (الهدف: {{ $performanceMetrics['conversion_rate']['target'] }}%)
                                    @endif
                                </small>
                            </div>
                        </div>
                    @endif

                    @if(isset($performanceMetrics['average_order_value']))
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <h6 class="metric-title">{{ $performanceMetrics['average_order_value']['label'] }}</h6>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-primary"
                                         role="progressbar"
                                         style="width: 85%"
                                         aria-valuenow="85"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">
                                    {{ number_format($performanceMetrics['average_order_value']['value']) }}
                                    {{ $performanceMetrics['average_order_value']['currency'] ?? '' }}
                                </small>
                            </div>
                        </div>
                    @endif

                    @if(isset($performanceMetrics['customer_satisfaction']))
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <h6 class="metric-title">{{ $performanceMetrics['customer_satisfaction']['label'] }}</h6>
                                <div class="progress mb-2">
                                    @php
                                        $percentage = ($performanceMetrics['customer_satisfaction']['value'] / ($performanceMetrics['customer_satisfaction']['max'] ?? 5)) * 100;
                                    @endphp
                                    <div class="progress-bar bg-warning"
                                         role="progressbar"
                                         style="width: {{ $percentage }}%"
                                         aria-valuenow="{{ $percentage }}"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">
                                    {{ $performanceMetrics['customer_satisfaction']['value'] }}/{{ $performanceMetrics['customer_satisfaction']['max'] ?? 5 }} نجوم
                                </small>
                            </div>
                        </div>
                    @endif

                    @if(isset($performanceMetrics['finance_approval_rate']))
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <h6 class="metric-title">{{ $performanceMetrics['finance_approval_rate']['label'] }}</h6>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-info"
                                         role="progressbar"
                                         style="width: {{ $performanceMetrics['finance_approval_rate']['value'] }}%"
                                         aria-valuenow="{{ $performanceMetrics['finance_approval_rate']['value'] }}"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">
                                    {{ $performanceMetrics['finance_approval_rate']['value'] }}%
                                    @if(isset($performanceMetrics['finance_approval_rate']['target']))
                                        (الهدف: {{ $performanceMetrics['finance_approval_rate']['target'] }}%)
                                    @endif
                                </small>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الرسوم البيانية مع البيانات الديناميكية
    // تم تحديث هذا القسم لاستخدام البيانات الديناميكية من Controller (PH02-TASK-020-DASH-ADMIN-HOME-DATA-INTEGRATION-002)

    // التحقق من وجود Chart.js
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js غير متوفر. لا يمكن تهيئة الرسوم البيانية.');
        return;
    }

    // تمرير البيانات الديناميكية من PHP إلى JavaScript
    const salesChartData = @json($salesChartData ?? ['labels' => [], 'datasets' => []]);
    const brandsPieData = @json($brandsPieData ?? ['labels' => [], 'datasets' => []]);
    const topCarsChartData = @json($topCarsChartData ?? ['labels' => [], 'datasets' => []]);
    const categoriesChartData = @json($categoriesChartData ?? ['labels' => [], 'datasets' => []]);
    const financingStatusChartData = @json($financingStatusChartData ?? ['labels' => [], 'datasets' => []]);
    const newCustomersChartData = @json($newCustomersChartData ?? ['labels' => [], 'datasets' => []]);
    const paymentMethodsChartData = @json($paymentMethodsChartData ?? ['labels' => [], 'datasets' => []]);

    // إعدادات الألوان
    const colors = {
        primary: '#1e3a8a',
        secondary: '#2563eb',
        accent: '#f97316',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444',
        info: '#06b6d4',
        purple: '#8b5cf6',
        indigo: '#4f46e5'
    };

    // إعدادات Chart.js العامة
    Chart.defaults.font.family = 'IBM Plex Sans Arabic, Tahoma, Geneva, Verdana, sans-serif';
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#64748b';

    // 1. رسم بياني للمبيعات (Sales Chart - Line Chart) - بيانات ديناميكية
    const salesChartElement = document.getElementById('salesChart');
    if (salesChartElement && salesChartData.labels.length > 0) {
        const ctx_sales = salesChartElement.getContext('2d');
        const salesGradient = ctx_sales.createLinearGradient(0, 0, 0, 300);
        salesGradient.addColorStop(0, 'rgba(30, 58, 138, 0.2)');
        salesGradient.addColorStop(1, 'rgba(30, 58, 138, 0.05)');

        // تطبيق الألوان والتنسيق على البيانات الديناميكية
        const processedSalesData = {
            labels: salesChartData.labels,
            datasets: salesChartData.datasets.map((dataset, index) => ({
                ...dataset,
                borderColor: index === 0 ? colors.primary : colors.accent,
                backgroundColor: index === 0 ? salesGradient : 'rgba(249, 115, 22, 0.1)',
                tension: 0.4,
                fill: index === 0,
                pointBackgroundColor: index === 0 ? colors.primary : colors.accent,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6,
                yAxisID: index === 0 ? 'y' : 'y1'
            }))
        };

        const salesOptions = {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    reverse: true, // RTL support
                    grid: {
                        drawBorder: false
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'المبيعات (ر.س)'
                    },
                    grid: {
                        drawBorder: false
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'عدد الطلبات'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        };

        new Chart(salesChartElement, {
            type: 'line',
            data: processedSalesData,
            options: salesOptions
        });
    }

    // 2. رسم بياني دائري للعلامات التجارية (Brands Pie Chart) - بيانات ديناميكية
    const brandsPieChartElement = document.getElementById('brandsPieChart');
    if (brandsPieChartElement && brandsPieData.labels.length > 0) {
        // تطبيق الألوان على البيانات الديناميكية
        const processedBrandsData = {
            labels: brandsPieData.labels,
            datasets: brandsPieData.datasets.map(dataset => ({
                ...dataset,
                backgroundColor: dataset.backgroundColor || [
                    colors.primary,
                    colors.secondary,
                    colors.accent,
                    colors.success,
                    colors.warning,
                    colors.info
                ].slice(0, brandsPieData.labels.length),
                borderWidth: 0,
                hoverOffset: 10
            }))
        };

        const brandsOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(brandsPieChartElement, {
            type: 'pie',
            data: processedBrandsData,
            options: brandsOptions
        });
    }

    // 3. رسم بياني شريطي لأفضل السيارات مبيعًا (Top Cars Chart) - بيانات ديناميكية
    const topCarsChartElement = document.getElementById('topCarsChart');
    if (topCarsChartElement && topCarsChartData.labels.length > 0) {
        // تطبيق الألوان على البيانات الديناميكية
        const processedTopCarsData = {
            labels: topCarsChartData.labels,
            datasets: topCarsChartData.datasets.map(dataset => ({
                ...dataset,
                backgroundColor: dataset.backgroundColor || Array(topCarsChartData.labels.length).fill(colors.primary),
                hoverBackgroundColor: colors.accent,
                borderRadius: 6
            }))
        };

        const topCarsOptions = {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'عدد السيارات المباعة'
                    }
                },
                y: {
                    reverse: false
                }
            }
        };

        new Chart(topCarsChartElement, {
            type: 'bar',
            data: processedTopCarsData,
            options: topCarsOptions
        });
    }

    // 4. رسم بياني دائري مجوف لتوزيع المبيعات حسب الفئة (Categories Chart) - بيانات ديناميكية
    const categoriesChartElement = document.getElementById('categoriesChart');
    if (categoriesChartElement && categoriesChartData.labels.length > 0) {
        const processedCategoriesData = {
            labels: categoriesChartData.labels,
            datasets: categoriesChartData.datasets.map(dataset => ({
                ...dataset,
                backgroundColor: dataset.backgroundColor || [
                    colors.secondary, colors.success, colors.warning, colors.accent, colors.info
                ].slice(0, categoriesChartData.labels.length),
                borderWidth: 0,
                hoverOffset: 8
            }))
        };

        const categoriesOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(categoriesChartElement, {
            type: 'doughnut',
            data: processedCategoriesData,
            options: categoriesOptions
        });
    }

    // 5. رسم بياني دائري مجوف لحالة طلبات التمويل (Financing Status Chart) - بيانات ديناميكية
    const financingStatusChartElement = document.getElementById('financingStatusChart');
    if (financingStatusChartElement && financingStatusChartData.labels.length > 0) {
        const processedFinancingData = {
            labels: financingStatusChartData.labels,
            datasets: financingStatusChartData.datasets.map(dataset => ({
                ...dataset,
                backgroundColor: dataset.backgroundColor || [colors.success, colors.danger, colors.warning, colors.info],
                borderWidth: 0,
                hoverOffset: 10
            }))
        };

        const financingStatusOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(financingStatusChartElement, {
            type: 'doughnut',
            data: processedFinancingData,
            options: financingStatusOptions
        });
    }

    // 6. رسم بياني خطي لتسجيل العملاء الجدد (New Customers Chart) - بيانات ديناميكية
    const newCustomersChartElement = document.getElementById('newCustomersChart');
    if (newCustomersChartElement && newCustomersChartData.labels.length > 0) {
        const ctx_new_customers = newCustomersChartElement.getContext('2d');
        const newCustomersGradient = ctx_new_customers.createLinearGradient(0, 0, 0, 250);
        newCustomersGradient.addColorStop(0, 'rgba(16, 185, 129, 0.2)');
        newCustomersGradient.addColorStop(1, 'rgba(16, 185, 129, 0.05)');

        const processedNewCustomersData = {
            labels: newCustomersChartData.labels,
            datasets: newCustomersChartData.datasets.map(dataset => ({
                ...dataset,
                borderColor: colors.success,
                backgroundColor: newCustomersGradient,
                tension: 0.4,
                fill: true,
                pointBackgroundColor: colors.success,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }))
        };

        const newCustomersOptions = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    },
                    ticks: {
                        display: false
                    }
                },
                x: {
                    reverse: true, // RTL support
                    grid: {
                        drawBorder: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            },
            elements: {
                line: {
                    borderWidth: 3
                }
            }
        };

        new Chart(newCustomersChartElement, {
            type: 'line',
            data: processedNewCustomersData,
            options: newCustomersOptions
        });
    }

    // 7. رسم بياني دائري مجوف لنسب طرق الدفع (Payment Methods Chart) - بيانات ديناميكية
    const paymentMethodsChartElement = document.getElementById('paymentMethodsChart');
    if (paymentMethodsChartElement && paymentMethodsChartData.labels.length > 0) {
        const processedPaymentMethodsData = {
            labels: paymentMethodsChartData.labels,
            datasets: paymentMethodsChartData.datasets.map(dataset => ({
                ...dataset,
                backgroundColor: dataset.backgroundColor || [colors.accent, colors.primary, colors.success, colors.info],
                borderWidth: 0,
                hoverOffset: 8
            }))
        };

        const paymentMethodsOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '50%',
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            }
        };

        new Chart(paymentMethodsChartElement, {
            type: 'doughnut',
            data: processedPaymentMethodsData,
            options: paymentMethodsOptions
        });
    }

    // إضافة رسالة في حالة عدم وجود بيانات
    console.log('تم تحميل لوحة البيانات بنجاح مع البيانات الديناميكية');
});
</script>
@endpush