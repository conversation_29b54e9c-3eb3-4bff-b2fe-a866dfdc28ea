@extends('dashboard::layouts.admin_layout')

@section('title', 'إضافة دور جديد')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إضافة دور جديد',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة المستخدمين', 'url' => null],
            ['name' => 'الأدوار', 'url' => route('admin.roles.index')],
            ['name' => 'إضافة جديد', 'active' => true]
        ],
        'actions' => view('usermanagement::admin.roles._create_actions')->render()
    ])

    {{-- نموذج إضافة دور جديد --}}
    <div class="row">
        <div class="col-12">
            <div class="brand-card brand-fade-in">
                <div class="brand-card-header">
                    <div class="d-flex align-items-center">
                        <div class="brand-icon-wrapper me-3">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold text-white">بيانات الدور الجديد</h5>
                            <small>قم بملء البيانات التالية لإنشاء دور جديد وتعيين الصلاحيات المناسبة له</small>
                        </div>
                    </div>
                </div>
                <div class="brand-card-body">
                    <form action="{{ route('admin.roles.store') }}" method="POST" id="roleForm">
                        @csrf
                        @include('usermanagement::admin.roles._form')
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
