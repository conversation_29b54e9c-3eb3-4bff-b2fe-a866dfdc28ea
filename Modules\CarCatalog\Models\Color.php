<?php

namespace Modules\CarCatalog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

/**
 * Color Model.
 *
 * يمثل هذا النموذج جدول ألوان السيارات في النظام
 *
 * @property int $id
 * @property string $name اسم اللون
 * @property string|null $hex_code الكود الست عشري للون
 * @property bool $status حالة اللون (نشط/غير نشط)
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\CarCatalog\Models\Car[] $cars علاقة السيارات
 */
class Color extends Model
{
    use HasFactory;
    use HasTranslations;

    /**
     * تعطيل الطوابع الزمنية (created_at, updated_at)
     * حيث أن بيانات الألوان عادة ما تكون ثابتة.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'hex_code',
        'status',
    ];

    /**
     * الخصائص التي يمكن ترجمتها.
     *
     * @var array
     */
    public $translatable = [
        'name',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * علاقة السيارات المرتبطة بهذا اللون.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cars()
    {
        return $this->hasMany(Car::class, 'main_color_id');
    }

    /**
     * مصنع النموذج.
     *
     * @return \Modules\CarCatalog\Database\factories\ColorFactory
     */
    protected static function newFactory()
    {
        return \Modules\CarCatalog\Database\factories\ColorFactory::new();
    }
}
