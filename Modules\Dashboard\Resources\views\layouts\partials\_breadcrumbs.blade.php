{{--
    مكون مسارات التنقل (Breadcrumbs) الموحد

    الاستخدام:
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'عنوان الصفحة',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null], // بدون رابط
            ['name' => 'السيارات', 'url' => route('admin.cars.index')],
            ['name' => 'إضافة جديدة', 'active' => true] // العنصر النشط
        ]
    ])
--}}

@php
    $breadcrumbs = $breadcrumbs ?? [];
    $title = $title ?? '';
@endphp

<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        @if($title)
            <h4 class="fw-bold mb-0" style="color: var(--primary-color);">{{ $title }}</h4>
        @endif

        @if(count($breadcrumbs) > 0)
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb brand-breadcrumb mb-0">
                    @foreach($breadcrumbs as $breadcrumb)
                        @if(isset($breadcrumb['active']) && $breadcrumb['active'])
                            <li class="breadcrumb-item active" aria-current="page">
                                {{ $breadcrumb['name'] }}
                            </li>
                        @elseif(isset($breadcrumb['url']) && $breadcrumb['url'])
                            <li class="breadcrumb-item">
                                <a href="{{ $breadcrumb['url'] }}" class="brand-link">{{ $breadcrumb['name'] }}</a>
                            </li>
                        @else
                            <li class="breadcrumb-item">{{ $breadcrumb['name'] }}</li>
                        @endif
                    @endforeach
                </ol>
            </nav>
        @endif
    </div>

    @if(isset($actions))
        <div>
            {!! $actions !!}
        </div>
    @endif
</div>
