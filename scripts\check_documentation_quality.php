<?php

/**
 * سكريبت فحص جودة التوثيق
 *
 * يفحص هذا السكريبت جودة التوثيق في ملفات المشروع
 * ويقدم تقريراً مفصلاً عن حالة التوثيق
 *
 * @package MotorLine\Scripts
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024
 */

require_once __DIR__ . '/../vendor/autoload.php';

class DocumentationQualityChecker
{
    /**
     * مسارات الملفات المراد فحصها
     *
     * @var array
     */
    private $paths = [
        'Modules/CarCatalog/Http/Controllers/Admin',
        'Modules/CarCatalog/Models',
        'Modules/CarCatalog/Http/Requests/Admin',
        'Modules/CarCatalog/Helpers',
    ];

    /**
     * إحصائيات الفحص
     *
     * @var array
     */
    private $stats = [
        'total_files' => 0,
        'documented_files' => 0,
        'total_classes' => 0,
        'documented_classes' => 0,
        'total_methods' => 0,
        'documented_methods' => 0,
        'total_properties' => 0,
        'documented_properties' => 0,
    ];

    /**
     * تشغيل فحص جودة التوثيق
     *
     * @return void
     */
    public function run(): void
    {
        echo "🔍 بدء فحص جودة التوثيق...\n\n";

        foreach ($this->paths as $path) {
            $this->checkPath($path);
        }

        $this->printReport();
    }

    /**
     * فحص مسار معين
     *
     * @param string $path المسار المراد فحصه
     * @return void
     */
    private function checkPath(string $path): void
    {
        $fullPath = __DIR__ . '/../' . $path;
        
        if (!is_dir($fullPath)) {
            echo "⚠️  المسار غير موجود: {$path}\n";
            return;
        }

        $files = glob($fullPath . '/*.php');
        
        foreach ($files as $file) {
            $this->checkFile($file);
        }
    }

    /**
     * فحص ملف معين
     *
     * @param string $file مسار الملف
     * @return void
     */
    private function checkFile(string $file): void
    {
        $this->stats['total_files']++;
        
        $content = file_get_contents($file);
        $tokens = token_get_all($content);
        
        $hasClassDocBlock = false;
        $classCount = 0;
        $documentedClassCount = 0;
        $methodCount = 0;
        $documentedMethodCount = 0;
        $propertyCount = 0;
        $documentedPropertyCount = 0;
        
        for ($i = 0; $i < count($tokens); $i++) {
            $token = $tokens[$i];
            
            if (is_array($token)) {
                // فحص الكلاسات
                if ($token[0] === T_CLASS) {
                    $classCount++;
                    $this->stats['total_classes']++;
                    
                    // البحث عن DocBlock قبل الكلاس
                    if ($this->hasDocBlockBefore($tokens, $i)) {
                        $documentedClassCount++;
                        $this->stats['documented_classes']++;
                        $hasClassDocBlock = true;
                    }
                }
                
                // فحص الدوال
                if ($token[0] === T_FUNCTION) {
                    $methodCount++;
                    $this->stats['total_methods']++;
                    
                    // البحث عن DocBlock قبل الدالة
                    if ($this->hasDocBlockBefore($tokens, $i)) {
                        $documentedMethodCount++;
                        $this->stats['documented_methods']++;
                    }
                }
                
                // فحص الخصائص
                if ($token[0] === T_VARIABLE && $this->isClassProperty($tokens, $i)) {
                    $propertyCount++;
                    $this->stats['total_properties']++;
                    
                    // البحث عن DocBlock قبل الخاصية
                    if ($this->hasDocBlockBefore($tokens, $i)) {
                        $documentedPropertyCount++;
                        $this->stats['documented_properties']++;
                    }
                }
            }
        }
        
        if ($hasClassDocBlock || $documentedMethodCount > 0) {
            $this->stats['documented_files']++;
        }
        
        // طباعة تقرير الملف
        $fileName = basename($file);
        $classPercentage = $classCount > 0 ? round(($documentedClassCount / $classCount) * 100) : 0;
        $methodPercentage = $methodCount > 0 ? round(($documentedMethodCount / $methodCount) * 100) : 0;
        
        echo "📄 {$fileName}: ";
        echo "Classes: {$documentedClassCount}/{$classCount} ({$classPercentage}%), ";
        echo "Methods: {$documentedMethodCount}/{$methodCount} ({$methodPercentage}%)\n";
    }

    /**
     * فحص وجود DocBlock قبل عنصر معين
     *
     * @param array $tokens مصفوفة الرموز
     * @param int $index فهرس العنصر الحالي
     * @return bool
     */
    private function hasDocBlockBefore(array $tokens, int $index): bool
    {
        // البحث للخلف عن DocBlock
        for ($i = $index - 1; $i >= 0; $i--) {
            $token = $tokens[$i];
            
            if (is_array($token)) {
                if ($token[0] === T_DOC_COMMENT) {
                    return true;
                }
                
                // إذا وجدنا رمز آخر غير مسافة أو تعليق، توقف
                if (!in_array($token[0], [T_WHITESPACE, T_COMMENT, T_PUBLIC, T_PRIVATE, T_PROTECTED, T_STATIC, T_FINAL, T_ABSTRACT])) {
                    break;
                }
            }
        }
        
        return false;
    }

    /**
     * فحص ما إذا كان المتغير خاصية كلاس
     *
     * @param array $tokens مصفوفة الرموز
     * @param int $index فهرس المتغير
     * @return bool
     */
    private function isClassProperty(array $tokens, int $index): bool
    {
        // البحث للخلف عن visibility modifier
        for ($i = $index - 1; $i >= 0; $i--) {
            $token = $tokens[$i];
            
            if (is_array($token)) {
                if (in_array($token[0], [T_PUBLIC, T_PRIVATE, T_PROTECTED])) {
                    return true;
                }
                
                if ($token[0] === T_FUNCTION || $token[0] === T_CLASS) {
                    break;
                }
            }
        }
        
        return false;
    }

    /**
     * طباعة التقرير النهائي
     *
     * @return void
     */
    private function printReport(): void
    {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 تقرير جودة التوثيق النهائي\n";
        echo str_repeat("=", 60) . "\n\n";
        
        // إحصائيات الملفات
        $filePercentage = $this->stats['total_files'] > 0 
            ? round(($this->stats['documented_files'] / $this->stats['total_files']) * 100) 
            : 0;
        echo "📁 الملفات: {$this->stats['documented_files']}/{$this->stats['total_files']} ({$filePercentage}%)\n";
        
        // إحصائيات الكلاسات
        $classPercentage = $this->stats['total_classes'] > 0 
            ? round(($this->stats['documented_classes'] / $this->stats['total_classes']) * 100) 
            : 0;
        echo "🏗️  الكلاسات: {$this->stats['documented_classes']}/{$this->stats['total_classes']} ({$classPercentage}%)\n";
        
        // إحصائيات الدوال
        $methodPercentage = $this->stats['total_methods'] > 0 
            ? round(($this->stats['documented_methods'] / $this->stats['total_methods']) * 100) 
            : 0;
        echo "⚙️  الدوال: {$this->stats['documented_methods']}/{$this->stats['total_methods']} ({$methodPercentage}%)\n";
        
        // إحصائيات الخصائص
        $propertyPercentage = $this->stats['total_properties'] > 0 
            ? round(($this->stats['documented_properties'] / $this->stats['total_properties']) * 100) 
            : 0;
        echo "🔧 الخصائص: {$this->stats['documented_properties']}/{$this->stats['total_properties']} ({$propertyPercentage}%)\n";
        
        // التقييم العام
        $overallPercentage = $this->calculateOverallPercentage();
        echo "\n🎯 التقييم العام: {$overallPercentage}%\n";
        
        if ($overallPercentage >= 90) {
            echo "✅ ممتاز! جودة التوثيق عالية جداً\n";
        } elseif ($overallPercentage >= 75) {
            echo "✅ جيد! جودة التوثيق مقبولة\n";
        } elseif ($overallPercentage >= 50) {
            echo "⚠️  متوسط! يحتاج تحسين في التوثيق\n";
        } else {
            echo "❌ ضعيف! يحتاج تحسين كبير في التوثيق\n";
        }
        
        echo "\n" . str_repeat("=", 60) . "\n";
    }

    /**
     * حساب النسبة المئوية العامة للتوثيق
     *
     * @return int
     */
    private function calculateOverallPercentage(): int
    {
        $totalElements = $this->stats['total_classes'] + $this->stats['total_methods'] + $this->stats['total_properties'];
        $documentedElements = $this->stats['documented_classes'] + $this->stats['documented_methods'] + $this->stats['documented_properties'];
        
        return $totalElements > 0 ? round(($documentedElements / $totalElements) * 100) : 0;
    }
}

// تشغيل الفحص
$checker = new DocumentationQualityChecker();
$checker->run();
