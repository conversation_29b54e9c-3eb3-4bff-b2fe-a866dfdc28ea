@extends('dashboard::layouts.admin_layout')

@section('title', 'إدارة السيارات')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إدارة السيارات',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'السيارات', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.cars.create') . '" class="btn btn-brand-primary">
            <i class="fas fa-plus me-1"></i> إضافة سيارة جديدة
        </a>'
    ])

    {{-- عرض رسائل النجاح والخطأ --}}
    @if(session('success'))
        <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {{-- نموذج البحث والفلترة --}}
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.cars.index') }}" class="mb-0">
                <div class="row gx-2 gy-2 align-items-center">
                    <div class="col-md-3">
                        <input type="text" name="search" class="form-control" placeholder="بحث بالاسم، الموديل، رقم الهيكل..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-2">
                        <select name="brand_id" class="form-select" id="brand_id">
                            <option value="">كل الماركات</option>
                            @foreach($brands as $id => $name)
                                <option value="{{ $id }}" {{ request('brand_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="model_id" class="form-select" id="model_id">
                            <option value="">كل الموديلات</option>
                            @foreach($models as $id => $name)
                                <option value="{{ $id }}" {{ request('model_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-select">
                            <option value="">كل الحالات</option>
                            @foreach($carStatuses as $value => $label)
                                <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-brand-info w-100">بحث</button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ route('admin.cars.index') }}" class="btn btn-brand-secondary w-100">إعادة تعيين</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- جدول السيارات --}}
    @if($cars->count() > 0)
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">قائمة السيارات</h5>
            </div>
            <div class="card-body table-responsive p-0">
                <table class="table table-hover recent-activity-table">
                    <thead>
                        <tr>
                            <th width="10%">صورة مصغرة</th>
                            <th width="40%">اسم السيارة</th>
                            <th width="20%">السعر</th>
                            <th width="10%">الحالة</th>
                            <th width="10%">تاريخ الإضافة</th>
                            <th width="10%">الإجراءات</th>
                        </tr>
                    </thead>
                                <tbody>
                                    @foreach($cars as $car)
                                        <tr>
                                            <td>
                                                <div class="position-relative">
                                                    @php
                                                        // البحث عن الصورة الرئيسية أولاً
                                                        $imageUrl = $car->getFirstMediaUrl('car_main_image', 'thumb');

                                                        // إذا لم توجد، جرب أول صورة من car_images
                                                        if (!$imageUrl) {
                                                            $imageUrl = $car->getFirstMediaUrl('car_images', 'thumb');
                                                        }

                                                        // إذا لم توجد أي صورة مصغرة، جرب الصورة الأصلية
                                                        if (!$imageUrl) {
                                                            $imageUrl = $car->getFirstMediaUrl('car_main_image') ?: $car->getFirstMediaUrl('car_images');
                                                        }

                                                        // إذا لم توجد أي صورة، استخدم صورة افتراضية
                                                        if (!$imageUrl) {
                                                            $imageUrl = asset('images/no-car-image.svg');
                                                        }

                                                        // للتشخيص - عدد الصور المرفوعة
                                                        $totalImages = $car->getMedia('car_images')->count();
                                                        $mainImages = $car->getMedia('car_main_image')->count();
                                                    @endphp
                                                    <img src="{{ $imageUrl }}"
                                                         alt="{{ $car->title }}"
                                                         width="60"
                                                         class="img-thumbnail rounded"
                                                         title="الصور: {{ $totalImages }}, الرئيسية: {{ $mainImages }}">
                                                    @if($car->is_featured)
                                                        <span class="position-absolute top-0 start-0 translate-middle badge rounded-pill bg-warning">
                                                            <i class="fas fa-star text-white" style="font-size: 10px;"></i>
                                                        </span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ $car->title }}</strong>
                                                </div>
                                                <small class="text-muted">
                                                    {{ $car->brand->name ?? '' }} {{ $car->carModel->name ?? '' }} - {{ $car->title }} ({{ $car->manufacturingYear->year ?? '' }})
                                                </small>
                                            </td>
                                            <td>
                                                @if($car->offer_price)
                                                    <span class="text-decoration-line-through text-muted small">{{ format_currency($car->price) }}</span><br>
                                                    <strong class="text-success">{{ format_currency($car->offer_price) }}</strong>
                                                @else
                                                    <strong>{{ format_currency($car->price) }}</strong>
                                                @endif
                                            </td>
                                            <td>
                                                @php
                                                    $status = 'active';
                                                    if ($car->is_sold) {
                                                        $status = 'sold';
                                                    } elseif (!$car->is_active) {
                                                        $status = 'inactive';
                                                    } elseif ($car->is_featured) {
                                                        $status = 'featured';
                                                    }
                                                @endphp
                                                <span class="badge rounded-pill bg-{{ car_status_badge_class($status) }}">
                                                    {{ car_status_label($status) }}
                                                </span>
                                            </td>
                                            <td>
                                                <small>{{ $car->created_at->format('d/m/Y') }}</small>
                                            </td>
                                            <td>
                                                <div class="d-flex">
                                                    <a href="{{ route('admin.cars.show', $car) }}" class="btn btn-sm btn-info me-1 action-btn view" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.cars.edit', $car) }}" class="btn btn-sm btn-warning me-1 action-btn edit" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger action-btn delete" onclick="confirmDelete({{ $car->id }}, '{{ $car->title }}')" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        {{-- ترقيم الصفحات --}}
        <div class="mt-3">
            {{ $cars->appends(request()->query())->links('pagination::bootstrap-5') }}
        </div>
    @else
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-car fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد سيارات</h5>
                <p class="text-muted">لم يتم العثور على أي سيارات. يمكنك إضافة سيارة جديدة.</p>
                <a href="{{ route('admin.cars.create') }}" class="btn btn-brand-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة سيارة جديدة
                </a>
            </div>
        </div>
    @endif
</div>

<!-- مودال تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد أنك تريد حذف السيارة: <strong id="carTitle"></strong>؟</p>
                <p class="text-danger">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmDelete(carId, carTitle) {
    document.getElementById('carTitle').textContent = carTitle;
    document.getElementById('deleteForm').action = `{{ route('admin.cars.index') }}/${carId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// تحميل الموديلات بناءً على الماركة المختارة
document.addEventListener('DOMContentLoaded', function() {
    const brandSelect = document.getElementById('brand_id');
    const modelSelect = document.getElementById('model_id');
    const currentModelId = '{{ request('model_id') }}';

    // تحميل الموديلات عند تغيير الماركة
    brandSelect.addEventListener('change', function() {
        const brandId = this.value;

        // مسح الموديلات الحالية وإضافة حالة التحميل
        modelSelect.innerHTML = '<option value="">جاري التحميل...</option>';
        modelSelect.disabled = true;

        if (brandId) {
            // جلب الموديلات من الخادم
            fetch(`{{ url('admin/brands') }}/${brandId}/models`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                credentials: 'same-origin'
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // إعادة تعيين قائمة الموديلات
                    modelSelect.innerHTML = '<option value="">جميع الموديلات</option>';
                    modelSelect.disabled = false;

                    console.log('Response data:', data); // للتشخيص

                    if (data.success && Array.isArray(data.models)) {
                        data.models.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.id;
                            option.textContent = model.name;
                            if (model.id == currentModelId) {
                                option.selected = true;
                            }
                            modelSelect.appendChild(option);
                        });
                        console.log(`تم تحميل ${data.models.length} موديل بنجاح`);
                    } else {
                        console.error('تنسيق البيانات غير صحيح:', data);
                        modelSelect.innerHTML = '<option value="">خطأ في تحميل الموديلات</option>';
                    }
                })
                .catch(error => {
                    console.error('خطأ في جلب الموديلات:', error);
                    modelSelect.innerHTML = '<option value="">خطأ في تحميل الموديلات</option>';
                    modelSelect.disabled = false;
                });
        } else {
            // إذا لم يتم اختيار ماركة، أعد تعيين القائمة
            modelSelect.innerHTML = '<option value="">جميع الموديلات</option>';
            modelSelect.disabled = false;
        }
    });

    // تحميل الموديلات عند تحميل الصفحة إذا كانت هناك ماركة مختارة
    if (brandSelect.value) {
        brandSelect.dispatchEvent(new Event('change'));
    }
});
</script>
@endpush
