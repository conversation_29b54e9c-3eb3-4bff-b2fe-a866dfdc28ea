<?php

namespace Modules\CarCatalog\Http\Controllers\Admin;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\CarCatalog\Http\Requests\Admin\StoreBrandRequest;
use Modules\CarCatalog\Http\Requests\Admin\UpdateBrandRequest;
use Modules\CarCatalog\Models\Brand;
use Modules\Core\Http\Controllers\BaseController;

/**
 * وحدة تحكم إدارة الماركات.
 *
 * تتعامل هذه الوحدة مع عمليات CRUD لماركات السيارات في لوحة تحكم الإدارة
 * تدعم البحث والفلترة وإدارة شعارات الماركات باستخدام مكتبة الوسائط
 *
 * <AUTHOR> Development Team
 *
 * @version 1.0.0
 *
 * @since 2024
 */
class BrandController extends BaseController
{
    /**
     * عرض قائمة الماركات مع إمكانيات البحث والفلترة.
     *
     * يعرض قائمة بجميع ماركات السيارات مع إمكانية البحث بالاسم
     * وفلترة حسب الحالة (نشط/غير نشط) مع عدد الموديلات لكل ماركة
     *
     * @param Request $request طلب HTTP يحتوي على معاملات البحث والفلترة
     *
     * @return View عرض صفحة قائمة الماركات
     *
     * @throws \Exception في حالة حدوث خطأ في الاستعلام
     */
    public function index(Request $request): View
    {
        $query = Brand::query()->withCount('carModels');

        // تطبيق فلتر البحث إذا تم تقديمه
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->input('search') . '%');
        }

        // تطبيق فلتر الحالة إذا تم تقديمه
        if ($request->has('status') && $request->input('status') !== '') {
            $query->where('status', $request->input('status'));
        }

        // ترتيب وترقيم النتائج
        $brands = $query->orderBy('name')->paginate(10);

        return view('car_catalog::admin.brands.index', compact('brands'));
    }

    /**
     * عرض نموذج إنشاء ماركة جديدة.
     *
     * @return View
     */
    public function create(): View
    {
        return view('car_catalog::admin.brands.create');
    }

    /**
     * تخزين ماركة جديدة.
     *
     * @param StoreBrandRequest $request طلب تخزين ماركة
     *
     * @return RedirectResponse
     */
    public function store(StoreBrandRequest $request): RedirectResponse
    {
        // إنشاء الماركة باستخدام البيانات المتحقق منها
        $brand = Brand::create($request->validated());

        // إضافة الشعار إذا تم تقديمه
        if ($request->hasFile('logo') && $request->file('logo')->isValid()) {
            $brand->addMediaFromRequest('logo')->toMediaCollection('brand_logos');
        }

        return redirect()->route('admin.brands.index')->with('success', 'تمت إضافة الماركة بنجاح.');
    }

    /**
     * عرض نموذج تعديل ماركة موجودة.
     *
     * @param Brand $brand الماركة المراد تعديلها
     *
     * @return View
     */
    public function edit(Brand $brand): View
    {
        return view('car_catalog::admin.brands.edit', compact('brand'));
    }

    /**
     * تحديث ماركة موجودة.
     *
     * @param UpdateBrandRequest $request طلب تحديث ماركة
     * @param Brand $brand الماركة المراد تحديثها
     *
     * @return RedirectResponse
     */
    public function update(UpdateBrandRequest $request, Brand $brand): RedirectResponse
    {
        // تحديث الماركة باستخدام البيانات المتحقق منها
        $brand->update($request->validated());

        // تحديث الشعار إذا تم تقديمه
        if ($request->hasFile('logo') && $request->file('logo')->isValid()) {
            $brand->clearMediaCollection('brand_logos');
            $brand->addMediaFromRequest('logo')->toMediaCollection('brand_logos');
        }

        return redirect()->route('admin.brands.index')->with('success', 'تم تعديل الماركة بنجاح.');
    }

    /**
     * حذف ماركة موجودة.
     *
     * @param Brand $brand الماركة المراد حذفها
     *
     * @return RedirectResponse
     */
    public function destroy(Brand $brand): RedirectResponse
    {
        // التحقق من عدم وجود موديلات مرتبطة بالماركة
        if ($brand->carModels()->exists()) {
            return back()->with('error', 'لا يمكن حذف الماركة لوجود موديلات مرتبطة بها. يمكنك تعطيلها بدلاً من ذلك.');
        }

        // حذف الماركة (Soft Delete)
        $brandName = $brand->name;
        $brand->delete();

        // TODO: PH02-TASK-025 - إرسال إشعار عند حذف ماركة
        // NotificationService::send([
        //     'type' => 'brand_deleted',
        //     'title' => 'تم حذف ماركة',
        //     'message' => "تم حذف الماركة '{$brandName}' من النظام",
        //     'data' => ['brand_name' => $brandName, 'deleted_by' => auth()->user()->name],
        //     'recipients' => ['admin', 'inventory_manager'],
        //     'channels' => ['database'],
        //     'priority' => 'low'
        // ]);

        return redirect()->route('admin.brands.index')->with('success', 'تم حذف الماركة بنجاح.');
    }
}
