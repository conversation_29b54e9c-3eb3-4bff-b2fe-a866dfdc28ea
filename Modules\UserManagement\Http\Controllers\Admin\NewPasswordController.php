<?php

namespace Modules\UserManagement\Http\Controllers\Admin;

use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Modules\Core\Http\Controllers\BaseController;

/**
 * متحكم إعادة تعيين كلمة المرور الجديدة للمديرين/الموظفين
 *
 * يتعامل هذا المتحكم مع عرض نموذج إعادة تعيين كلمة المرور ومعالجة طلب إعادة التعيين
 */
class NewPasswordController extends BaseController
{
    /**
     * عرض صفحة إعادة تعيين كلمة المرور
     *
     * @return \Illuminate\View\View
     */
    public function create(Request $request)
    {
        return view('usermanagement::admin.auth.reset-password', ['request' => $request]);
    }

    /**
     * معالجة طلب إعادة تعيين كلمة المرور
     *
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $request->validate([
            'token' => ['required'],
            'email' => ['required', 'email'],
            'password' => ['required', 'confirmed', 'min:8'],
        ]);

        // هنا نقوم بمعالجة إعادة تعيين كلمة المرور
        // يمكن تخصيص هذا لاستخدام وسيط مخصص إذا لزم الأمر
        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user) use ($request) {
                $user->forceFill([
                    'password' => Hash::make($request->password),
                    'remember_token' => Str::random(60),
                ])->save();

                event(new PasswordReset($user));
            }
        );

        // بعد إعادة تعيين كلمة المرور، نقوم بتوجيه المستخدم إلى صفحة تسجيل الدخول
        return $status == Password::PASSWORD_RESET
                    ? redirect()->route('admin.login')->with('status', __($status))
                    : back()->withErrors(['email' => [__($status)]]);
    }
}
