@extends('dashboard::layouts.admin_layout')

@section('title', 'إضافة ماركة جديدة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إضافة ماركة جديدة',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'الماركات', 'url' => route('admin.brands.index')],
            ['name' => 'إضافة جديدة', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.brands.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>'
    ])

    {{-- نموذج إضافة ماركة جديدة --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">بيانات الماركة</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.brands.store') }}" method="POST" enctype="multipart/form-data">
                @csrf

                @include('car_catalog::admin.brands._form')

                <div class="mt-4 d-flex justify-content-end">
                    <button type="submit" class="btn btn-success me-2">حفظ الماركة</button>
                    <a href="{{ route('admin.brands.index') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
