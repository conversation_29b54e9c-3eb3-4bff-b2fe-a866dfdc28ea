<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تخزين ميزة سيارة جديدة.
 *
 * يتحقق هذا الطلب من صحة بيانات إضافة ميزة سيارة جديدة
 */
class StoreCarFeatureRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'           => 'required|string|max:100|unique:car_features,name',
            'category_id'    => 'nullable|exists:feature_categories,id',
            'description'    => 'nullable|string|max:500',
            'is_filterable'  => 'required|boolean',
            'is_highlighted' => 'required|boolean',
            'status'         => 'required|boolean',
            'display_order'  => 'nullable|integer|min:0',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة لقواعد التحقق
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required'           => 'اسم الميزة مطلوب.',
            'name.string'             => 'اسم الميزة يجب أن يكون نص.',
            'name.max'                => 'اسم الميزة يجب ألا يتجاوز 100 حرف.',
            'name.unique'             => 'اسم الميزة موجود مسبقاً.',
            'category_id.exists'      => 'فئة الميزة المحددة غير موجودة.',
            'description.string'      => 'وصف الميزة يجب أن يكون نص.',
            'description.max'         => 'وصف الميزة يجب ألا يتجاوز 500 حرف.',
            'is_filterable.required'  => 'خاصية القابلية للتصفية مطلوبة.',
            'is_filterable.boolean'   => 'خاصية القابلية للتصفية يجب أن تكون نعم أو لا.',
            'is_highlighted.required' => 'خاصية الميزة المميزة مطلوبة.',
            'is_highlighted.boolean'  => 'خاصية الميزة المميزة يجب أن تكون نعم أو لا.',
            'status.required'         => 'حالة الميزة مطلوبة.',
            'status.boolean'          => 'حالة الميزة يجب أن تكون نشط أو غير نشط.',
            'display_order.integer'   => 'ترتيب العرض يجب أن يكون رقم صحيح.',
            'display_order.min'       => 'ترتيب العرض يجب أن يكون أكبر من أو يساوي 0.',
        ];
    }

    /**
     * الحصول على أسماء الخصائص المخصصة للتحقق
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name'           => 'اسم الميزة',
            'category_id'    => 'فئة الميزة',
            'description'    => 'وصف الميزة',
            'is_filterable'  => 'قابلة للتصفية',
            'is_highlighted' => 'ميزة مميزة',
            'status'         => 'حالة الميزة',
            'display_order'  => 'ترتيب العرض',
        ];
    }
}
