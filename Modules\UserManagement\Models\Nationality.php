<?php

namespace Modules\UserManagement\Models;

use Modules\Core\Models\BaseModel;

/**
 * Nationality Model
 *
 * يمثل هذا النموذج جدول الجنسيات في النظام
 *
 * @property int $id
 * @property string $name_ar اسم الجنسية باللغة العربية
 * @property string|null $name_en اسم الجنسية باللغة الإنجليزية
 * @property string|null $iso_code رمز ISO 3166-1 alpha-2
 */
class Nationality extends BaseModel
{
    /**
     * تعطيل الطوابع الزمنية (created_at, updated_at)
     * حيث أن بيانات الجنسيات عادة ما تكون ثابتة
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي
     *
     * @var array
     */
    protected $fillable = [
        'name_ar',
        'name_en',
        'iso_code',
    ];

    /**
     * علاقة المستخدمين المرتبطين بهذه الجنسية
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }
}
