@extends('site.layouts.site_layout')

@section('title', 'تسجيل الدخول - موتور لاين')

@section('meta_description', 'سجل دخولك إلى حسابك في موتور لاين للوصول إلى خدماتنا المتميزة وإدارة طلباتك')

@section('content')
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="auth-card">
                    <!-- شعار الموقع -->
                    <div class="text-center mb-4">
                        <img src="{{ asset('images/logo.png') }}" alt="موتور لاين" class="auth-logo">
                        <h2 class="auth-title mt-3">أهلاً بعودتك</h2>
                        <p class="auth-subtitle text-muted">سجل دخولك للوصول إلى حسابك</p>
                    </div>

                    <!-- عرض رسائل الخطأ العامة -->
                    @if(session('loginError'))
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ session('loginError') }}
                        </div>
                    @endif

                    <!-- نموذج تسجيل الدخول -->
                    <form action="{{ route('site.auth.login.submit') }}" method="POST" class="auth-form">
                        @csrf

                        <!-- حقل معرف تسجيل الدخول -->
                        <div class="form-group mb-3">
                            <label for="identifier" class="form-label">
                                <i class="fas fa-user me-2"></i>
                                البريد الإلكتروني أو رقم الجوال
                            </label>
                            <input type="text" 
                                   class="form-control @error('identifier') is-invalid @enderror" 
                                   id="identifier" 
                                   name="identifier" 
                                   value="{{ old('identifier') }}" 
                                   placeholder="أدخل بريدك الإلكتروني أو رقم جوالك"
                                   required>
                            @error('identifier')
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- حقل كلمة المرور -->
                        <div class="form-group mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>
                                كلمة المرور
                            </label>
                            <div class="password-input-wrapper">
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password" 
                                       placeholder="أدخل كلمة المرور"
                                       required>
                                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="password-eye"></i>
                                </button>
                            </div>
                            @error('password')
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- صف تذكرني ونسيت كلمة المرور -->
                        <div class="form-group mb-4 d-flex justify-content-between align-items-center">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
                                <label class="form-check-label" for="remember">
                                    تذكرني
                                </label>
                            </div>
                            <a href="{{ route('site.auth.password.request.form') }}" class="text-primary">
                                نسيت كلمة المرور؟
                            </a>
                        </div>

                        <!-- زر تسجيل الدخول -->
                        <div class="form-group mb-3">
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>

                        <!-- رابط إنشاء حساب جديد -->
                        <div class="text-center">
                            <p class="mb-0">
                                ليس لديك حساب؟ 
                                <a href="{{ route('site.auth.register.form') }}" class="text-primary fw-bold">إنشاء حساب جديد</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* أنماط خاصة بصفحة تسجيل الدخول */
.auth-container {
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    padding: 2rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.auth-logo {
    max-height: 60px;
    width: auto;
}

.auth-title {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    font-size: 1rem;
    margin-bottom: 0;
}

.auth-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.auth-form .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.auth-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.password-input-wrapper {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: #495057;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 576px) {
    .auth-container {
        padding: 1rem 0;
    }
    
    .auth-card {
        padding: 1.5rem;
        margin: 0 1rem;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// دالة إظهار/إخفاء كلمة المرور
function togglePassword(inputId) {
    const passwordInput = document.getElementById(inputId);
    const eyeIcon = document.getElementById(inputId + '-eye');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}

// التركيز على أول حقل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const firstInput = document.getElementById('identifier');
    if (firstInput) {
        firstInput.focus();
    }
});
</script>
@endpush
