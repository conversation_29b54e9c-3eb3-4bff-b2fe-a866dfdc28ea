# وحدة إدارة الطلبات - OrderManagement Module

## نظرة عامة

وحدة إدارة الطلبات هي جزء من نظام MotorLine لإدارة معارض السيارات. تتولى هذه الوحدة معالجة طلبات شراء السيارات بطريقة الدفع النقدي (كاش) من خلال عملية متعددة المراحل.

## الميزات الرئيسية

### 1. عملية الطلب متعددة المراحل
- **الخطوة 1**: إدخال البيانات الشخصية للعميل
- **الخطوة 2**: تحديد تفاصيل الحجز وطريقة الدفع
- **الخطوة 3**: رفع المستندات المطلوبة
- **الخطوة 4**: المراجعة النهائية والتأكيد

### 2. أنظمة الدفع المدعومة
- الدفع الإلكتروني (فيزا/ماستركارد، مدى، Apple Pay، STC Pay)
- الدفع في المعرض

### 3. إدارة المستندات
- رفع وتخزين المستندات باستخدام `spatie/laravel-medialibrary`
- دعم أنواع ملفات متعددة (JPEG, PNG, WEBP, PDF)
- التحقق من صحة الملفات وأحجامها

### 4. نظام الإشعارات
- إشعارات للعملاء عبر البريد الإلكتروني وقاعدة البيانات
- إشعارات للإدارة عند وصول طلبات جديدة
- دعم الرسائل النصية (SMS)

## الهيكل التقني

### Controllers
- `SiteOrderController`: معالجة طلبات العملاء في الموقع العام
- `AdminOrderController`: إدارة الطلبات من لوحة التحكم

### Models
- `Order`: نموذج الطلب الرئيسي مع العلاقات والدوال المساعدة

### Services
- `OrderProcessingService`: معالجة منطق الطلبات
- `DocumentUploadService`: إدارة رفع المستندات
- `PaymentGatewayService`: التكامل مع بوابات الدفع

### Form Requests
- `CashOrderStep1Request`: التحقق من البيانات الشخصية
- `CashOrderStep2Request`: التحقق من بيانات الحجز والدفع
- `CashOrderStep3Request`: التحقق من المستندات
- `CashOrderStep4Request`: التحقق من التأكيد النهائي

### Notifications
- `OrderCreatedNotification`: إشعار إنشاء طلب للعميل
- `NewOrderAdminNotification`: إشعار طلب جديد للإدارة

## التثبيت والإعداد

### 1. تثبيت الحزم المطلوبة
```bash
composer require spatie/laravel-medialibrary
composer require spatie/laravel-permission
```

### 2. نشر ملفات التكوين
```bash
php artisan vendor:publish --provider="Spatie\MediaLibrary\MediaLibraryServiceProvider" --tag="migrations"
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
```

### 3. تشغيل الهجرات
```bash
php artisan migrate
```

### 4. إنشاء رابط التخزين
```bash
php artisan storage:link
```

### 5. تكوين متغيرات البيئة
```env
# إعدادات البريد الإلكتروني
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="MotorLine"

# إعدادات الرسائل النصية
SMS_ENABLED=true
SMS_PROVIDER=taqnyat
SMS_SENDER_NAME="MotorLine"

# إعدادات الإشعارات
NOTIFICATION_QUEUE_CONNECTION=database
NOTIFICATION_QUEUE_NAME=notifications
```

## الاستخدام

### إنشاء طلب جديد

```php
// في Controller
use Modules\OrderManagement\Services\OrderProcessingService;

$orderService = new OrderProcessingService();
$order = $orderService->createCashOrder($orderData);
```

### رفع المستندات

```php
use Modules\OrderManagement\Services\DocumentUploadService;

$documentService = new DocumentUploadService();

// رفع مستند واحد
$documentService->uploadDocument($order, $file, 'national_id_front', 'وصف المستند');

// رفع مستند من Request مباشرة
$documentService->uploadSingleDocumentFromRequest($order, 'file_input_name', 'national_id_front');

// حذف مستند
$documentService->deleteDocumentById($order, $mediaId);

// التحقق من وجود مستند
$hasDocument = $documentService->hasDocument($order, 'national_id_front');

// عدد المستندات
$count = $documentService->getDocumentCount($order, 'national_id_front');
```

### استخدام API رفع المستندات عبر AJAX

```javascript
// رفع مستند
const formData = new FormData();
formData.append('order_id', orderId);
formData.append('document_type', 'national_id_front');
formData.append('file', fileInput.files[0]);

fetch('/site/order/document/upload', {
    method: 'POST',
    body: formData,
    headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('تم رفع المستند بنجاح', data.document);
    }
});

// حذف مستند
fetch('/site/order/document/delete', {
    method: 'DELETE',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': csrfToken
    },
    body: JSON.stringify({
        order_id: orderId,
        media_id: mediaId
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('تم حذف المستند بنجاح');
    }
});
```

### أنواع المستندات المدعومة

#### المستندات الأساسية (مطلوبة لجميع الطلبات)
- `national_id_front`: صورة الهوية الوطنية (الوجه الأمامي)
- `national_id_back`: صورة الهوية الوطنية (الوجه الخلفي)
- `driving_license`: رخصة القيادة

#### مستندات التمويل (مطلوبة لطلبات التمويل)
- `salary_certificate`: شهادة الراتب
- `bank_statement`: كشف حساب بنكي

#### مستندات إضافية (اختيارية)
- `additional_documents`: مستندات إضافية

#### أنواع الملفات المدعومة
- **الصور**: JPEG, PNG, JPG, WEBP
- **المستندات**: PDF
- **الحد الأقصى للحجم**: 2 ميجابايت لكل ملف

### Routes المتاحة لإدارة المستندات

```php
// رفع مستند عبر AJAX
POST /site/order/document/upload

// حذف مستند عبر AJAX
DELETE /site/order/document/delete

// معاينة مستند
GET /site/order/document/preview/{order}/{mediaId}

// تحميل مستند
GET /site/order/document/download/{order}/{mediaId}

// قائمة مستندات الطلب
GET /site/order/document/list/{order}
```

### معالجة الدفع

```php
use Modules\OrderManagement\Services\PaymentGatewayService;

$paymentService = new PaymentGatewayService();
$paymentUrl = $paymentService->createPaymentSession($order);
```

## Routes المتاحة

### للعملاء (Site)
- `GET /site/order/cash/step1/{car}` - الخطوة الأولى
- `POST /site/order/cash/step1/process` - معالجة الخطوة الأولى
- `GET /site/order/cash/step2` - الخطوة الثانية
- `POST /site/order/cash/step2/process` - معالجة الخطوة الثانية
- `GET /site/order/cash/step3` - الخطوة الثالثة
- `POST /site/order/cash/step3/process` - معالجة الخطوة الثالثة
- `GET /site/order/cash/step4` - الخطوة الرابعة
- `POST /site/order/cash/step4/process` - معالجة الخطوة الرابعة

### للدفع
- `GET /site/order/payment/success/{order}` - نجاح الدفع
- `GET /site/order/payment/cancel/{order}` - إلغاء الدفع
- `GET /site/order/payment/error/{order}` - خطأ في الدفع

## قواعد التحقق

### البيانات الشخصية
- الاسم الكامل: مطلوب، 3-100 حرف
- رقم الهوية: مطلوب، رقم سعودي صالح (10 أرقام)
- تاريخ الميلاد: مطلوب، العمر 18+ سنة
- رقم الجوال: مطلوب، رقم سعودي صالح (05xxxxxxxx)

### المستندات
- صورة الهوية (الوجهان): مطلوبة، حد أقصى 2MB
- رخصة القيادة: مطلوبة، حد أقصى 2MB
- أنواع الملفات المدعومة: JPEG, PNG, WEBP, PDF

### الدفع
- مبلغ الحجز: 10% من سعر السيارة كحد أدنى
- الحد الأقصى: 50% من سعر السيارة

## الأمان

- التحقق من ملكية الطلب للمستخدم
- تشفير البيانات الحساسة
- التحقق من صحة الملفات المرفوعة
- حماية من CSRF
- تنظيف البيانات المدخلة

## الاختبار

```bash
# تشغيل الاختبارات
php artisan test --filter=OrderManagement

# اختبار الإشعارات
php artisan queue:work --queue=notifications
```

## المساهمة

يرجى اتباع معايير الكود المحددة في المشروع والتأكد من:
- كتابة التعليقات باللغة العربية
- اتباع PSR-12 coding standards
- كتابة الاختبارات للميزات الجديدة
- توثيق أي تغييرات في API

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
