## TS-FR.md - المواصفات التقنية (نسخة نهائية معتمدة ذاتيًا)

**تاريخ الإنشاء:** (تاريخ اليوم)
**إصدار المستند:** 1.0 (نهائي)

### مقدمة

الغرض من هذا المستند هو تقديم المواصفات التقنية التفصيلية لمنصة معرض السيارات الإلكترونية المتكاملة. هذا المستند هو الترجمة التقنية المباشرة والدقيقة للمتطلبات المحددة في مستند "المتطلبات التفصيلية" (`REQ-FR.md` - النسخة النهائية المعتمدة ذاتيًا، إصدار 1.0، بتاريخ 2024-07-27)، ويستند إلى الرؤية من `PO-FR.md` والتحليل الأولي من `00-FR.md`. يفترض هذا المستند استخدام إطار عمل Laravel 10، وبناء لوحة تحكم مخصصة (Dash) من الصفر باستخدام أصول HTML/CSS/JS المتوفرة ودمجها كواجهات Blade ديناميكية، واستخدام حزمة `nwidart/laravel-modules` لتنظيم الموديولات، وتطوير تطبيق Flutter للعملاء، وواجهة موقع عامة باستخدام Blade.

يعتبر هذا المستند المخطط التنفيذي التفصيلي الذي سيُبنى عليه هيكل المشروع (`STRU-FR.md`)، تصميم واجهات المستخدم (`UIUX-FR.md` فيما يتعلق بتكامل البيانات)، وتعليمات التنفيذ التفصيلية للمرحلة 03. يجب أن تكون المواصفات هنا مفصلة للغاية، دقيقة، قابلة للاستهلاك الآلي من قبل أنظمة الذكاء الاصطناعي الأخرى (LLMs)، وموجهة نحو تحقيق المخرجات النهائية المتوقعة للمشروع بفعالية وجودة عالية. هذا المستند وصفي بالكامل ولا يتضمن أي كود برمجي أو تمثيلات تشبه الكود أو استجابات بصيغة JSON أو أي تنسيقات غير وصفية.

**المخرجات النهائية المتوقعة للمشروع (المرجع الأساسي والنهائي):**
1.  **موقع إلكتروني فعال:** مرفوع على خادم عبر الإنترنت، جاهز لاستقبال الزوار بدون مشاكل.
2.  **لوحة تحكم احترافية (مخصصة من الصفر باستخدام أصول Dash HTML/CSS/JS):** (للإدارة و/أو العملاء) جاهزة للاستخدام، توفر تجربة مستخدم ممتازة، وتدعم اللغة العربية (RTL) بشكل كامل.
3.  **واجهة موقع (Frontend) جذابة وفعالة:** (باستخدام Blade إذا كانت مختلفة عن لوحة التحكم) جاهزة لاستقبال الزوار وتوفير تجربة مستخدم سلسة.
4.  **تطبيق موبايل (Flutter) كامل:** جاهز للرفع على متاجر التطبيقات ويعمل بشكل صحيح ومتكامل.

---

### 1. مخطط قاعدة البيانات (Database Schema)

سيتم تصميم مخطط قاعدة البيانات لدعم جميع وظائف النظام والمتطلبات المحددة، مع مراعاة الأداء، قابلية التوسع، وتكامل اللغة العربية. ستستخدم جميع الجداول محرك `InnoDB` لدعم العلاقات والمفاتيح الخارجية. سيتم استخدام ترميز `utf8mb4` ومقارنة `utf8mb4_unicode_ci` لجميع الأعمدة النصية لضمان دعم كامل للغة العربية. سيتم إنشاء الجداول ضمن migrations الخاصة بكل موديول في `nwidart/laravel-modules`.

#### **1.1. جدول المستخدمين (Users)**
*   **معرف الجدول:** `DB-TBL-001`
*   **اسم الجدول:** `users`
*   **وصف موجز للغرض من الجدول:** تخزين معلومات جميع أنواع المستخدمين للنظام (عملاء، مديرين، موظفين).
*   **الأعمدة:**
    | اسم العمود                     | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                 | مفهرس؟                                  | ملاحظات                                                                                                |
    | :------------------------------ | :------------------------------- | :----------------------------------------------------------------------------------------------------- | :--------------------------------------- | :----------------------------------------------------------------------------------------------------- |
    | `DB-COL-U-001` id               | `bigIncrements` (PK)             | PRIMARY KEY                                                                                            | نعم (أساسي)                             | تم اختيار `bigIncrements` للبساطة والافتراض في Laravel. إذا ظهرت حاجة لـ `uuid` مستقبلًا، يمكن إعادة النظر. |
    | `DB-COL-U-002` first_name       | `string(100)`                    | NOT NULL                                                                                               | نعم                                      | اسم المستخدم الأول.                                                                                     |
    | `DB-COL-U-003` last_name        | `string(100)`                    | NOT NULL                                                                                               | نعم                                      | اسم المستخدم الأخير.                                                                                     |
    | `DB-COL-U-004` email            | `string(255)`                    | NOT NULL, UNIQUE                                                                                       | نعم (فريد)                              | البريد الإلكتروني للمستخدم، يستخدم لتسجيل الدخول.                                                      |
    | `DB-COL-U-005` phone_number     | `string(20)`                     | NOT NULL, UNIQUE                                                                                       | نعم (فريد)                              | رقم الجوال للمستخدم، يستخدم لتسجيل الدخول والتحقق عبر OTP. يجب أن يكون بصيغة دولية أو موحدة.          |
    | `DB-COL-U-006` password         | `string(255)`                    | NOT NULL                                                                                               |                                          | كلمة المرور (مجزأة - hashed).                                                                          |
    | `DB-COL-U-007` email_verified_at| `timestamp`                      | NULLABLE                                                                                               |                                          | تاريخ ووقت التحقق من البريد الإلكتروني.                                                                 |
    | `DB-COL-U-008` phone_verified_at| `timestamp`                      | NULLABLE                                                                                               |                                          | تاريخ ووقت التحقق من رقم الجوال.                                                                        |
    | `DB-COL-U-009` profile_photo_id | `unsignedBigInteger` أو `uuid`   | NULLABLE, FOREIGN KEY references `media(id) ON DELETE SET NULL ON UPDATE CASCADE`                    | نعم                                      | معرّف الصورة الشخصية من جدول `media` (Spatie Media Library).                                         |
    | `DB-COL-U-010` status           | `string(50)`                     | NOT NULL, DEFAULT 'pending_verification'                                                               | نعم                                      | حالة الحساب (e.g., 'active', 'inactive', 'pending_verification', 'banned').                          |
    | `DB-COL-U-011` can_refer_customer| `boolean`                        | NOT NULL, DEFAULT false                                                                                |                                          | يحدد ما إذا كان العميل مؤهلاً لترشيح عملاء جدد (لـ `MOD-USER-MGMT-FEAT-008`).                         |
    | `DB-COL-U-012` last_login_at    | `timestamp`                      | NULLABLE                                                                                               |                                          | تاريخ ووقت آخر تسجيل دخول.                                                                            |
    | `DB-COL-U-013` address_line1    | `string(255)`                    | NULLABLE                                                                                               |                                          | جزء من عنوان العميل.                                                                                  |
    | `DB-COL-U-014` city             | `string(100)`                    | NULLABLE                                                                                               |                                          | مدينة العميل.                                                                                         |
    | `DB-COL-U-015` remember_token   | `string(100)`                    | NULLABLE                                                                                               |                                          | لخاصية "تذكرني".                                                                                      |
    | `DB-COL-U-016` created_at       | `timestamp`                      | NULLABLE                                                                                               |                                          | تاريخ إنشاء السجل.                                                                                     |
    | `DB-COL-U-017` updated_at       | `timestamp`                      | NULLABLE                                                                                               |                                          | تاريخ آخر تحديث للسجل.                                                                                 |
    | `DB-COL-U-018` national_id      | `string(20)`                     | NULLABLE                                                                                               | نعم (إذا كان يستخدم للبحث بشكل متكرر)   | رقم الهوية الوطنية أو الإقامة (للعملاء عند الشراء).                                                      |
    | `DB-COL-U-019` date_of_birth    | `date`                           | NULLABLE                                                                                               |                                          | تاريخ ميلاد العميل.                                                                                   |
    | `DB-COL-U-020` nationality_id   | `unsignedBigInteger`             | NULLABLE, FOREIGN KEY references `nationalities(id) ON DELETE SET NULL ON UPDATE CASCADE`             | نعم                                      | معرّف جنسية العميل.                                                                                   |
    | `DB-COL-U-021` otp_code         | `string(10)`                     | NULLABLE                                                                                               |                                          | لتخزين رمز OTP للتحقق من الجوال (يجب تشفيره أو التعامل معه بحذر).                                       |
    | `DB-COL-U-022` otp_expires_at   | `timestamp`                      | NULLABLE                                                                                               |                                          | تاريخ انتهاء صلاحية رمز OTP.                                                                          |

#### **1.2. جدول الجنسيات (Nationalities)**
*   **معرف الجدول:** `DB-TBL-020`
*   **اسم الجدول:** `nationalities`
*   **وصف موجز للغرض من الجدول:** تخزين قائمة بالجنسيات للاختيار منها.
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                                     | مفهرس؟       | ملاحظات                     |
    | :---------------------- | :------------------------------- | :----------------------------------------- | :----------- | :-------------------------- |
    | `DB-COL-NAT-001` id     | `bigIncrements` (PK)             | PRIMARY KEY                                | نعم (أساسي) |                             |
    | `DB-COL-NAT-002` name_ar| `string(100)`                    | NOT NULL, UNIQUE                           | نعم (فريد)  | اسم الجنسية باللغة العربية. |
    | `DB-COL-NAT-003` name_en| `string(100)`                    | NULLABLE, UNIQUE                           | نعم (فريد)  | اسم الجنسية باللغة الإنجليزية (للتوسع المستقبلي). |
    | `DB-COL-NAT-004` iso_code| `string(2)`                     | NULLABLE, UNIQUE                           | نعم (فريد)  | رمز ISO 3166-1 alpha-2.    |

#### **1.3. جدول الماركات (Brands)**
*   **معرف الجدول:** `DB-TBL-002`
*   **اسم الجدول:** `brands`
*   **وصف موجز للغرض من الجدول:** تخزين ماركات السيارات المتاحة. (يخدم `MOD-CAR-CATALOG-FEAT-008`)
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                               | مفهرس؟       | ملاحظات                                                                                                         |
    | :---------------------- | :------------------------------- | :--------------------------------------------------------------------------------------------------- | :----------- | :-------------------------------------------------------------------------------------------------------------- |
    | `DB-COL-B-001` id       | `bigIncrements` (PK)             | PRIMARY KEY                                                                                          | نعم (أساسي) |                                                                                                                 |
    | `DB-COL-B-002` name     | `string(100)`                    | NOT NULL, UNIQUE (ستتم إدارة التفرد والترجمة عبر `spatie/laravel-translatable` إذا تم استخدامه هنا) | نعم (فريد)  | اسم الماركة. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها على مستوى النموذج.     |
    | `DB-COL-B-003` logo_id  | `unsignedBigInteger` أو `uuid`   | NULLABLE, FOREIGN KEY references `media(id) ON DELETE SET NULL ON UPDATE CASCADE`                  | نعم          | معرّف شعار الماركة من جدول `media`.                                                                               |
    | `DB-COL-B-004` description| `text`                           | NULLABLE                                                                                             |              | وصف مختصر للماركة. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.                 |
    | `DB-COL-B-005` status   | `boolean`                        | NOT NULL, DEFAULT true                                                                               | نعم          | حالة الماركة (نشطة/غير نشطة).                                                                                      |
    | `DB-COL-B-006` created_at| `timestamp`                      | NULLABLE                                                                                             |              |                                                                                                                 |
    | `DB-COL-B-007` updated_at| `timestamp`                      | NULLABLE                                                                                             |              |                                                                                                                 |
    | `DB-COL-B-008` deleted_at| `timestamp`                      | NULLABLE                                                                                             |              | للحذف الناعم (Soft Delete).                                                                                     |

#### **1.4. جدول الموديلات (Models)**
*   **معرف الجدول:** `DB-TBL-003`
*   **اسم الجدول:** `car_models`
*   **وصف موجز للغرض من الجدول:** تخزين موديلات السيارات المرتبطة بالماركات. (يخدم `FEAT-CAR-009`)
*   **الأعمدة:**
    | اسم العمود              | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                | مفهرس؟        | ملاحظات                                                                                                   |
    | :----------------------- | :------------------------------- | :---------------------------------------------------------------------------------------------------- | :------------ | :-------------------------------------------------------------------------------------------------------- |
    | `DB-COL-CM-001` id       | `bigIncrements` (PK)             | PRIMARY KEY                                                                                           | نعم (أساسي)  |                                                                                                           |
    | `DB-COL-CM-002` brand_id | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `brands(id) ON DELETE CASCADE ON UPDATE CASCADE`                   | نعم           | معرّف الماركة.                                                                                             |
    | `DB-COL-CM-003` name     | `string(100)`                    | NOT NULL (الفرادة مع `brand_id`)                                                                    | نعم (مركب)   | اسم الموديل. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها على مستوى النموذج.  |
    | `DB-COL-CM-004` status   | `boolean`                        | NOT NULL, DEFAULT true                                                                                | نعم           | حالة الموديل (نشط/غير نشط).                                                                                 |
    | `DB-COL-CM-005` created_at| `timestamp`                      | NULLABLE                                                                                              |               |                                                                                                           |
    | `DB-COL-CM-006` updated_at| `timestamp`                      | NULLABLE                                                                                              |               |                                                                                                           |
    | `DB-COL-CM-007` deleted_at| `timestamp`                      | NULLABLE                                                                                              |               | للحذف الناعم.                                                                                              |

#### **1.5. جدول سنوات الصنع (Years)**
*   **معرف الجدول:** `DB-TBL-004`
*   **اسم الجدول:** `manufacturing_years`
*   **وصف موجز للغرض من الجدول:** تخزين سنوات الصنع المتاحة. (يخدم `FEAT-CAR-012`)
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                 | مفهرس؟       | ملاحظات            |
    | :---------------------- | :------------------------------- | :--------------------- | :----------- | :----------------- |
    | `DB-COL-Y-001` id       | `bigIncrements` (PK)             | PRIMARY KEY            | نعم (أساسي) |                    |
    | `DB-COL-Y-002` year     | `year`                           | NOT NULL, UNIQUE       | نعم (فريد)  | سنة الصنع (4 أرقام). |
    | `DB-COL-Y-003` status   | `boolean`                        | NOT NULL, DEFAULT true | نعم          | نشطة/غير نشطة.    |

#### **1.6. جدول الألوان (Colors)**
*   **معرف الجدول:** `DB-TBL-005`
*   **اسم الجدول:** `colors`
*   **وصف موجز للغرض من الجدول:** تخزين ألوان السيارات المتاحة. (يخدم `FEAT-CAR-011`)
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                                                                   | مفهرس؟       | ملاحظات                                                                                                   |
    | :---------------------- | :------------------------------- | :----------------------------------------------------------------------- | :----------- | :-------------------------------------------------------------------------------------------------------- |
    | `DB-COL-CLR-001` id     | `bigIncrements` (PK)             | PRIMARY KEY                                                              | نعم (أساسي) |                                                                                                           |
    | `DB-COL-CLR-002` name   | `string(50)`                     | NOT NULL, UNIQUE                                                         | نعم (فريد)  | اسم اللون (e.g., "أحمر ناري"). سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها. |
    | `DB-COL-CLR-003` hex_code| `string(7)`                      | NULLABLE, UNIQUE (إذا كان اللون ثابتًا، e.g., "#FF0000")                  | نعم (فريد)  | الكود الست عشري للون.                                                                                       |
    | `DB-COL-CLR-004` status | `boolean`                        | NOT NULL, DEFAULT true                                                   | نعم          | نشط/غير نشط.                                                                                              |

#### **1.7. جدول أنواع ناقل الحركة (Transmission Types)**
*   **معرف الجدول:** `DB-TBL-006`
*   **اسم الجدول:** `transmission_types`
*   **وصف موجز للغرض من الجدول:** تخزين أنواع نواقل الحركة المتاحة. (يخدم `FEAT-CAR-013`)
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                          | مفهرس؟       | ملاحظات                                                                                                         |
    | :---------------------- | :------------------------------- | :------------------------------ | :----------- | :-------------------------------------------------------------------------------------------------------------- |
    | `DB-COL-TT-001` id      | `bigIncrements` (PK)             | PRIMARY KEY                     | نعم (أساسي) |                                                                                                                 |
    | `DB-COL-TT-002` name    | `string(50)`                     | NOT NULL, UNIQUE                | نعم (فريد)  | اسم نوع ناقل الحركة. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.              |
    | `DB-COL-TT-003` status  | `boolean`                        | NOT NULL, DEFAULT true          | نعم          | نشط/غير نشط.                                                                                                     |

#### **1.8. جدول أنواع الوقود (Fuel Types)**
*   **معرف الجدول:** `DB-TBL-007`
*   **اسم الجدول:** `fuel_types`
*   **وصف موجز للغرض من الجدول:** تخزين أنواع الوقود المتاحة. (يخدم `FEAT-CAR-014`)
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                          | مفهرس؟       | ملاحظات                                                                                             |
    | :---------------------- | :------------------------------- | :------------------------------ | :----------- | :-------------------------------------------------------------------------------------------------- |
    | `DB-COL-FT-001` id      | `bigIncrements` (PK)             | PRIMARY KEY                     | نعم (أساسي) |                                                                                                     |
    | `DB-COL-FT-002` name    | `string(50)`                     | NOT NULL, UNIQUE                | نعم (فريد)  | اسم نوع الوقود. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.        |
    | `DB-COL-FT-003` status  | `boolean`                        | NOT NULL, DEFAULT true          | نعم          | نشط/غير نشط.                                                                                         |

#### **1.9. جدول أنواع هياكل السيارات (Body Types)**
*   **معرف الجدول:** `DB-TBL-021`
*   **اسم الجدول:** `body_types`
*   **وصف موجز للغرض من الجدول:** تخزين أنواع هياكل السيارات (سيدان، SUV، إلخ).
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                          | مفهرس؟       | ملاحظات                                                                                             |
    | :---------------------- | :------------------------------- | :------------------------------ | :----------- | :-------------------------------------------------------------------------------------------------- |
    | `DB-COL-BT-001` id      | `bigIncrements` (PK)             | PRIMARY KEY                     | نعم (أساسي) |                                                                                                     |
    | `DB-COL-BT-002` name    | `string(50)`                     | NOT NULL, UNIQUE                | نعم (فريد)  | اسم نوع الهيكل. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.        |
    | `DB-COL-BT-003` status  | `boolean`                        | NOT NULL, DEFAULT true          | نعم          | نشط/غير نشط.                                                                                         |

#### **1.10. جدول ميزات السيارات (Car Features)**
*   **معرف الجدول:** `DB-TBL-008`
*   **اسم الجدول:** `car_features`
*   **وصف موجز للغرض من الجدول:** تخزين قائمة الميزات العامة للسيارات (التي يمكن إدارتها من لوحة التحكم). (يخدم `FEAT-CAR-010`)
*   **الأعمدة:**
    | اسم العمود                | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                               | مفهرس؟       | ملاحظات                                                                                                         |
    | :------------------------- | :------------------------------- | :--------------------------------------------------------------------------------------------------- | :----------- | :-------------------------------------------------------------------------------------------------------------- |
    | `DB-COL-CF-001` id         | `bigIncrements` (PK)             | PRIMARY KEY                                                                                          | نعم (أساسي) |                                                                                                                 |
    | `DB-COL-CF-002` name       | `string(100)`                    | NOT NULL, UNIQUE                                                                                     | نعم (فريد)  | اسم الميزة (مثال: "فتحة سقف بانورامية"). سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable`.      |
    | `DB-COL-CF-003` category_id| `unsignedBigInteger`             | NULLABLE, FOREIGN KEY references `feature_categories(id) ON DELETE SET NULL ON UPDATE CASCADE`     | نعم          | فئة الميزة (مثل: أمان، راحة، ترفيه) - لغرض التنظيم.                                                               |
    | `DB-COL-CF-004` icon       | `string(50)`                     | NULLABLE                                                                                             |              | اسم أيقونة FontAwesome أو مسار SVG.                                                                            |
    | `DB-COL-CF-005` status     | `boolean`                        | NOT NULL, DEFAULT true                                                                               | نعم          | نشطة/غير نشطة.                                                                                                   |

#### **1.11. جدول فئات الميزات (Feature Categories)**
*   **معرف الجدول:** `DB-TBL-022`
*   **اسم الجدول:** `feature_categories`
*   **وصف موجز للغرض من الجدول:** تخزين فئات ميزات السيارات لتنظيمها.
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                          | مفهرس؟       | ملاحظات                                                                                                   |
    | :---------------------- | :------------------------------- | :------------------------------ | :----------- | :-------------------------------------------------------------------------------------------------------- |
    | `DB-COL-FCAT-001` id    | `bigIncrements` (PK)             | PRIMARY KEY                     | نعم (أساسي) |                                                                                                           |
    | `DB-COL-FCAT-002` name  | `string(100)`                    | NOT NULL, UNIQUE                | نعم (فريد)  | اسم فئة الميزة. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها على مستوى النموذج. |
    | `DB-COL-FCAT-003` status| `boolean`                        | NOT NULL, DEFAULT true          | نعم          | نشطة/غير نشطة.                                                                                             |

#### **1.12. جدول السيارات (Cars)**
*   **معرف الجدول:** `DB-TBL-009`
*   **اسم الجدول:** `cars`
*   **وصف موجز للغرض من الجدول:** تخزين معلومات السيارات المعروضة للبيع. (يخدم `MOD-CAR-CATALOG-FEAT-018` وغيرها)
*   **الأعمدة:**
    | اسم العمود                        | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                       | مفهرس؟         | ملاحظات                                                                                                                                                                                                                                                             |
    | :--------------------------------- | :------------------------------- | :----------------------------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
    | `DB-COL-C-001` id                  | `bigIncrements` (PK)             | PRIMARY KEY                                                                                                  | نعم (أساسي)   |                                                                                                                                                                                                                                                                 |
    | `DB-COL-C-002` brand_id            | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `brands(id) ON DELETE RESTRICT ON UPDATE CASCADE`                         | نعم            |                                                                                                                                                                                                                                                                 |
    | `DB-COL-C-003` model_id            | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `car_models(id) ON DELETE RESTRICT ON UPDATE CASCADE`                     | نعم            |                                                                                                                                                                                                                                                                 |
    | `DB-COL-C-004` manufacturing_year_id| `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `manufacturing_years(id) ON DELETE RESTRICT ON UPDATE CASCADE`             | نعم            |                                                                                                                                                                                                                                                                 |
    | `DB-COL-C-005` main_color_id       | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `colors(id) ON DELETE RESTRICT ON UPDATE CASCADE`                         | نعم            | اللون الخارجي الأساسي للسيارة.                                                                                                                                                                                                                                    |
    | `DB-COL-C-006` vin                 | `string(17)`                     | NULLABLE, UNIQUE                                                                                             | نعم (فريد)    | رقم الهيكل (Vehicle Identification Number).                                                                                                                                                                                                                         |
    | `DB-COL-C-007` trim_name           | `string(100)`                    | NOT NULL                                                                                                     | نعم            | اسم الفئة أو الطراز (e.g., "GLX", "Limited"). سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها على مستوى النموذج. |
    | `DB-COL-C-008` description         | `text`                           | NOT NULL                                                                                                     |                | الوصف العام للسيارة (يدعم Rich Text). سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable`.                                                                                                                                                                 |
    | `DB-COL-C-009` engine_type         | `string(255)`                    | NULLABLE                                                                                                     |                | وصف المحرك (e.g., "2.5L 4-Cylinder Turbo").                                                                                                                                                                                                                         |
    | `DB-COL-C-010` engine_power_hp     | `integer`                        | NOT NULL                                                                                                     |                | قوة المحرك بالحصان.                                                                                                                                                                                                                                                   |
    | `DB-COL-C-011` torque_nm           | `integer`                        | NULLABLE                                                                                                     |                | عزم الدوران (نيوتن.متر).                                                                                                                                                                                                                                               |
    | `DB-COL-C-012` transmission_type_id| `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `transmission_types(id) ON DELETE RESTRICT ON UPDATE CASCADE`             | نعم            |                                                                                                                                                                                                                                                                 |
    | `DB-COL-C-013` num_speeds          | `integer`                        | NULLABLE                                                                                                     |                | عدد السرعات (إذا كان يدوي أو أوتوماتيك تقليدي).                                                                                                                                                                                                                            |
    | `DB-COL-C-014` drive_type          | `string(50)`                     | NOT NULL                                                                                                     | نعم            | نظام الدفع (e.g., 'FWD', 'RWD', 'AWD', '4WD').                                                                                                                                                                                                                         |
    | `DB-COL-C-015` fuel_type_id        | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `fuel_types(id) ON DELETE RESTRICT ON UPDATE CASCADE`                     | نعم            |                                                                                                                                                                                                                                                                 |
    | `DB-COL-C-016` fuel_consumption_kml| `decimal(5,2)`                   | NULLABLE                                                                                                     |                | معدل استهلاك الوقود (كم/لتر).                                                                                                                                                                                                                                         |
    | `DB-COL-C-017` fuel_tank_capacity_l| `integer`                        | NULLABLE                                                                                                     |                | سعة خزان الوقود (لتر).                                                                                                                                                                                                                                                |
    | `DB-COL-C-018` length_mm           | `integer`                        | NULLABLE                                                                                                     |                | الطول (ملم).                                                                                                                                                                                                                                                         |
    | `DB-COL-C-019` width_mm            | `integer`                        | NULLABLE                                                                                                     |                | العرض (ملم).                                                                                                                                                                                                                                                         |
    | `DB-COL-C-020` height_mm           | `integer`                        | NULLABLE                                                                                                     |                | الارتفاع (ملم).                                                                                                                                                                                                                                                        |
    | `DB-COL-C-021` wheelbase_mm        | `integer`                        | NULLABLE                                                                                                     |                | قاعدة العجلات (ملم).                                                                                                                                                                                                                                                        |
    | `DB-COL-C-022` curb_weight_kg      | `integer`                        | NULLABLE                                                                                                     |                | وزن السيارة فارغة (كجم).                                                                                                                                                                                                                                                |
    | `DB-COL-C-023` num_doors           | `tinyInteger`                    | NOT NULL                                                                                                     |                | عدد الأبواب.                                                                                                                                                                                                                                                          |
    | `DB-COL-C-024` num_seats           | `tinyInteger`                    | NOT NULL                                                                                                     |                | عدد المقاعد.                                                                                                                                                                                                                                                          |
    | `DB-COL-C-025` tire_size           | `string(100)`                    | NULLABLE                                                                                                     |                | مقاس الإطارات.                                                                                                                                                                                                                                                        |
    | `DB-COL-C-026` body_type_id        | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `body_types(id) ON DELETE RESTRICT ON UPDATE CASCADE`                     | نعم            | نوع جسم السيارة.                                                                                                                                                                                                                                                        |
    | `DB-COL-C-027` base_price          | `decimal(12,2)`                  | NOT NULL                                                                                                     |                | سعر السيارة الأساسي (قبل الضريبة).                                                                                                                                                                                                                                     |
    | `DB-COL-C-028` tax_percentage      | `decimal(5,2)`                   | NOT NULL                                                                                                     |                | نسبة الضريبة المضافة (تُجلب من إعدادات النظام ولكن يمكن تخزينها هنا وقت إنشاء السجل للتاريخ).                                                                                                                                                                              |
    | `DB-COL-C-029` total_price         | `decimal(12,2)`                  | NOT NULL                                                                                                     |                | السعر الإجمالي شامل الضريبة (محسوب).                                                                                                                                                                                                                                      |
    | `DB-COL-C-030` offer_price         | `decimal(12,2)`                  | NULLABLE                                                                                                     |                | سعر العرض الخاص (قبل الضريبة).                                                                                                                                                                                                                                            |
    | `DB-COL-C-031` offer_total_price   | `decimal(12,2)`                  | NULLABLE                                                                                                     |                | سعر العرض الإجمالي شامل الضريبة (محسوب).                                                                                                                                                                                                                                  |
    | `DB-COL-C-032` offer_start_date    | `date`                           | NULLABLE                                                                                                     |                | تاريخ بدء العرض الخاص.                                                                                                                                                                                                                                                     |
    | `DB-COL-C-033` offer_end_date      | `date`                           | NULLABLE                                                                                                     |                | تاريخ انتهاء العرض الخاص.                                                                                                                                                                                                                                                    |
    | `DB-COL-C-034` status              | `string(50)`                     | NOT NULL, DEFAULT 'available'                                                                                | نعم            | حالة السيارة (e.g., 'available', 'reserved', 'sold', 'coming_soon', 'unavailable').                                                                                                                                                                             |
    | `DB-COL-C-035` is_featured         | `boolean`                        | NOT NULL, DEFAULT false                                                                                      | نعم            | هل السيارة مميزة؟ (يخدم `FEAT-CAR-017`).                                                                                                                                                                                                                                   |
    | `DB-COL-C-036` internal_notes      | `text`                           | NULLABLE                                                                                                     |                | ملاحظات داخلية للموظفين.                                                                                                                                                                                                                                                     |
    | `DB-COL-C-037` units_available     | `integer`                        | NULLABLE, DEFAULT 1                                                                                          |                | عدد الوحدات المتاحة (إذا كان هناك تتبع للمخزون).                                                                                                                                                                                                                            |
    | `DB-COL-C-038` views_count         | `integer`                        | NOT NULL, DEFAULT 0                                                                                          |                | عدد المشاهدات (إذا تم تتبعه).                                                                                                                                                                                                                                                |
    | `DB-COL-C-039` custom_specifications| `json`                           | NULLABLE                                                                                                     |                | لتخزين أي مواصفات إضافية ديناميكية غير مغطاة بالحقول القياسية (key-value pairs).                                                                                                                                                                                          |
    | `DB-COL-C-040` custom_features     | `json`                           | NULLABLE                                                                                                     |                | لتخزين أي ميزات مخصصة غير موجودة في `car_features` لهذه السيارة فقط (key-value pairs).                                                                                                                                                                                     |
    | `DB-COL-C-041` created_at          | `timestamp`                      | NULLABLE                                                                                                     |                |                                                                                                                                                                                                                                                                         |
    | `DB-COL-C-042` updated_at          | `timestamp`                      | NULLABLE                                                                                                     |                |                                                                                                                                                                                                                                                                         |
    | `DB-COL-C-043` deleted_at          | `timestamp`                      | NULLABLE                                                                                                     |                | للحذف الناعم.                                                                                                                                                                                                                                                                |

#### **1.13. جدول ربط السيارات بالميزات (Car-Feature Pivot Table)**
*   **معرف الجدول:** `DB-TBL-010`
*   **اسم الجدول:** `car_car_feature` (أو `car_feature_pivot`)
*   **وصف موجز للغرض من الجدول:** جدول وسيط لربط السيارات بميزاتها (علاقة كثير إلى كثير).
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                | مفهرس؟       | ملاحظات                                                                 |
    | :---------------------- | :------------------------------- | :---------------------------------------------------------------------------------------------------- | :----------- | :---------------------------------------------------------------------- |
    | `DB-COL-CCF-001` id     | `bigIncrements` (PK)             | PRIMARY KEY                                                                                           | نعم (أساسي) |                                                                         |
    | `DB-COL-CCF-002` car_id | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `cars(id) ON DELETE CASCADE ON UPDATE CASCADE`                     | نعم          | معرّف السيارة.                                                          |
    | `DB-COL-CCF-003` car_feature_id | `unsignedBigInteger`     | NOT NULL, FOREIGN KEY references `car_features(id) ON DELETE CASCADE ON UPDATE CASCADE`             | نعم          | معرّف الميزة.                                                           |
    |                         |                                  | UNIQUE (`car_id`, `car_feature_id`)                                                                   | نعم (مركب)  | لضمان عدم تكرار نفس الميزة لنفس السيارة.                               |

#### **1.14. جدول مفضلة المستخدمين (User Favorites)**
*   **معرف الجدول:** `DB-TBL-011`
*   **اسم الجدول:** `user_favorites`
*   **وصف موجز للغرض من الجدول:** تخزين السيارات المفضلة لكل مستخدم. (يخدم `MOD-CAR-CATALOG-FEAT-005`)
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                               | مفهرس؟       | ملاحظات                                   |
    | :---------------------- | :------------------------------- | :--------------------------------------------------------------------------------------------------- | :----------- | :---------------------------------------- |
    | `DB-COL-UF-001` id      | `bigIncrements` (PK)             | PRIMARY KEY                                                                                          | نعم (أساسي) |                                           |
    | `DB-COL-UF-002` user_id | `unsignedBigInteger` أو `uuid`   | NOT NULL, FOREIGN KEY references `users(id) ON DELETE CASCADE ON UPDATE CASCADE`                   | نعم          | معرّف المستخدم.                            |
    | `DB-COL-UF-003` car_id  | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `cars(id) ON DELETE CASCADE ON UPDATE CASCADE`                    | نعم          | معرّف السيارة.                            |
    | `DB-COL-UF-004` created_at| `timestamp`                      | NULLABLE                                                                                             |              |                                           |
    |                         |                                  | UNIQUE (`user_id`, `car_id`)                                                                         | نعم (مركب)  | لمنع إضافة نفس السيارة للمفضلة مرتين.     |

#### **1.15. جدول الطلبات (Orders)**
*   **معرف الجدول:** `DB-TBL-012`
*   **اسم الجدول:** `orders`
*   **وصف موجز للغرض من الجدول:** تخزين معلومات طلبات شراء السيارات (كاش أو تمويل). (يخدم `MOD-ORDER-MGMT`)
*   **الأعمدة:**
    | اسم العمود                        | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                        | مفهرس؟         | ملاحظات                                                                                                                               |
    | :--------------------------------- | :------------------------------- | :------------------------------------------------------------------------------------------------------------ | :------------- | :------------------------------------------------------------------------------------------------------------------------------------ |
    | `DB-COL-O-001` id                  | `bigIncrements` (PK)             | PRIMARY KEY                                                                                                   | نعم (أساسي)   |                                                                                                                                       |
    | `DB-COL-O-002` user_id             | `unsignedBigInteger` أو `uuid`   | NOT NULL, FOREIGN KEY references `users(id) ON DELETE RESTRICT ON UPDATE CASCADE`                           | نعم            | معرّف العميل الذي قدم الطلب.                                                                                                          |
    | `DB-COL-O-003` car_id              | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `cars(id) ON DELETE RESTRICT ON UPDATE CASCADE`                            | نعم            | معرّف السيارة المطلوبة.                                                                                                                |
    | `DB-COL-O-004` order_number        | `string(50)`                     | NOT NULL, UNIQUE                                                                                              | نعم (فريد)    | رقم الطلب الفريد (يتم توليده).                                                                                                         |
    | `DB-COL-O-005` order_type          | `string(50)`                     | NOT NULL                                                                                                      | نعم            | نوع الطلب (e.g., 'cash_reservation', 'finance_application', 'custom_car_request').                                                  |
    | `DB-COL-O-006` status              | `string(50)`                     | NOT NULL, DEFAULT 'pending_payment'                                                                           | نعم            | حالة الطلب (e.g., 'pending_payment', 'pending_admin_review', 'processing', 'awaiting_documents', 'approved', 'rejected', 'completed', 'cancelled'). |
    | `DB-COL-O-007` car_price_at_order  | `decimal(12,2)`                  | NOT NULL                                                                                                      |                | سعر السيارة وقت الطلب (شامل الضريبة أو السعر الأساسي حسب السياسة).                                                                         |
    | `DB-COL-O-008` reservation_amount  | `decimal(10,2)`                  | NULLABLE                                                                                                      |                | مبلغ الحجز المطلوب/المدفوع.                                                                                                           |
    | `DB-COL-O-009` remaining_amount    | `decimal(12,2)`                  | NULLABLE                                                                                                      |                | المبلغ المتبقي للدفع في المعرض.                                                                                                       |
    | `DB-COL-O-010` payment_method      | `string(50)`                     | NULLABLE                                                                                                      |                | طريقة دفع الحجز (e.g., 'online_card', 'showroom_payment').                                                                              |
    | `DB-COL-O-011` payment_status      | `string(50)`                     | NULLABLE                                                                                                      | نعم            | حالة دفع الحجز (e.g., 'pending', 'paid', 'failed').                                                                                     |
    | `DB-COL-O-012` payment_transaction_id| `string(255)`                    | NULLABLE                                                                                                      | نعم            | معرّف معاملة الدفع من بوابة الدفع.                                                                                                      |
    | `DB-COL-O-013` customer_national_id| `string(20)`                     | NULLABLE                                                                                                      |                | رقم هوية/إقامة العميل (وقت الطلب).                                                                                                    |
    | `DB-COL-O-014` customer_dob        | `date`                           | NULLABLE                                                                                                      |                | تاريخ ميلاد العميل (وقت الطلب).                                                                                                       |
    | `DB-COL-O-015` customer_nationality_id| `unsignedBigInteger`           | NULLABLE, FOREIGN KEY references `nationalities(id) ON DELETE SET NULL ON UPDATE CASCADE`                   | نعم            | جنسية العميل (وقت الطلب).                                                                                                             |
    | `DB-COL-O-016` customer_address_details| `text`                           | NULLABLE                                                                                                      |                | تفاصيل عنوان العميل (وقت الطلب).                                                                                                      |
    | `DB-COL-O-017` admin_notes         | `text`                           | NULLABLE                                                                                                      |                | ملاحظات إدارية على الطلب.                                                                                                             |
    | `DB-COL-O-018` finance_details     | `json`                           | NULLABLE                                                                                                      |                | تفاصيل طلب التمويل (الدخل، الالتزامات، جهة العمل، إلخ - إذا كان الطلب تمويليًا).                                                         |
    | `DB-COL-O-019` created_at          | `timestamp`                      | NULLABLE                                                                                                      |                |                                                                                                                                       |
    | `DB-COL-O-020` updated_at          | `timestamp`                      | NULLABLE                                                                                                      |                |                                                                                                                                       |
    | `DB-COL-O-021` assigned_employee_id| `unsignedBigInteger` أو `uuid`   | NULLABLE, FOREIGN KEY references `users(id) ON DELETE SET NULL ON UPDATE CASCADE`                           | نعم            | الموظف المعين لمتابعة الطلب.                                                                                                           |
    | `DB-COL-O-022` payment_gateway_response| `json`                       | NULLABLE                                                                                                      |                | لتخزين الاستجابة الكاملة من بوابة الدفع للتدقيق والمتابعة.                                                                             |

#### **1.16. جدول مستندات الطلبات (Order Documents) - استخدام `media` table**
*   **معرف الجدول:** `DB-TBL-023`
*   **اسم الجدول:** (يتم إلغاء هذا الجدول المخصص)
*   **وصف موجز للغرض:** **توضيح:** سيتم الاعتماد كليًا على جدول `media` الذي توفره حزمة `spatie/laravel-medialibrary` لإدارة مستندات الطلبات. سيتم ربط المستندات بنموذج `Order` باستخدام `model_type` و `model_id`. سيتم استخدام `collection_name` في جدول `media` لتمييز نوع المستند (مثل `national_id_front`, `national_id_back`, `driving_license`, `salary_certificate`, `bank_statement`). يمكن إضافة حقول مخصصة (`custom_properties` في جدول `media`) لتخزين أي بيانات وصفية إضافية خاصة بالمستند إذا لزم الأمر (مثل تاريخ انتهاء صلاحية المستند، أو حالة مراجعة المستند من قبل الإدارة: `pending_review`, `approved`, `rejected`). هذا يخدم `MOD-ORDER-MGMT-FEAT-006`.

#### **1.17. جدول ترشيحات العملاء (Customer Referrals)**
*   **معرف الجدول:** `DB-TBL-013`
*   **اسم الجدول:** `customer_referrals`
*   **وصف موجز للغرض من الجدول:** تخزين معلومات العملاء المرشحين. (يخدم `MOD-USER-MGMT-FEAT-008`)
*   **الأعمدة:**
    | اسم العمود                      | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                       | مفهرس؟         | ملاحظات                                                                   |
    | :------------------------------- | :------------------------------- | :----------------------------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------------------------------ |
    | `DB-COL-CR-001` id               | `bigIncrements` (PK)             | PRIMARY KEY                                                                                                  | نعم (أساسي)   |                                                                           |
    | `DB-COL-CR-002` referrer_user_id | `unsignedBigInteger` أو `uuid`   | NOT NULL, FOREIGN KEY references `users(id) ON DELETE CASCADE ON UPDATE CASCADE`                           | نعم            | معرّف العميل الذي قام بالترشيح.                                            |
    | `DB-COL-CR-003` referred_name    | `string(255)`                    | NOT NULL                                                                                                     | نعم            | اسم العميل المرشح.                                                        |
    | `DB-COL-CR-004` referred_phone   | `string(20)`                     | NOT NULL                                                                                                     | نعم            | رقم جوال العميل المرشح.                                                    |
    | `DB-COL-CR-005` referred_email   | `string(255)`                    | NULLABLE                                                                                                     | نعم            | البريد الإلكتروني للعميل المرشح.                                           |
    | `DB-COL-CR-006` notes            | `text`                           | NULLABLE                                                                                                     |                | ملاحظات من العميل المرشِح.                                                 |
    | `DB-COL-CR-007` status           | `string(50)`                     | NOT NULL, DEFAULT 'new'                                                                                      | نعم            | حالة الترشيح (e.g., 'new', 'contacted', 'interested', 'not_interested', 'purchased'). |
    | `DB-COL-CR-008` admin_notes      | `text`                           | NULLABLE                                                                                                     |                | ملاحظات إدارية على الترشيح.                                                 |
    | `DB-COL-CR-009` created_at       | `timestamp`                      | NULLABLE                                                                                                     |                |                                                                           |
    | `DB-COL-CR-010` updated_at       | `timestamp`                      | NULLABLE                                                                                                     |                |                                                                           |

#### **1.18. جدول فئات الخدمات (Service Categories)**
*   **معرف الجدول:** `DB-TBL-014`
*   **اسم الجدول:** `service_categories`
*   **وصف موجز للغرض من الجدول:** تخزين فئات الخدمات الإضافية. (يخدم `FEAT-SERVICE-003`)
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                          | مفهرس؟       | ملاحظات                                                                                                         |
    | :---------------------- | :------------------------------- | :------------------------------ | :----------- | :-------------------------------------------------------------------------------------------------------------- |
    | `DB-COL-SCAT-001` id    | `bigIncrements` (PK)             | PRIMARY KEY                     | نعم (أساسي) |                                                                                                                 |
    | `DB-COL-SCAT-002` name  | `string(100)`                    | NOT NULL, UNIQUE                | نعم (فريد)  | اسم فئة الخدمة. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.                   |
    | `DB-COL-SCAT-003` status| `boolean`                        | NOT NULL, DEFAULT true          | نعم          | نشطة/غير نشطة.                                                                                                   |

#### **1.19. جدول الخدمات (Services)**
*   **معرف الجدول:** `DB-TBL-015`
*   **اسم الجدول:** `services`
*   **وصف موجز للغرض من الجدول:** تخزين الخدمات الإضافية التي يقدمها المعرض. (يخدم `FEAT-SERVICE-004`)
*   **الأعمدة:**
    | اسم العمود                | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                        | مفهرس؟         | ملاحظات                                                                                                    |
    | :------------------------- | :------------------------------- | :------------------------------------------------------------------------------------------------------------ | :------------- | :--------------------------------------------------------------------------------------------------------- |
    | `DB-COL-SVC-001` id        | `bigIncrements` (PK)             | PRIMARY KEY                                                                                                   | نعم (أساسي)   |                                                                                                            |
    | `DB-COL-SVC-002` category_id| `unsignedBigInteger`             | NULLABLE, FOREIGN KEY references `service_categories(id) ON DELETE SET NULL ON UPDATE CASCADE`              | نعم            | فئة الخدمة.                                                                                                 |
    | `DB-COL-SVC-003` name      | `string(255)`                    | NOT NULL                                                                                                      | نعم            | اسم الخدمة. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.                   |
    | `DB-COL-SVC-004` description| `text`                           | NULLABLE                                                                                                      |                | وصف الخدمة. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.                   |
    | `DB-COL-SVC-005` price     | `decimal(10,2)`                  | NOT NULL                                                                                                      |                | سعر الخدمة.                                                                                                 |
    | `DB-COL-SVC-006` image_id  | `unsignedBigInteger` أو `uuid`   | NULLABLE, FOREIGN KEY references `media(id) ON DELETE SET NULL ON UPDATE CASCADE`                           | نعم            | صورة الخدمة من جدول `media`.                                                                                |
    | `DB-COL-SVC-007` status    | `boolean`                        | NOT NULL, DEFAULT true                                                                                        | نعم            | نشطة/غير نشطة.                                                                                              |

#### **1.20. جدول طلبات الخدمات (Service Requests)**
*   **معرف الجدول:** `DB-TBL-016`
*   **اسم الجدول:** `service_requests`
*   **وصف موجز للغرض من الجدول:** تخزين طلبات الخدمات من العملاء. (يخدم `FEAT-SERVICE-002`, `FEAT-SERVICE-005`)
*   **الأعمدة:**
    | اسم العمود                        | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                       | مفهرس؟         | ملاحظات                                                                                                                          |
    | :--------------------------------- | :------------------------------- | :----------------------------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------------------------------------------------------------------------------------- |
    | `DB-COL-SR-001` id                 | `bigIncrements` (PK)             | PRIMARY KEY                                                                                                  | نعم (أساسي)   |                                                                                                                                  |
    | `DB-COL-SR-002` service_id         | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `services(id) ON DELETE RESTRICT ON UPDATE CASCADE`                       | نعم            | الخدمة المطلوبة.                                                                                                                 |
    | `DB-COL-SR-003` user_id            | `unsignedBigInteger` أو `uuid`   | NULLABLE, FOREIGN KEY references `users(id) ON DELETE SET NULL ON UPDATE CASCADE`                          | نعم            | معرّف العميل (إذا كان مسجلاً).                                                                                                     |
    | `DB-COL-SR-004` customer_name      | `string(255)`                    | NOT NULL (إذا لم يكن المستخدم مسجلاً)                                                                         | نعم            | اسم العميل.                                                                                                                      |
    | `DB-COL-SR-005` customer_phone     | `string(20)`                     | NOT NULL (إذا لم يكن المستخدم مسجلاً)                                                                         | نعم            | رقم جوال العميل.                                                                                                                 |
    | `DB-COL-SR-006` customer_email     | `string(255)`                    | NULLABLE (إذا لم يكن المستخدم مسجلاً)                                                                        | نعم            | بريد العميل.                                                                                                                    |
    | `DB-COL-SR-007` notes              | `text`                           | NULLABLE                                                                                                     |                | ملاحظات إضافية من العميل.                                                                                                          |
    | `DB-COL-SR-008` status             | `string(50)`                     | NOT NULL, DEFAULT 'new'                                                                                      | نعم            | حالة طلب الخدمة (e.g., 'new', 'contacted', 'confirmed', 'in_progress', 'completed', 'cancelled').                                |
    | `DB-COL-SR-009` admin_notes        | `text`                           | NULLABLE                                                                                                     |                | ملاحظات إدارية.                                                                                                                |
    | `DB-COL-SR-010` created_at         | `timestamp`                      | NULLABLE                                                                                                     |                |                                                                                                                                  |
    | `DB-COL-SR-011` updated_at         | `timestamp`                      | NULLABLE                                                                                                     |                |                                                                                                                                  |

#### **1.21. جدول طلبات الشركات (Corporate Enquiries)**
*   **معرف الجدول:** `DB-TBL-017`
*   **اسم الجدول:** `corporate_enquiries`
*   **وصف موجز للغرض من الجدول:** تخزين طلبات شراء الأساطيل من الشركات. (يخدم `FEAT-CORP-002`, `FEAT-CORP-003`)
*   **الأعمدة:**
    | اسم العمود                        | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                       | مفهرس؟         | ملاحظات                                                                                                                                |
    | :--------------------------------- | :------------------------------- | :----------------------------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------------------------------------------------------------------------------------------- |
    | `DB-COL-CE-001` id                 | `bigIncrements` (PK)             | PRIMARY KEY                                                                                                  | نعم (أساسي)   |                                                                                                                                        |
    | `DB-COL-CE-002` company_name       | `string(255)`                    | NOT NULL                                                                                                     | نعم            | اسم الشركة.                                                                                                                             |
    | `DB-COL-CE-003` contact_person_name| `string(255)`                    | NOT NULL                                                                                                     | نعم            | اسم شخص الاتصال.                                                                                                                       |
    | `DB-COL-CE-004` contact_person_email| `string(255)`                    | NOT NULL                                                                                                     | نعم            | بريد شخص الاتصال.                                                                                                                      |
    | `DB-COL-CE-005` contact_person_phone| `string(20)`                     | NOT NULL                                                                                                     | نعم            | جوال شخص الاتصال.                                                                                                                      |
    | `DB-COL-CE-006` enquiry_details    | `text`                           | NOT NULL                                                                                                     |                | تفاصيل الطلب (أنواع السيارات، الكميات، أي مواصفات خاصة).                                                                                 |
    | `DB-COL-CE-007` status             | `string(50)`                     | NOT NULL, DEFAULT 'new'                                                                                      | نعم            | حالة الطلب (e.g., 'new', 'contacted', 'negotiating', 'closed_won', 'closed_lost').                                                       |
    | `DB-COL-CE-008` admin_notes        | `text`                           | NULLABLE                                                                                                     |                | ملاحظات إدارية.                                                                                                                       |
    | `DB-COL-CE-009` assigned_employee_id| `unsignedBigInteger` أو `uuid`   | NULLABLE, FOREIGN KEY references `users(id) ON DELETE SET NULL ON UPDATE CASCADE`                          | نعم            | الموظف المعين لمتابعة الطلب.                                                                                                            |
    | `DB-COL-CE-010` created_at         | `timestamp`                      | NULLABLE                                                                                                     |                |                                                                                                                                        |
    | `DB-COL-CE-011` updated_at         | `timestamp`                      | NULLABLE                                                                                                     |                |                                                                                                                                        |

#### **1.22. جدول العروض الترويجية (Promotions)**
*   **معرف الجدول:** `DB-TBL-018`
*   **اسم الجدول:** `promotions`
*   **وصف موجز للغرض من الجدول:** تخزين معلومات العروض الترويجية. (يخدم `FEAT-PROMO-003`)
*   **الأعمدة:**
    | اسم العمود                   | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                 | مفهرس؟       | ملاحظات                                                                                                     |
    | :---------------------------- | :------------------------------- | :----------------------------------------------------------------------------------------------------- | :----------- | :---------------------------------------------------------------------------------------------------------- |
    | `DB-COL-P-001` id             | `bigIncrements` (PK)             | PRIMARY KEY                                                                                            | نعم (أساسي) |                                                                                                             |
    | `DB-COL-P-002` name           | `string(255)`                    | NOT NULL                                                                                               | نعم          | اسم العرض. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.                     |
    | `DB-COL-P-003` description    | `text`                           | NULLABLE                                                                                               |              | وصف العرض. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.                     |
    | `DB-COL-P-004` banner_image_id| `unsignedBigInteger` أو `uuid`   | NULLABLE, FOREIGN KEY references `media(id) ON DELETE SET NULL ON UPDATE CASCADE`                    | نعم          | صورة البنر للعرض من جدول `media`.                                                                           |
    | `DB-COL-P-005` start_date     | `date`                           | NOT NULL                                                                                               | نعم          | تاريخ بدء العرض.                                                                                            |
    | `DB-COL-P-006` end_date       | `date`                           | NOT NULL                                                                                               | نعم          | تاريخ انتهاء العرض.                                                                                         |
    | `DB-COL-P-007` status         | `boolean`                        | NOT NULL, DEFAULT true                                                                                 | نعم          | نشط/غير نشط.                                                                                                |
    | `DB-COL-P-008` created_at     | `timestamp`                      | NULLABLE                                                                                               |              |                                                                                                             |
    | `DB-COL-P-009` updated_at     | `timestamp`                      | NULLABLE                                                                                               |              |                                                                                                             |

#### **1.23. جدول ربط السيارات بالعروض (Car-Promotion Pivot Table)**
*   **معرف الجدول:** `DB-TBL-019`
*   **اسم الجدول:** `car_promotion`
*   **وصف موجز للغرض من الجدول:** جدول وسيط لربط السيارات بالعروض الترويجية (علاقة كثير إلى كثير).
*   **الأعمدة:**
    | اسم العمود             | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                    | مفهرس؟       | ملاحظات                                                                 |
    | :---------------------- | :------------------------------- | :-------------------------------------------------------------------------------------------------------- | :----------- | :---------------------------------------------------------------------- |
    | `DB-COL-CPR-001` id     | `bigIncrements` (PK)             | PRIMARY KEY                                                                                               | نعم (أساسي) |                                                                         |
    | `DB-COL-CPR-002` car_id | `unsignedBigInteger`             | NOT NULL, FOREIGN KEY references `cars(id) ON DELETE CASCADE ON UPDATE CASCADE`                         | نعم          | معرّف السيارة.                                                          |
    | `DB-COL-CPR-003` promotion_id | `unsignedBigInteger`         | NOT NULL, FOREIGN KEY references `promotions(id) ON DELETE CASCADE ON UPDATE CASCADE`                 | نعم          | معرّف العرض.                                                            |
    | `DB-COL-CPR-004` car_offer_price | `decimal(12,2)`            | NULLABLE                                                                                                  |              | السعر الخاص للسيارة ضمن هذا العرض (إذا كان مختلفًا عن سعر السيارة العام للعرض). |
    |                         |                                  | UNIQUE (`car_id`, `promotion_id`)                                                                         | نعم (مركب)  | لضمان عدم تكرار نفس السيارة لنفس العرض.                               |

#### **1.24. جدول الصفحات الثابتة (Cms Pages)**
*   **معرف الجدول:** `DB-TBL-CMS-001`
*   **اسم الجدول:** `cms_pages`
*   **وصف موجز للغرض من الجدول:** تخزين محتوى الصفحات الثابتة. (يخدم `MOD-CMS-FEAT-001`)
*   **الأعمدة:**
    | اسم العمود                 | نوع البيانات الدقيق (Laravel 10) | القيود                          | مفهرس؟       | ملاحظات                                                                                                   |
    | :-------------------------- | :------------------------------- | :------------------------------ | :----------- | :-------------------------------------------------------------------------------------------------------- |
    | `DB-COL-CMSP-001` id        | `bigIncrements` (PK)             | PRIMARY KEY                     | نعم (أساسي) |                                                                                                           |
    | `DB-COL-CMSP-002` title     | `string(255)`                    | NOT NULL                        | نعم          | عنوان الصفحة. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها على مستوى النموذج. |
    | `DB-COL-CMSP-003` slug      | `string(255)`                    | NOT NULL, UNIQUE                | نعم (فريد)  | المعرف الفريد للصفحة في الرابط (e.g., 'about-us').                                                         |
    | `DB-COL-CMSP-004` content   | `longText`                       | NOT NULL                        |              | محتوى الصفحة (HTML من محرر Rich Text). سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable`.     |
    | `DB-COL-CMSP-005` meta_keywords| `text`                           | NULLABLE                        |              | كلمات مفتاحية SEO. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable`.                           |
    | `DB-COL-CMSP-006` meta_description| `text`                       | NULLABLE                        |              | وصف ميتا SEO. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable`.                                |
    | `DB-COL-CMSP-007` status    | `string(50)`                     | NOT NULL, DEFAULT 'published'   | نعم          | حالة الصفحة ('published', 'draft').                                                                         |
    | `DB-COL-CMSP-008` created_at| `timestamp`                      | NULLABLE                        |              |                                                                                                           |
    | `DB-COL-CMSP-009` updated_at| `timestamp`                      | NULLABLE                        |              |                                                                                                           |

#### **1.25. جدول بنرات الصفحة الرئيسية (Homepage Banners)**
*   **معرف الجدول:** `DB-TBL-CMS-002`
*   **اسم الجدول:** `homepage_banners`
*   **وصف موجز للغرض من الجدول:** تخزين معلومات بنرات السلايدر في الصفحة الرئيسية. (يخدم `MOD-CMS-FEAT-002`)
*   **الأعمدة:**
    | اسم العمود                  | نوع البيانات الدقيق (Laravel 10) | القيود                                                                                                 | مفهرس؟       | ملاحظات                                                                                                   |
    | :--------------------------- | :------------------------------- | :----------------------------------------------------------------------------------------------------- | :----------- | :-------------------------------------------------------------------------------------------------------- |
    | `DB-COL-CMSB-001` id         | `bigIncrements` (PK)             | PRIMARY KEY                                                                                            | نعم (أساسي) |                                                                                                           |
    | `DB-COL-CMSB-002` title      | `string(255)`                    | NULLABLE                                                                                               |              | العنوان الرئيسي على البنر. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.   |
    | `DB-COL-CMSB-003` subtitle   | `string(255)`                    | NULLABLE                                                                                               |              | العنوان الفرعي على البنر. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.   |
    | `DB-COL-CMSB-004` button_text| `string(100)`                    | NULLABLE                                                                                               |              | نص زر الإجراء. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable` إذا تم تفعيلها.                |
    | `DB-COL-CMSB-005` button_link| `string(255)`                    | NULLABLE                                                                                               |              | رابط زر الإجراء.                                                                                           |
    | `DB-COL-CMSB-006` image_id   | `unsignedBigInteger` أو `uuid`   | NOT NULL, FOREIGN KEY references `media(id) ON DELETE RESTRICT ON UPDATE CASCADE`                    | نعم          | صورة البنر من جدول `media`.                                                                                |
    | `DB-COL-CMSB-007` order      | `integer`                        | NOT NULL, DEFAULT 0                                                                                    | نعم          | ترتيب عرض البنر.                                                                                           |
    | `DB-COL-CMSB-008` status     | `boolean`                        | NOT NULL, DEFAULT true                                                                                 | نعم          | نشط/غير نشط.                                                                                              |

#### **1.26. جدول الإعدادات (Settings)**
*   **معرف الجدول:** `DB-TBL-CORE-001`
*   **اسم الجدول:** `settings`
*   **وصف موجز للغرض من الجدول:** تخزين إعدادات النظام العامة. (يخدم `MOD-CORE-FEAT-003`)
*   **الأعمدة:**
    | اسم العمود                    | نوع البيانات الدقيق (Laravel 10) | القيود                                   | مفهرس؟       | ملاحظات                                                                                                |
    | :----------------------------- | :------------------------------- | :--------------------------------------- | :----------- | :----------------------------------------------------------------------------------------------------- |
    | `DB-COL-SET-001` id            | `bigIncrements` (PK)             | PRIMARY KEY                              | نعم (أساسي) |                                                                                                        |
    | `DB-COL-SET-002` key           | `string(255)`                    | NOT NULL, UNIQUE                         | نعم (فريد)  | مفتاح الإعداد (e.g., 'site_name').                                                                      |
    | `DB-COL-SET-003` value         | `text`                           | NULLABLE                                 |              | قيمة الإعداد (يمكن أن تكون JSON إذا كانت القيمة معقدة).                                                    |
    | `DB-COL-SET-004` group_name    | `string(100)`                    | NULLABLE                                 | نعم          | اسم مجموعة الإعداد (لتنظيمها في لوحة التحكم، e.g., 'general', 'seo', 'payment').                       |
    | `DB-COL-SET-005` display_name  | `string(255)`                    | NULLABLE                                 |              | الاسم الذي يظهر في واجهة لوحة التحكم. سيتم التعامل مع الترجمة باستخدام `spatie/laravel-translatable`.      |
    | `DB-COL-SET-006` type          | `string(50)`                     | NOT NULL, DEFAULT 'text'                 |              | نوع حقل الإدخال في لوحة التحكم (e.g., 'text', 'textarea', 'boolean', 'number', 'select', 'image_id'). |
    | `DB-COL-SET-007` options       | `json`                           | NULLABLE                                 |              | خيارات إضافية للنوع (مثل خيارات قائمة select، أو قواعد التحقق).                                           |
    | `DB-COL-SET-008` is_translatable| `boolean`                        | NOT NULL, DEFAULT false                  |              | هل قيمة الإعداد قابلة للترجمة؟ (إذا كانت value هي JSON، يمكن تخزين الترجمات داخلها).                   |

#### **1.27. جداول مكتبة Spatie Media Library**
*   **معرف الجدول:** `DB-TBL-SPATIE-MEDIA`
*   **اسم الجدول:** `media`
*   **وصف موجز للغرض من الجدول:** يُستخدم بواسطة حزمة `spatie/laravel-medialibrary` لتخزين جميع الملفات المرفوعة (صور السيارات، شعارات الماركات، صور البنرات، مستندات العملاء، إلخ). يتم ربط السجلات في هذا الجدول بالنماذج (Models) الموافقة عبر الأعمدة `model_type`, `model_id`, و `collection_name`.
    *   سيتم استخدام هذا الجدول كما هو معرف بواسطة الحزمة. الأعمدة الرئيسية تشمل `id`, `model_type`, `model_id`, `uuid`, `collection_name`, `name`, `file_name`, `mime_type`, `disk`, `conversions_disk`, `size`, `manipulations`, `custom_properties`, `generated_conversions`, `responsive_images`, `order_column`.
    *   **تفاصيل إضافية:** سيتم استخدام `disk_name` الافتراضي `public` للتطوير، مع إمكانية التغيير إلى `s3` أو ما يعادله في بيئة الإنتاج. سيتم تعريف `conversions` للصور (مثل `thumbnail` بحجم 150x100، و `medium` بحجم 600x400) في النماذج التي تستخدم `HasMedia` trait (مثل `Car`, `Brand`, `Promotion`, `HomepageBanner`, `Service`, `User` (للصورة الشخصية)).

#### **1.28. جداول مكتبة Spatie Laravel Permission**
*   **معرفات الجداول:** `DB-TBL-SPATIE-PERM-ROLES`, `DB-TBL-SPATIE-PERM-PERMISSIONS`, `DB-TBL-SPATIE-PERM-MODEL-HAS-PERMISSIONS`, `DB-TBL-SPATIE-PERM-MODEL-HAS-ROLES`, `DB-TBL-SPATIE-PERM-ROLE-HAS-PERMISSIONS`
*   **أسماء الجداول:** `roles`, `permissions`, `model_has_permissions`, `model_has_roles`, `role_has_permissions`
*   **وصف موجز للغرض من الجداول:** تُستخدم بواسطة حزمة `spatie/laravel-permission` لإدارة الأدوار والصلاحيات للمستخدمين (خاصة الموظفين والمديرين).
    *   سيتم استخدام هذه الجداول كما هي معرفة بواسطة الحزمة.

#### **1.29. جدول Laravel Notifications**
*   **معرف الجدول:** `DB-TBL-LARAVEL-NOTIF`
*   **اسم الجدول:** `notifications`
*   **وصف موجز للغرض من الجدول:** يُستخدم بواسطة نظام الإشعارات في Laravel (database channel) لتخزين الإشعارات الداخلية للمستخدمين.
    *   سيتم استخدام هذا الجدول كما هو معرف بواسطة Laravel. الأعمدة الرئيسية تشمل `id` (UUID), `type`, `notifiable_type`, `notifiable_id`, `data` (JSON), `read_at`, `created_at`, `updated_at`.

#### **1.30. العلاقات بين الجداول (وصف نصي)**

*   **User Relationships:**
    *   `User` له العديد من `Order` (واحد لمتعدد).
    *   `User` له العديد من `UserFavorite` (واحد لمتعدد).
    *   `User` له العديد من `CustomerReferral` (كمرشِّح - واحد لمتعدد).
    *   `User` له العديد من `ServiceRequest` (واحد لمتعدد).
    *   `User` يمكن أن يكون `assigned_employee_id` في `Order` (واحد لمتعدد، اختياري).
    *   `User` يمكن أن يكون `assigned_employee_id` في `CorporateEnquiry` (واحد لمتعدد، اختياري).
    *   `User` ينتمي إلى `Nationality` (متعدد لواحد، اختياري).
    *   `User` يرتبط بـ `Media` (صورة شخصية - واحد لواحد، اختياري، عبر `profile_photo_id` الذي يشير إلى `id` في `media` table مع `model_type='App\Modules\UserManagement\Models\User'` و `collection_name='profile_photos'`).
    *   `User` يرتبط بـ `Roles` و `Permissions` (متعدد لمتعدد، عبر جداول Spatie).
    *   `User` له العديد من `Notification` (عبر علاقة morph).

*   **CarCatalog Relationships:**
    *   `Brand` لها العديد من `CarModel` (واحد لمتعدد).
    *   `Brand` ترتبط بـ `Media` (شعار - واحد لواحد، اختياري، عبر `logo_id` الذي يشير إلى `id` في `media` table مع `model_type='App\Modules\CarCatalog\Models\Brand'` و `collection_name='logos'`).
    *   `CarModel` ينتمي إلى `Brand` (متعدد لواحد).
    *   `Car` تنتمي إلى `Brand`, `CarModel`, `ManufacturingYear`, `Color` (الرئيسي), `TransmissionType`, `FuelType`, `BodyType` (علاقات متعدد لواحد لكل منها).
    *   `Car` لها العديد من `UserFavorite` (واحد لمتعدد).
    *   `Car` لها العديد من `Order` (واحد لمتعدد).
    *   `Car` لها علاقة متعدد لمتعدد مع `CarFeature` عبر `car_car_feature` (pivot table).
    *   `Car` لها علاقة متعدد لمتعدد مع `Promotion` عبر `car_promotion` (pivot table).
    *   `Car` ترتبط بـ `Media` (صور متعددة، عبر علاقة morphMany من Spatie، مع `collection_name='car_images'` أو ما شابه).
    *   `CarFeature` تنتمي إلى `FeatureCategory` (متعدد لواحد، اختياري).

*   **OrderManagement Relationships:**
    *   `Order` تنتمي إلى `User` و `Car` (علاقات متعدد لواحد).
    *   `Order` تنتمي إلى `Nationality` (جنسية العميل وقت الطلب - متعدد لواحد، اختياري).
    *   `Order` ترتبط بـ `Media` (مستندات الطلب، عبر علاقة morphMany من Spatie مع collections محددة مثل `national_id_docs`, `license_docs`).

*   **ServiceManagement Relationships:**
    *   `Service` تنتمي إلى `ServiceCategory` (متعدد لواحد، اختياري).
    *   `Service` ترتبط بـ `Media` (صورة الخدمة - واحد لواحد، اختياري، مع `collection_name='service_images'`).
    *   `ServiceRequest` تنتمي إلى `Service` (متعدد لواحد).
    *   `ServiceRequest` تنتمي إلى `User` (متعدد لواحد، اختياري، إذا كان العميل مسجلاً).

*   **PromotionManagement Relationships:**
    *   `Promotion` ترتبط بـ `Media` (صورة البنر - واحد لواحد، اختياري، مع `collection_name='promotion_banners'`).
    *   `Promotion` لها علاقة متعدد لمتعدد مع `Car` عبر `car_promotion` (pivot table).

*   **Cms Relationships:**
    *   `HomepageBanner` ترتبط بـ `Media` (صورة البنر - واحد لواحد، مع `collection_name='homepage_banners'`).

---

### 2. نقاط نهاية الـ API (API Endpoints)

ستركز نقاط نهاية الـ API هذه بشكل أساسي على خدمة تطبيق Flutter وأي تفاعلات خارجية أخرى. لوحة تحكم Dash المخصصة، المبنية على Blade، ستعتمد بشكل رئيسي على تفاعلات Laravel Controller-View التقليدية. ومع ذلك، قد تحتاج بعض مكونات Dash الديناميكية (مثل الجداول القابلة للفرز والفلترة بدون إعادة تحميل الصفحة بالكامل، أو الرسوم البيانية التي تتطلب تحديثًا دوريًا للبيانات، أو عمليات الحفظ السريعة من داخل القوائم) إلى نقاط نهاية API داخلية مخصصة (Internal APIs). هذه النقاط ستكون محمية بنفس آليات المصادقة والتفويض (Session-based للمستخدمين المسجلين في Dash) وسيتم تصميمها لتكون خفيفة وموجهة لغرض محدد. **سيتم تحديد هذه الـ Internal APIs لاحقًا أثناء مرحلة تصميم واجهات المستخدم التفصيلية (`UIUX-FR.md`) والتنفيذ، ولكن من المهم الإشارة إلى إمكانية وجودها.** النقاط المدرجة أدناه هي للواجهات الخارجية بشكل أساسي.
جميع نقاط النهاية الموجهة لـ Flutter ستكون مسبوقة بـ `/api/v1/flutter/` (أو ما يعادله)، وستستخدم Laravel Sanctum للمصادقة المستندة إلى token. سيتم استخدام `spatie/laravel-permission` للتحقق من الصلاحيات عند الحاجة.
سيتم بناء الاستجابات بتنسيق JSON موحد (مثل JSend: `{ "status": "success/fail/error", "data": {...} / "message": "..." }`).

#### **2.1. المصادقة (Authentication - `MOD-API-FEAT-001` / `MOD-USER-MGMT`)**

*   **معرف نقطة النهاية:** `API-EP-AUTH-001`
    *   **الوظيفة/الغرض:** تسجيل عميل جديد. (يخدم `MOD-USER-MGMT-FEAT-001`)
    *   **المسار:** `/api/v1/flutter/auth/register`
    *   **نوع طلب HTTP:** `POST`
    *   **المدخلات المتوقعة (Request Payload):**
        *   `first_name` (string, required, min:2, max:50, regex: `^[\\p{L}\\s'-]+$`)
        *   `last_name` (string, required, min:2, max:50, regex: `^[\\p{L}\\s'-]+$`)
        *   `email` (string, required, email, unique:users,email)
        *   `phone_number` (string, required, regex for Saudi phone: `^05[0-9]{8}$`, unique:users,phone_number)
        *   `password` (string, required, min:8, confirmed, regex: `^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]).{8,}$`)
        *   `password_confirmation` (string, required)
        *   `agree_terms` (boolean, required, accepted)
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (201 Created):** `{ "status": "success", "message": "OTP sent to your phone number for verification." }`
        *   **فشل التحقق (422 Unprocessable Entity):** `{ "status": "fail", "data": { "field_name": ["error message"] } }`
        *   **خطأ (500 Internal Server Error):** `{ "status": "error", "message": "An unexpected error occurred." }`
    *   **المصادقة/التفويض:** لا يتطلب مصادقة.

*   **معرف نقطة النهاية:** `API-EP-AUTH-002`
    *   **الوظيفة/الغرض:** التحقق من OTP المرسل لرقم الجوال. (يخدم `MOD-USER-MGMT-FEAT-001B`)
    *   **المسار:** `/api/v1/flutter/auth/verify-otp`
    *   **نوع طلب HTTP:** `POST`
    *   **المدخلات المتوقعة (Request Payload):**
        *   `phone_number` (string, required, exists:users,phone_number where status='pending_verification' or for profile update scenario)
        *   `otp_code` (string, required, numeric, digits_between:4,6)
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (200 OK):** `{ "status": "success", "message": "Phone number verified successfully. Account activated.", "data": { "user": { "id": 1, "first_name": "...", "email": "...", ...other_safe_details... }, "token": "api_token_here" } }` (إذا كان تفعيل حساب جديد)
        *   **نجاح (200 OK):** `{ "status": "success", "message": "Phone number verified successfully." }` (إذا كان تحديث رقم جوال من الملف الشخصي)
        *   **فشل (400 Bad Request / 422 Unprocessable Entity):** `{ "status": "fail", "data": { "otp_code": ["Invalid or expired OTP."] } }`
    *   **المصادقة/التفويض:** لا يتطلب مصادقة للتحقق الأولي، قد يتطلب للملف الشخصي.

*   **معرف نقطة النهاية:** `API-EP-AUTH-003`
    *   **الوظيفة/الغرض:** إعادة إرسال OTP. (يخدم `MOD-USER-MGMT-FEAT-001B`)
    *   **المسار:** `/api/v1/flutter/auth/resend-otp`
    *   **نوع طلب HTTP:** `POST`
    *   **المدخلات المتوقعة (Request Payload):**
        *   `phone_number` (string, required, regex for Saudi phone: `^05[0-9]{8}$`)
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (200 OK):** `{ "status": "success", "message": "OTP resent successfully." }`
        *   **فشل (429 Too Many Requests):** `{ "status": "fail", "message": "Too many attempts. Please try again later." }`
    *   **المصادقة/التفويض:** لا يتطلب مصادقة.

*   **معرف نقطة النهاية:** `API-EP-AUTH-004`
    *   **الوظيفة/الغرض:** تسجيل الدخول للمستخدمين. (يخدم `MOD-USER-MGMT-FEAT-002`)
    *   **المسار:** `/api/v1/flutter/auth/login`
    *   **نوع طلب HTTP:** `POST`
    *   **المدخلات المتوقعة (Request Payload):**
        *   `identifier` (string, required, can be email or phone_number)
        *   `password` (string, required)
        *   `device_name` (string, required, e.g., "My iPhone 15") - لتسمية الـ token في Sanctum.
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (200 OK):** `{ "status": "success", "data": { "user": { "id": 1, "first_name": "...", "email": "...", ...other_safe_details... }, "token": "api_token_here" } }`
        *   **فشل (401 Unauthorized):** `{ "status": "fail", "message": "Invalid credentials or account inactive." }`
    *   **المصادقة/التفويض:** لا يتطلب مصادقة.

*   **معرف نقطة النهاية:** `API-EP-AUTH-005`
    *   **الوظيفة/الغرض:** تسجيل الخروج للمستخدمين.
    *   **المسار:** `/api/v1/flutter/auth/logout`
    *   **نوع طلب HTTP:** `POST`
    *   **المدخلات المتوقعة (Request Payload):** لا يوجد (يتم إرسال الـ token في الـ Header).
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (200 OK):** `{ "status": "success", "message": "Logged out successfully." }`
    *   **المصادقة/التفويض:** يتطلب مصادقة (Sanctum token).

*   **معرف نقطة النهاية:** `API-EP-AUTH-006`
    *   **الوظيفة/الغرض:** طلب إعادة تعيين كلمة المرور. (يخدم `MOD-USER-MGMT-FEAT-003`)
    *   **المسار:** `/api/v1/flutter/auth/password/request-reset`
    *   **نوع طلب HTTP:** `POST`
    *   **المدخلات المتوقعة (Request Payload):**
        *   `email` (string, required, email, exists:users,email)
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (200 OK):** `{ "status": "success", "message": "Password reset link sent to your email." }`
    *   **المصادقة/التفويض:** لا يتطلب مصادقة.

*   **معرف نقطة النهاية:** `API-EP-AUTH-007`
    *   **الوظيفة/الغرض:** إعادة تعيين كلمة المرور باستخدام الرمز. (يخدم `MOD-USER-MGMT-FEAT-003`)
    *   **المسار:** `/api/v1/flutter/auth/password/reset`
    *   **نوع طلب HTTP:** `POST`
    *   **المدخلات المتوقعة (Request Payload):**
        *   `token` (string, required, from email link)
        *   `email` (string, required, email)
        *   `password` (string, required, min:8, confirmed, regex: `^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]).{8,}$`)
        *   `password_confirmation` (string, required)
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (200 OK):** `{ "status": "success", "message": "Password reset successfully." }`
        *   **فشل (400 Bad Request / 422 Unprocessable Entity):** `{ "status": "fail", "message": "Invalid token or email." / "data": { ...validation_errors... } }`
    *   **المصادقة/التفويض:** لا يتطلب مصادقة.

#### **2.2. ملف المستخدم (User Profile - `MOD-API-FEAT-001` / `MOD-USER-MGMT`)**

*   **معرف نقطة النهاية:** `API-EP-USER-001`
    *   **الوظيفة/الغرض:** جلب بيانات الملف الشخصي للمستخدم المسجل دخوله. (يخدم `MOD-USER-MGMT-FEAT-005`)
    *   **المسار:** `/api/v1/flutter/user/profile`
    *   **نوع طلب HTTP:** `GET`
    *   **المدخلات المتوقعة (Request Payload):** لا يوجد.
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (200 OK):** `{ "status": "success", "data": { "user": { "id": 1, "first_name": "...", "last_name": "...", "email": "...", "phone_number": "...", "profile_photo_url": "url_or_null", "address_line1": "...", "city": "...", "national_id": "...", "date_of_birth": "YYYY-MM-DD or null", "nationality_name_ar": "...", "nationality_name_en": "..." } } }`
    *   **المصادقة/التفويض:** يتطلب مصادقة (Sanctum token).

*   **معرف نقطة النهاية:** `API-EP-USER-002`
    *   **الوظيفة/الغرض:** تحديث بيانات الملف الشخصي للمستخدم. (يخدم `MOD-USER-MGMT-FEAT-005`)
    *   **المسار:** `/api/v1/flutter/user/profile`
    *   **نوع طلب HTTP:** `POST` (استخدام POST مع `_method=PUT` لتسهيل رفع الملفات من Flutter).
    *   **المدخلات المتوقعة (Request Payload - `multipart/form-data`):**
        *   `_method` (string, value: "PUT")
        *   `first_name` (string, optional, min:2, max:50)
        *   `last_name` (string, optional, min:2, max:50)
        *   `phone_number` (string, optional, regex for Saudi phone: `^05[0-9]{8}$`, unique:users,phone_number,except,self_id) - يتطلب OTP إذا تغير.
        *   `address_line1` (string, optional, max:255)
        *   `city` (string, optional, max:100)
        *   `national_id` (string, optional, regex for Saudi ID: `^[12][0-9]{9}$`, size:10)
        *   `date_of_birth` (date, optional, YYYY-MM-DD format, before:today)
        *   `nationality_id` (integer, optional, exists:nationalities,id)
        *   `profile_photo` (file, optional, image, max:2048 (KB), mimes:jpg,png,webp)
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (200 OK):** `{ "status": "success", "message": "Profile updated successfully.", "data": { "user": { ...updated_user_details... } } }`
        *   **نجاح (200 OK) مع طلب OTP:** `{ "status": "success", "message": "Profile data updated. OTP sent to new phone number for verification.", "data": { "user": { ...updated_user_details... }, "phone_verification_required": true } }` (إذا تم تغيير رقم الجوال).
        *   **فشل التحقق (422 Unprocessable Entity):** `{ "status": "fail", "data": { "field_name": ["error message"] } }`
    *   **المصادقة/التفويض:** يتطلب مصادقة (Sanctum token).

*   **معرف نقطة النهاية:** `API-EP-USER-003`
    *   **الوظيفة/الغرض:** تغيير كلمة مرور المستخدم. (يخدم `MOD-USER-MGMT-FEAT-005`)
    *   **المسار:** `/api/v1/flutter/user/profile/change-password`
    *   **نوع طلب HTTP:** `POST`
    *   **المدخلات المتوقعة (Request Payload):**
        *   `current_password` (string, required)
        *   `new_password` (string, required, min:8, confirmed, regex: `^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]).{8,}$`)
        *   `new_password_confirmation` (string, required)
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (200 OK):** `{ "status": "success", "message": "Password changed successfully." }`
        *   **فشل (400 Bad Request / 422 Unprocessable Entity):** `{ "status": "fail", "message": "Incorrect current password." / "data": { ...validation_errors... } }`
    *   **المصادقة/التفويض:** يتطلب مصادقة (Sanctum token).

#### **2.3. كتالوج السيارات (Car Catalog - `MOD-API-FEAT-001` / `MOD-CAR-CATALOG`)**

*   **معرف نقطة النهاية:** `API-EP-CAR-001`
    *   **الوظيفة/الغرض:** جلب قائمة السيارات الجديدة مع الفلترة والترتيب والترقيم. (يخدم `MOD-CAR-CATALOG-FEAT-001`, `MOD-CAR-CATALOG-FEAT-002`)
    *   **المسار:** `/api/v1/flutter/cars`
    *   **نوع طلب HTTP:** `GET`
    *   **المدخلات المتوقعة (Query Parameters):**
        *   `page` (integer, optional, default:1)
        *   `per_page` (integer, optional, default:15, max:50)
        *   `sort_by` (string, optional, in:price_asc,price_desc,latest,featured,most_viewed)
        *   `brand_ids[]` (array of integers, optional)
        *   `model_ids[]` (array of integers, optional)
        *   `year_ids[]` (array of integers, optional)
        *   `color_ids[]` (array of integers, optional)
        *   `transmission_type_ids[]` (array of integers, optional)
        *   `fuel_type_ids[]` (array of integers, optional)
        *   `body_type_ids[]` (array of integers, optional)
        *   `min_price` (numeric, optional, min:0)
        *   `max_price` (numeric, optional, gt:min_price)
        *   `search_term` (string, optional, min:3)
    *   **المخرجات المتوقعة (Response Payload - نجاح 200 OK):**
        ```json
        {
          "status": "success",
          "data": [
            {
              "id": 1,
              "name": "تويوتا كامري GLX 2023",
              "main_image_url": "url_to_thumbnail_conversion_of_main_image",
              "price": "120,000.00 ر.س",
              "offer_price": "115,000.00 ر.س", // (null if no offer)
              "brief_specs": [
                { "label": "ناقل الحركة", "value": "أوتوماتيك", "icon": "transmission_icon_class_or_svg_path" },
                { "label": "نوع الوقود", "value": "بنزين", "icon": "fuel_icon_class_or_svg_path" },
                { "label": "قوة المحرك", "value": "203 حصان", "icon": "engine_icon_class_or_svg_path" }
              ],
              "is_favorite": true, // (boolean, true if user logged in and car is favorited)
              "status_tag": "متوفر" // (e.g., "متوفر", "عرض خاص", "محجوز", "مباع")
            }
          ],
          "meta": {
            "current_page": 1,
            "from": 1,
            "last_page": 10,
            "links": [ /* Laravel pagination links */ ],
            "path": "/api/v1/flutter/cars",
            "per_page": 15,
            "to": 15,
            "total": 150
          }
        }
        ```
    *   **المصادقة/التفويض:** اختياري (لتحديد `is_favorite`).

*   **معرف نقطة النهاية:** `API-EP-CAR-002`
    *   **الوظيفة/الغرض:** جلب تفاصيل سيارة محددة. (يخدم `MOD-CAR-CATALOG-FEAT-003`)
    *   **المسار:** `/api/v1/flutter/cars/{id}` (حيث `{id}` هو معرف السيارة)
    *   **نوع طلب HTTP:** `GET`
    *   **المخرجات المتوقعة (Response Payload - نجاح 200 OK):**
        ```json
        {
          "status": "success",
          "data": {
            "id": 1,
            "full_name": "تويوتا كامري GLX فل كامل 2023",
            "images": [
              { "id": 1, "url": "url_to_medium_conversion_image1.jpg", "original_url": "url_to_original_image1.jpg", "is_main": true },
              { "id": 2, "url": "url_to_medium_conversion_image2.jpg", "original_url": "url_to_original_image2.jpg", "is_main": false }
            ],
            "video_url": "url_to_youtube_video_or_null",
            "price_info": {
              "base_price": "110,000.00 ر.س", // (السعر قبل الضريبة)
              "tax_amount": "16,500.00 ر.س",  // (قيمة الضريبة)
              "total_price": "126,500.00 ر.س", // (السعر شامل الضريبة)
              "offer_total_price": "120,000.00 ر.س", // (null if no offer, السعر بالعرض شامل الضريبة)
              "reservation_amount": "1,000.00 ر.س" // (يُجلب من إعدادات النظام)
            },
            "description": "وصف تفصيلي للسيارة...",
            "technical_specifications": [
              { "label": "نوع المحرك", "value": "2.5L 4-Cylinder" },
              { "label": "قوة المحرك (حصان)", "value": "203" },
              // ... more specs
            ],
            "features": [
              {
                "category_name": "الأمان والسلامة",
                "items": [
                  { "name": "وسائد هوائية أمامية", "icon_class": "fas fa-airbag" },
                  { "name": "نظام ABS", "icon_class": "fas fa-shield-alt" }
                ]
              }
              // ... more feature categories
            ],
            "promotion_details": { // (null if not part of a promotion)
              "name": "عرض نهاية العام",
              "description": "خصم خاص وشروط تمويل ميسرة.",
              "end_date": "2024-12-31"
            },
            "after_sales_services_text": "تشمل خدمات ما بعد البيع المجانية: غسيل مجاني أول شهر وخصم 10% على أول صيانة.", // (يُجلب من إعدادات النظام `MOD-CORE-FEAT-003`)
            "pdf_specs_url": "/api/v1/cars/1/download-specs-pdf",
            "is_favorite": false,
            "status": "available" // (e.g., "available", "reserved", "sold")
          }
        }
        ```
        *   **فشل (404 Not Found):** `{ "status": "fail", "message": "Car not found." }`
    *   **المصادقة/التفويض:** اختياري.

*   **معرف نقطة النهاية:** `API-EP-CAR-003`
    *   **الوظيفة/الغرض:** جلب خيارات الفلترة الديناميكية لقائمة السيارات.
    *   **المسار:** `/api/v1/flutter/cars/filters-options`
    *   **نوع طلب HTTP:** `GET`
    *   **المخرجات المتوقعة (Response Payload - نجاح 200 OK):**
        ```json
        {
          "status": "success",
          "data": {
            "brands": [ { "id": 1, "name": "تويوتا", "model_count": 25 }, ... ],
            "models": [ { "id": 10, "name": "كامري", "brand_id": 1, "car_count": 10 }, ... ],
            "years": [ { "id": 1, "year": 2023, "car_count": 50 }, ... ],
            "colors": [ { "id": 1, "name": "أحمر", "hex_code": "#FF0000", "car_count": 30 }, ... ],
            "transmission_types": [ { "id": 1, "name": "أوتوماتيك", "car_count": 120 }, ... ],
            "fuel_types": [ { "id": 1, "name": "بنزين", "car_count": 150 }, ... ],
            "body_types": [ { "id": 1, "name": "سيدان", "car_count": 80 }, ... ],
            "price_range": { "min": 50000, "max": 500000 }
          }
        }
        ```
    *   **المصادقة/التفويض:** لا يتطلب مصادقة.

*   **معرف نقطة النهاية:** `API-EP-CAR-004`
    *   **الوظيفة/الغرض:** إضافة/إزالة سيارة من المفضلة. (يخدم `MOD-CAR-CATALOG-FEAT-005`)
    *   **المسار:** `/api/v1/flutter/user/favorites/{car_id}`
    *   **نوع طلب HTTP:** `POST` (للإضافة), `DELETE` (للإزالة)
    *   **المدخلات المتوقعة (Request Payload):** لا يوجد (معرف السيارة في المسار).
    *   **المخرجات المتوقعة (Response Payload):**
        *   **نجاح (200 OK / 201 Created):** `{ "status": "success", "message": "Car added to/removed from favorites." }`
    *   **المصادقة/التفويض:** يتطلب مصادقة (Sanctum token).

*   **معرف نقطة النهاية:** `API-EP-CAR-005`
    *   **الوظيفة/الغرض:** جلب قائمة السيارات المفضلة للمستخدم. (يخدم `MOD-CAR-CATALOG-FEAT-005`)
    *   **المسار:** `/api/v1/flutter/user/favorites`
    *   **نوع طلب HTTP:** `GET`
    *   **المدخلات المتوقعة (Query Parameters):** `page`, `per_page` (اختياري للترقيم).
    *   **المخرجات المتوقعة (Response Payload):** مشابهة لـ `API-EP-CAR-001` ولكن تحتوي فقط على السيارات المفضلة.
    *   **المصادقة/التفويض:** يتطلب مصادقة (Sanctum token).

*   **معرف نقطة النهاية:** `API-EP-CAR-006`
    *   **الوظيفة/الغرض:** تحميل ملف PDF بمواصفات السيارة. (يخدم `MOD-CAR-CATALOG-FEAT-004`)
    *   **المسار:** `/api/v1/cars/{id}/download-specs-pdf`
    *   **نوع طلب HTTP:** `GET`
    *   **المخرجات المتوقعة:** استجابة ملف PDF (`application/pdf`) مع `Content-Disposition: attachment; filename="car-specs-{id}.pdf"`.
    *   **المصادقة/التفويض:** لا يتطلب مصادقة.

#### **2.4. الطلبات (Orders - `MOD-API-FEAT-001` / `MOD-ORDER-MGMT`)**
(سيتم تفصيلها لاحقًا بنفس مستوى الدقة إذا استمر الطلب، بناءً على الهيكل المتبع أعلاه، مع الأخذ في الاعتبار جميع حقول الإدخال من `REQ-FR.md` وهياكل الاستجابة المناسبة)

#### **2.5. عملية "اطلب سيارتك" (Custom Car Request - `MOD-API-FEAT-001` / `MOD-ORDER-MGMT`)**
(سيتم تفصيلها لاحقًا بنفس مستوى الدقة)

#### **2.6. الإشعارات (Notifications - `MOD-API-FEAT-001` / `MOD-NOTIFICATION`)**
(سيتم تفصيلها لاحقًا بنفس مستوى الدقة)

#### **2.7. المحتوى العام (CMS & Settings - `MOD-API-FEAT-001` / `MOD-CMS`, `MOD-CORE`)**
(سيتم تفصيلها لاحقًا بنفس مستوى الدقة)

---

### 3. التصميم التقني للجوانب المعقدة أو الحرجة

#### **3.1. تكامل لوحة تحكم Dash المخصصة مع Laravel Blade (`TECH-CMPLX-DASH-001`)**
*   **الوصف:** هذا الجانب حيوي لتحقيق **المخرج النهائي رقم 2 (لوحة تحكم احترافية)**. يتضمن تحويل أصول Dash الثابتة (`dashboard.html`, `style.css`, `script.js`) إلى نظام Blade ديناميكي ومتكامل مع Laravel.
*   **التدفق المنطقي والمكونات الرئيسية:**
    1.  **تحليل الأصول الثابتة:** سيتم تحليل `dashboard.html` لتحديد الأقسام القابلة لإعادة الاستخدام (header, sidebar, footer, content area, stepper). سيتم تحليل `style.css` لضمان التوافق مع RTL وتجنب التعارضات. سيتم تحليل `script.js` لفهم وظائفه (تبديل القائمة، الرسوم البيانية، Stepper) وتحديد الأجزاء التي تحتاج إلى تعديل للتفاعل مع بيانات Laravel.
    2.  **إنشاء Blade Layouts:**
        *   `admin_layout.blade.php`: سيكون الـ Layout الرئيسي للوحة تحكم الإدارة. سيشمل `<head>` (مع روابط CSS بما في ذلك `style.css` وملفات RTL Bootstrap)، و `<body>` يحتوي على `@include` للقائمة الجانبية والشريط العلوي، و `@yield('content')` للمحتوى المتغير، و `@include` للتذييل، وروابط JS (بما في ذلك `script.js` وملف JS مخصص لـ Laravel إذا لزم الأمر، مثل `dash_app.js`).
        *   `customer_layout.blade.php`: مشابه لـ `admin_layout` ولكن قد يحتوي على قائمة جانبية وعناصر شريط علوي مختلفة، أو ألوان مميزة.
    3.  **مكونات Blade للقائمة الجانبية (`_sidebar.blade.php`):**
        *   سيتم توليد عناصر القائمة ديناميكيًا باستخدام `spatie/laravel-permission`. سيتحكم `Controller` (أو `View Composer`) في البيانات المرسلة للقائمة بناءً على دور المستخدم.
        *   ستستخدم شروط `@can` أو `@hasrole` لعرض/إخفاء عناصر القائمة.
        *   سيتم الحفاظ على دعم القوائم الفرعية المتداخلة وآلية الفتح/الإغلاق من `script.js`.
        *   سيتم التعامل مع حالة `mini-mode` و `hover-expand` عبر CSS classes وتفاعلات JS المعدلة.
    4.  **مكونات Blade للشريط العلوي (`_topbar.blade.php`):**
        *   سيعرض اسم المستخدم المسجل (من `Auth::user()`).
        *   سيعرض أيقونة الإشعارات مع عداد ديناميكي (يتم جلبه من `MOD-NOTIFICATION-FEAT-002`).
        *   سيتضمن قائمة منسدلة للملف الشخصي وتسجيل الخروج.
    5.  **تكييف `script.js`:**
        *   **الرسوم البيانية:** سيتم تعديل أجزاء تهيئة الرسوم البيانية (Chart.js) في `script.js` لتلقي البيانات الديناميكية من Laravel Controllers. سيتم تمرير البيانات كمتغيرات JSON إلى Blade view ثم إلى JS. (كما هو محدد في `MOD-DASHBOARD-FEAT-002`).
        *   **Stepper (`bs-stepper.min.js`):** سيتم استخدامه كما هو في `dashboard.html` ضمن Blade view لنموذج إضافة/تعديل السيارة. سيتم تقديم النموذج بالكامل إلى Laravel Controller عند الانتهاء.
        *   **وظائف القائمة الجانبية:** سيتم مراجعة وظائف `script.js` المتعلقة بالقائمة الجانبية (فتح/إغلاق القوائم الفرعية، تبديل وضع mini-mode، hover-expand) لضمان عملها بشكل صحيح مع الروابط الديناميكية التي يولدها Laravel (باستخدام `route()` helper) وحالات النشاط (active state) التي يتم تحديدها في Blade بناءً على المسار الحالي. قد يتطلب ذلك تعديل بعض محددات JS لتتوافق مع بنية HTML التي يولدها Blade.
        *   **تفاعلات أخرى:** أي تفاعلات JS أخرى في `script.js` (مثل التعامل مع dropdowns، modals، tabs إذا كانت مستخدمة في `dashboard.html` وتم نقلها إلى Blade) سيتم مراجعتها وتكييفها لتعمل ضمن سياق Laravel Blade ومع البيانات الديناميكية.
    6.  **إنشاء طرق عرض Blade للصفحات الداخلية:**
        *   كل صفحة في لوحة التحكم (مثل عرض جداول، نماذج CRUD، صفحات الإعدادات) ستكون ملف Blade view يمتد من `admin_layout.blade.php` أو `customer_layout.blade.php`.
        *   ستستخدم هذه الـ Views مكونات Blade لـ tables, forms, cards لضمان الاتساق.
        *   سيتم استخدام `Laravel Collective/HTML` أو مكونات Blade مخصصة لتوليد النماذج والجداول بشكل أسهل.
    7.  **Controllers (ضمن موديولات `nwidart/laravel-modules`):**
        *   ستقوم الـ Controllers بإعداد البيانات اللازمة (من قواعد البيانات عبر Services/Repositories) وتمريرها إلى Blade views.
        *   ستتعامل مع تقديم النماذج (Form Submissions)، التحقق من الصحة (Validation)، وتنفيذ الإجراءات.
    *   **تفاعلات البيانات:** ستكون بشكل أساسي عبر Laravel Controllers التي تجلب البيانات من Models/Services وتمررها إلى Blade views. أي تحديثات AJAX داخل لوحة التحكم (إذا كانت ضرورية) ستستخدم نقاط نهاية API داخلية مخصصة (غير تلك الموجهة لـ Flutter).
    *   **نقاط التكامل:** `MOD-USER-MGMT` (للمصادقة وصلاحيات القائمة)، `MOD-CAR-CATALOG` (لعرض وإدارة السيارات)، `MOD-ORDER-MGMT` (للطلبات)، إلخ.

#### **3.2. عملية إضافة/تعديل سيارة عبر Stepper في لوحة التحكم Dash (`TECH-CMPLX-CAR-CRUD-001`)**
*   **الوصف:** هذه العملية محورية لإدارة كتالوج السيارات (`MOD-CAR-CATALOG-FEAT-018`) وتستخدم واجهة Stepper المدمجة من أصول Dash.
*   **التدفق المنطقي والمكونات الرئيسية:**
    1.  **الوصول:** المسؤول ينقر على "إضافة سيارة" أو "تعديل سيارة" في لوحة التحكم.
    2.  **عرض النموذج (Controller & View):**
        *   الـ Controller المسؤول (مثال: `CarController` في موديول `CarCatalog`) يستدعي `create` أو `edit` method.
        *   يقوم الـ Controller بتجهيز البيانات اللازمة للـ Stepper (قوائم الماركات، الموديلات، الألوان، السنوات، أنواع ناقل الحركة، أنواع الوقود، الميزات المتاحة، إلخ) من خلال استدعاء Services/Repositories من موديول `CarCatalog` أو `Core`.
        *   يتم تمرير هذه البيانات إلى Blade view (مثال: `add_edit_car.blade.php`) الذي يحتوي على هيكل Stepper (المكون من 5 خطوات كما في `dashboard.html` و `REQ-FR.md`).
        *   إذا كان تعديلاً، يتم ملء حقول النموذج ببيانات السيارة الحالية.
    3.  **التنقل بين خطوات Stepper (Client-Side JS & Blade):**
        *   مكتبة `bs-stepper.min.js` (من أصول Dash) ستدير التنقل بين الخطوات في الواجهة الأمامية.
        *   يمكن إجراء تحقق أساسي من صحة البيانات في الواجهة الأمامية باستخدام JS لكل خطوة، ولكن التحقق الرئيسي سيكون في الـ Backend.
        *   لا يتم حفظ البيانات في الـ Backend عند التنقل بين الخطوات، بل يتم تقديم النموذج بالكامل في النهاية.
    4.  **تقديم النموذج (Submission to Controller):**
        *   عند الضغط على زر "حفظ السيارة" في الخطوة الأخيرة، يتم تقديم النموذج بالكامل (عبر `POST` أو `PUT`) إلى `store` أو `update` method في `CarController`.
    5.  **المعالجة في الـ Backend (Controller, FormRequest, Service/Repository):**
        *   **التحقق من الصحة (Validation):** يتم استخدام Laravel FormRequest مخصص (مثال: `StoreCarRequest.php`, `UpdateCarRequest.php`) للتحقق من صحة جميع المدخلات من جميع خطوات Stepper بناءً على القواعد المحددة في `MOD-CAR-CATALOG-FEAT-018`.
        *   **معالجة البيانات:**
            *   إذا نجح التحقق، يقوم الـ Controller باستدعاء Service (مثال: `CarManagementService`) أو Repository.
            *   يقوم الـ Service بإنشاء أو تحديث سجل السيارة في جدول `cars`.
            *   يتم التعامل مع العلاقات (ربط الماركة، الموديل، إلخ).
            *   يتم ربط الميزات المختارة (من جدول `car_features` عبر جدول `car_car_feature`).
            *   يتم التعامل مع رفع الصور وتخزينها وربطها بسجل السيارة باستخدام `spatie/laravel-medialibrary` (مع تحديد `collection_name` لكل نوع من الصور إذا لزم الأمر، مثل `main_image`, `gallery_images`).
            *   يتم حساب السعر الإجمالي شامل الضريبة وتخزينه.
    6.  **الاستجابة (Controller & View):**
        *   في حالة النجاح، يتم توجيه المستخدم إلى قائمة السيارات أو صفحة تفاصيل السيارة المضافة/المعدلة في لوحة التحكم Dash مع رسالة نجاح (session flash message).
        *   في حالة فشل التحقق، يتم إعادة توجيه المستخدم إلى نموذج Stepper مع عرض رسائل الخطأ بجانب الحقول المعنية (يتم التعامل مع ذلك تلقائيًا بواسطة Laravel).
*   **تفاعلات البيانات:** استخدام Eloquent ORM للتفاعل مع قاعدة البيانات. `spatie/laravel-medialibrary` لإدارة الملفات.
*   **نقاط التكامل:** `MOD-CAR-CATALOG` (لإدارة البيانات الوصفية للسيارات)، `MOD-CORE` (لإعدادات الضريبة)، `MOD-DASHBOARD` (لتوفير واجهة Stepper).

#### **3.3. نظام الإشعارات متعدد القنوات (`TECH-CMPLX-NOTIF-001`)**
*   **الوصف:** بناء نظام إشعارات مرن (يخدم `MOD-NOTIFICATION-FEAT-001`) يدعم البريد الإلكتروني، الإشعارات داخل النظام (database channel)، و SMS (إذا تم تفعيلها).
*   **التدفق المنطقي والمكونات الرئيسية:**
    1.  **إنشاء Laravel Notification Classes:** لكل نوع إشعار (ترحيب، تأكيد طلب، تحديث حالة، إلخ)، سيتم إنشاء فئة Notification مخصصة (e.g., `NewUserWelcome.php`, `OrderConfirmed.php`) ترث من `Illuminate\Notifications\Notification` وتستخدم `ShouldQueue` للمعالجة في الخلفية.
    2.  **تحديد القنوات (Channels):** داخل كل فئة Notification، سيحدد `via($notifiable)` method القنوات التي سيتم إرسال الإشعار عبرها (e.g., `['mail', 'database']` أو `['mail', 'database', 'sms']`).
    3.  **تكوين رسائل القنوات:**
        *   **البريد الإلكتروني (`toMail`):** ستُرجع هذه الدالة Mailable (e.g., `NewUserWelcomeMail.php`) يحتوي على منطق بناء رسالة البريد (الموضوع، المحتوى من Blade view، أي مرفقات). سيتم استخدام قوالب Blade لتصميم رسائل البريد الإلكتروني بشكل احترافي.
        *   **الإشعارات داخل النظام (`toDatabase` أو `toArray` إذا كان `database` channel هو الافتراضي):** ستُرجع هذه الدالة مصفوفة بالبيانات التي سيتم تخزينها في عمود `data` (JSON) في جدول `notifications` (مثل عنوان الإشعار، النص، الرابط المستهدف، أيقونة).
        *   **SMS (`toVonage`/`toTwilio` أو ما يعادلها لحزمة SMS المختارة):** ستُرجع هذه الدالة محتوى رسالة SMS. يجب التكامل مع بوابة SMS (مفاتيح API في إعدادات النظام `MOD-CORE-FEAT-003`).
    4.  **إرسال الإشعارات:** سيتم إرسال الإشعارات من الموديولات المختلفة باستخدام `$user->notify(new NotificationClass($data));`.
    5.  **قوالب الإشعارات القابلة للتعديل (ميزة متقدمة):**
        *   إذا كانت قوالب البريد الإلكتروني قابلة للتعديل من لوحة التحكم Dash، فيمكن تخزين محتوى القوالب (HTML/Blade) في قاعدة البيانات (ربما في جدول `email_templates` ضمن `MOD-CMS` أو `MOD-NOTIFICATION`).
        *   عند بناء Mailable، يتم جلب القالب من قاعدة البيانات بدلاً من ملف Blade ثابت، مع تمرير المتغيرات إليه.
    6.  **عرض الإشعارات في لوحة التحكم Dash:** (يخدم `MOD-NOTIFICATION-FEAT-002`)
        *   سيقوم Controller (أو View Composer) بجلب إشعارات المستخدم المسجل (`$user->notifications()`, `$user->unreadNotifications()`) من جدول `notifications`.
        *   سيتم عرضها في الشريط العلوي وفي صفحة الإشعارات المخصصة كما هو موضح في `REQ-FR.md`.
        *   سيتم استخدام AJAX لتعليم الإشعارات كمقروءة دون إعادة تحميل الصفحة.
*   **تفاعلات البيانات:** Laravel Notifications system, Eloquent, Queues.
*   **نقاط التكامل:** جميع الموديولات التي تحتاج لإرسال إشعارات، `MOD-CORE` (لإعدادات بوابات SMS وقوالب البريد)، `MOD-DASHBOARD` (لعرض الإشعارات).

---

### 4. استراتيجية التعامل مع الأخطاء وتسجيلها

*   **تسجيل الأخطاء (Logging):**
    *   **Laravel Backend:** سيتم استخدام نظام Laravel Logging المدمج بشكل كامل. سيتم تكوين قنوات تسجيل متعددة (e.g., `daily` files, `stack` channel to log to multiple places). سيتم تسجيل جميع الاستثناءات غير المعالجة تلقائيًا. سيتم تسجيل أخطاء التحقق من الصحة (Validation errors) عند الحاجة (خاصة للـ API). سيتم إضافة تسجيلات مخصصة (`Log::info()`, `Log::warning()`, `Log::error()`) في الأجزاء الحرجة من الكود لتتبع التدفق وتشخيص المشاكل. سيتم تضمين سياق كافٍ في السجلات (معرف المستخدم، معرف الطلب، إلخ).
    *   **Flutter Application:** سيتم استخدام حزم تسجيل مناسبة (مثل `logger`) لتسجيل الأخطاء المهمة، تفاعلات API، والأحداث غير المتوقعة. يمكن إرسال السجلات الحرجة إلى خدمة تجميع سجلات مركزية (مثل Sentry, Firebase Crashlytics) إذا كانت ضمن النطاق.
*   **معالجة الاستثناءات (Exception Handling) في الـ Backend:**
    *   سيتم الاعتماد على معالج الاستثناءات العام في Laravel (`App\Exceptions\Handler.php`).
    *   سيتم تخصيص `render` method في `Handler.php` لإرجاع استجابات JSON موحدة ومنسقة بشكل جيد لنقاط نهاية API عندما يحدث استثناء (e.g., إرجاع رمز HTTP 4xx أو 5xx مناسب مع رسالة خطأ واضحة بتنسيق JSend).
    *   سيتم إنشاء فئات استثناء مخصصة (Custom Exception Classes) عند الحاجة لتمثيل حالات خطأ محددة في منطق الأعمال (e.g., `PaymentFailedException`, `CarNotAvailableException`).
    *   سيتم استخدام `try-catch` blocks في الـ Services والـ Controllers للتعامل مع الاستثناءات المتوقعة بشكل رشيق.
*   **عرض رسائل الأخطاء في الواجهات الأمامية:**
    *   **لوحة تحكم Dash المخصصة (Blade) وواجهة الموقع العامة (Blade):**
        *   أخطاء التحقق من صحة النماذج: سيتم عرضها تلقائيًا بواسطة Laravel بجانب الحقول المعنية (باستخدام متغير `$errors` في Blade).
        *   رسائل الخطأ العامة (الناتجة عن استثناءات غير متوقعة أو إجراءات فاشلة): سيتم عرضها باستخدام session flash messages (e.g., `with('error', 'message')` في الـ Controller وعرضها في Blade).
        *   يجب أن تكون الرسائل واضحة للمستخدم وموجهة باللغة العربية.
    *   **تطبيق Flutter:**
        *   سيقوم التطبيق بمعالجة استجابات API التي تشير إلى خطأ (رموز HTTP 4xx, 5xx، أو `status: "fail"` / `status: "error"` في JSON).
        *   سيتم عرض رسائل خطأ مناسبة للمستخدم (e.g., في snackbars, dialogs, أو تحت حقول النموذج).
        *   سيتم التعامل مع أخطاء الشبكة (network errors) بشكل منفصل وعرض رسائل مناسبة.

---

### 5. اعتبارات الأمان التقنية

سيتم تطبيق الإجراءات التقنية التالية لضمان أمان النظام والمخرجات النهائية:

1.  **HTTPS الإلزامي:** سيتم فرض استخدام HTTPS (TLS 1.2 أو أعلى) لجميع الاتصالات. سيتم تكوين HSTS (HTTP Strict Transport Security) لإجبار المتصفحات على استخدام HTTPS فقط.
2.  **حماية ضد هجمات الويب الشائعة (OWASP Top 10):**
    *   **SQL Injection:** استخدام Eloquent ORM و Query Builder في Laravel، وتجنب الاستعلامات الخام (Raw SQL queries) قدر الإمكان.
    *   **Cross-Site Scripting (XSS):** استخدام Blade templating engine في Laravel الذي يقوم بعمل escape للمخرجات افتراضيًا. سيتم تطهير أي مدخلات مستخدم ستُعرض كـ HTML (وهو أمر يجب تجنبه قدر الإمكان). تطبيق Content Security Policy (CSP) header.
    *   **Cross-Site Request Forgery (CSRF):** استخدام حماية CSRF المدمجة في Laravel لجميع نماذج الويب (Blade forms).
    *   **Broken Authentication:** تطبيق سياسات كلمة مرور قوية، مصادقة متعددة العوامل (MFA) للمديرين (إذا كانت ضمن النطاق - `NFR-SEC-005` تتطلب سياسات قوية، MFA اختيارية ما لم يُذكر)، تأمين جلسات العمل، استخدام Laravel Sanctum لـ API tokens مع انتهاء صلاحية مناسب.
    *   **Security Misconfiguration:** مراجعة تكوينات الخادم و Laravel بانتظام، إزالة الميزات غير الضرورية، الحفاظ على تحديث جميع البرمجيات.
    *   **Sensitive Data Exposure:** تشفير كلمات المرور (Argon2id/bcrypt)، تجنب تخزين بيانات حساسة غير ضرورية، استخدام HTTPS، تقييد الوصول للبيانات الحساسة.
3.  **إدارة الصلاحيات والتحكم في الوصول:**
    *   استخدام حزمة `spatie/laravel-permission` لتحديد الأدوار والصلاحيات للموظفين والمديرين.
    *   سيتم تطبيق التحقق من الصلاحيات في الـ Backend (Middleware, Gates, Policies) لجميع الإجراءات والموارد، وعدم الاعتماد على الواجهة الأمامية فقط.
4.  **تأمين API:**
    *   استخدام Laravel Sanctum لتأمين نقاط نهاية API المستخدمة من قبل تطبيق Flutter (API tokens).
    *   تطبيق Rate Limiting على نقاط نهاية API لمنع الاستغلال.
    *   التحقق من صحة جميع المدخلات إلى API بشكل صارم.
    *   تكوين CORS (Cross-Origin Resource Sharing) بشكل صحيح للسماح بالوصول من تطبيق Flutter فقط (إذا كان مستضافًا على نطاق مختلف) أو واجهات أخرى مصرح بها.
5.  **تأمين رفع الملفات:**
    *   استخدام `spatie/laravel-medialibrary` التي تساعد في إدارة الملفات.
    *   التحقق من صحة نوع الملف (MIME type) وحجمه في الـ Backend.
    *   تخزين الملفات المرفوعة في مسار غير قابل للوصول مباشرة عبر الويب (e.g., `storage/app`) وتقديمها عبر Laravel route إذا كان التحكم في الوصول مطلوبًا.
    *   إعادة تسمية الملفات عند التخزين لتجنب التعارضات أو تنفيذ تعليمات برمجية.
    *   فحص الفيروسات للملفات المرفوعة إذا توفرت الإمكانية.
6.  **تحديث الحزم وإدارة التبعيات:**
    *   سيتم استخدام Composer لإدارة تبعيات PHP/Laravel. سيتم مراقبة التحديثات الأمنية للحزم بانتظام (e.g., `composer outdated -D`).
    *   سيتم استخدام npm/yarn لإدارة تبعيات JavaScript. سيتم مراقبة التحديثات الأمنية.
7.  **HTTP Security Headers:**
    *   سيتم تكوين Headers أمان إضافية مثل: `X-Frame-Options`, `X-Content-Type-Options`, `Referrer-Policy`, `Permissions-Policy`.
8.  **تأمين تطبيقات Blade المخصصة من Dash:**
    *   بما أن لوحة التحكم Dash مبنية باستخدام Blade، فإنها تستفيد من حماية Laravel (CSRF, XSS escaping).
    *   سيتم مراجعة أي JavaScript مخصص يتم إضافته أو تعديله من `script.js` الأصلي للتأكد من عدم إدخال ثغرات XSS.
    *   سيتم التحقق من صحة جميع المدخلات من النماذج في لوحة التحكم Dash في الـ Backend.
9.  **تشفير البيانات الحساسة:**
    *   بالإضافة لكلمات المرور، إذا كانت هناك أي بيانات أخرى تعتبر حساسة جدًا ويجب تشفيرها في قاعدة البيانات (at-rest)، سيتم استخدام ميزات التشفير في Laravel (`Crypt` facade).
10. **تسجيل ومراقبة الأمان:**
    *   تسجيل محاولات تسجيل الدخول الفاشلة، أحداث الأمان الهامة (مثل تغيير كلمة المرور، تغيير الصلاحيات).
    *   مراجعة السجلات بانتظام.
11. **التحقق من صحة البيانات على مستوى الخادم (Server-Side Validation):** التأكيد على أن جميع البيانات القادمة من أي واجهة مستخدم (Blade forms من Dash، الموقع العام، أو API لـ Flutter) يجب أن تخضع لتحقق صارم من الصحة في الـ Backend باستخدام Laravel Validation (Form Requests) قبل معالجتها أو تخزينها، حتى لو كان هناك تحقق في الواجهة الأمامية.

---
*(نهاية مستند المواصفات التقنية `TS-FR.md`)*