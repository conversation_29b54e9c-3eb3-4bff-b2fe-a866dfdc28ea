<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class UpdateCarRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user()->can('manage_cars_admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $carId = $this->route('car')->id ?? $this->route('car');

        // DEBUG: فحص الملفات في UpdateCarRequest
        Log::info('=== UPDATE CAR REQUEST VALIDATION ===');
        Log::info('Request method: ' . $this->method());
        Log::info('Car ID: ' . $carId);
        Log::info('Request has files: ' . ($this->hasFile('car_images') ? 'Yes' : 'No'));

        if ($this->hasFile('car_images')) {
            $files = $this->file('car_images');
            Log::info('Car images in UpdateFormRequest: ' . (is_array($files) ? count($files) : 'not array'));

            if (is_array($files)) {
                foreach ($files as $key => $file) {
                    if ($file) {
                        Log::info("UpdateFormRequest File {$key}: " . $file->getClientOriginalName() .
                                 ' (' . $file->getMimeType() . ', ' . $file->getSize() . ' bytes)');
                    }
                }
            }
        }

        if ($this->has('delete_images')) {
            Log::info('Delete images in FormRequest: ', $this->input('delete_images'));
        }

        Log::info('All request files: ', $this->allFiles());
        Log::info('=== END UPDATE CAR REQUEST VALIDATION ===');

        return [
            // البيانات الأساسية - الخطوة 1
            'title'                 => 'required|string|max:200',
            'description'           => 'nullable|string',
            'brand_id'              => 'required|exists:brands,id',
            'car_model_id'          => 'required|exists:car_models,id',
            'manufacturing_year_id' => 'required|exists:manufacturing_years,id',
            'main_color_id'         => 'required|exists:colors,id',
            'interior_color_id'     => 'nullable|exists:colors,id',
            'vin'                   => [
                'nullable',
                'string',
                'max:17',
                Rule::unique('cars', 'vin')->ignore($carId),
            ],
            'plate_number' => 'nullable|string|max:20',

            // المواصفات الفنية - الخطوة 2
            'body_type_id'         => 'required|exists:body_types,id',
            'transmission_type_id' => 'required|exists:transmission_types,id',
            'fuel_type_id'         => 'required|exists:fuel_types,id',
            'engine_capacity'      => 'nullable|integer|min:100|max:8000',
            'doors_count'          => 'nullable|integer|min:2|max:6',
            'seats_count'          => 'nullable|integer|min:2|max:12',
            'mileage'              => 'nullable|integer|min:0',
            'mileage_unit'         => 'nullable|in:km,miles',
            'condition'            => 'required|in:new,used,certified_pre_owned',

            // الميزات - الخطوة 3
            'features'   => 'nullable|array',
            'features.*' => 'exists:car_features,id',

            // الصور والفيديو - الخطوة 4
            'car_images'       => 'nullable|array|max:10',
            'car_images.*'     => 'image|mimes:jpeg,png,jpg,webp|max:5120', // 5MB max
            'main_image_index' => 'nullable|integer|min:0',
            'video_url'        => 'nullable|url',
            'delete_images'    => 'nullable|array',
            'delete_images.*'  => 'integer|exists:media,id',

            // السعر والحالة - الخطوة 5
            'price'            => 'required|numeric|min:0',
            'currency'         => 'required|in:SAR,USD,EUR',
            'offer_price'      => 'nullable|numeric|min:0|lt:price',
            'offer_start_date' => 'nullable|date|after_or_equal:today',
            'offer_end_date'   => 'nullable|date|after:offer_start_date',
            'is_featured'      => 'boolean',
            'is_sold'          => 'boolean',
            'is_active'        => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'title.required'                  => 'عنوان السيارة مطلوب',
            'title.max'                       => 'عنوان السيارة يجب ألا يتجاوز 200 حرف',
            'brand_id.required'               => 'الماركة مطلوبة',
            'brand_id.exists'                 => 'الماركة المختارة غير صحيحة',
            'car_model_id.required'           => 'الموديل مطلوب',
            'car_model_id.exists'             => 'الموديل المختار غير صحيح',
            'manufacturing_year_id.required'  => 'سنة الصنع مطلوبة',
            'manufacturing_year_id.exists'    => 'سنة الصنع المختارة غير صحيحة',
            'main_color_id.required'          => 'اللون الرئيسي مطلوب',
            'main_color_id.exists'            => 'اللون المختار غير صحيح',
            'interior_color_id.exists'        => 'لون المقصورة الداخلية المختار غير صحيح',
            'vin.unique'                      => 'رقم الهيكل (VIN) مستخدم من قبل',
            'vin.max'                         => 'رقم الهيكل يجب ألا يتجاوز 17 حرف',
            'body_type_id.required'           => 'نوع الهيكل مطلوب',
            'body_type_id.exists'             => 'نوع الهيكل المختار غير صحيح',
            'transmission_type_id.required'   => 'نوع ناقل الحركة مطلوب',
            'transmission_type_id.exists'     => 'نوع ناقل الحركة المختار غير صحيح',
            'fuel_type_id.required'           => 'نوع الوقود مطلوب',
            'fuel_type_id.exists'             => 'نوع الوقود المختار غير صحيح',
            'engine_capacity.integer'         => 'سعة المحرك يجب أن تكون رقم صحيح',
            'engine_capacity.min'             => 'سعة المحرك يجب أن تكون أكبر من 100 سي سي',
            'engine_capacity.max'             => 'سعة المحرك يجب أن تكون أقل من 8000 سي سي',
            'doors_count.integer'             => 'عدد الأبواب يجب أن يكون رقم صحيح',
            'doors_count.min'                 => 'عدد الأبواب يجب أن يكون على الأقل 2',
            'doors_count.max'                 => 'عدد الأبواب يجب أن يكون أقل من 6',
            'seats_count.integer'             => 'عدد المقاعد يجب أن يكون رقم صحيح',
            'seats_count.min'                 => 'عدد المقاعد يجب أن يكون على الأقل 2',
            'seats_count.max'                 => 'عدد المقاعد يجب أن يكون أقل من 12',
            'mileage.integer'                 => 'المسافة المقطوعة يجب أن تكون رقم صحيح',
            'mileage.min'                     => 'المسافة المقطوعة يجب أن تكون أكبر من أو تساوي 0',
            'mileage_unit.in'                 => 'وحدة المسافة يجب أن تكون كيلومتر أو ميل',
            'condition.required'              => 'حالة السيارة مطلوبة',
            'condition.in'                    => 'حالة السيارة يجب أن تكون جديدة أو مستعملة أو معتمدة',
            'features.array'                  => 'الميزات يجب أن تكون مصفوفة',
            'features.*.exists'               => 'إحدى الميزات المختارة غير صحيحة',
            'car_images.array'                => 'الصور يجب أن تكون مصفوفة',
            'car_images.max'                  => 'لا يمكن رفع أكثر من 10 صور',
            'car_images.*.image'              => 'الملف يجب أن يكون صورة',
            'car_images.*.mimes'              => 'الصورة يجب أن تكون من نوع: jpeg, png, jpg, webp',
            'car_images.*.max'                => 'حجم الصورة يجب ألا يتجاوز 5 ميجابايت',
            'main_image_index.integer'        => 'فهرس الصورة الرئيسية يجب أن يكون رقم صحيح',
            'main_image_index.min'            => 'فهرس الصورة الرئيسية يجب أن يكون أكبر من أو يساوي 0',
            'video_url.url'                   => 'رابط الفيديو يجب أن يكون رابط صحيح',
            'delete_images.array'             => 'قائمة الصور المحذوفة يجب أن تكون مصفوفة',
            'delete_images.*.integer'         => 'معرف الصورة المحذوفة يجب أن يكون رقم صحيح',
            'delete_images.*.exists'          => 'إحدى الصور المحذوفة غير موجودة',
            'price.required'                  => 'السعر مطلوب',
            'price.numeric'                   => 'السعر يجب أن يكون رقم',
            'price.min'                       => 'السعر يجب أن يكون أكبر من أو يساوي 0',
            'currency.required'               => 'العملة مطلوبة',
            'currency.in'                     => 'العملة يجب أن تكون ريال سعودي أو دولار أمريكي أو يورو',
            'offer_price.numeric'             => 'سعر العرض يجب أن يكون رقم',
            'offer_price.min'                 => 'سعر العرض يجب أن يكون أكبر من أو يساوي 0',
            'offer_price.lt'                  => 'سعر العرض يجب أن يكون أقل من السعر الأساسي',
            'offer_start_date.date'           => 'تاريخ بداية العرض يجب أن يكون تاريخ صحيح',
            'offer_start_date.after_or_equal' => 'تاريخ بداية العرض يجب أن يكون اليوم أو بعده',
            'offer_end_date.date'             => 'تاريخ نهاية العرض يجب أن يكون تاريخ صحيح',
            'offer_end_date.after'            => 'تاريخ نهاية العرض يجب أن يكون بعد تاريخ البداية',
            'is_featured.boolean'             => 'حقل السيارة المميزة يجب أن يكون صحيح أو خطأ',
            'is_sold.boolean'                 => 'حقل السيارة المباعة يجب أن يكون صحيح أو خطأ',
            'is_active.boolean'               => 'حقل السيارة النشطة يجب أن يكون صحيح أو خطأ',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'title'                 => 'عنوان السيارة',
            'description'           => 'وصف السيارة',
            'brand_id'              => 'الماركة',
            'car_model_id'          => 'الموديل',
            'manufacturing_year_id' => 'سنة الصنع',
            'main_color_id'         => 'اللون الرئيسي',
            'interior_color_id'     => 'لون المقصورة الداخلية',
            'vin'                   => 'رقم الهيكل (VIN)',
            'plate_number'          => 'رقم اللوحة',
            'body_type_id'          => 'نوع الهيكل',
            'transmission_type_id'  => 'نوع ناقل الحركة',
            'fuel_type_id'          => 'نوع الوقود',
            'engine_capacity'       => 'سعة المحرك',
            'doors_count'           => 'عدد الأبواب',
            'seats_count'           => 'عدد المقاعد',
            'mileage'               => 'المسافة المقطوعة',
            'mileage_unit'          => 'وحدة المسافة',
            'condition'             => 'حالة السيارة',
            'features'              => 'الميزات',
            'car_images'            => 'صور السيارة',
            'main_image_index'      => 'فهرس الصورة الرئيسية',
            'video_url'             => 'رابط الفيديو',
            'delete_images'         => 'الصور المحذوفة',
            'price'                 => 'السعر',
            'currency'              => 'العملة',
            'offer_price'           => 'سعر العرض',
            'offer_start_date'      => 'تاريخ بداية العرض',
            'offer_end_date'        => 'تاريخ نهاية العرض',
            'is_featured'           => 'سيارة مميزة',
            'is_sold'               => 'سيارة مباعة',
            'is_active'             => 'سيارة نشطة',
        ];
    }
}
