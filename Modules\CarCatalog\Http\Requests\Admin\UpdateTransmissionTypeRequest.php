<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تحديث نوع ناقل حركة موجود.
 *
 * يتحقق هذا الطلب من صحة بيانات تعديل نوع ناقل حركة موجود
 */
class UpdateTransmissionTypeRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:50',
                Rule::unique('transmission_types')->ignore($this->transmissiontype->id),
            ],
            'description' => 'nullable|string|max:500',
            'status'      => 'required|boolean',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة للقواعد المحددة.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required'      => 'اسم نوع ناقل الحركة مطلوب',
            'name.string'        => 'اسم نوع ناقل الحركة يجب أن يكون نصًا',
            'name.max'           => 'اسم نوع ناقل الحركة يجب ألا يتجاوز 50 حرف',
            'name.unique'        => 'اسم نوع ناقل الحركة موجود بالفعل',
            'description.string' => 'الوصف يجب أن يكون نصًا',
            'description.max'    => 'الوصف يجب ألا يتجاوز 500 حرف',
            'status.required'    => 'حالة نوع ناقل الحركة مطلوبة',
            'status.boolean'     => 'حالة نوع ناقل الحركة يجب أن تكون صحيحة أو خاطئة',
        ];
    }

    /**
     * الحصول على أسماء الخصائص المخصصة للتحقق
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name'        => 'اسم نوع ناقل الحركة',
            'description' => 'الوصف',
            'status'      => 'الحالة',
        ];
    }
}
