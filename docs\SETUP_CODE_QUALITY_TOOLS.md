# دليل إعداد أدوات جودة الكود

## 🎯 نظرة عامة

يوضح هذا الدليل كيفية إعداد واستخدام أدوات جودة الكود في مشروع MotorLine.

## 🔧 الأدوات المطلوبة

### 1. **PHP CS Fixer**
أداة لتنسيق الكود وفقاً لمعايير PSR-12.

#### التثبيت:
```bash
composer require --dev friendsofphp/php-cs-fixer
```

#### الاستخدام:
```bash
# فحص التنسيق فقط
./vendor/bin/php-cs-fixer fix --dry-run --diff

# تطبيق التنسيق
./vendor/bin/php-cs-fixer fix --allow-risky=yes

# فحص ملفات محددة
./vendor/bin/php-cs-fixer fix Modules/CarCatalog --dry-run --diff
```

### 2. **سكريبت فحص التوثيق**
سكريبت مخصص لفحص جودة التوثيق.

#### الاستخدام:
```bash
php scripts/check_documentation_quality.php
```

### 3. **Pre-commit Hook**
سكريبت يتم تشغيله تلقائياً قبل كل commit.

#### التثبيت:
```bash
# نسخ السكريبت إلى مجلد Git hooks
cp scripts/pre-commit-hook.sh .git/hooks/pre-commit

# جعل السكريبت قابل للتنفيذ
chmod +x .git/hooks/pre-commit
```

## 📋 إعداد البيئة

### 1. **إعداد PHP CS Fixer**

إنشاء ملف `.php-cs-fixer.php` في جذر المشروع:

```php
<?php

$finder = PhpCsFixer\Finder::create()
    ->in([
        __DIR__ . '/app',
        __DIR__ . '/Modules',
        __DIR__ . '/config',
        __DIR__ . '/database',
        __DIR__ . '/routes',
        __DIR__ . '/tests',
    ])
    ->name('*.php')
    ->notName('*.blade.php')
    ->exclude([
        'bootstrap',
        'storage',
        'vendor',
        'node_modules',
    ]);

$config = new PhpCsFixer\Config();

return $config
    ->setRules([
        '@PSR12' => true,
        'array_syntax' => ['syntax' => 'short'],
        // المزيد من القواعد...
    ])
    ->setFinder($finder);
```

### 2. **إعداد Git Hooks**

```bash
# إنشاء مجلد hooks إذا لم يكن موجوداً
mkdir -p .git/hooks

# نسخ pre-commit hook
cp scripts/pre-commit-hook.sh .git/hooks/pre-commit

# جعله قابل للتنفيذ
chmod +x .git/hooks/pre-commit
```

### 3. **إعداد IDE/Editor**

#### VS Code:
إضافة الإعدادات التالية إلى `.vscode/settings.json`:

```json
{
    "php.validate.executablePath": "/path/to/php",
    "php-cs-fixer.executablePath": "./vendor/bin/php-cs-fixer",
    "php-cs-fixer.onsave": true,
    "editor.formatOnSave": true,
    "files.associations": {
        "*.php": "php"
    }
}
```

#### PhpStorm:
1. اذهب إلى `Settings > Tools > External Tools`
2. أضف أداة جديدة لـ PHP CS Fixer
3. اضبط المسار: `$ProjectFileDir$/vendor/bin/php-cs-fixer`
4. اضبط المعاملات: `fix $FileDir$ --allow-risky=yes`

## 🚀 سير العمل اليومي

### 1. **قبل البدء في العمل**
```bash
# تحديث الأدوات
composer update --dev

# فحص حالة التوثيق
php scripts/check_documentation_quality.php
```

### 2. **أثناء التطوير**
```bash
# فحص التنسيق للملفات المعدلة
./vendor/bin/php-cs-fixer fix --dry-run --diff

# إصلاح التنسيق
./vendor/bin/php-cs-fixer fix --allow-risky=yes
```

### 3. **قبل الـ Commit**
```bash
# فحص شامل (يتم تلقائياً مع pre-commit hook)
scripts/pre-commit-hook.sh

# أو يدوياً:
php -l file.php  # فحص الأخطاء النحوية
./vendor/bin/php-cs-fixer fix --dry-run --diff  # فحص التنسيق
php scripts/check_documentation_quality.php  # فحص التوثيق
```

## 📊 مراقبة الجودة

### 1. **تقارير دورية**
```bash
# تقرير جودة التوثيق
php scripts/check_documentation_quality.php > reports/documentation_$(date +%Y%m%d).txt

# تقرير مشاكل التنسيق
./vendor/bin/php-cs-fixer fix --dry-run --diff > reports/formatting_$(date +%Y%m%d).txt
```

### 2. **مؤشرات الجودة**
- **التوثيق**: هدف 95%، حالياً 90%
- **التنسيق**: هدف 100%، حالياً 100%
- **التغطية**: هدف 80%، قيد التطوير
- **التعقيد**: هدف < 10، قيد المراجعة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. **PHP CS Fixer لا يعمل**
```bash
# التحقق من التثبيت
composer show friendsofphp/php-cs-fixer

# إعادة التثبيت
composer remove --dev friendsofphp/php-cs-fixer
composer require --dev friendsofphp/php-cs-fixer
```

#### 2. **Pre-commit Hook لا يعمل**
```bash
# التحقق من الصلاحيات
ls -la .git/hooks/pre-commit

# إعادة تعيين الصلاحيات
chmod +x .git/hooks/pre-commit

# اختبار السكريبت
.git/hooks/pre-commit
```

#### 3. **مشاكل في التوثيق**
```bash
# فحص مفصل
php scripts/check_documentation_quality.php

# فحص ملف محدد
php -l Modules/CarCatalog/Models/Car.php
```

## 📚 مراجع إضافية

### الوثائق الرسمية:
- [PHP CS Fixer Documentation](https://cs.symfony.com/)
- [PSR-12 Standard](https://www.php-fig.org/psr/psr-12/)
- [PHPDoc Standards](https://docs.phpdoc.org/)

### أدوات إضافية (مستقبلية):
- **PHPStan**: تحليل ثابت للكود
- **Psalm**: فحص الأنواع
- **PHPMD**: كشف المشاكل في التصميم
- **PHPCPD**: كشف التكرار في الكود

## 🎯 الخطوات التالية

### قريباً:
1. إضافة PHPStan للتحليل الثابت
2. إعداد CI/CD pipeline للجودة
3. إضافة تقارير تلقائية
4. تدريب الفريق على الأدوات

### مستقبلياً:
1. إضافة Psalm للتحقق من الأنواع
2. إعداد SonarQube للمراقبة المستمرة
3. إضافة metrics dashboard
4. تطوير أدوات مخصصة إضافية

---

**آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0  
**المسؤول**: فريق تطوير MotorLine
