@extends('dashboard::layouts.admin_layout')

@section('title', 'تفاصيل نوع ناقل الحركة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid">
    {{-- عنوان الصفحة --}}
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">تفاصيل نوع ناقل الحركة: {{ $transmissiontype->name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.transmission-types.index') }}">أنواع ناقل الحركة</a></li>
                        <li class="breadcrumb-item active">التفاصيل</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    {{-- تفاصيل نوع ناقل الحركة --}}
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">معلومات نوع ناقل الحركة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الاسم:</label>
                                <p class="text-muted">{{ $transmissiontype->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p>
                                    <span class="badge rounded-pill bg-{{ $transmissiontype->status ? 'success' : 'danger' }}">
                                        {{ $transmissiontype->status ? 'نشط' : 'غير نشط' }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    @if($transmissiontype->description)
                        <div class="mb-3">
                            <label class="form-label fw-bold">الوصف:</label>
                            <p class="text-muted">{{ $transmissiontype->description }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            {{-- إحصائيات --}}
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">الإحصائيات</h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h3 class="text-primary">{{ $transmissiontype->cars_count }}</h3>
                            <p class="text-muted mb-0">عدد السيارات المرتبطة</p>
                        </div>
                    </div>
                </div>
            </div>
            
            {{-- الإجراءات --}}
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">الإجراءات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.transmission-types.edit', $transmissiontype) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="{{ route('admin.transmission-types.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        @if($transmissiontype->cars_count == 0)
                            <form action="{{ route('admin.transmission-types.destroy', $transmissiontype) }}" 
                                  method="POST" 
                                  onsubmit="return confirm('هل أنت متأكد من حذف نوع ناقل الحركة هذا؟')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </form>
                        @else
                            <button type="button" class="btn btn-danger" disabled title="لا يمكن حذف نوع ناقل الحركة لأنه مرتبط بسيارات">
                                <i class="fas fa-trash"></i> حذف (غير متاح)
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
