<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('nationalities', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name_ar', 100)->unique()->comment('اسم الجنسية باللغة العربية');
            $table->string('name_en', 100)->nullable()->unique()->comment('اسم الجنسية باللغة الإنجليزية');
            $table->string('iso_code', 2)->nullable()->unique()->comment('رمز ISO 3166-1 alpha-2');

            // إنشاء الفهارس
            $table->index('name_ar');
            $table->index('name_en');
            $table->index('iso_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('nationalities');
    }
};
