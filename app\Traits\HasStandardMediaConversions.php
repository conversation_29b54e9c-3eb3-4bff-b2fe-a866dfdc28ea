<?php

namespace App\Traits;

/**
 * Trait HasStandardMediaConversions
 *
 * يوفر تحويلات صور معيارية موحدة عبر النماذج المختلفة
 * لضمان الاتساق في أحجام الصور المصغرة والمتوسطة
 *
 * @package App\Traits
 */
trait HasStandardMediaConversions
{
    /**
     * تسجيل تحويلات الوسائط المعيارية
     *
     * يمكن استدعاء هذه الطريقة من registerMediaConversions في النماذج
     * لتطبيق التحويلات المعيارية
     *
     * @param mixed $media
     * @return void
     */
    public function registerStandardConversions($media = null): void
    {
        // صورة مصغرة صغيرة للجداول والقوائم
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->sharpen(10)
            ->optimize()
            ->nonQueued()
            ->performOnCollections('*');

        // صورة مصغرة متوسطة للعرض في البطاقات
        $this->addMediaConversion('medium')
            ->width(400)
            ->height(300)
            ->sharpen(10)
            ->optimize()
            ->nonQueued()
            ->performOnCollections('*');

        // صورة كبيرة للعرض التفصيلي
        $this->addMediaConversion('large')
            ->width(800)
            ->height(600)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('*');
    }

    /**
     * تحويلات خاصة بشعارات الماركات
     *
     * @param mixed $media
     * @return void
     */
    public function registerBrandLogoConversions($media = null): void
    {
        // شعار صغير للقوائم المنسدلة
        $this->addMediaConversion('thumb')
            ->width(50)
            ->height(50)
            ->sharpen(10)
            ->optimize()
            ->nonQueued()
            ->performOnCollections('brand_logos');

        // شعار متوسط للعرض في الجداول
        $this->addMediaConversion('medium')
            ->width(100)
            ->height(100)
            ->sharpen(10)
            ->optimize()
            ->nonQueued()
            ->performOnCollections('brand_logos');

        // شعار كبير للعرض التفصيلي
        $this->addMediaConversion('large')
            ->width(200)
            ->height(200)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('brand_logos');
    }

    /**
     * تحويلات خاصة بصور السيارات
     *
     * @param mixed $media
     * @return void
     */
    public function registerCarImageConversions($media = null): void
    {
        // صورة مصغرة للجداول
        $this->addMediaConversion('thumb')
            ->width(200)
            ->height(150)
            ->sharpen(10)
            ->optimize()
            ->nonQueued()
            ->performOnCollections('car_images', 'car_main_image');

        // صورة متوسطة للمعاينة
        $this->addMediaConversion('medium')
            ->width(600)
            ->height(450)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('car_images', 'car_main_image');

        // صورة كبيرة للعرض الكامل
        $this->addMediaConversion('large')
            ->width(1200)
            ->height(900)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('car_images', 'car_main_image');

        // صورة عالية الجودة للطباعة
        $this->addMediaConversion('print')
            ->width(1920)
            ->height(1440)
            ->sharpen(5)
            ->optimize()
            ->performOnCollections('car_images');
    }

    /**
     * تحويلات خاصة بأيقونات الفئات والأنواع
     *
     * @param mixed $media
     * @return void
     */
    public function registerIconConversions($media = null): void
    {
        // أيقونة صغيرة
        $this->addMediaConversion('thumb')
            ->width(32)
            ->height(32)
            ->sharpen(10)
            ->optimize()
            ->nonQueued()
            ->performOnCollections('*_icons');

        // أيقونة متوسطة
        $this->addMediaConversion('medium')
            ->width(64)
            ->height(64)
            ->sharpen(10)
            ->optimize()
            ->nonQueued()
            ->performOnCollections('*_icons');

        // أيقونة كبيرة
        $this->addMediaConversion('large')
            ->width(128)
            ->height(128)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('*_icons');
    }

    /**
     * الحصول على URL الصورة المصغرة مع fallback
     *
     * @param string $collection
     * @param string $conversion
     * @param string $fallback
     * @return string
     */
    public function getThumbUrl(string $collection = 'default', string $conversion = 'thumb', string $fallback = '/images/no-image.png'): string
    {
        $media = $this->getFirstMedia($collection);

        if ($media) {
            return $media->getUrl($conversion);
        }

        return $fallback;
    }

    /**
     * التحقق من وجود صورة في المجموعة
     *
     * @param string $collection
     * @return bool
     */
    public function hasMediaInCollection(string $collection): bool
    {
        return $this->getMedia($collection)->count() > 0;
    }
}
