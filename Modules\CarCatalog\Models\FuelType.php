<?php

namespace Modules\CarCatalog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

/**
 * FuelType Model.
 *
 * يمثل هذا النموذج جدول أنواع الوقود في النظام
 *
 * @property int $id
 * @property string $name اسم نوع الوقود
 * @property string|null $description وصف نوع الوقود
 * @property bool $status حالة نوع الوقود (نشط/غير نشط)
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\CarCatalog\Models\Car[] $cars علاقة السيارات
 */
class FuelType extends Model
{
    use HasFactory;
    use HasTranslations;

    /**
     * تعطيل الطوابع الزمنية (created_at, updated_at)
     * حيث أن بيانات أنواع الوقود عادة ما تكون ثابتة.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'status',
    ];

    /**
     * الخصائص التي يمكن ترجمتها.
     *
     * @var array
     */
    public $translatable = [
        'name',
        'description',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * علاقة السيارات المرتبطة بهذا النوع من الوقود.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cars()
    {
        return $this->hasMany(Car::class);
    }

    /**
     * مصنع النموذج.
     *
     * @return \Modules\CarCatalog\Database\factories\FuelTypeFactory
     */
    protected static function newFactory()
    {
        return \Modules\CarCatalog\Database\factories\FuelTypeFactory::new();
    }
}
