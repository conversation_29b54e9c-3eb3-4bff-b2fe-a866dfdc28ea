<?php

namespace Modules\CarCatalog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

/**
 * BodyType Model.
 *
 * يمثل هذا النموذج جدول أنواع هياكل السيارات في النظام
 *
 * @property int $id
 * @property string $name اسم نوع هيكل السيارة
 * @property string|null $description وصف نوع هيكل السيارة
 * @property int|null $icon_id معرّف أيقونة نوع الهيكل
 * @property bool $status حالة نوع هيكل السيارة (نشط/غير نشط)
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\CarCatalog\Models\Car[] $cars علاقة السيارات
 * @property \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection|\Spatie\MediaLibrary\MediaCollections\Models\Media[] $media مجموعة الوسائط
 */
class BodyType extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use HasTranslations;
    use \App\Traits\HasStandardMediaConversions;

    /**
     * تعطيل الطوابع الزمنية (created_at, updated_at)
     * حيث أن بيانات أنواع هياكل السيارات عادة ما تكون ثابتة.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * الخصائص التي يمكن تعبئتها بشكل جماعي.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'status',
    ];

    /**
     * الخصائص التي يمكن ترجمتها.
     *
     * @var array
     */
    public $translatable = [
        'name',
        'description',
    ];

    /**
     * الخصائص التي يجب تحويلها إلى أنواع بيانات محددة.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * علاقة السيارات المرتبطة بهذا النوع من هياكل السيارات.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cars()
    {
        return $this->hasMany(Car::class);
    }

    /**
     * تسجيل مجموعات الوسائط.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('body_type_icons')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp']);
    }

    /**
     * تسجيل تحويلات الوسائط.
     *
     * @param mixed|null $media
     */
    public function registerMediaConversions($media = null): void
    {
        // استخدام التحويلات المعيارية للأيقونات
        $this->registerIconConversions($media);
    }

    /**
     * مصنع النموذج.
     *
     * @return \Modules\CarCatalog\Database\factories\BodyTypeFactory
     */
    protected static function newFactory()
    {
        return \Modules\CarCatalog\Database\factories\BodyTypeFactory::new();
    }
}
