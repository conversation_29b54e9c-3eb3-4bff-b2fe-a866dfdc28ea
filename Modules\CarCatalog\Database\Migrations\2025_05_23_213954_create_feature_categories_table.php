<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('feature_categories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name', 100)->unique()->comment('اسم فئة الميزات');
            $table->text('description')->nullable()->comment('وصف فئة الميزات');
            $table->unsignedBigInteger('icon_id')->nullable()->comment('معرّف أيقونة الفئة من جدول media');
            $table->boolean('status')->default(true)->comment('نشطة/غير نشطة');
            $table->unsignedInteger('display_order')->default(0)->comment('ترتيب العرض');
            $table->timestamps();

            // إنشاء الفهارس
            $table->index('name');
            $table->index('icon_id');
            $table->index('status');
            $table->index('display_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('feature_categories');
    }
};
