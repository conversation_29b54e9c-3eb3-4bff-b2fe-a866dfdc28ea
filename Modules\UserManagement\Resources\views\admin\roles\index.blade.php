@extends('dashboard::layouts.admin_layout')

@section('title', 'إدارة الأدوار')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إدارة الأدوار',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة المستخدمين', 'url' => null],
            ['name' => 'الأدوار', 'active' => true]
        ],
        'actions' => view('usermanagement::admin.roles._index_actions')->render()
    ])

    {{-- رسائل النجاح والخطأ --}}
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    {{-- نموذج البحث --}}
    <div class="dashboard-card p-3 mb-4">
        <form method="GET" action="{{ route('admin.roles.index') }}" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">البحث بالاسم</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="{{ request('search') }}" placeholder="ادخل اسم الدور">
            </div>
            <div class="col-md-6 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>مسح
                </a>
            </div>
        </form>
    </div>

    {{-- جدول الأدوار --}}
    <div class="dashboard-card p-3">
        <div class="mb-3">
            <h5 class="card-title fw-bold mb-0">قائمة الأدوار</h5>
        </div>
        @if($roles->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover recent-activity-table">
                    <thead>
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">اسم الدور</th>
                            <th scope="col">عدد المستخدمين</th>
                            <th scope="col">عدد الصلاحيات</th>
                            <th scope="col">تاريخ الإنشاء</th>
                            <th scope="col">الإجراءات</th>
                        </tr>
                    </thead>
                        <tbody>
                            @foreach($roles as $role)
                                <tr>
                                    <td>{{ $loop->iteration + ($roles->currentPage() - 1) * $roles->perPage() }}</td>
                                    <td>
                                        <strong>{{ $role->name }}</strong>
                                        @if(in_array($role->name, ['Super Admin', 'Employee', 'Customer']))
                                            <span class="status-badge status-completed">أساسي</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($role->users_count > 0)
                                            <span class="status-badge status-processing">{{ $role->users_count }}</span>
                                        @else
                                            <span class="status-badge status-pending">{{ $role->users_count }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($role->permissions_count > 0)
                                            <span class="status-badge status-completed">{{ $role->permissions_count }}</span>
                                        @else
                                            <span class="status-badge status-cancelled">{{ $role->permissions_count }}</span>
                                        @endif
                                    </td>
                                    <td>{{ $role->created_at ? $role->created_at->format('d/m/Y H:i') : '-' }}</td>
                                    <td>
                                        <div class="d-flex">
                                            @can('manage_roles_permissions')
                                            <a href="{{ route('admin.roles.show', $role) }}"
                                               class="action-btn view me-1" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.roles.edit', $role) }}"
                                               class="action-btn edit me-1" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if(!in_array($role->name, ['Super Admin', 'Employee', 'Customer']))
                                            <form action="{{ route('admin.roles.destroy', $role) }}"
                                                  method="POST" class="d-inline"
                                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا الدور؟')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="action-btn delete" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            @endif
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

            </div>

            {{-- روابط التصفح --}}
            <div class="d-flex justify-content-center mt-4">
                {{ $roles->appends(request()->query())->links() }}
            </div>
        @else
            <div class="table-responsive">
                <table class="table table-hover recent-activity-table">
                    <thead>
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">اسم الدور</th>
                            <th scope="col">عدد المستخدمين</th>
                            <th scope="col">عدد الصلاحيات</th>
                            <th scope="col">تاريخ الإنشاء</th>
                            <th scope="col">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="6" class="text-center text-muted py-4">
                                <i class="fas fa-user-shield fa-2x mb-2"></i>
                                <br>لا توجد أدوار مطابقة لمعايير البحث
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
/* تحسينات إضافية لجدول الأدوار */
.recent-activity-table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.status-badge {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.action-btn {
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: scale(1.1);
}

/* تحسين مظهر العنوان */
.card-title {
    color: var(--primary-color);
}

/* تحسين مظهر نموذج البحث */
.dashboard-card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
    transition: box-shadow 0.3s ease;
}

.dashboard-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
</style>
@endpush
