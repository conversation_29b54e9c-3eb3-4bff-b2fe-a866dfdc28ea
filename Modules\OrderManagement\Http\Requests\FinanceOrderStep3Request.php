<?php

namespace Modules\OrderManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * طلب التحقق من صحة بيانات الخطوة الثالثة لطلب التمويل
 * 
 * يتحقق من المستندات المطلوبة للتمويل
 * بناءً على MOD-ORDER-MGMT-FEAT-004 في REQ-FR.md
 */
class FinanceOrderStep3Request extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return auth()->check() && 
               session()->has('finance_order_car_id') &&
               session()->has('finance_order_personal_data') &&
               session()->has('finance_order_finance_data');
    }

    /**
     * قواعد التحقق من صحة البيانات
     */
    public function rules(): array
    {
        return [
            // المستندات الأساسية (مطلوبة)
            'national_id_front' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB
            ],
            
            'national_id_back' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB
            ],
            
            'driving_license' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB
            ],
            
            // مستندات التمويل الإضافية (مطلوبة للتمويل)
            'salary_certificate' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB
            ],
            
            'bank_statement' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB
            ],
            
            // مستندات إضافية (اختيارية)
            'additional_documents' => [
                'nullable',
                'array',
                'max:3' // حد أقصى 3 مستندات إضافية
            ],
            
            'additional_documents.*' => [
                'file',
                'mimes:jpeg,png,jpg,webp,pdf',
                'max:2048' // 2MB لكل ملف
            ],
            
            // أوصاف المستندات الإضافية
            'additional_documents_descriptions' => [
                'nullable',
                'array'
            ],
            
            'additional_documents_descriptions.*' => [
                'nullable',
                'string',
                'max:100'
            ],
            
            // الموافقات المطلوبة
            'documents_authenticity_confirmed' => [
                'required',
                'accepted'
            ],
            
            'data_processing_consent' => [
                'required',
                'accepted'
            ],
            
            'document_verification_consent' => [
                'required',
                'accepted'
            ]
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            // المستندات الأساسية
            'national_id_front.required' => 'صورة الهوية الوطنية (الوجه الأمامي) مطلوبة',
            'national_id_front.file' => 'يجب أن تكون صورة الهوية الوطنية (الوجه الأمامي) ملفاً صالحاً',
            'national_id_front.mimes' => 'صورة الهوية الوطنية (الوجه الأمامي) يجب أن تكون من نوع: JPEG, PNG, JPG, WEBP, PDF',
            'national_id_front.max' => 'حجم صورة الهوية الوطنية (الوجه الأمامي) يجب ألا يزيد عن 2 ميجابايت',
            
            'national_id_back.required' => 'صورة الهوية الوطنية (الوجه الخلفي) مطلوبة',
            'national_id_back.file' => 'يجب أن تكون صورة الهوية الوطنية (الوجه الخلفي) ملفاً صالحاً',
            'national_id_back.mimes' => 'صورة الهوية الوطنية (الوجه الخلفي) يجب أن تكون من نوع: JPEG, PNG, JPG, WEBP, PDF',
            'national_id_back.max' => 'حجم صورة الهوية الوطنية (الوجه الخلفي) يجب ألا يزيد عن 2 ميجابايت',
            
            'driving_license.required' => 'صورة رخصة القيادة مطلوبة',
            'driving_license.file' => 'يجب أن تكون صورة رخصة القيادة ملفاً صالحاً',
            'driving_license.mimes' => 'صورة رخصة القيادة يجب أن تكون من نوع: JPEG, PNG, JPG, WEBP, PDF',
            'driving_license.max' => 'حجم صورة رخصة القيادة يجب ألا يزيد عن 2 ميجابايت',
            
            // مستندات التمويل
            'salary_certificate.required' => 'تعريف بالراتب مطلوب',
            'salary_certificate.file' => 'يجب أن يكون تعريف بالراتب ملفاً صالحاً',
            'salary_certificate.mimes' => 'تعريف بالراتب يجب أن يكون من نوع: JPEG, PNG, JPG, WEBP, PDF',
            'salary_certificate.max' => 'حجم تعريف بالراتب يجب ألا يزيد عن 2 ميجابايت',
            
            'bank_statement.required' => 'كشف الحساب البنكي مطلوب',
            'bank_statement.file' => 'يجب أن يكون كشف الحساب البنكي ملفاً صالحاً',
            'bank_statement.mimes' => 'كشف الحساب البنكي يجب أن يكون من نوع: JPEG, PNG, JPG, WEBP, PDF',
            'bank_statement.max' => 'حجم كشف الحساب البنكي يجب ألا يزيد عن 2 ميجابايت',
            
            // المستندات الإضافية
            'additional_documents.max' => 'لا يمكن رفع أكثر من 3 مستندات إضافية',
            'additional_documents.*.file' => 'يجب أن تكون المستندات الإضافية ملفات صالحة',
            'additional_documents.*.mimes' => 'المستندات الإضافية يجب أن تكون من نوع: JPEG, PNG, JPG, WEBP, PDF',
            'additional_documents.*.max' => 'حجم كل مستند إضافي يجب ألا يزيد عن 2 ميجابايت',
            
            'additional_documents_descriptions.*.max' => 'وصف المستند الإضافي يجب ألا يزيد عن 100 حرف',
            
            // الموافقات
            'documents_authenticity_confirmed.required' => 'يجب تأكيد صحة المستندات',
            'documents_authenticity_confirmed.accepted' => 'يجب تأكيد صحة المستندات',
            
            'data_processing_consent.required' => 'يجب الموافقة على معالجة البيانات',
            'data_processing_consent.accepted' => 'يجب الموافقة على معالجة البيانات',
            
            'document_verification_consent.required' => 'يجب الموافقة على التحقق من المستندات',
            'document_verification_consent.accepted' => 'يجب الموافقة على التحقق من المستندات'
        ];
    }

    /**
     * أسماء الحقول المخصصة للعرض في رسائل الخطأ
     */
    public function attributes(): array
    {
        return [
            'national_id_front' => 'صورة الهوية الوطنية (الوجه الأمامي)',
            'national_id_back' => 'صورة الهوية الوطنية (الوجه الخلفي)',
            'driving_license' => 'صورة رخصة القيادة',
            'salary_certificate' => 'تعريف بالراتب',
            'bank_statement' => 'كشف الحساب البنكي',
            'additional_documents' => 'المستندات الإضافية',
            'additional_documents_descriptions' => 'أوصاف المستندات الإضافية',
            'documents_authenticity_confirmed' => 'تأكيد صحة المستندات',
            'data_processing_consent' => 'الموافقة على معالجة البيانات',
            'document_verification_consent' => 'الموافقة على التحقق من المستندات'
        ];
    }

    /**
     * تحضير البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تنظيف أوصاف المستندات الإضافية
        if ($this->has('additional_documents_descriptions') && is_array($this->additional_documents_descriptions)) {
            $descriptions = [];
            foreach ($this->additional_documents_descriptions as $description) {
                $descriptions[] = trim($description ?? '');
            }
            $this->merge(['additional_documents_descriptions' => $descriptions]);
        }
    }

    /**
     * التحقق من صحة الملفات المرفوعة
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من أن الملفات المرفوعة ليست فارغة أو تالفة
            $requiredFiles = ['national_id_front', 'national_id_back', 'driving_license', 'salary_certificate', 'bank_statement'];
            
            foreach ($requiredFiles as $fileField) {
                $file = $this->file($fileField);
                if ($file && !$file->isValid()) {
                    $validator->errors()->add($fileField, "الملف المرفوع تالف أو غير صالح");
                }
            }

            // التحقق من المستندات الإضافية
            if ($this->hasFile('additional_documents')) {
                foreach ($this->file('additional_documents') as $index => $file) {
                    if ($file && !$file->isValid()) {
                        $validator->errors()->add("additional_documents.{$index}", "المستند الإضافي رقم " . ($index + 1) . " تالف أو غير صالح");
                    }
                }
            }

            // التحقق من تطابق عدد الأوصاف مع عدد المستندات الإضافية
            if ($this->hasFile('additional_documents') && $this->has('additional_documents_descriptions')) {
                $documentsCount = count($this->file('additional_documents'));
                $descriptionsCount = count(array_filter($this->additional_documents_descriptions ?? []));
                
                if ($descriptionsCount > 0 && $descriptionsCount !== $documentsCount) {
                    $validator->errors()->add('additional_documents_descriptions', 'يجب تقديم وصف لكل مستند إضافي مرفوع');
                }
            }
        });
    }
}
