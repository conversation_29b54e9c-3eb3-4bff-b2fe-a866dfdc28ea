# دليل الهوية البصرية الموحدة للمشروع

## نظرة عامة

هذا الدليل يوضح كيفية الحفاظ على الهوية البصرية الموحدة عبر جميع أجزاء المشروع باستخدام نظام CSS موحد.

## ملف الهوية البصرية

**المسار:** `public/css/brand-identity.css`

هذا الملف يحتوي على جميع الأنماط المطلوبة للحفاظ على الهوية البصرية الموحدة.

## الألوان الأساسية

```css
:root {
    --primary-color: #0f172a;      /* الأزرق الداكن الأساسي */
    --secondary-color: #1e3a8a;    /* الأزرق الثانوي */
    --accent-color: #f97316;       /* البرتقالي للتمييز */
    --success-color: #10b981;      /* الأخضر للنجاح */
    --warning-color: #f59e0b;      /* الأصفر للتحذير */
    --danger-color: #ef4444;       /* الأحمر للخطر */
    --info-color: #06b6d4;         /* الأزرق الفاتح للمعلومات */
}
```

## كيفية الاستخدام

### 1. تضمين ملف الهوية البصرية

```blade
@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush
```

### 2. استخدام الأزرار الموحدة

```html
<!-- الزر الأساسي -->
<button class="btn btn-brand-primary">حفظ</button>

<!-- الزر الثانوي -->
<button class="btn btn-brand-secondary">إلغاء</button>

<!-- زر التمييز -->
<button class="btn btn-brand-accent">إجراء مهم</button>
```

### 3. استخدام الكروت الموحدة

```html
<div class="brand-card">
    <div class="brand-card-header">
        <h5>عنوان الكارت</h5>
    </div>
    <div class="brand-card-body">
        <p>محتوى الكارت</p>
    </div>
</div>
```

### 4. استخدام النماذج الموحدة

```html
<label class="brand-form-label">اسم الحقل</label>
<input type="text" class="brand-form-control" placeholder="أدخل النص">
```

### 5. استخدام التنبيهات الموحدة

```html
<div class="brand-alert brand-alert-success">رسالة نجاح</div>
<div class="brand-alert brand-alert-warning">رسالة تحذير</div>
<div class="brand-alert brand-alert-danger">رسالة خطر</div>
<div class="brand-alert brand-alert-info">رسالة معلومات</div>
```

### 6. استخدام الجداول الموحدة

```html
<table class="table brand-table">
    <thead>
        <tr>
            <th>العمود الأول</th>
            <th>العمود الثاني</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>البيانات</td>
            <td>البيانات</td>
        </tr>
    </tbody>
</table>
```

### 7. استخدام أزرار الإجراءات

```html
<button class="brand-action-btn brand-action-view" title="عرض">
    <i class="fas fa-eye"></i>
</button>
<button class="brand-action-btn brand-action-edit" title="تعديل">
    <i class="fas fa-edit"></i>
</button>
<button class="brand-action-btn brand-action-delete" title="حذف">
    <i class="fas fa-trash"></i>
</button>
```

### 8. استخدام Status Badges

```html
<span class="brand-status-badge brand-status-success">نشط</span>
<span class="brand-status-badge brand-status-warning">معلق</span>
<span class="brand-status-badge brand-status-danger">محذوف</span>
<span class="brand-status-badge brand-status-info">جديد</span>
```

### 9. استخدام الأيقونات الموحدة

```html
<div class="brand-icon-wrapper">
    <i class="fas fa-user"></i>
</div>
<div class="brand-icon-wrapper brand-icon-accent">
    <i class="fas fa-star"></i>
</div>
```

### 10. استخدام الروابط الموحدة

```html
<a href="#" class="brand-link">رابط موحد</a>
```

### 11. استخدام Breadcrumb الموحد

```html
<nav aria-label="breadcrumb">
    <ol class="breadcrumb brand-breadcrumb">
        <li class="breadcrumb-item">
            <a href="#" class="brand-link">الرئيسية</a>
        </li>
        <li class="breadcrumb-item active">الصفحة الحالية</li>
    </ol>
</nav>
```

## التأثيرات والحركات

### تأثيرات الظهور

```html
<div class="brand-fade-in">محتوى يظهر تدريجياً</div>
<div class="brand-slide-up">محتوى ينزلق للأعلى</div>
```

### حالات التحميل

```html
<div class="brand-loading">عنصر في حالة تحميل</div>
```

## أفضل الممارسات

### 1. استخدم دائماً متغيرات CSS

```css
/* صحيح */
color: var(--primary-color);

/* خطأ */
color: #0f172a;
```

### 2. استخدم الفئات الموحدة

```html
<!-- صحيح -->
<button class="btn btn-brand-primary">زر</button>

<!-- خطأ -->
<button class="btn btn-primary">زر</button>
```

### 3. حافظ على التناسق

- استخدم نفس الألوان في جميع أنحاء التطبيق
- استخدم نفس أحجام الخطوط والمسافات
- استخدم نفس أنماط الأزرار والعناصر

### 4. اختبر على شاشات مختلفة

- تأكد من أن التصميم يعمل على الهواتف المحمولة
- اختبر على شاشات مختلفة الأحجام
- تأكد من وضوح النصوص والألوان

## إضافة عناصر جديدة

عند إضافة عناصر جديدة للهوية البصرية:

1. أضف الأنماط إلى `brand-identity.css`
2. استخدم متغيرات الألوان الموجودة
3. اتبع نفس نمط التسمية (`brand-*`)
4. أضف التوثيق هنا
5. اختبر التوافق مع العناصر الموجودة

## مثال كامل

```blade
@extends('dashboard::layouts.admin_layout')

@section('title', 'صفحة مثال')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    <div class="mb-4">
        <h4 style="color: var(--primary-color);">عنوان الصفحة</h4>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb brand-breadcrumb">
                <li class="breadcrumb-item">
                    <a href="#" class="brand-link">الرئيسية</a>
                </li>
                <li class="breadcrumb-item active">الصفحة الحالية</li>
            </ol>
        </nav>
    </div>

    <div class="brand-card brand-fade-in">
        <div class="brand-card-header">
            <h5>عنوان الكارت</h5>
        </div>
        <div class="brand-card-body">
            <form>
                <div class="mb-3">
                    <label class="brand-form-label">اسم الحقل</label>
                    <input type="text" class="brand-form-control">
                </div>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-brand-primary">حفظ</button>
                    <button type="button" class="btn btn-brand-secondary">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
```

## الدعم والمساعدة

للحصول على المساعدة أو إضافة عناصر جديدة للهوية البصرية، يرجى:

1. مراجعة هذا الدليل أولاً
2. التأكد من استخدام الفئات الموحدة
3. اختبار التوافق مع العناصر الموجودة
4. توثيق أي إضافات جديدة

---

**ملاحظة:** هذا النظام مصمم ليكون قابلاً للتوسع والصيانة، لذا يرجى الالتزام بالمعايير المحددة للحفاظ على جودة وتناسق التصميم.
