<?php

namespace Modules\CarCatalog\Http\Controllers\Admin;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\CarCatalog\Http\Requests\Admin\StoreCarFeatureRequest;
use Modules\CarCatalog\Http\Requests\Admin\UpdateCarFeatureRequest;
use Modules\CarCatalog\Models\CarFeature;
use Modules\CarCatalog\Models\FeatureCategory;
use Modules\Core\Http\Controllers\BaseController;

/**
 * وحدة تحكم إدارة ميزات السيارات.
 *
 * تتعامل هذه الوحدة مع عمليات CRUD لميزات السيارات في لوحة تحكم الإدارة
 */
class CarFeatureController extends BaseController
{
    /**
     * عرض قائمة ميزات السيارات.
     *
     * @param Request $request طلب HTTP
     *
     * @return View
     */
    public function index(Request $request): View
    {
        $query = CarFeature::with('category')->withCount('cars');

        // تطبيق فلتر البحث بالاسم إذا تم تقديمه
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->input('search') . '%');
        }

        // تطبيق فلتر فئة الميزة إذا تم تقديمه
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }

        // تطبيق فلتر الحالة إذا تم تقديمه
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // تطبيق فلتر القابلية للتصفية إذا تم تقديمه
        if ($request->filled('is_filterable')) {
            $query->where('is_filterable', $request->input('is_filterable'));
        }

        // تطبيق فلتر الميزة المميزة إذا تم تقديمه
        if ($request->filled('is_highlighted')) {
            $query->where('is_highlighted', $request->input('is_highlighted'));
        }

        // ترتيب وترقيم النتائج
        $carFeatures = $query->orderBy('display_order')->orderBy('name')->paginate(10);

        // الحصول على فئات الميزات للفلترة
        $categories = FeatureCategory::where('status', 1)->orderBy('name')->pluck('name', 'id');

        return view('carcatalog::admin.carfeatures.index', compact('carFeatures', 'categories'));
    }

    /**
     * عرض نموذج إنشاء ميزة سيارة جديدة.
     *
     * @return View
     */
    public function create(): View
    {
        // الحصول على فئات الميزات النشطة فقط
        $categories = FeatureCategory::where('status', 1)->orderBy('name')->pluck('name', 'id');

        return view('carcatalog::admin.carfeatures.create', compact('categories'));
    }

    /**
     * تخزين ميزة سيارة جديدة.
     *
     * @param StoreCarFeatureRequest $request طلب تخزين ميزة سيارة
     *
     * @return RedirectResponse
     */
    public function store(StoreCarFeatureRequest $request): RedirectResponse
    {
        // إنشاء ميزة السيارة باستخدام البيانات المتحقق منها
        CarFeature::create($request->validated());

        return redirect()->route('admin.car-features.index')->with('success', 'تمت إضافة ميزة السيارة بنجاح.');
    }

    /**
     * عرض تفاصيل ميزة سيارة محددة.
     *
     * @param CarFeature $carfeature ميزة السيارة المراد عرضها
     *
     * @return View
     */
    public function show(CarFeature $carfeature): View
    {
        // تحميل العلاقات المرتبطة بميزة السيارة
        $carfeature->loadCount('cars');
        $carfeature->load('category');

        return view('carcatalog::admin.carfeatures.show', compact('carfeature'));
    }

    /**
     * عرض نموذج تعديل ميزة سيارة موجودة.
     *
     * @param CarFeature $carfeature ميزة السيارة المراد تعديلها
     *
     * @return View
     */
    public function edit(CarFeature $carfeature): View
    {
        // الحصول على فئات الميزات النشطة فقط
        $categories = FeatureCategory::where('status', 1)->orderBy('name')->pluck('name', 'id');

        return view('carcatalog::admin.carfeatures.edit', compact('carfeature', 'categories'));
    }

    /**
     * تحديث ميزة سيارة موجودة.
     *
     * @param UpdateCarFeatureRequest $request طلب تحديث ميزة سيارة
     * @param CarFeature $carfeature ميزة السيارة المراد تحديثها
     *
     * @return RedirectResponse
     */
    public function update(UpdateCarFeatureRequest $request, CarFeature $carfeature): RedirectResponse
    {
        // تحديث ميزة السيارة باستخدام البيانات المتحقق منها
        $carfeature->update($request->validated());

        return redirect()->route('admin.car-features.index')->with('success', 'تم تحديث ميزة السيارة بنجاح.');
    }

    /**
     * حذف ميزة سيارة محددة.
     *
     * @param CarFeature $carfeature ميزة السيارة المراد حذفها
     *
     * @return RedirectResponse
     */
    public function destroy(CarFeature $carfeature): RedirectResponse
    {
        // التحقق من وجود سيارات مرتبطة بميزة السيارة
        if ($carfeature->cars()->count() > 0) {
            return redirect()->route('admin.car-features.index')
                ->with('error', 'لا يمكن حذف ميزة السيارة لأنها مرتبطة بسيارات أخرى.');
        }

        // حذف ميزة السيارة
        $carfeature->delete();

        return redirect()->route('admin.car-features.index')->with('success', 'تم حذف ميزة السيارة بنجاح.');
    }
}
