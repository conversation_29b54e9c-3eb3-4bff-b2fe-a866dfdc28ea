@extends('dashboard::layouts.admin_layout')

@section('title', 'تعديل الدور: ' . $role->name)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'تعديل الدور: ' . $role->name,
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة المستخدمين', 'url' => null],
            ['name' => 'الأدوار', 'url' => route('admin.roles.index')],
            ['name' => $role->name, 'url' => route('admin.roles.show', $role)],
            ['name' => 'تعديل', 'active' => true]
        ],
        'actions' => view('usermanagement::admin.roles._edit_actions', compact('role'))->render()
    ])

    {{-- نموذج تعديل الدور --}}
    <div class="row">
        <div class="col-12">
            <div class="brand-card brand-fade-in">
                <div class="brand-card-header border-bottom">
                    <div class="d-flex align-items-center">
                        <div class="brand-icon-wrapper me-3">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold text-white">تعديل بيانات الدور</h5>
                            <small>قم بتعديل البيانات التالية وتحديث الصلاحيات المعينة للدور</small>
                        </div>
                    </div>
                </div>
                <div class="brand-card-body">

                    <form action="{{ route('admin.roles.update', $role) }}" method="POST" id="roleForm">
                        @csrf
                        @method('PUT')
                        @include('usermanagement::admin.roles._form')
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
