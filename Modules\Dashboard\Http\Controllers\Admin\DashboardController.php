<?php

namespace Modules\Dashboard\Http\Controllers\Admin;

use Illuminate\View\View;
use Modules\Core\Http\Controllers\BaseController;
use Modules\Dashboard\Services\DashboardDataService;

/**
 * متحكم لوحة البيانات الإدارية
 *
 * يتعامل هذا المتحكم مع عرض صفحة لوحة البيانات الرئيسية للوحة تحكم الإدارة
 * ويقوم بجلب البيانات الديناميكية اللازمة لجميع البطاقات الإحصائية والرسوم البيانية
 */
class DashboardController extends BaseController
{
    /**
     * خدمة تجميع البيانات الإحصائية
     *
     * @var DashboardDataService
     */
    private DashboardDataService $dashboardDataService;

    /**
     * إنشاء مثيل جديد من المتحكم
     *
     * @param DashboardDataService $dashboardDataService
     */
    public function __construct(DashboardDataService $dashboardDataService)
    {
        $this->dashboardDataService = $dashboardDataService;
    }

    /**
     * عرض صفحة لوحة البيانات الرئيسية مع البيانات الديناميكية
     *
     * يقوم هذا المتحكم بجلب جميع البيانات المطلوبة للوحة البيانات الرئيسية
     * بما في ذلك البطاقات الإحصائية والرسوم البيانية والأقسام الأخرى
     *
     * @return View
     */
    public function home(): View
    {
        try {
            // جلب جميع البيانات المطلوبة للوحة البيانات
            $dashboardData = $this->dashboardDataService->getDashboardData();

            // تمرير البيانات إلى الـ view
            return view('dashboard::admin.home', [
                // البطاقات الإحصائية
                'newOrdersTodayCount' => $dashboardData['stats']['newOrdersToday'],
                'pendingFinanceRequestsCount' => $dashboardData['stats']['pendingFinanceRequests'],
                'availableCarsCount' => $dashboardData['stats']['availableCars'],
                'newCustomersThisMonthCount' => $dashboardData['stats']['newCustomersThisMonth'],
                'totalSalesThisMonth' => $dashboardData['stats']['totalSalesThisMonth'],
                'totalSalesThisYear' => $dashboardData['stats']['totalSalesThisYear'],

                // بيانات الرسوم البيانية
                'salesChartData' => $dashboardData['charts']['sales'],
                'brandsPieData' => $dashboardData['charts']['brands'],
                'topCarsChartData' => $dashboardData['charts']['topCars'],
                'categoriesChartData' => $dashboardData['charts']['categories'],
                'financingStatusChartData' => $dashboardData['charts']['financing'],
                'newCustomersChartData' => $dashboardData['charts']['newCustomers'],
                'paymentMethodsChartData' => $dashboardData['charts']['paymentMethods'],

                // الأقسام الأخرى
                'recentActivities' => $dashboardData['recentActivities'],
                'latestCars' => $dashboardData['latestCars'],
                'alerts' => $dashboardData['alerts'],
                'performanceMetrics' => $dashboardData['performanceMetrics'],

                // معلومات إضافية
                'lastUpdated' => now(),
            ]);
        } catch (\Exception $e) {
            // في حالة حدوث خطأ، نعرض رسالة خطأ ونسجل الخطأ
            \Log::error('خطأ في جلب بيانات لوحة البيانات: ' . $e->getMessage());

            // عرض الصفحة مع بيانات افتراضية أو رسالة خطأ
            return view('dashboard::admin.home', [
                'error' => 'حدث خطأ في جلب البيانات. يرجى المحاولة مرة أخرى.',
                'newOrdersTodayCount' => 0,
                'pendingFinanceRequestsCount' => 0,
                'availableCarsCount' => 0,
                'newCustomersThisMonthCount' => 0,
                'totalSalesThisMonth' => ['amount' => 0, 'change_percentage' => 0, 'is_increase' => false],
                'totalSalesThisYear' => ['amount' => 0, 'change_percentage' => 0, 'is_increase' => false],
                'salesChartData' => ['labels' => [], 'datasets' => []],
                'brandsPieData' => ['labels' => [], 'datasets' => []],
                'topCarsChartData' => ['labels' => [], 'datasets' => []],
                'categoriesChartData' => ['labels' => [], 'datasets' => []],
                'financingStatusChartData' => ['labels' => [], 'datasets' => []],
                'newCustomersChartData' => ['labels' => [], 'datasets' => []],
                'paymentMethodsChartData' => ['labels' => [], 'datasets' => []],
                'recentActivities' => [],
                'latestCars' => [],
                'alerts' => [],
                'performanceMetrics' => [],
                'lastUpdated' => now(),
            ]);
        }
    }
}
