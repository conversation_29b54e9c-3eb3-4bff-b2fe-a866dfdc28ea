<?php

namespace Modules\OrderManagement\Services;

use Modules\OrderManagement\Models\Order;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Exception;

/**
 * خدمة بوابة الدفع
 *
 * تتولى التكامل مع بوابات الدفع المختلفة لمعالجة دفع مبلغ الحجز
 * بناءً على MOD-ORDER-MGMT-FEAT-005 في REQ-FR.md
 */
class PaymentGatewayService
{
    /**
     * بوابات الدفع المدعومة
     */
    private const SUPPORTED_GATEWAYS = [
        'visa_mastercard' => 'فيزا/ماستركارد',
        'mada' => 'مدى',
        'apple_pay' => 'Apple Pay',
        'stc_pay' => 'STC Pay'
    ];

    /**
     * بوابة الدفع الحالية
     */
    private string $currentGateway;

    /**
     * إعدادات بوابة الدفع الحالية
     */
    private array $gatewayConfig;

    /**
     * Constructor
     */
    public function __construct(string $gateway = null)
    {
        $this->currentGateway = $gateway ?? config('payment.default_gateway', 'test');
        $this->gatewayConfig = config("payment.gateways.{$this->currentGateway}");

        if (!$this->gatewayConfig || !$this->gatewayConfig['enabled']) {
            throw new Exception("بوابة الدفع غير مفعلة أو غير موجودة: {$this->currentGateway}");
        }
    }

    /**
     * إنشاء جلسة دفع جديدة
     */
    public function createPaymentSession(Order $order): string
    {
        try {
            $paymentData = session('cash_order_payment_data');
            $gateway = $paymentData['payment_gateway'] ?? 'visa_mastercard';

            // تحضير بيانات الدفع
            $paymentRequest = [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'amount' => $order->reservation_amount,
                'currency' => config('payment.currency', 'SAR'),
                'customer_name' => $order->user->full_name,
                'customer_email' => $order->user->email,
                'customer_phone' => $order->user->phone_number,
                'description' => "حجز السيارة - {$order->car->title}",
                'return_url' => route('site.order.payment.success', $order->id),
                'cancel_url' => route('site.order.payment.cancel', $order->id),
                'webhook_url' => route('api.payment.webhook'),
                'gateway' => $gateway
            ];

            // تسجيل محاولة إنشاء جلسة الدفع
            $this->logPaymentActivity('payment_session_create_attempt', $order, $paymentRequest);

            // إنشاء جلسة الدفع حسب البوابة المختارة
            $paymentUrl = match ($gateway) {
                'visa_mastercard', 'mada' => $this->createCardPaymentSession($paymentRequest),
                'apple_pay' => $this->createApplePaySession($paymentRequest),
                'stc_pay' => $this->createSTCPaySession($paymentRequest),
                default => throw new Exception("بوابة الدفع غير مدعومة: {$gateway}")
            };

            // تسجيل نجاح إنشاء جلسة الدفع
            $this->logPaymentActivity('payment_session_created', $order, [
                'payment_url' => $paymentUrl,
                'gateway' => $gateway
            ]);

            return $paymentUrl;

        } catch (Exception $e) {
            $this->logPaymentActivity('payment_session_create_failed', $order, [
                'error' => $e->getMessage(),
                'gateway' => $gateway ?? 'unknown'
            ]);

            throw $e;
        }
    }

    /**
     * معالجة استجابة الدفع من البوابة
     */
    public function processPaymentResponse(array $response): array
    {
        try {
            // التحقق من صحة الاستجابة
            $this->validatePaymentResponse($response);

            $orderId = $response['order_id'] ?? null;
            $order = Order::find($orderId);

            if (!$order) {
                throw new Exception('الطلب غير موجود');
            }

            // تحديث حالة الطلب بناءً على نتيجة الدفع
            $orderService = new OrderProcessingService();

            if ($response['status'] === 'success') {
                $orderService->updateOrderPaymentStatus($order, 'completed', $response);

                return [
                    'success' => true,
                    'message' => 'تم الدفع بنجاح',
                    'order' => $order
                ];
            } else {
                $orderService->updateOrderPaymentStatus($order, 'failed', $response);

                return [
                    'success' => false,
                    'message' => $response['error_message'] ?? 'فشل في عملية الدفع',
                    'order' => $order
                ];
            }

        } catch (Exception $e) {
            Log::error('خطأ في معالجة استجابة الدفع', [
                'response' => $response,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ في معالجة الدفع',
                'order' => null
            ];
        }
    }

    /**
     * إنشاء جلسة دفع بالبطاقة (فيزا/ماستركارد/مدى)
     */
    private function createCardPaymentSession(array $paymentRequest): string
    {
        // إذا كانت بوابة الاختبار
        if ($this->currentGateway === 'test') {
            return $this->createTestPaymentSession($paymentRequest);
        }

        // للبوابات الحقيقية
        return $this->createRealPaymentSession($paymentRequest);
    }

    /**
     * إنشاء جلسة دفع اختبار
     */
    private function createTestPaymentSession(array $paymentRequest): string
    {
        $testPaymentUrl = route('site.order.payment.test', [
            'order_id' => $paymentRequest['order_id'],
            'amount' => $paymentRequest['amount'],
            'gateway' => $paymentRequest['gateway']
        ]);

        $this->logPaymentActivity('test_payment_session_created', null, [
            'order_id' => $paymentRequest['order_id'],
            'amount' => $paymentRequest['amount'],
            'gateway' => $paymentRequest['gateway'],
            'test_url' => $testPaymentUrl
        ]);

        return $testPaymentUrl;
    }

    /**
     * إنشاء جلسة دفع حقيقية
     */
    private function createRealPaymentSession(array $paymentRequest): string
    {
        $headers = [
            'Authorization' => 'Bearer ' . $this->gatewayConfig['api_key'],
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ];

        $requestData = [
            'amount' => $paymentRequest['amount'] * 100, // تحويل للهللة
            'currency' => $paymentRequest['currency'],
            'order_reference' => $paymentRequest['order_number'],
            'description' => $paymentRequest['description'],
            'customer' => [
                'name' => $paymentRequest['customer_name'],
                'email' => $paymentRequest['customer_email'],
                'phone' => $paymentRequest['customer_phone']
            ],
            'return_url' => $paymentRequest['return_url'],
            'cancel_url' => $paymentRequest['cancel_url'],
            'webhook_url' => $paymentRequest['webhook_url'],
            'metadata' => [
                'order_id' => $paymentRequest['order_id'],
                'gateway' => $paymentRequest['gateway']
            ]
        ];

        $response = Http::timeout(config('payment.timeout', 30))
            ->withHeaders($headers)
            ->post($this->gatewayConfig['base_url'] . '/payments', $requestData);

        if ($response->successful()) {
            $data = $response->json();

            $this->logPaymentActivity('real_payment_session_created', null, [
                'order_id' => $paymentRequest['order_id'],
                'gateway' => $this->currentGateway,
                'payment_id' => $data['id'] ?? null,
                'status' => $data['status'] ?? null
            ]);

            return $data['payment_url'] ?? $data['checkout_url'] ?? $data['redirect_url'];
        } else {
            $errorMessage = 'فشل في إنشاء جلسة الدفع: ' . $response->body();

            $this->logPaymentActivity('real_payment_session_failed', null, [
                'order_id' => $paymentRequest['order_id'],
                'gateway' => $this->currentGateway,
                'error' => $errorMessage,
                'status_code' => $response->status()
            ]);

            throw new Exception($errorMessage);
        }
    }

    /**
     * إنشاء جلسة Apple Pay
     */
    private function createApplePaySession(array $paymentRequest): string
    {
        // تطبيق مشابه للبطاقات مع تخصيص Apple Pay
        return $this->createCardPaymentSession($paymentRequest);
    }

    /**
     * إنشاء جلسة STC Pay
     */
    private function createSTCPaySession(array $paymentRequest): string
    {
        // تطبيق مخصص لـ STC Pay
        return $this->createCardPaymentSession($paymentRequest);
    }

    /**
     * التحقق من صحة استجابة الدفع
     */
    private function validatePaymentResponse(array $response): void
    {
        $requiredFields = ['order_id', 'status', 'transaction_id'];

        foreach ($requiredFields as $field) {
            if (!isset($response[$field])) {
                throw new Exception("حقل مطلوب مفقود في استجابة الدفع: {$field}");
            }
        }

        // التحقق من صحة التوقيع إذا كان متوفراً
        if (isset($response['signature'])) {
            $this->verifyPaymentSignature($response);
        }
    }

    /**
     * التحقق من توقيع الدفع
     */
    private function verifyPaymentSignature(array $response): void
    {
        // تطبيق التحقق من التوقيع حسب بوابة الدفع المستخدمة
        // هذا مثال عام - يجب تخصيصه حسب البوابة

        $expectedSignature = hash_hmac(
            'sha256',
            $response['order_id'] . $response['status'] . $response['transaction_id'],
            config('payment.secret_key')
        );

        if (!hash_equals($expectedSignature, $response['signature'])) {
            throw new Exception('توقيع الدفع غير صحيح');
        }
    }

    /**
     * الحصول على قائمة بوابات الدفع المدعومة
     */
    public function getSupportedGateways(): array
    {
        return self::SUPPORTED_GATEWAYS;
    }

    /**
     * التحقق من دعم بوابة دفع معينة
     */
    public function isGatewaySupported(string $gateway): bool
    {
        return array_key_exists($gateway, self::SUPPORTED_GATEWAYS);
    }

    /**
     * إلغاء جلسة دفع
     */
    public function cancelPaymentSession(Order $order): bool
    {
        try {
            // تطبيق إلغاء الجلسة حسب بوابة الدفع
            $this->logPaymentActivity('payment_session_cancelled', $order, [
                'gateway' => $this->currentGateway
            ]);

            return true;

        } catch (Exception $e) {
            $this->logPaymentActivity('payment_session_cancel_failed', $order, [
                'error' => $e->getMessage(),
                'gateway' => $this->currentGateway
            ]);

            return false;
        }
    }

    /**
     * تسجيل نشاط الدفع
     */
    private function logPaymentActivity(string $activity, ?Order $order = null, array $data = []): void
    {
        if (!config('payment.logging.enabled', true)) {
            return;
        }

        $logData = [
            'activity' => $activity,
            'gateway' => $this->currentGateway,
            'timestamp' => now()->toISOString(),
            ...$data
        ];

        if ($order) {
            $logData['order_id'] = $order->id;
            $logData['order_number'] = $order->order_number;
        }

        $level = config('payment.logging.level', 'info');
        $channel = config('payment.logging.channel', 'single');

        Log::channel($channel)->log($level, "Payment Activity: {$activity}", $logData);
    }

    /**
     * الحصول على معلومات بوابة الدفع الحالية
     */
    public function getGatewayInfo(): array
    {
        return [
            'name' => $this->gatewayConfig['name'],
            'gateway' => $this->currentGateway,
            'enabled' => $this->gatewayConfig['enabled'],
            'supported_methods' => $this->gatewayConfig['supported_methods'] ?? []
        ];
    }

    /**
     * التحقق من دعم طريقة دفع معينة
     */
    public function supportsPaymentMethod(string $method): bool
    {
        return in_array($method, $this->gatewayConfig['supported_methods'] ?? []);
    }
}
