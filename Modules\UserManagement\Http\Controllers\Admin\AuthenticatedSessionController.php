<?php

namespace Modules\UserManagement\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Core\Http\Controllers\BaseController;
use Modules\UserManagement\Http\Requests\Admin\LoginRequest;

/**
 * متحكم جلسات المصادقة للمديرين/الموظفين
 *
 * يتعامل هذا المتحكم مع عمليات تسجيل الدخول والخروج للوحة التحكم الإدارية
 */
class AuthenticatedSessionController extends BaseController
{
    /**
     * عرض صفحة تسجيل الدخول
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('usermanagement::admin.auth.login');
    }

    /**
     * معالجة طلب تسجيل الدخول
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(LoginRequest $request)
    {
        $request->authenticate();

        $request->session()->regenerate();

        return redirect()->intended(route('admin.dashboard'));
    }

    /**
     * تسجيل خروج المستخدم
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect(route('admin.login'));
    }
}
