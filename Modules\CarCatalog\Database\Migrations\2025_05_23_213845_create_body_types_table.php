<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('body_types', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name', 50)->unique()->comment('اسم نوع هيكل السيارة');
            $table->text('description')->nullable()->comment('وصف نوع هيكل السيارة');
            $table->unsignedBigInteger('icon_id')->nullable()->comment('معرّف أيقونة نوع الهيكل من جدول media');
            $table->boolean('status')->default(true)->comment('نشط/غير نشط');

            // إنشاء الفهارس
            $table->index('name');
            $table->index('icon_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('body_types');
    }
};
