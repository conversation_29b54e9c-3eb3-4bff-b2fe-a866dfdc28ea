<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

/**
 * Migration لإنشاء جدول الطلبات (Orders)
 *
 * بناءً على المواصفات في TS-FR.md (القسم DB-TBL-012)
 * يخدم MOD-ORDER-MGMT لتخزين معلومات طلبات شراء السيارات (كاش أو تمويل)
 */
class CreateOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            // DB-COL-O-001: المفتاح الأساسي
            $table->bigIncrements('id');

            // DB-COL-O-002: معرّف العميل الذي قدم الطلب
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('restrict')
                  ->onUpdate('cascade');
            $table->index('user_id');

            // DB-COL-O-003: معرّف السيارة المطلوبة
            $table->unsignedBigInteger('car_id');
            $table->foreign('car_id')
                  ->references('id')
                  ->on('cars')
                  ->onDelete('restrict')
                  ->onUpdate('cascade');
            $table->index('car_id');

            // DB-COL-O-004: رقم الطلب الفريد (يتم توليده)
            $table->string('order_number', 50)->unique();
            $table->index('order_number');

            // DB-COL-O-005: نوع الطلب
            $table->string('order_type', 50);
            $table->index('order_type');

            // DB-COL-O-006: حالة الطلب
            $table->string('status', 50)->default('pending_payment');
            $table->index('status');

            // DB-COL-O-007: سعر السيارة وقت الطلب
            $table->decimal('car_price_at_order', 12, 2);

            // DB-COL-O-008: مبلغ الحجز المطلوب/المدفوع
            $table->decimal('reservation_amount', 10, 2)->nullable();

            // DB-COL-O-009: المبلغ المتبقي للدفع في المعرض
            $table->decimal('remaining_amount', 12, 2)->nullable();

            // DB-COL-O-010: طريقة دفع الحجز
            $table->string('payment_method', 50)->nullable();

            // DB-COL-O-011: حالة دفع الحجز
            $table->string('payment_status', 50)->nullable();
            $table->index('payment_status');

            // DB-COL-O-012: معرّف معاملة الدفع من بوابة الدفع
            $table->string('payment_transaction_id', 255)->nullable();
            $table->index('payment_transaction_id');

            // DB-COL-O-013: رقم هوية/إقامة العميل (وقت الطلب)
            $table->string('customer_national_id', 20)->nullable();

            // DB-COL-O-014: تاريخ ميلاد العميل (وقت الطلب)
            $table->date('customer_dob')->nullable();

            // DB-COL-O-015: جنسية العميل (وقت الطلب)
            $table->unsignedBigInteger('customer_nationality_id')->nullable();
            $table->foreign('customer_nationality_id')
                  ->references('id')
                  ->on('nationalities')
                  ->onDelete('set null')
                  ->onUpdate('cascade');
            $table->index('customer_nationality_id');

            // DB-COL-O-016: تفاصيل عنوان العميل (وقت الطلب)
            $table->text('customer_address_details')->nullable();

            // DB-COL-O-017: ملاحظات إدارية على الطلب
            $table->text('admin_notes')->nullable();

            // DB-COL-O-018: تفاصيل طلب التمويل (JSON)
            $table->json('finance_details')->nullable();

            // DB-COL-O-019 & DB-COL-O-020: timestamps
            $table->timestamps();

            // DB-COL-O-021: الموظف المعين لمتابعة الطلب
            $table->unsignedBigInteger('assigned_employee_id')->nullable();
            $table->foreign('assigned_employee_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('set null')
                  ->onUpdate('cascade');
            $table->index('assigned_employee_id');

            // DB-COL-O-022: استجابة بوابة الدفع (JSON)
            $table->json('payment_gateway_response')->nullable();

            // إضافة soft deletes (ممارسة جيدة)
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
}
