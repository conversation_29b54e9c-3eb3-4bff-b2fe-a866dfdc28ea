@extends('dashboard::layouts.admin_layout')

@section('title', 'عرض السيارة: ' . $car->title)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        عرض السيارة: {{ $car->title }}
                    </h5>
                    <div>
                        <a href="{{ route('admin.cars.edit', $car) }}" class="btn btn-warning btn-sm me-2">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                        <a href="{{ route('admin.cars.index') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- معلومات أساسية -->
                        <div class="col-lg-8">
                            <div class="row">
                                <!-- البيانات الأساسية -->
                                <div class="col-12 mb-4">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        البيانات الأساسية
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <strong>العنوان:</strong><br>
                                            <span class="text-muted">{{ $car->title }}</span>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <strong>الماركة:</strong><br>
                                            <span class="text-muted">{{ $car->brand->name }}</span>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <strong>الموديل:</strong><br>
                                            <span class="text-muted">{{ $car->carModel->name }}</span>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <strong>سنة الصنع:</strong><br>
                                            <span class="text-muted">{{ $car->manufacturingYear->year }}</span>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <strong>اللون الخارجي:</strong><br>
                                            <span class="text-muted">{{ $car->mainColor->name }}</span>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <strong>لون المقصورة:</strong><br>
                                            <span class="text-muted">{{ $car->interiorColor->name ?? 'غير محدد' }}</span>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <strong>رقم الهيكل:</strong><br>
                                            <span class="text-muted">{{ $car->vin ?? 'غير محدد' }}</span>
                                        </div>
                                        @if($car->plate_number)
                                            <div class="col-md-6 mb-3">
                                                <strong>رقم اللوحة:</strong><br>
                                                <span class="text-muted">{{ $car->plate_number }}</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- المواصفات الفنية -->
                                <div class="col-12 mb-4">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-cogs me-2"></i>
                                        المواصفات الفنية
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <strong>نوع الهيكل:</strong><br>
                                            <span class="text-muted">{{ $car->bodyType->name }}</span>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <strong>ناقل الحركة:</strong><br>
                                            <span class="text-muted">{{ $car->transmissionType->name }}</span>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <strong>نوع الوقود:</strong><br>
                                            <span class="text-muted">{{ $car->fuelType->name }}</span>
                                        </div>
                                        @if($car->engine_capacity)
                                            <div class="col-md-4 mb-3">
                                                <strong>سعة المحرك:</strong><br>
                                                <span class="text-muted">{{ $car->engine_capacity }} لتر</span>
                                            </div>
                                        @endif
                                        @if($car->doors_count)
                                            <div class="col-md-4 mb-3">
                                                <strong>عدد الأبواب:</strong><br>
                                                <span class="text-muted">{{ $car->doors_count }}</span>
                                            </div>
                                        @endif
                                        @if($car->seats_count)
                                            <div class="col-md-4 mb-3">
                                                <strong>عدد المقاعد:</strong><br>
                                                <span class="text-muted">{{ $car->seats_count }}</span>
                                            </div>
                                        @endif
                                        @if($car->mileage)
                                            <div class="col-md-4 mb-3">
                                                <strong>المسافة المقطوعة:</strong><br>
                                                <span class="text-muted">{{ number_format($car->mileage) }} {{ $car->mileage_unit == 'km' ? 'كيلومتر' : 'ميل' }}</span>
                                            </div>
                                        @endif
                                        <div class="col-md-4 mb-3">
                                            <strong>حالة السيارة:</strong><br>
                                            <span class="text-muted">
                                                @switch($car->condition)
                                                    @case('new') جديدة @break
                                                    @case('used') مستعملة @break
                                                    @case('certified_pre_owned') مستعملة معتمدة @break
                                                    @default {{ $car->condition }}
                                                @endswitch
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- الوصف -->
                                @if($car->description)
                                    <div class="col-12 mb-4">
                                        <h6 class="text-primary border-bottom pb-2 mb-3">
                                            <i class="fas fa-align-left me-2"></i>
                                            وصف السيارة
                                        </h6>
                                        <div class="bg-light p-3 rounded">
                                            {!! nl2br(e($car->description)) !!}
                                        </div>
                                    </div>
                                @endif

                                <!-- الميزات -->
                                @if($car->features->count() > 0)
                                    <div class="col-12 mb-4">
                                        <h6 class="text-primary border-bottom pb-2 mb-3">
                                            <i class="fas fa-list-ul me-2"></i>
                                            ميزات السيارة
                                        </h6>
                                        <div class="row">
                                            @foreach($car->features->groupBy('featureCategory.name') as $categoryName => $features)
                                                <div class="col-md-6 mb-3">
                                                    <div class="card h-100">
                                                        <div class="card-header bg-light py-2">
                                                            <h6 class="mb-0 text-dark">{{ $categoryName }}</h6>
                                                        </div>
                                                        <div class="card-body py-2">
                                                            <ul class="list-unstyled mb-0">
                                                                @foreach($features as $feature)
                                                                    <li class="mb-1">
                                                                        <i class="fas fa-check text-success me-2"></i>
                                                                        {{ $feature->name }}
                                                                    </li>
                                                                @endforeach
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- الشريط الجانبي -->
                        <div class="col-lg-4">
                            <!-- السعر والحالة -->
                            <div class="card mb-4">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-tag me-2"></i>
                                        السعر والحالة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="text-center mb-3">
                                        @if($car->offer_price)
                                            <div class="text-decoration-line-through text-muted">
                                                {{ number_format($car->price) }} {{ $car->currency }}
                                            </div>
                                            <div class="h4 text-success mb-0">
                                                {{ number_format($car->offer_price) }} {{ $car->currency }}
                                            </div>
                                            <small class="text-success">
                                                خصم {{ number_format((($car->price - $car->offer_price) / $car->price) * 100, 1) }}%
                                            </small>
                                        @else
                                            <div class="h4 text-primary mb-0">
                                                {{ number_format($car->price) }} {{ $car->currency }}
                                            </div>
                                        @endif
                                    </div>

                                    <hr>

                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="mb-2">
                                                @if($car->is_featured)
                                                    <i class="fas fa-star text-warning fa-2x"></i>
                                                @else
                                                    <i class="far fa-star text-muted fa-2x"></i>
                                                @endif
                                            </div>
                                            <small class="text-muted">{{ $car->is_featured ? 'مميزة' : 'غير مميزة' }}</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="mb-2">
                                                @if($car->is_sold)
                                                    <i class="fas fa-check-circle text-danger fa-2x"></i>
                                                @else
                                                    <i class="fas fa-shopping-cart text-success fa-2x"></i>
                                                @endif
                                            </div>
                                            <small class="text-muted">{{ $car->is_sold ? 'مباعة' : 'متاحة' }}</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="mb-2">
                                                @if($car->is_active)
                                                    <i class="fas fa-eye text-success fa-2x"></i>
                                                @else
                                                    <i class="fas fa-eye-slash text-muted fa-2x"></i>
                                                @endif
                                            </div>
                                            <small class="text-muted">{{ $car->is_active ? 'نشطة' : 'غير نشطة' }}</small>
                                        </div>
                                    </div>

                                    @if($car->offer_start_date || $car->offer_end_date)
                                        <hr>
                                        <div class="text-center">
                                            <small class="text-muted">
                                                @if($car->offer_start_date && $car->offer_end_date)
                                                    فترة العرض: {{ $car->offer_start_date->format('d/m/Y') }} - {{ $car->offer_end_date->format('d/m/Y') }}
                                                @elseif($car->offer_start_date)
                                                    بداية العرض: {{ $car->offer_start_date->format('d/m/Y') }}
                                                @elseif($car->offer_end_date)
                                                    نهاية العرض: {{ $car->offer_end_date->format('d/m/Y') }}
                                                @endif
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- معلومات إضافية -->
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات إضافية
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>تاريخ الإضافة:</strong><br>
                                        <small class="text-muted">{{ $car->created_at->format('d/m/Y H:i') }}</small>
                                    </div>
                                    <div class="mb-2">
                                        <strong>آخر تحديث:</strong><br>
                                        <small class="text-muted">{{ $car->updated_at->format('d/m/Y H:i') }}</small>
                                    </div>
                                    @if($car->video_url)
                                        <div class="mb-2">
                                            <strong>رابط الفيديو:</strong><br>
                                            <a href="{{ $car->video_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-play me-1"></i>
                                                مشاهدة الفيديو
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صور السيارة -->
                    @if($car->getMedia('car_images')->count() > 0)
                        <div class="row mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-images me-2"></i>
                                    صور السيارة ({{ $car->getMedia('car_images')->count() }})
                                </h6>
                                <div class="row">
                                    @foreach($car->getMedia('car_images') as $media)
                                        <div class="col-md-3 mb-3">
                                            <div class="card">
                                                @php
                                                    $imageUrl = $media->getUrl('medium') ?: $media->getUrl();
                                                    $fullImageUrl = $media->getUrl('large') ?: $media->getUrl();
                                                @endphp
                                                <img src="{{ $imageUrl }}" class="card-img-top"
                                                     style="height: 200px; object-fit: cover; cursor: pointer;"
                                                     alt="صورة السيارة"
                                                     data-bs-toggle="modal"
                                                     data-bs-target="#imageModal"
                                                     data-image="{{ $fullImageUrl }}">
                                                <div class="card-body p-2">
                                                    <small class="text-muted">{{ $media->name }}</small>
                                                    @if($car->getFirstMedia('car_thumbnail') && $car->getFirstMedia('car_thumbnail')->id == $media->id)
                                                        <span class="badge bg-success ms-2">رئيسية</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض الصورة -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">صورة السيارة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="صورة السيارة">
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// عرض الصورة في المودال
document.addEventListener('click', function(e) {
    if (e.target.hasAttribute('data-image')) {
        document.getElementById('modalImage').src = e.target.getAttribute('data-image');
    }
});
</script>
@endpush
