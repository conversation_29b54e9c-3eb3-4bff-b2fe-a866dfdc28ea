{{-- حقل اسم اللون --}}
<div class="mb-3">
    <label for="name" class="form-label">اسم اللون <span class="text-danger">*</span></label>
    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $color->name ?? '') }}" required>
    @error('name')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>

{{-- حقل كود Hex --}}
<div class="mb-3">
    <label for="hex_code" class="form-label">كود اللون (Hex)</label>
    <div class="input-group">
        <input type="text" class="form-control @error('hex_code') is-invalid @enderror" id="hex_code" name="hex_code" 
               value="{{ old('hex_code', $color->hex_code ?? '') }}" placeholder="#FF0000" maxlength="7">
        <input type="color" class="form-control form-control-color" id="color_picker" title="اختر اللون">
    </div>
    <small class="form-text text-muted">مثال: #FF0000 للأحمر أو #00FF00 للأخضر (اختياري)</small>
    @error('hex_code')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>

{{-- حقل حالة اللون --}}
<div class="mb-3">
    <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
        <option value="1" {{ (old('status', isset($color) ? $color->status : 1) == 1) ? 'selected' : '' }}>نشط</option>
        <option value="0" {{ (old('status', isset($color) ? $color->status : '') == '0') ? 'selected' : '' }}>غير نشط</option>
    </select>
    @error('status')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>

{{-- معاينة اللون --}}
<div class="mb-3" id="color_preview_container" style="display: none;">
    <label class="form-label">معاينة اللون</label>
    <div id="color_preview" style="width: 50px; height: 50px; border: 1px solid #ddd; border-radius: 4px; background-color: #ffffff;"></div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const hexInput = document.getElementById('hex_code');
    const colorPicker = document.getElementById('color_picker');
    const colorPreview = document.getElementById('color_preview');
    const previewContainer = document.getElementById('color_preview_container');

    // تحديث معاينة اللون
    function updateColorPreview(color) {
        if (color && color.match(/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/)) {
            colorPreview.style.backgroundColor = color;
            previewContainer.style.display = 'block';
        } else {
            previewContainer.style.display = 'none';
        }
    }

    // تحديث حقل النص عند تغيير color picker
    colorPicker.addEventListener('change', function() {
        hexInput.value = this.value.toUpperCase();
        updateColorPreview(this.value);
    });

    // تحديث color picker عند تغيير حقل النص
    hexInput.addEventListener('input', function() {
        const value = this.value;
        if (value.match(/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/)) {
            colorPicker.value = value;
        }
        updateColorPreview(value);
    });

    // تحديث المعاينة عند تحميل الصفحة إذا كان هناك قيمة موجودة
    if (hexInput.value) {
        updateColorPreview(hexInput.value);
        if (hexInput.value.match(/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/)) {
            colorPicker.value = hexInput.value;
        }
    }
});
</script>
@endpush
