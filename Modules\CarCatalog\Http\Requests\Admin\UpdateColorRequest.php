<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تحديث لون موجود.
 *
 * يتحقق هذا الطلب من صحة بيانات تعديل لون موجود
 */
class UpdateColorRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:50',
                Rule::unique('colors')->ignore($this->color->id),
            ],
            'hex_code' => [
                'nullable',
                'string',
                'max:7',
                'regex:/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/',
                Rule::unique('colors')->ignore($this->color->id),
            ],
            'status' => 'required|boolean',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة لقواعد التحقق
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required'   => 'اسم اللون مطلوب.',
            'name.string'     => 'اسم اللون يجب أن يكون نصاً.',
            'name.max'        => 'اسم اللون يجب ألا يتجاوز 50 حرفاً.',
            'name.unique'     => 'اسم اللون موجود مسبقاً.',
            'hex_code.string' => 'كود اللون يجب أن يكون نصاً.',
            'hex_code.max'    => 'كود اللون يجب ألا يتجاوز 7 أحرف.',
            'hex_code.regex'  => 'كود اللون يجب أن يكون بصيغة صحيحة (مثال: #FF0000 أو #F00).',
            'hex_code.unique' => 'كود اللون موجود مسبقاً.',
            'status.required' => 'حالة اللون مطلوبة.',
            'status.boolean'  => 'حالة اللون يجب أن تكون صحيحة أو خاطئة.',
        ];
    }

    /**
     * الحصول على أسماء الحقول المخصصة.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name'     => 'اسم اللون',
            'hex_code' => 'كود اللون',
            'status'   => 'الحالة',
        ];
    }
}
