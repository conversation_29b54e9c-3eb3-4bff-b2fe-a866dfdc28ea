# Car Favorites & Compare Implementation - CarCatalog Module

## Overview
تم تنفيذ منطق الـ Backend لإضافة وإزالة السيارات من قائمة مفضلة المستخدم ومنطق مقارنة السيارات في الموقع العام.

## Features Implemented

### 1. Car Favorites System

#### Database Structure
- **Migration**: `2025_05_23_214600_create_user_favorites_table.php`
- **Table**: `user_favorites`
- **Columns**:
  - `id` (Primary Key)
  - `user_id` (Foreign Key to users table)
  - `car_id` (Foreign Key to cars table)
  - `created_at`, `updated_at`
  - **Unique Index**: `(user_id, car_id)` - منع التكرار

#### Model Relationships
- **User Model**: إضافة علاقة `favorites()` - Many-to-Many مع Car
- **Car Model**: إضافة علاقة `favoritedByUsers()` - Many-to-Many مع User

#### FavoriteController
- **Location**: `Modules/CarCatalog/Http/Controllers/Site/FavoriteController.php`
- **Methods**:
  - `add(Car $car)` - إضافة سيارة للمفضلة
  - `remove(Car $car)` - إزالة سيارة من المفضلة
  - `toggle(Car $car)` - تبديل حالة السيارة (إضافة/إزالة)
  - `count()` - عدد السيارات في المفضلة
  - `status(Car $car)` - التحقق من حالة سيارة معينة

#### Security & Validation
- التحقق من تسجيل دخول المستخدم (`Auth::check()`)
- التحقق من توفر السيارة (`is_sold = false`, `is_active = true`)
- استخدام `syncWithoutDetaching()` لتجنب التكرار
- إرجاع استجابات JSON مناسبة مع رسائل الخطأ

### 2. Car Compare System

#### Session-Based Storage
- **Storage**: Laravel Session
- **Session Key**: `car_compare_list`
- **Max Items**: 4 سيارات
- **Min Items**: 2 سيارات للمقارنة

#### CompareController
- **Location**: `Modules/CarCatalog/Http/Controllers/Site/CompareController.php`
- **Methods**:
  - `add(Car $car)` - إضافة سيارة للمقارنة
  - `remove(Car $car)` - إزالة سيارة من المقارنة
  - `clear()` - مسح سلة المقارنة
  - `index()` - عرض صفحة المقارنة
  - `count()` - عدد السيارات في المقارنة
  - `status(Car $car)` - التحقق من حالة سيارة معينة
  - `list()` - قائمة معرفات السيارات في المقارنة

#### Features
- لا يتطلب تسجيل دخول (Session-based)
- حد أقصى 4 سيارات، حد أدنى 2 سيارات
- التحقق من توفر السيارة
- ترتيب السيارات حسب ترتيب الإضافة
- منع إضافة نفس السيارة مرتين

### 3. Routes Configuration

#### Favorites Routes
```php
Route::prefix('favorites')->name('site.favorites.')->middleware(['web'])->group(function () {
    Route::post('/add/{car}', [FavoriteController::class, 'add'])->name('add');
    Route::delete('/remove/{car}', [FavoriteController::class, 'remove'])->name('remove');
    Route::post('/toggle/{car}', [FavoriteController::class, 'toggle'])->name('toggle');
    Route::get('/count', [FavoriteController::class, 'count'])->name('count');
    Route::get('/status/{car}', [FavoriteController::class, 'status'])->name('status');
});
```

#### Compare Routes
```php
Route::prefix('compare')->name('site.compare.')->middleware(['web'])->group(function () {
    Route::get('/', [CompareController::class, 'index'])->name('index');
    Route::post('/add/{car}', [CompareController::class, 'add'])->name('add');
    Route::delete('/remove/{car}', [CompareController::class, 'remove'])->name('remove');
    Route::delete('/clear', [CompareController::class, 'clear'])->name('clear');
    Route::get('/count', [CompareController::class, 'count'])->name('count');
    Route::get('/status/{car}', [CompareController::class, 'status'])->name('status');
    Route::get('/list', [CompareController::class, 'list'])->name('list');
});
```

## API Endpoints

### Favorites API
- `POST /favorites/add/{car}` - إضافة للمفضلة
- `DELETE /favorites/remove/{car}` - إزالة من المفضلة
- `POST /favorites/toggle/{car}` - تبديل الحالة
- `GET /favorites/count` - عدد المفضلة
- `GET /favorites/status/{car}` - حالة سيارة معينة

### Compare API
- `POST /compare/add/{car}` - إضافة للمقارنة
- `DELETE /compare/remove/{car}` - إزالة من المقارنة
- `DELETE /compare/clear` - مسح السلة
- `GET /compare/count` - عدد المقارنة
- `GET /compare/status/{car}` - حالة سيارة معينة
- `GET /compare/list` - قائمة السيارات

## JSON Response Format

### Success Response
```json
{
    "success": true,
    "message": "تم إضافة السيارة إلى المفضلة بنجاح",
    "is_favorite": true,
    "favorites_count": 5
}
```

### Error Response
```json
{
    "success": false,
    "message": "يجب تسجيل الدخول أولاً لإضافة السيارات للمفضلة",
    "redirect": "/login"
}
```

## Usage Examples

### Frontend JavaScript (AJAX)
```javascript
// إضافة للمفضلة
fetch('/favorites/toggle/123', {
    method: 'POST',
    headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        // تحديث الواجهة
        updateFavoriteButton(data.is_favorite);
        updateFavoriteCount(data.favorites_count);
    }
});

// إضافة للمقارنة
fetch('/compare/add/123', {
    method: 'POST',
    headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        updateCompareCount(data.compare_count);
        toggleCompareButton(data.can_compare);
    }
});
```

## Database Migration
لتطبيق التغييرات على قاعدة البيانات:
```bash
php artisan migrate
```

## Dependencies
- **Laravel Framework**: 10.x
- **spatie/laravel-permission**: للأدوار والصلاحيات
- **nwidart/laravel-modules**: لتنظيم الموديولات

## Next Steps
1. إنشاء واجهات Blade لصفحة المفضلة وصفحة المقارنة
2. تطبيق JavaScript للتفاعل مع الـ APIs
3. إضافة أزرار المفضلة والمقارنة في قوائم السيارات
4. تطبيق التصميم المطلوب للواجهات

## Task Status
- ✅ **PH03-TASK-017**: تنفيذ منطق المفضلة - مكتمل
- ✅ **PH03-TASK-018**: تنفيذ منطق المقارنة - مكتمل
