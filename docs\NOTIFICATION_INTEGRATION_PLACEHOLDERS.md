# دليل أماكن تكامل الإشعارات - PH02-TASK-025

**تاريخ الإنشاء:** {{ date('Y-m-d H:i:s') }}  
**المهمة:** `PH02-TASK-025-DASH-NOTIFICATION-INTEGRATION-PLACEHOLDERS-001`  
**المستوى:** منخفض  
**النوع:** إعداد للميزات المستقبلية

## نظرة عامة

تم تحديد وتوثيق جميع الأماكن في وحدات التحكم التي تم إنشاؤها في المرحلة الثانية (PH-02) والتي ستحتاج إلى إشعارات في المستقبل. تم إضافة placeholders معلقة (commented out) في الأماكن المناسبة لتسهيل التكامل المستقبلي.

## 📊 إحصائيات أماكن الإشعارات

| الموديول | عدد الأماكن | نوع الإشعارات | الأولوية |
|----------|-------------|---------------|----------|
| **CarCatalog** | 4 أماكن | إدارة السيارات والبيانات الوصفية | عالية |
| **UserManagement** | 3 أماكن | إدارة الأدوار والصلاحيات | عالية |
| **Dashboard** | 3 أماكن | إعدادات النظام والتنبيهات | متوسطة |
| **المجموع** | **10 أماكن** | **متنوعة** | **متنوعة** |

## 🚗 CarCatalog Module

### 1. **CarController** - إدارة السيارات
**الملف:** `Modules/CarCatalog/Http/Controllers/Admin/CarController.php`

#### أ. إنشاء سيارة جديدة (store method)
```php
// TODO: PH02-TASK-025 - إرسال إشعار عند إضافة سيارة جديدة
// NotificationService::send([
//     'type' => 'car_created',
//     'title' => 'تم إضافة سيارة جديدة',
//     'message' => "تم إضافة السيارة {$car->title} بنجاح",
//     'data' => ['car_id' => $car->id, 'car_title' => $car->title],
//     'recipients' => ['admin', 'inventory_manager'],
//     'channels' => ['database', 'email']
// ]);
```

#### ب. تحديث السيارة (update method)
```php
// TODO: PH02-TASK-025 - إرسال إشعار عند تحديث السيارة
// $statusChanged = $car->wasChanged(['is_active', 'is_sold', 'is_featured']);
// if ($statusChanged) {
//     NotificationService::send([
//         'type' => 'car_status_changed',
//         'title' => 'تم تغيير حالة السيارة',
//         'message' => "تم تحديث حالة السيارة {$car->title}",
//         'data' => ['car_id' => $car->id, 'car_title' => $car->title, 'changes' => $car->getChanges()],
//         'recipients' => ['admin', 'sales_manager'],
//         'channels' => ['database', 'push']
//     ]);
// }
```

#### ج. حذف السيارة (destroy method)
```php
// TODO: PH02-TASK-025 - إرسال إشعار عند حذف السيارة
// NotificationService::send([
//     'type' => 'car_deleted',
//     'title' => 'تم حذف سيارة',
//     'message' => "تم حذف السيارة {$car->title} من النظام",
//     'data' => ['car_id' => $car->id, 'car_title' => $car->title, 'deleted_by' => auth()->user()->name],
//     'recipients' => ['admin', 'inventory_manager'],
//     'channels' => ['database', 'email'],
//     'priority' => 'high'
// ]);
```

### 2. **BrandController** - إدارة الماركات
**الملف:** `Modules/CarCatalog/Http/Controllers/Admin/BrandController.php`

#### حذف ماركة (destroy method)
```php
// TODO: PH02-TASK-025 - إرسال إشعار عند حذف ماركة
// NotificationService::send([
//     'type' => 'brand_deleted',
//     'title' => 'تم حذف ماركة',
//     'message' => "تم حذف الماركة '{$brandName}' من النظام",
//     'data' => ['brand_name' => $brandName, 'deleted_by' => auth()->user()->name],
//     'recipients' => ['admin', 'inventory_manager'],
//     'channels' => ['database'],
//     'priority' => 'low'
// ]);
```

## 👥 UserManagement Module

### **RoleController** - إدارة الأدوار
**الملف:** `Modules/UserManagement/Http/Controllers/Admin/RoleController.php`

#### أ. إنشاء دور جديد (store method)
```php
// TODO: PH02-TASK-025 - إرسال إشعار عند إنشاء دور جديد
// NotificationService::send([
//     'type' => 'role_created',
//     'title' => 'تم إنشاء دور جديد',
//     'message' => "تم إنشاء الدور '{$role->name}' بنجاح",
//     'data' => ['role_id' => $role->id, 'role_name' => $role->name, 'permissions_count' => count($request->input('permissions', []))],
//     'recipients' => ['super_admin'],
//     'channels' => ['database', 'email'],
//     'priority' => 'medium'
// ]);
```

#### ب. تحديث دور (update method)
```php
// TODO: PH02-TASK-025 - إرسال إشعار عند تحديث دور
// $permissionsChanged = array_diff($oldPermissions, $newPermissions) || array_diff($newPermissions, $oldPermissions);
// if ($permissionsChanged || $role->wasChanged('name')) {
//     NotificationService::send([
//         'type' => 'role_updated',
//         'title' => 'تم تحديث دور',
//         'message' => "تم تحديث الدور '{$role->name}' وصلاحياته",
//         'data' => [
//             'role_id' => $role->id,
//             'role_name' => $role->name,
//             'old_permissions' => $oldPermissions,
//             'new_permissions' => $newPermissions,
//             'updated_by' => auth()->user()->name
//         ],
//         'recipients' => ['super_admin'],
//         'channels' => ['database', 'email'],
//         'priority' => 'high'
//     ]);
// }
```

#### ج. حذف دور (destroy method)
```php
// TODO: PH02-TASK-025 - إرسال إشعار عند حذف دور
// NotificationService::send([
//     'type' => 'role_deleted',
//     'title' => 'تم حذف دور',
//     'message' => "تم حذف الدور '{$roleName}' من النظام",
//     'data' => ['role_name' => $roleName, 'deleted_by' => auth()->user()->name],
//     'recipients' => ['super_admin'],
//     'channels' => ['database', 'email'],
//     'priority' => 'high'
// ]);
```

## 🏠 Dashboard Module

### 1. **SystemSettingsController** - إعدادات النظام
**الملف:** `Modules/Dashboard/Http/Controllers/Admin/SystemSettingsController.php`

#### تحديث إعدادات النظام الحرجة (update method)
```php
// TODO: PH02-TASK-025 - إرسال إشعار عند تحديث إعدادات النظام الحرجة
// if (!empty($changedSettings)) {
//     $criticalSettings = ['site_name', 'admin_email', 'vat_percentage', 'default_currency'];
//     $criticalChanges = array_intersect_key($changedSettings, array_flip($criticalSettings));
//     
//     if (!empty($criticalChanges)) {
//         NotificationService::send([
//             'type' => 'system_settings_updated',
//             'title' => 'تم تحديث إعدادات النظام الحرجة',
//             'message' => 'تم تحديث إعدادات مهمة في النظام',
//             'data' => [
//                 'changed_settings' => $criticalChanges,
//                 'updated_by' => auth()->user()->name,
//                 'ip_address' => request()->ip()
//             ],
//             'recipients' => ['super_admin'],
//             'channels' => ['database', 'email'],
//             'priority' => 'high'
//         ]);
//     }
// }
```

### 2. **DashboardDataService** - التنبيهات التلقائية
**الملف:** `Modules/Dashboard/Services/DashboardDataService.php`

#### أ. تنبيه طلبات التمويل المعلقة (getAlerts method)
```php
// TODO: PH02-TASK-025 - إرسال إشعار تلقائي عند تجاوز حد طلبات التمويل المعلقة
// NotificationService::sendAutoAlert([
//     'type' => 'pending_finance_requests_high',
//     'title' => 'تحذير: طلبات تمويل معلقة كثيرة',
//     'message' => "يوجد {$pendingFinanceCount} طلب تمويل معلق يحتاج للمراجعة العاجلة",
//     'data' => ['count' => $pendingFinanceCount, 'threshold' => 10],
//     'recipients' => ['admin', 'finance_manager'],
//     'channels' => ['database', 'email', 'push'],
//     'priority' => 'high',
//     'auto_repeat' => false // لتجنب الإرسال المتكرر
// ]);
```

#### ب. تنبيه انخفاض مخزون السيارات (getAlerts method)
```php
// TODO: PH02-TASK-025 - إرسال إشعار تلقائي عند انخفاض مخزون السيارات
// NotificationService::sendAutoAlert([
//     'type' => 'low_car_inventory',
//     'title' => 'تحذير: مخزون السيارات منخفض',
//     'message' => "المخزون المتاح من السيارات منخفض ({$lowStockCars} سيارة فقط)",
//     'data' => ['available_cars' => $lowStockCars, 'threshold' => 10],
//     'recipients' => ['admin', 'inventory_manager', 'sales_manager'],
//     'channels' => ['database', 'email'],
//     'priority' => 'medium',
//     'auto_repeat' => 'daily' // إرسال يومي حتى يتم حل المشكلة
// ]);
```

## 📋 أنواع الإشعارات المحددة

### 1. **إشعارات إدارة السيارات:**
- `car_created` - إضافة سيارة جديدة
- `car_status_changed` - تغيير حالة السيارة
- `car_deleted` - حذف سيارة
- `brand_deleted` - حذف ماركة

### 2. **إشعارات إدارة الأدوار:**
- `role_created` - إنشاء دور جديد
- `role_updated` - تحديث دور وصلاحياته
- `role_deleted` - حذف دور

### 3. **إشعارات النظام:**
- `system_settings_updated` - تحديث إعدادات النظام الحرجة
- `pending_finance_requests_high` - طلبات تمويل معلقة كثيرة
- `low_car_inventory` - مخزون السيارات منخفض

## 🎯 المستقبلين المحددين

### **أدوار المستقبلين:**
- `super_admin` - المدير العام
- `admin` - الإدارة
- `inventory_manager` - مدير المخزون
- `sales_manager` - مدير المبيعات
- `finance_manager` - مدير الشؤون المالية

### **قنوات الإرسال:**
- `database` - حفظ في قاعدة البيانات
- `email` - إرسال بريد إلكتروني
- `push` - إشعارات فورية
- `sms` - رسائل نصية (مستقبلي)

## 🔧 هيكل NotificationService المقترح

```php
class NotificationService
{
    public static function send(array $config): void
    {
        // تنفيذ إرسال الإشعار العادي
    }
    
    public static function sendAutoAlert(array $config): void
    {
        // تنفيذ إرسال التنبيهات التلقائية مع منع التكرار
    }
}
```

## 📝 ملاحظات للتطوير المستقبلي

1. **إنشاء NotificationService** - خدمة موحدة لإدارة الإشعارات
2. **تنفيذ قنوات الإرسال** - database, email, push, sms
3. **إدارة المستقبلين** - نظام لتحديد من يستقبل كل نوع إشعار
4. **منع التكرار** - للتنبيهات التلقائية
5. **أولويات الإشعارات** - high, medium, low
6. **قوالب الإشعارات** - قوالب قابلة للتخصيص
7. **إعدادات المستخدم** - تفضيلات الإشعارات لكل مستخدم

## ✅ الخلاصة

تم تحديد **10 أماكن** في **3 موديولات** تحتاج إلى إشعارات مستقبلية. جميع الأماكن موثقة ومعلقة بـ placeholders جاهزة للتنفيذ عند إنشاء نظام الإشعارات الكامل.
