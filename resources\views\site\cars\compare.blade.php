{{--
    صفحة مقارنة السيارات - الموقع العام
    تعرض جدول مقارنة تفاعلي للسيارات المختارة
    مستوحاة من UIUX-FR.md و REQ-FR.md (MOD-CAR-CATALOG-FEAT-006)
--}}

@extends('site.layouts.site_layout')

@section('title', 'مقارنة السيارات - موتور لاين')

@section('meta_description', 'قارن بين السيارات المختلفة واختر الأنسب لك من حيث المواصفات والأسعار والميزات')

@section('meta_keywords', 'مقارنة سيارات، مقارنة مواصفات، اختيار سيارة، موتور لاين')

@push('styles')
<style>
/* أنماط خاصة بصفحة مقارنة السيارات */
.compare-page {
    padding: 2rem 0;
    background: var(--light-bg);
}

.page-header {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.page-title {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: var(--text-muted);
    font-size: 1.1rem;
}

/* جدول المقارنة */
.comparison-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.comparison-table {
    width: 100%;
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.comparison-table th,
.comparison-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: top;
}

.comparison-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.comparison-table .spec-label {
    background: var(--light-bg);
    font-weight: 600;
    color: var(--primary-color);
    border-left: 1px solid var(--border-color);
    min-width: 200px;
    position: sticky;
    right: 0;
    z-index: 5;
}

.comparison-table .car-column {
    text-align: center;
    border-left: 1px solid var(--border-color);
    min-width: 250px;
}

/* بطاقة السيارة في الجدول */
.car-card-header {
    padding: 1.5rem;
    text-align: center;
}

.car-image {
    width: 150px;
    height: 100px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.car-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.car-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: 1rem;
}

.car-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 6px;
}

.btn-remove {
    background: var(--danger-color);
    color: white;
    border: none;
}

.btn-remove:hover {
    background: #dc2626;
}

.btn-order {
    background: var(--accent-color);
    color: white;
    border: none;
}

.btn-order:hover {
    background: #e85d04;
}

/* قيم المواصفات */
.spec-value {
    font-weight: 500;
}

.spec-value.highlight {
    background: var(--accent-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
}

.spec-value.not-available {
    color: var(--text-muted);
    font-style: italic;
}

/* حالة الفراغ */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 4rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.empty-message {
    color: var(--text-muted);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.btn-browse {
    background: var(--primary-color);
    color: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-browse:hover {
    background: #1e40af;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* أدوات التحكم */
.compare-controls {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.compare-info {
    color: var(--text-muted);
}

.compare-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-clear-all {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
}

.btn-clear-all:hover {
    background: #dc2626;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .comparison-table-container {
        overflow-x: auto;
    }

    .car-actions {
        flex-direction: column;
    }

    .compare-controls {
        flex-direction: column;
        text-align: center;
    }

    .compare-actions {
        justify-content: center;
    }
}
</style>
@endpush

@section('content')
<div class="compare-page">
    <div class="container">
        {{-- رأس الصفحة --}}
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-balance-scale me-2"></i>
                مقارنة السيارات
            </h1>
            <p class="page-subtitle">
                قارن بين السيارات المختلفة واختر الأنسب لك من حيث المواصفات والأسعار والميزات
            </p>
        </div>

        @if($cars && $cars->count() >= 2)
            {{-- أدوات التحكم --}}
            <div class="compare-controls">
                <div class="compare-info">
                    <i class="fas fa-info-circle me-1"></i>
                    تتم مقارنة {{ $cars->count() }} سيارات
                </div>
                <div class="compare-actions">
                    <button class="btn btn-clear-all" onclick="clearAllComparison()">
                        <i class="fas fa-trash me-1"></i>
                        مسح جميع السيارات
                    </button>
                    <a href="{{ route('site.cars.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة المزيد
                    </a>
                </div>
            </div>

            {{-- جدول المقارنة --}}
            <div class="comparison-table-container">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th class="spec-label">المواصفات</th>
                            @foreach($cars as $car)
                            <th class="car-column">
                                <div class="car-card-header">
                                    @php
                                        $carImage = $car->getFirstMedia('car_main_image') ?: $car->getFirstMedia('car_images');
                                    @endphp
                                    <img src="{{ $carImage ? $carImage->getUrl('medium') : asset('images/no-car-image.jpg') }}"
                                         alt="{{ $car->title }}" class="car-image">

                                    <div class="car-name">{{ $car->title }}</div>
                                    <div class="car-price">
                                        @if($car->offer_price && $car->offer_start_date <= now() && $car->offer_end_date >= now())
                                            {{ format_currency($car->offer_price) }}
                                            <small class="text-muted d-block">
                                                <del>{{ format_currency($car->price) }}</del>
                                            </small>
                                        @else
                                            {{ format_currency($car->price) }}
                                        @endif
                                    </div>

                                    <div class="car-actions">
                                        <button class="btn btn-remove btn-sm" onclick="removeFromCompare({{ $car->id }})">
                                            <i class="fas fa-times me-1"></i>
                                            إزالة
                                        </button>
                                        <a href="{{ route('site.cars.show', $car->id) }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض
                                        </a>
                                        <button class="btn btn-order btn-sm" onclick="orderCar({{ $car->id }})">
                                            <i class="fas fa-shopping-cart me-1"></i>
                                            اطلب
                                        </button>
                                    </div>
                                </div>
                            </th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody>
                        {{-- معلومات أساسية --}}
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-info-circle me-2"></i>
                                الماركة
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                <span class="spec-value">{{ $car->brand->name }}</span>
                            </td>
                            @endforeach
                        </tr>

                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-car me-2"></i>
                                الموديل
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                <span class="spec-value">{{ $car->carModel->name }}</span>
                            </td>
                            @endforeach
                        </tr>

                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-calendar me-2"></i>
                                سنة الصنع
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                <span class="spec-value">{{ $car->manufacturingYear->year }}</span>
                            </td>
                            @endforeach
                        </tr>

                        {{-- المحرك والأداء --}}
                        @if($cars->whereNotNull('engine_capacity')->count() > 0)
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                سعة المحرك
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                @if($car->engine_capacity)
                                    <span class="spec-value">{{ format_number($car->engine_capacity) }} سي سي</span>
                                @else
                                    <span class="spec-value not-available">غير محدد</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endif

                        @if($cars->whereNotNull('transmission_type_id')->count() > 0)
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-cogs me-2"></i>
                                ناقل الحركة
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                @if($car->transmissionType)
                                    <span class="spec-value">{{ $car->transmissionType->name }}</span>
                                @else
                                    <span class="spec-value not-available">غير محدد</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endif

                        @if($cars->whereNotNull('fuel_type_id')->count() > 0)
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-gas-pump me-2"></i>
                                نوع الوقود
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                @if($car->fuelType)
                                    <span class="spec-value">{{ $car->fuelType->name }}</span>
                                @else
                                    <span class="spec-value not-available">غير محدد</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endif

                        {{-- التصميم والأبعاد --}}
                        @if($cars->whereNotNull('body_type_id')->count() > 0)
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-car-side me-2"></i>
                                نوع الهيكل
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                @if($car->bodyType)
                                    <span class="spec-value">{{ $car->bodyType->name }}</span>
                                @else
                                    <span class="spec-value not-available">غير محدد</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endif

                        @if($cars->whereNotNull('doors_count')->count() > 0)
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-door-open me-2"></i>
                                عدد الأبواب
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                @if($car->doors_count)
                                    <span class="spec-value">{{ $car->doors_count }}</span>
                                @else
                                    <span class="spec-value not-available">غير محدد</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endif

                        @if($cars->whereNotNull('seats_count')->count() > 0)
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-chair me-2"></i>
                                عدد المقاعد
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                @if($car->seats_count)
                                    <span class="spec-value">{{ $car->seats_count }}</span>
                                @else
                                    <span class="spec-value not-available">غير محدد</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endif

                        {{-- الألوان --}}
                        @if($cars->whereNotNull('main_color_id')->count() > 0)
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-palette me-2"></i>
                                اللون الخارجي
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                @if($car->mainColor)
                                    <span class="spec-value">{{ $car->mainColor->name }}</span>
                                @else
                                    <span class="spec-value not-available">غير محدد</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endif

                        @if($cars->whereNotNull('interior_color_id')->count() > 0)
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-couch me-2"></i>
                                لون المقصورة
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                @if($car->interiorColor)
                                    <span class="spec-value">{{ $car->interiorColor->name }}</span>
                                @else
                                    <span class="spec-value not-available">غير محدد</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endif

                        {{-- معلومات إضافية --}}
                        @if($cars->whereNotNull('mileage')->count() > 0)
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-road me-2"></i>
                                المسافة المقطوعة
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                @if($car->mileage)
                                    <span class="spec-value">{{ format_number($car->mileage) }} {{ $car->mileage_unit ?? 'كم' }}</span>
                                @else
                                    <span class="spec-value not-available">غير محدد</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endif

                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-certificate me-2"></i>
                                الحالة
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                <span class="spec-value">{{ $car->condition === 'new' ? 'جديدة' : 'مستعملة' }}</span>
                            </td>
                            @endforeach
                        </tr>

                        {{-- الميزات الرئيسية --}}
                        @if($cars->flatMap->features->count() > 0)
                        <tr>
                            <td class="spec-label">
                                <i class="fas fa-star me-2"></i>
                                أبرز الميزات
                            </td>
                            @foreach($cars as $car)
                            <td class="car-column">
                                @if($car->features->count() > 0)
                                    <div class="features-list">
                                        @foreach($car->features->take(5) as $feature)
                                            <small class="d-block text-success">
                                                <i class="fas fa-check me-1"></i>
                                                {{ $feature->name }}
                                            </small>
                                        @endforeach
                                        @if($car->features->count() > 5)
                                            <small class="text-muted">
                                                و {{ $car->features->count() - 5 }} ميزة أخرى
                                            </small>
                                        @endif
                                    </div>
                                @else
                                    <span class="spec-value not-available">لا توجد ميزات محددة</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>

        @else
            {{-- حالة الفراغ --}}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <h2 class="empty-title">{{ $message ?? 'لا توجد سيارات للمقارنة' }}</h2>
                <p class="empty-message">
                    @if($cars && $cars->count() === 1)
                        لديك سيارة واحدة فقط في المقارنة. أضف سيارة أخرى على الأقل لبدء المقارنة.
                    @else
                        ابدأ بإضافة السيارات التي تريد مقارنتها من صفحة السيارات.
                    @endif
                </p>
                <a href="{{ route('site.cars.index') }}" class="btn-browse">
                    <i class="fas fa-car me-2"></i>
                    تصفح السيارات
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
// وظائف JavaScript لإدارة المقارنة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث عداد المقارنة في الـ header إذا كان موجوداً
    updateCompareCounter();
});

// إزالة سيارة من المقارنة
function removeFromCompare(carId) {
    if (confirm('هل أنت متأكد من إزالة هذه السيارة من المقارنة؟')) {
        fetch(`/compare/remove/${carId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إعادة تحميل الصفحة لتحديث الجدول
                window.location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء إزالة السيارة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إزالة السيارة');
        });
    }
}

// مسح جميع السيارات من المقارنة
function clearAllComparison() {
    if (confirm('هل أنت متأكد من مسح جميع السيارات من المقارنة؟')) {
        fetch('/compare/clear', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء مسح المقارنة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء مسح المقارنة');
        });
    }
}

// طلب سيارة
function orderCar(carId) {
    // توجيه المستخدم لصفحة تفاصيل السيارة
    window.location.href = `/cars/${carId}`;
}

// تحديث عداد المقارنة
function updateCompareCounter() {
    fetch('/compare/count')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث العداد في الـ header إذا كان موجوداً
                const counter = document.querySelector('.compare-counter');
                if (counter) {
                    counter.textContent = data.compare_count;
                    counter.style.display = data.compare_count > 0 ? 'inline' : 'none';
                }
            }
        })
        .catch(error => {
            console.error('Error updating compare counter:', error);
        });
}
</script>
@endpush
