<?php

namespace Modules\UserManagement\Http\Requests\Site;

use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تسجيل دخول مستخدم من الموقع العام
 *
 * يتحقق هذا الطلب من صحة بيانات تسجيل الدخول للعملاء
 * ويطبق جميع قواعد التحقق المطلوبة حسب المتطلبات
 */
class LoginRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب
     *
     * @return array
     */
    public function rules()
    {
        return [
            'identifier' => [
                'required',
                'string',
                'max:255'
            ],
            'password' => [
                'required',
                'string'
            ],
            'remember' => [
                'nullable',
                'boolean'
            ]
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة للتحقق من صحة البيانات
     *
     * @return array
     */
    public function messages()
    {
        return [
            'identifier.required' => 'معرف تسجيل الدخول مطلوب.',
            'identifier.string' => 'معرف تسجيل الدخول يجب أن يكون نصاً.',
            'identifier.max' => 'معرف تسجيل الدخول يجب ألا يتجاوز 255 حرفاً.',
            
            'password.required' => 'كلمة المرور مطلوبة.',
            'password.string' => 'كلمة المرور يجب أن تكون نصاً.',
            
            'remember.boolean' => 'خيار تذكرني يجب أن يكون قيمة منطقية.'
        ];
    }

    /**
     * الحصول على أسماء الخصائص المخصصة للتحقق من صحة البيانات
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'identifier' => 'معرف تسجيل الدخول',
            'password' => 'كلمة المرور',
            'remember' => 'تذكرني'
        ];
    }

    /**
     * تحضير البيانات للتحقق
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // تنظيف معرف تسجيل الدخول من المسافات الزائدة
        if ($this->has('identifier')) {
            $this->merge([
                'identifier' => trim($this->identifier)
            ]);
        }

        // تحويل remember إلى boolean إذا كان موجوداً
        if ($this->has('remember')) {
            $this->merge([
                'remember' => $this->boolean('remember')
            ]);
        }
    }

    /**
     * تحديد ما إذا كان معرف تسجيل الدخول بريد إلكتروني أم رقم جوال
     *
     * @return string 'email' أو 'phone' أو 'invalid'
     */
    public function getIdentifierType()
    {
        $identifier = $this->identifier;

        // التحقق من تنسيق البريد الإلكتروني
        if (filter_var($identifier, FILTER_VALIDATE_EMAIL)) {
            return 'email';
        }

        // التحقق من تنسيق رقم الجوال السعودي (05xxxxxxxx)
        if (preg_match('/^05\d{8}$/', $identifier)) {
            return 'phone';
        }

        return 'invalid';
    }

    /**
     * الحصول على معرف تسجيل الدخول منظفاً
     *
     * @return string
     */
    public function getCleanIdentifier()
    {
        $identifier = trim($this->identifier);
        
        // إذا كان بريد إلكتروني، تحويله إلى أحرف صغيرة
        if ($this->getIdentifierType() === 'email') {
            return strtolower($identifier);
        }

        return $identifier;
    }
}
