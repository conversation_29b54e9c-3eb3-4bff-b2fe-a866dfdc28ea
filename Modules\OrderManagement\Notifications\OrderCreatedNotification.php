<?php

namespace Modules\OrderManagement\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\OrderManagement\Models\Order;

/**
 * إشعار إنشاء طلب جديد للعميل
 * 
 * يتم إرساله للعميل عند تقديم طلب شراء سيارة بنجاح
 */
class OrderCreatedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $order;

    /**
     * إنشاء instance جديد من الإشعار
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * تحديد قنوات الإرسال
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * تكوين رسالة البريد الإلكتروني
     */
    public function toMail($notifiable): MailMessage
    {
        $orderUrl = route('customer.orders.show', $this->order->id);
        
        return (new MailMessage)
                    ->subject('تأكيد طلب الحجز - ' . $this->order->order_number)
                    ->greeting('مرحباً ' . $notifiable->first_name)
                    ->line('تم استلام طلب حجز السيارة الخاص بك بنجاح.')
                    ->line('**تفاصيل الطلب:**')
                    ->line('رقم الطلب: ' . $this->order->order_number)
                    ->line('السيارة: ' . $this->order->car->title)
                    ->line('مبلغ الحجز: ' . number_format($this->order->reservation_amount, 2) . ' ريال')
                    ->line('حالة الطلب: ' . $this->order->getStatusInArabic())
                    ->when($this->order->payment_method === 'online_payment', function ($message) {
                        if ($this->order->payment_status === 'completed') {
                            return $message->line('✅ تم دفع مبلغ الحجز بنجاح');
                        } else {
                            return $message->line('⏳ في انتظار دفع مبلغ الحجز');
                        }
                    })
                    ->when($this->order->payment_method === 'showroom_payment', function ($message) {
                        return $message->line('💰 سيتم دفع مبلغ الحجز في المعرض');
                    })
                    ->line('سيتم التواصل معك قريباً من قبل فريق المبيعات لتأكيد التفاصيل وترتيب موعد الاستلام.')
                    ->action('عرض تفاصيل الطلب', $orderUrl)
                    ->line('شكراً لك على ثقتك بنا!')
                    ->salutation('مع أطيب التحيات، فريق ' . config('app.name'));
    }

    /**
     * تكوين الإشعار في قاعدة البيانات
     */
    public function toDatabase($notifiable): array
    {
        return [
            'title' => 'تم تقديم طلب الحجز بنجاح',
            'message' => "تم استلام طلب حجز السيارة {$this->order->car->title} برقم {$this->order->order_number}",
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'car_title' => $this->order->car->title,
            'reservation_amount' => $this->order->reservation_amount,
            'status' => $this->order->status,
            'payment_method' => $this->order->payment_method,
            'payment_status' => $this->order->payment_status,
            'action_url' => route('customer.orders.show', $this->order->id),
            'icon' => 'fas fa-car',
            'type' => 'order_created'
        ];
    }

    /**
     * تكوين إشعار SMS (إذا كان مفعلاً)
     */
    public function toSms($notifiable): string
    {
        $message = "مرحباً {$notifiable->first_name}، ";
        $message .= "تم استلام طلب حجز السيارة {$this->order->car->title} ";
        $message .= "برقم {$this->order->order_number}. ";
        
        if ($this->order->payment_method === 'online_payment') {
            if ($this->order->payment_status === 'completed') {
                $message .= "تم دفع مبلغ الحجز بنجاح. ";
            } else {
                $message .= "يرجى إكمال دفع مبلغ الحجز. ";
            }
        } else {
            $message .= "سيتم دفع مبلغ الحجز في المعرض. ";
        }
        
        $message .= "سيتم التواصل معك قريباً.";
        
        return $message;
    }
}
