<?php

namespace App\Http\Controllers\Site;

use App\Http\Controllers\Controller;
use Illuminate\View\View;
use Modules\CarCatalog\Models\Car;

/**
 * HomepageController
 * 
 * Controller مسؤول عن عرض الصفحة الرئيسية للموقع العام
 * يجلب البيانات الديناميكية المطلوبة (البنرات، السيارات المميزة، أحدث العروض)
 */
class HomepageController extends Controller
{
    /**
     * عرض الصفحة الرئيسية للموقع العام
     * 
     * يجلب البيانات التالية:
     * - البنرات النشطة (من موديول Cms عند توفره)
     * - السيارات المميزة (من موديول CarCatalog)
     * - أحدث العروض النشطة (من موديول PromotionManagement عند توفره)
     * 
     * @return View
     */
    public function index(): View
    {
        // جلب البنرات النشطة
        // ملاحظة: سيتم تفعيل هذا عند إنشاء موديول Cms
        $banners = collect(); // مجموعة فارغة مؤقتاً
        
        // TODO: عند إنشاء موديول Cms، استخدم الكود التالي:
        // $banners = \Modules\Cms\Models\HomepageBanner::where('status', true)
        //     ->orderBy('order')
        //     ->with('media')
        //     ->get();

        // جلب السيارات المميزة مع الصور والعلاقات المطلوبة
        $featuredCars = Car::where('is_featured', true)
            ->where('is_active', true)
            ->where('is_sold', false)
            ->with([
                'media',
                'brand:id,name',
                'carModel:id,name',
                'manufacturingYear:id,year'
            ])
            ->take(6)
            ->get();

        // جلب أحدث العروض النشطة
        // ملاحظة: سيتم تفعيل هذا عند إنشاء موديول PromotionManagement
        $latestPromotions = collect(); // مجموعة فارغة مؤقتاً
        
        // TODO: عند إنشاء موديول PromotionManagement، استخدم الكود التالي:
        // $latestPromotions = \Modules\PromotionManagement\Models\Promotion::where('status', true)
        //     ->where('start_date', '<=', now())
        //     ->where('end_date', '>=', now())
        //     ->with('media')
        //     ->orderBy('created_at', 'desc')
        //     ->take(4)
        //     ->get();

        // تمرير البيانات إلى view الصفحة الرئيسية
        return view('site.home', compact('banners', 'featuredCars', 'latestPromotions'));
    }
}
