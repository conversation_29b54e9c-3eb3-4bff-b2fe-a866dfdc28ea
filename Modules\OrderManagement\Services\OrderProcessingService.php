<?php

namespace Modules\OrderManagement\Services;

use Modules\OrderManagement\Models\Order;
use Modules\CarCatalog\Models\Car;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * خدمة معالجة الطلبات
 *
 * تتولى منطق إنشاء ومعالجة طلبات شراء السيارات
 * بناءً على MOD-ORDER-MGMT-FEAT-003 في REQ-FR.md
 */
class OrderProcessingService
{
    /**
     * إنشاء طلب شراء سيارة كاش جديد
     */
    public function createCashOrder(array $orderData): Order
    {
        try {
            return DB::transaction(function () use ($orderData) {
                // التحقق من توفر السيارة
                $car = Car::findOrFail($orderData['car_id']);

                if (!$this->isCarAvailable($car)) {
                    throw new Exception('السيارة غير متوفرة للحجز');
                }

                // إنشاء رقم طلب فريد
                $orderNumber = $this->generateOrderNumber();

                // تحضير بيانات الطلب
                $orderAttributes = [
                    'user_id' => auth()->id(),
                    'car_id' => $car->id,
                    'order_number' => $orderNumber,
                    'order_type' => 'cash_reservation',
                    'status' => $this->determineInitialStatus($orderData),
                    'car_price_at_order' => $car->price,
                    'reservation_amount' => $orderData['reservation_amount'],
                    'remaining_amount' => $car->price - $orderData['reservation_amount'],
                    'payment_method' => $orderData['payment_method'],
                    'payment_status' => 'pending',

                    // بيانات العميل وقت الطلب
                    'customer_national_id' => $orderData['personal_data']['national_id'],
                    'customer_dob' => $orderData['personal_data']['date_of_birth'],
                    'customer_nationality_id' => $orderData['personal_data']['nationality_id'],
                    'customer_address_details' => $this->formatAddress($orderData['personal_data']),

                    // تفاصيل إضافية
                    'admin_notes' => null,
                    'finance_details' => null,
                ];

                // إنشاء الطلب
                $order = Order::create($orderAttributes);

                // تحديث حالة السيارة إذا لزم الأمر
                if ($orderData['payment_method'] === 'online_payment') {
                    $car->update(['status' => 'reserved']);
                }

                Log::info('تم إنشاء طلب شراء كاش جديد', [
                    'order_id' => $order->id,
                    'order_number' => $orderNumber,
                    'user_id' => auth()->id(),
                    'car_id' => $car->id
                ]);

                return $order;
            });
        } catch (Exception $e) {
            Log::error('خطأ في إنشاء طلب شراء كاش', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'car_id' => $orderData['car_id'] ?? null
            ]);

            throw $e;
        }
    }

    /**
     * إنشاء طلب تمويل جديد
     */
    public function createFinanceOrder(array $orderData): Order
    {
        try {
            return DB::transaction(function () use ($orderData) {
                // التحقق من توفر السيارة
                $car = Car::findOrFail($orderData['car_id']);

                if (!$this->isCarAvailable($car)) {
                    throw new Exception('السيارة غير متوفرة لطلب التمويل');
                }

                // إنشاء رقم طلب فريد
                $orderNumber = $this->generateFinanceOrderNumber();

                // تحضير تفاصيل التمويل
                $financeDetails = [
                    'down_payment_amount' => $orderData['finance_data']['down_payment_amount'],
                    'monthly_income' => $orderData['finance_data']['monthly_income'],
                    'monthly_obligations' => $orderData['finance_data']['monthly_obligations'],
                    'employment_type' => $orderData['finance_data']['employment_type'],
                    'job_title' => $orderData['finance_data']['job_title'],
                    'employer_name' => $orderData['finance_data']['employer_name'] ?? null,
                    'work_experience_years' => $orderData['finance_data']['work_experience_years'],
                    'current_job_years' => $orderData['finance_data']['current_job_years'],
                    'salary_bank' => $orderData['finance_data']['salary_bank'],
                    'bank_account_number' => $orderData['finance_data']['bank_account_number'],
                    'iban_number' => $orderData['finance_data']['iban_number'],
                    'has_other_loans' => $orderData['finance_data']['has_other_loans'],
                    'other_loans_details' => $orderData['finance_data']['other_loans_details'] ?? null,
                    'requested_financing_period' => $orderData['finance_data']['requested_financing_period'],
                    'net_income' => $orderData['finance_data']['monthly_income'] - $orderData['finance_data']['monthly_obligations'],
                    'application_date' => now()->toDateString(),
                    'application_status' => 'pending_review'
                ];

                // تحضير بيانات الطلب
                $orderAttributes = [
                    'user_id' => auth()->id(),
                    'car_id' => $car->id,
                    'order_number' => $orderNumber,
                    'order_type' => 'finance_application',
                    'status' => 'pending_finance_review',
                    'car_price_at_order' => $car->price,
                    'reservation_amount' => $orderData['finance_data']['down_payment_amount'],
                    'remaining_amount' => $car->price - $orderData['finance_data']['down_payment_amount'],
                    'payment_method' => 'financing',
                    'payment_status' => 'pending_approval',

                    // بيانات العميل وقت الطلب
                    'customer_national_id' => $orderData['personal_data']['national_id'],
                    'customer_dob' => $orderData['personal_data']['date_of_birth'],
                    'customer_nationality_id' => $orderData['personal_data']['nationality_id'],
                    'customer_address_details' => $this->formatAddress($orderData['personal_data']),

                    // تفاصيل التمويل
                    'finance_details' => $financeDetails,
                    'admin_notes' => null,
                ];

                // إنشاء الطلب
                $order = Order::create($orderAttributes);

                // تحديث حالة السيارة (محجوزة مؤقتاً لطلب التمويل)
                $car->update(['status' => 'finance_pending']);

                Log::info('تم إنشاء طلب تمويل جديد', [
                    'order_id' => $order->id,
                    'order_number' => $orderNumber,
                    'user_id' => auth()->id(),
                    'car_id' => $car->id,
                    'requested_amount' => $car->price - $orderData['finance_data']['down_payment_amount']
                ]);

                return $order;
            });
        } catch (Exception $e) {
            Log::error('خطأ في إنشاء طلب تمويل', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'car_id' => $orderData['car_id'] ?? null
            ]);

            throw $e;
        }
    }

    /**
     * تحديث حالة الطلب بناءً على نتيجة الدفع
     */
    public function updateOrderPaymentStatus(Order $order, string $paymentStatus, array $paymentResponse = []): bool
    {
        try {
            return DB::transaction(function () use ($order, $paymentStatus, $paymentResponse) {
                $updateData = [
                    'payment_status' => $paymentStatus,
                    'payment_gateway_response' => $paymentResponse,
                    'updated_at' => now()
                ];

                // تحديث حالة الطلب بناءً على نتيجة الدفع
                if ($paymentStatus === 'completed') {
                    $updateData['status'] = 'confirmed';
                    $updateData['payment_transaction_id'] = $paymentResponse['transaction_id'] ?? null;

                    // تحديث حالة السيارة
                    $order->car->update(['status' => 'sold']);

                } elseif ($paymentStatus === 'failed') {
                    $updateData['status'] = 'payment_failed';

                    // إعادة السيارة للحالة المتاحة
                    $order->car->update(['status' => 'available']);
                }

                $order->update($updateData);

                Log::info('تم تحديث حالة دفع الطلب', [
                    'order_id' => $order->id,
                    'payment_status' => $paymentStatus,
                    'order_status' => $updateData['status']
                ]);

                return true;
            });
        } catch (Exception $e) {
            Log::error('خطأ في تحديث حالة دفع الطلب', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * التحقق من توفر السيارة للحجز
     */
    private function isCarAvailable(Car $car): bool
    {
        return $car->status === 'available' && $car->is_active;
    }

    /**
     * تحديد الحالة الأولية للطلب
     */
    private function determineInitialStatus(array $orderData): string
    {
        if ($orderData['payment_method'] === 'online_payment') {
            return 'pending_payment';
        }

        return 'pending_admin_review';
    }

    /**
     * تنسيق عنوان العميل
     */
    private function formatAddress(array $personalData): string
    {
        $addressParts = [
            $personalData['address_details'],
            $personalData['district'],
            $personalData['city']
        ];

        return implode(', ', array_filter($addressParts));
    }

    /**
     * توليد رقم طلب فريد
     */
    private function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $year = date('Y');
        $month = date('m');

        // البحث عن آخر رقم طلب في الشهر الحالي
        $lastOrder = Order::where('order_number', 'like', "{$prefix}-{$year}{$month}%")
                          ->orderBy('order_number', 'desc')
                          ->first();

        if ($lastOrder) {
            $lastNumber = (int) substr($lastOrder->order_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return sprintf('%s-%s%s%04d', $prefix, $year, $month, $newNumber);
    }

    /**
     * توليد رقم طلب تمويل فريد
     */
    private function generateFinanceOrderNumber(): string
    {
        $prefix = 'FIN';
        $year = date('Y');
        $month = date('m');

        // البحث عن آخر رقم طلب تمويل في الشهر الحالي
        $lastOrder = Order::where('order_number', 'like', "{$prefix}-{$year}{$month}%")
                          ->orderBy('order_number', 'desc')
                          ->first();

        if ($lastOrder) {
            $lastNumber = (int) substr($lastOrder->order_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return sprintf('%s-%s%s%04d', $prefix, $year, $month, $newNumber);
    }

    /**
     * حساب مبلغ الحجز الافتراضي
     */
    public function calculateDefaultReservationAmount(float $carPrice): float
    {
        // 10% من سعر السيارة كحد أدنى، بحد أدنى 5000 ريال
        $percentage = 0.10;
        $minAmount = 5000;

        $calculatedAmount = $carPrice * $percentage;

        return max($calculatedAmount, $minAmount);
    }

    /**
     * التحقق من صحة مبلغ الحجز
     */
    public function validateReservationAmount(float $reservationAmount, float $carPrice): bool
    {
        $minAmount = $this->calculateDefaultReservationAmount($carPrice);
        $maxAmount = $carPrice * 0.5; // حد أقصى 50% من سعر السيارة

        return $reservationAmount >= $minAmount && $reservationAmount <= $maxAmount;
    }
}
