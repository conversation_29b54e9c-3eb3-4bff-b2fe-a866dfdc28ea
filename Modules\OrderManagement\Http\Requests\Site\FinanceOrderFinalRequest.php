<?php

namespace Modules\OrderManagement\Http\Requests\Site;

use Illuminate\Foundation\Http\FormRequest;

/**
 * طلب التحقق من البيانات النهائية لطلب التمويل
 * 
 * يتم استخدامه للتحقق من جميع البيانات قبل إنشاء طلب التمويل النهائي
 * بناءً على MOD-ORDER-MGMT-FEAT-004 في REQ-FR.md
 */
class FinanceOrderFinalRequest extends FormRequest
{
    /**
     * تحديد ما إذا كان المستخدم مخولاً لتقديم هذا الطلب
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * قواعد التحقق من البيانات
     */
    public function rules(): array
    {
        return [
            // تأكيد الموافقة على الشروط والأحكام
            'terms_accepted' => 'required|accepted',
            'privacy_policy_accepted' => 'required|accepted',
            'finance_terms_accepted' => 'required|accepted',
            
            // تأكيد صحة البيانات المقدمة
            'data_accuracy_confirmed' => 'required|accepted',
            
            // موافقة على فحص الائتمان
            'credit_check_consent' => 'required|accepted',
            
            // ملاحظات إضافية (اختيارية)
            'additional_notes' => 'nullable|string|max:1000',
            
            // تأكيد طريقة التواصل المفضلة
            'preferred_contact_method' => 'required|in:phone,email,whatsapp',
            'preferred_contact_time' => 'required|in:morning,afternoon,evening,anytime',
        ];
    }

    /**
     * رسائل الخطأ المخصصة
     */
    public function messages(): array
    {
        return [
            'terms_accepted.required' => 'يجب الموافقة على الشروط والأحكام',
            'terms_accepted.accepted' => 'يجب الموافقة على الشروط والأحكام',
            
            'privacy_policy_accepted.required' => 'يجب الموافقة على سياسة الخصوصية',
            'privacy_policy_accepted.accepted' => 'يجب الموافقة على سياسة الخصوصية',
            
            'finance_terms_accepted.required' => 'يجب الموافقة على شروط التمويل',
            'finance_terms_accepted.accepted' => 'يجب الموافقة على شروط التمويل',
            
            'data_accuracy_confirmed.required' => 'يجب تأكيد صحة البيانات المقدمة',
            'data_accuracy_confirmed.accepted' => 'يجب تأكيد صحة البيانات المقدمة',
            
            'credit_check_consent.required' => 'يجب الموافقة على فحص الائتمان',
            'credit_check_consent.accepted' => 'يجب الموافقة على فحص الائتمان',
            
            'additional_notes.max' => 'الملاحظات الإضافية يجب ألا تتجاوز 1000 حرف',
            
            'preferred_contact_method.required' => 'يجب اختيار طريقة التواصل المفضلة',
            'preferred_contact_method.in' => 'طريقة التواصل المختارة غير صحيحة',
            
            'preferred_contact_time.required' => 'يجب اختيار وقت التواصل المفضل',
            'preferred_contact_time.in' => 'وقت التواصل المختار غير صحيح',
        ];
    }

    /**
     * أسماء الحقول المخصصة
     */
    public function attributes(): array
    {
        return [
            'terms_accepted' => 'الموافقة على الشروط والأحكام',
            'privacy_policy_accepted' => 'الموافقة على سياسة الخصوصية',
            'finance_terms_accepted' => 'الموافقة على شروط التمويل',
            'data_accuracy_confirmed' => 'تأكيد صحة البيانات',
            'credit_check_consent' => 'الموافقة على فحص الائتمان',
            'additional_notes' => 'الملاحظات الإضافية',
            'preferred_contact_method' => 'طريقة التواصل المفضلة',
            'preferred_contact_time' => 'وقت التواصل المفضل',
        ];
    }

    /**
     * التحقق من البيانات بعد التحقق من القواعد الأساسية
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // التحقق من وجود جميع بيانات الجلسة المطلوبة
            if (!$this->validateSessionData()) {
                $validator->errors()->add('session', 'بيانات طلب التمويل غير مكتملة. يرجى إكمال جميع الخطوات أولاً');
            }

            // التحقق من صحة السيارة المختارة
            if (!$this->validateSelectedCar()) {
                $validator->errors()->add('car', 'السيارة المختارة غير متوفرة أو غير صحيحة');
            }
        });
    }

    /**
     * التحقق من بيانات الجلسة
     */
    private function validateSessionData(): bool
    {
        $requiredSessionKeys = [
            'finance_order_car_id',
            'finance_order_personal_data',
            'finance_order_finance_data',
            'finance_order_documents_data'
        ];

        foreach ($requiredSessionKeys as $key) {
            if (!session()->has($key) || empty(session($key))) {
                return false;
            }
        }

        return true;
    }

    /**
     * التحقق من صحة السيارة المختارة
     */
    private function validateSelectedCar(): bool
    {
        $carId = session('finance_order_car_id');
        
        if (!$carId) {
            return false;
        }

        try {
            $car = \Modules\CarCatalog\Entities\Car::findOrFail($carId);
            return $car->is_active && !$car->is_sold && in_array($car->status, ['available', 'reserved']);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * إعداد البيانات للتحقق
     */
    protected function prepareForValidation(): void
    {
        // تحويل القيم النصية إلى boolean للحقول المطلوبة
        $booleanFields = [
            'terms_accepted',
            'privacy_policy_accepted', 
            'finance_terms_accepted',
            'data_accuracy_confirmed',
            'credit_check_consent'
        ];

        $data = $this->all();
        
        foreach ($booleanFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = filter_var($data[$field], FILTER_VALIDATE_BOOLEAN);
            }
        }

        $this->replace($data);
    }
}
