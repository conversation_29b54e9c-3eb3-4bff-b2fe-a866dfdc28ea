@extends('dashboard::layouts.admin_layout')

@section('title', 'إدارة أنواع ناقل الحركة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h4 class="fw-bold mb-0" style="color: var(--primary-color);">إدارة أنواع ناقل الحركة</h4>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb brand-breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="brand-link">لوحة التحكم</a>
                    </li>
                    <li class="breadcrumb-item">إدارة السيارات</li>
                    <li class="breadcrumb-item active" aria-current="page">أنواع ناقل الحركة</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.transmission-types.create') }}" class="btn btn-brand-primary">
                <i class="fas fa-plus me-1"></i> إضافة نوع جديد
            </a>
        </div>
    </div>

    {{-- عرض رسائل النجاح والخطأ --}}
    @if(session('success'))
        <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {{-- نموذج البحث والفلترة --}}
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.transmission-types.index') }}" class="mb-0">
                <div class="row gx-2 gy-2 align-items-center">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="بحث بالاسم..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">كل الحالات</option>
                            <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>نشط</option>
                            <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-brand-info w-100">بحث</button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ route('admin.transmission-types.index') }}" class="btn btn-brand-secondary w-100">إعادة تعيين</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- جدول أنواع ناقل الحركة --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">قائمة أنواع ناقل الحركة</h5>
        </div>
        <div class="card-body table-responsive p-0">
            <table class="table table-hover recent-activity-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم نوع ناقل الحركة</th>
                        <th>الحالة</th>
                        <th>عدد السيارات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($transmissionTypes as $transmissionType)
                        <tr>
                            <td>{{ $transmissionType->id }}</td>
                            <td>{{ $transmissionType->name }}</td>
                            <td>
                                <span class="badge rounded-pill bg-{{ $transmissionType->status ? 'success' : 'danger' }}">
                                    {{ $transmissionType->status ? 'نشط' : 'غير نشط' }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $transmissionType->cars_count }}</span>
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('admin.transmission-types.edit', $transmissionType) }}" class="btn btn-sm btn-warning me-1 action-btn edit" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.transmission-types.destroy', $transmissionType) }}" method="POST" class="d-inline"
                                          onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف نوع ناقل الحركة هذا؟');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger action-btn delete" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-cogs fa-3x mb-3"></i>
                                    <p>لا توجد أنواع ناقل حركة مسجلة</p>
                                    <a href="{{ route('admin.transmission-types.create') }}" class="btn btn-primary">
                                        إضافة أول نوع ناقل حركة
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

    </div>

    {{-- ترقيم الصفحات --}}
    <div class="mt-3">
        {{ $transmissionTypes->appends(request()->query())->links('pagination::bootstrap-5') }}
    </div>
</div>
@endsection
