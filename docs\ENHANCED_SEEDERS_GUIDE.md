# دليل الـ Seeders المحسنة - Enhanced Seeders Guide

## نظرة عامة

تم تحسين وتطوير جميع الـ Seeders في النظام لتوفير بيانات اختبار شاملة وواقعية تغطي مختلف السيناريوهات للوحة التحكم والواجهة العامة.

## الـ Seeders المحسنة

### 1. CarCatalogTestDataSeeder (محسن)
**المسار**: `Modules/CarCatalog/Database/Seeders/CarCatalogTestDataSeeder.php`

#### التحسينات المضافة:
- **الماركات**: 20 ماركة عالمية مع أوصاف تفصيلية
- **الموديلات**: أكثر من 150 موديل متنوع لكل ماركة
- **السيارات**: 50 سيارة بدلاً من 20 مع مواصفات واقعية
- **الأسعار**: أسعار متدرجة حسب الماركة والسنة
- **الميزات**: أكثر من 60 ميزة مقسمة على 6 فئات

#### البيانات المُنشأة:
```
الماركات: تويوتا، هوندا، نيسان، هيونداي، كيا، مرسيدس بنز، بي إم دبليو، أودي، فولكس واجن، فورد، شيفروليه، لكزس، إنفينيتي، جينيسيس، مازدا، سوبارو، ميتسوبيشي، جيب، لاند روفر، جاكوار

سنوات الصنع: 2015-2025
الألوان: 18 لون مع أكواد الألوان
أنواع ناقل الحركة: 7 أنواع متقدمة
أنواع الوقود: 7 أنواع شاملة
أنواع الهياكل: 10 أنواع مختلفة
فئات الميزات: الأمان، الراحة، التكنولوجيا، الأداء، التصميم الخارجي، التصميم الداخلي
```

### 2. TestUsersSeeder (جديد)
**المسار**: `Modules/UserManagement/Database/Seeders/TestUsersSeeder.php`

#### البيانات المُنشأة:
- **5 موظفين** بأسماء عربية وبيانات واقعية
- **45 عميل** مع تواريخ إنشاء متنوعة
- **15 عميل جديد** خلال الشهر الحالي (لإحصائيات لوحة البيانات)

### 3. TestOrdersSeeder (جديد)
**المسار**: `Modules/CarCatalog/Database/Seeders/TestOrdersSeeder.php`

#### الوظيفة:
- إنشاء جدول مؤقت للطلبات (`temp_orders`)
- إنشاء طلبات وهمية لاختبار الإحصائيات
- تواريخ متنوعة (اليوم، الأسبوع، الشهر، السنة)
- حالات مختلفة (معلقة، مؤكدة، مكتملة، ملغية)

## كيفية التشغيل

### الطريقة الأولى: تشغيل جميع الـ Seeders
```bash
php artisan db:seed
```

### الطريقة الثانية: تشغيل Seeder محدد
```bash
# تشغيل seeder السيارات المحسن
php artisan db:seed --class="Modules\CarCatalog\Database\Seeders\CarCatalogTestDataSeeder"

# تشغيل seeder المستخدمين
php artisan db:seed --class="Modules\UserManagement\Database\Seeders\TestUsersSeeder"

# تشغيل seeder الطلبات الوهمية
php artisan db:seed --class="Modules\CarCatalog\Database\Seeders\TestOrdersSeeder"
```

### الطريقة الثالثة: استخدام الملف السريع
```bash
php run_dashboard_test_data.php
```

## الميزات الجديدة

### 1. أسعار واقعية
- أسعار متدرجة حسب فئة الماركة (اقتصادية، متوسطة، فاخرة)
- عامل السنة يؤثر على السعر
- عروض خاصة (30% من السيارات لها عروض)

### 2. مواصفات منطقية
- سعة المحرك حسب نوع الهيكل
- عدد الأبواب والمقاعد حسب نوع السيارة
- المسافة المقطوعة حسب حالة السيارة (جديدة/مستعملة)

### 3. ميزات ذكية
- ربط الميزات حسب فئة السيارة
- السيارات الفاخرة الحديثة تحصل على ميزات أكثر
- توزيع منطقي للميزات حسب الفئات

### 4. تواريخ متنوعة
- تواريخ إنشاء متنوعة للسيارات (آخر 90 يوم)
- تواريخ إنشاء متنوعة للمستخدمين (آخر سنة)
- عملاء جدد خلال الشهر الحالي

## بيانات تسجيل الدخول للاختبار

```
المدير العام:
Email: <EMAIL>
Password: password

الموظفين:
Email: <EMAIL>
Password: password

العملاء:
Email: <EMAIL>
Password: password
```

## الفوائد للمرحلة القادمة (PH-03)

### 1. اختبار الواجهة العامة
- بيانات متنوعة لاختبار البحث والفلترة
- أسعار واقعية لاختبار عمليات الشراء
- ميزات شاملة لعرض تفاصيل السيارات

### 2. اختبار الأداء
- 50 سيارة مع علاقات معقدة
- أكثر من 60 ميزة مرتبطة
- بيانات كافية لاختبار الصفحات المتعددة

### 3. اختبار الإحصائيات
- طلبات وهمية لاختبار لوحة البيانات
- مستخدمين جدد لاختبار إحصائيات النمو
- تواريخ متنوعة لاختبار التقارير الزمنية

## ملاحظات مهمة

1. **الأمان**: جميع كلمات المرور هي `password` - يجب تغييرها في بيئة الإنتاج
2. **البيانات المؤقتة**: جدول `temp_orders` مؤقت حتى إنشاء OrderManagement module
3. **التكرار**: يمكن تشغيل الـ Seeders عدة مرات دون تضارب (استخدام `firstOrCreate`)
4. **الأداء**: الـ Seeders محسنة للأداء مع استخدام `array_chunk` للإدراج المجمع

## التحديثات المستقبلية

عند إنشاء OrderManagement module في المرحلة القادمة:
1. حذف جدول `temp_orders`
2. تحديث TestOrdersSeeder لاستخدام نموذج Order الحقيقي
3. إضافة علاقات مع جدول السيارات والمستخدمين
