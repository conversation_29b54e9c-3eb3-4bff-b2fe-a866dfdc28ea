<?php

namespace App\Http\Controllers\Site;

use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\View\View;

/**
 * SitePageController
 *
 * Controller مسؤول عن عرض الصفحات الثابتة في الموقع العام
 * يدعم عرض الصفحات من موديول CMS (عند توفره) أو المحتوى الافتراضي
 *
 * يخدم هذا Controller المتطلبات التالية:
 * - عرض الصفحات الثابتة مثل "من نحن"، "سياسة الخصوصية"، "الشروط والأحكام"
 * - دعم الـ slug الديناميكي لجلب الصفحات من قاعدة البيانات
 * - معالجة حالة عدم العثور على الصفحة (404)
 * - دعم المحتوى الافتراضي عند عدم توفر موديول CMS
 *
 * @package App\Http\Controllers\Site
 * <AUTHOR> System
 * @version 1.0.0
 */
class SitePageController extends Controller
{
    /**
     * عرض صفحة ثابتة بناءً على الـ slug
     *
     * يحاول جلب الصفحة من موديول CMS إذا كان متوفراً، وإلا يعرض محتوى افتراضي
     * أو يرجع خطأ 404 إذا لم تكن الصفحة موجودة
     *
     * الصفحات المدعومة افتراضياً:
     * - about-us: من نحن
     * - privacy-policy: سياسة الخصوصية
     * - terms-conditions: الشروط والأحكام
     * - faq: الأسئلة الشائعة
     * - contact-us: اتصل بنا
     *
     * @param string $slug معرف الصفحة الفريد (slug)
     * @return View|Response عرض الصفحة أو استجابة 404
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException عند عدم العثور على الصفحة
     */
    public function show(string $slug): View|Response
    {
        // محاولة جلب الصفحة من موديول CMS إذا كان متوفراً
        $page = $this->getPageFromCms($slug);

        // إذا لم يتم العثور على الصفحة في CMS، محاولة الحصول على محتوى افتراضي
        if (!$page) {
            $page = $this->getDefaultPageContent($slug);
        }

        // إذا لم يتم العثور على أي محتوى، إرجاع 404
        if (!$page) {
            abort(404, 'الصفحة المطلوبة غير موجودة');
        }

        // تمرير بيانات الصفحة إلى الـ view
        return view('site.pages.show', compact('page'));
    }

    /**
     * محاولة جلب الصفحة من موديول CMS
     *
     * يتحقق من وجود موديول CMS ونموذج CmsPage، ثم يحاول جلب الصفحة
     * بناءً على الـ slug والحالة المنشورة
     *
     * @param string $slug معرف الصفحة
     * @return object|null كائن الصفحة أو null إذا لم توجد
     */
    private function getPageFromCms(string $slug): ?object
    {
        try {
            // التحقق من وجود موديول CMS ونموذج CmsPage
            if (class_exists('\Modules\Cms\Models\CmsPage')) {
                $cmsPageClass = '\Modules\Cms\Models\CmsPage';

                // جلب الصفحة من قاعدة البيانات
                $page = $cmsPageClass::where('slug', $slug)
                    ->where('status', 'published')
                    ->first();

                if ($page) {
                    return (object) [
                        'title' => $page->title,
                        'content' => $page->content,
                        'slug' => $page->slug,
                        'meta_description' => $page->meta_description ?? null,
                        'meta_keywords' => $page->meta_keywords ?? null,
                        'source' => 'cms'
                    ];
                }
            }
        } catch (\Exception $e) {
            // في حالة حدوث خطأ، تسجيل الخطأ والمتابعة للمحتوى الافتراضي
            \Log::warning('خطأ في جلب الصفحة من CMS: ' . $e->getMessage(), [
                'slug' => $slug,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * الحصول على محتوى افتراضي للصفحات الأساسية
     *
     * يوفر محتوى افتراضي للصفحات الأساسية عندما لا يكون موديول CMS متوفراً
     * أو عندما لا تكون الصفحة موجودة في قاعدة البيانات
     *
     * @param string $slug معرف الصفحة
     * @return object|null كائن الصفحة الافتراضية أو null
     */
    private function getDefaultPageContent(string $slug): ?object
    {
        $defaultPages = [
            'about-us' => [
                'title' => 'من نحن',
                'content' => $this->getAboutUsContent(),
                'meta_description' => 'تعرف على شركة MotorLine وخدماتنا المتميزة في مجال بيع السيارات',
                'meta_keywords' => 'من نحن, MotorLine, بيع السيارات, خدمات السيارات'
            ],
            'privacy-policy' => [
                'title' => 'سياسة الخصوصية',
                'content' => $this->getPrivacyPolicyContent(),
                'meta_description' => 'سياسة الخصوصية وحماية البيانات في MotorLine',
                'meta_keywords' => 'سياسة الخصوصية, حماية البيانات, الخصوصية'
            ],
            'terms-conditions' => [
                'title' => 'الشروط والأحكام',
                'content' => $this->getTermsConditionsContent(),
                'meta_description' => 'الشروط والأحكام الخاصة باستخدام خدمات MotorLine',
                'meta_keywords' => 'الشروط والأحكام, شروط الاستخدام, أحكام الخدمة'
            ],
            'faq' => [
                'title' => 'الأسئلة الشائعة',
                'content' => $this->getFaqContent(),
                'meta_description' => 'الأسئلة الشائعة حول خدمات MotorLine وعملية شراء السيارات',
                'meta_keywords' => 'أسئلة شائعة, استفسارات, مساعدة, دعم'
            ],
            'contact-us' => [
                'title' => 'اتصل بنا',
                'content' => $this->getContactUsContent(),
                'meta_description' => 'تواصل مع فريق MotorLine للاستفسارات والدعم',
                'meta_keywords' => 'اتصل بنا, تواصل, دعم العملاء, استفسارات'
            ]
        ];

        if (isset($defaultPages[$slug])) {
            $pageData = $defaultPages[$slug];
            return (object) [
                'title' => $pageData['title'],
                'content' => $pageData['content'],
                'slug' => $slug,
                'meta_description' => $pageData['meta_description'],
                'meta_keywords' => $pageData['meta_keywords'],
                'source' => 'default'
            ];
        }

        return null;
    }

    /**
     * محتوى صفحة "من نحن" الافتراضي
     *
     * @return string محتوى HTML للصفحة
     */
    private function getAboutUsContent(): string
    {
        return '
        <div class="about-us-content">
            <div class="hero-section mb-5">
                <h2 class="text-primary mb-3">مرحباً بكم في MotorLine</h2>
                <p class="lead">وجهتكم الأولى لشراء السيارات الجديدة والمستعملة بأفضل الأسعار وأعلى معايير الجودة</p>
            </div>

            <div class="row">
                <div class="col-md-6 mb-4">
                    <h3 class="h4 text-secondary mb-3">رؤيتنا</h3>
                    <p>أن نكون الخيار الأول والأكثر ثقة للعملاء في المملكة العربية السعودية عند البحث عن سيارة جديدة أو مستعملة، من خلال تقديم خدمة استثنائية وتجربة شراء مميزة.</p>
                </div>

                <div class="col-md-6 mb-4">
                    <h3 class="h4 text-secondary mb-3">مهمتنا</h3>
                    <p>نسعى لتوفير أوسع تشكيلة من السيارات عالية الجودة مع خدمات متكاملة تشمل التمويل، الصيانة، وخدمة ما بعد البيع، لضمان رضا عملائنا الكامل.</p>
                </div>
            </div>

            <div class="values-section mt-5">
                <h3 class="h4 text-secondary mb-4">قيمنا</h3>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="value-item text-center">
                            <i class="fas fa-shield-alt fa-2x text-primary mb-2"></i>
                            <h5>الثقة</h5>
                            <p class="small">نبني علاقات طويلة الأمد مع عملائنا</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="value-item text-center">
                            <i class="fas fa-star fa-2x text-primary mb-2"></i>
                            <h5>الجودة</h5>
                            <p class="small">نقدم أفضل السيارات والخدمات</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="value-item text-center">
                            <i class="fas fa-handshake fa-2x text-primary mb-2"></i>
                            <h5>الشفافية</h5>
                            <p class="small">وضوح كامل في جميع تعاملاتنا</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="value-item text-center">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h5>خدمة العملاء</h5>
                            <p class="small">عملاؤنا هم أولويتنا الأولى</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }

    /**
     * محتوى صفحة "سياسة الخصوصية" الافتراضي
     *
     * @return string محتوى HTML للصفحة
     */
    private function getPrivacyPolicyContent(): string
    {
        return '
        <div class="privacy-policy-content">
            <div class="intro-section mb-4">
                <p class="lead">نحن في MotorLine نقدر خصوصيتكم ونلتزم بحماية معلوماتكم الشخصية. توضح هذه السياسة كيفية جمع واستخدام وحماية بياناتكم.</p>
            </div>

            <div class="section mb-4">
                <h3 class="h5 text-secondary">1. المعلومات التي نجمعها</h3>
                <ul>
                    <li>المعلومات الشخصية: الاسم، رقم الهاتف، البريد الإلكتروني، العنوان</li>
                    <li>معلومات الهوية: صورة الهوية الوطنية، رخصة القيادة (للمعاملات)</li>
                    <li>المعلومات المالية: تفاصيل التمويل والدفع (مشفرة وآمنة)</li>
                    <li>معلومات الاستخدام: كيفية تفاعلكم مع موقعنا وخدماتنا</li>
                </ul>
            </div>

            <div class="section mb-4">
                <h3 class="h5 text-secondary">2. كيفية استخدام المعلومات</h3>
                <ul>
                    <li>معالجة طلبات شراء السيارات والخدمات</li>
                    <li>التواصل معكم بخصوص طلباتكم واستفساراتكم</li>
                    <li>تحسين خدماتنا وتجربة المستخدم</li>
                    <li>إرسال العروض والتحديثات (بموافقتكم)</li>
                    <li>الامتثال للمتطلبات القانونية والتنظيمية</li>
                </ul>
            </div>

            <div class="section mb-4">
                <h3 class="h5 text-secondary">3. حماية المعلومات</h3>
                <p>نستخدم أحدث تقنيات الأمان لحماية معلوماتكم، بما في ذلك:</p>
                <ul>
                    <li>تشفير SSL لجميع عمليات نقل البيانات</li>
                    <li>خوادم آمنة ومحمية</li>
                    <li>وصول محدود للموظفين المخولين فقط</li>
                    <li>مراجعة دورية لأنظمة الأمان</li>
                </ul>
            </div>

            <div class="section mb-4">
                <h3 class="h5 text-secondary">4. حقوقكم</h3>
                <ul>
                    <li>الحق في الوصول إلى معلوماتكم الشخصية</li>
                    <li>الحق في تصحيح أو تحديث المعلومات</li>
                    <li>الحق في حذف المعلومات (وفقاً للقوانين)</li>
                    <li>الحق في إلغاء الاشتراك من الرسائل التسويقية</li>
                </ul>
            </div>

            <div class="contact-section mt-5 p-3 bg-light rounded">
                <h4 class="h6 text-secondary">للاستفسارات حول سياسة الخصوصية</h4>
                <p class="mb-1"><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                <p class="mb-0"><strong>الهاتف:</strong> 920000000</p>
            </div>
        </div>';
    }

    /**
     * محتوى صفحة "الشروط والأحكام" الافتراضي
     *
     * @return string محتوى HTML للصفحة
     */
    private function getTermsConditionsContent(): string
    {
        return '
        <div class="terms-conditions-content">
            <div class="intro-section mb-4">
                <p class="lead">مرحباً بكم في MotorLine. باستخدام موقعنا وخدماتنا، فإنكم توافقون على الالتزام بالشروط والأحكام التالية.</p>
            </div>

            <div class="section mb-4">
                <h3 class="h5 text-secondary">1. شروط الاستخدام العامة</h3>
                <ul>
                    <li>يجب أن تكونوا بالغين (18 سنة أو أكثر) لاستخدام خدماتنا</li>
                    <li>يجب تقديم معلومات صحيحة ودقيقة عند التسجيل</li>
                    <li>أنتم مسؤولون عن الحفاظ على سرية بيانات حسابكم</li>
                    <li>يُمنع استخدام الموقع لأي أغراض غير قانونية</li>
                </ul>
            </div>

            <div class="section mb-4">
                <h3 class="h5 text-secondary">2. شروط الشراء والدفع</h3>
                <ul>
                    <li>جميع الأسعار المعروضة شاملة ضريبة القيمة المضافة</li>
                    <li>مبلغ الحجز غير قابل للاسترداد إلا في حالات محددة</li>
                    <li>يجب إكمال عملية الشراء خلال المدة المحددة</li>
                    <li>نحتفظ بالحق في تعديل الأسعار دون إشعار مسبق</li>
                </ul>
            </div>

            <div class="section mb-4">
                <h3 class="h5 text-secondary">3. التمويل والقروض</h3>
                <ul>
                    <li>خدمات التمويل تخضع لموافقة البنوك والمؤسسات المالية</li>
                    <li>يجب تقديم جميع المستندات المطلوبة للتمويل</li>
                    <li>شروط التمويل تحددها الجهة المانحة للتمويل</li>
                    <li>MotorLine وسيط فقط ولا تتحمل مسؤولية قرارات التمويل</li>
                </ul>
            </div>

            <div class="section mb-4">
                <h3 class="h5 text-secondary">4. الضمان وخدمة ما بعد البيع</h3>
                <ul>
                    <li>ضمان الوكيل ساري على جميع السيارات الجديدة</li>
                    <li>السيارات المستعملة تأتي بضمان محدود حسب العمر والحالة</li>
                    <li>خدمة ما بعد البيع متوفرة في مراكزنا المعتمدة</li>
                    <li>يُرجى قراءة شروط الضمان بعناية قبل الشراء</li>
                </ul>
            </div>

            <div class="section mb-4">
                <h3 class="h5 text-secondary">5. المسؤولية والتنازل</h3>
                <ul>
                    <li>MotorLine غير مسؤولة عن أي أضرار غير مباشرة</li>
                    <li>مسؤوليتنا محدودة بقيمة السيارة المشتراة</li>
                    <li>العميل مسؤول عن فحص السيارة قبل الاستلام</li>
                    <li>أي نزاعات تحل وفقاً لقوانين المملكة العربية السعودية</li>
                </ul>
            </div>

            <div class="contact-section mt-5 p-3 bg-light rounded">
                <h4 class="h6 text-secondary">للاستفسارات القانونية</h4>
                <p class="mb-1"><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                <p class="mb-0"><strong>الهاتف:</strong> 920000000</p>
            </div>
        </div>';
    }

    /**
     * محتوى صفحة "الأسئلة الشائعة" الافتراضي
     *
     * @return string محتوى HTML للصفحة
     */
    private function getFaqContent(): string
    {
        return '
        <div class="faq-content">
            <div class="intro-section mb-4">
                <p class="lead">إليكم أهم الأسئلة الشائعة حول خدماتنا وعملية شراء السيارات</p>
            </div>

            <div class="accordion" id="faqAccordion">
                <div class="accordion-item mb-3">
                    <h2 class="accordion-header" id="faq1">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                            كيف يمكنني شراء سيارة من MotorLine؟
                        </button>
                    </h2>
                    <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            يمكنكم شراء السيارة بطريقتين: الدفع كاش أو التمويل. في كلا الحالتين، تحتاجون لتسجيل حساب، اختيار السيارة، تعبئة البيانات المطلوبة، ودفع مبلغ الحجز.
                        </div>
                    </div>
                </div>

                <div class="accordion-item mb-3">
                    <h2 class="accordion-header" id="faq2">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                            ما هي المستندات المطلوبة للشراء؟
                        </button>
                    </h2>
                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            للشراء كاش: الهوية الوطنية ورخصة القيادة. للتمويل: بالإضافة للمستندات السابقة، تحتاجون لتعريف بالراتب، كشف حساب بنكي، وأي مستندات أخرى يطلبها البنك.
                        </div>
                    </div>
                </div>

                <div class="accordion-item mb-3">
                    <h2 class="accordion-header" id="faq3">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                            هل يمكنني إلغاء الحجز واسترداد المبلغ؟
                        </button>
                    </h2>
                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            مبلغ الحجز غير قابل للاسترداد في الحالات العادية. ولكن في حالات خاصة مثل رفض التمويل أو عيوب في السيارة، يمكن مناقشة الاسترداد مع فريق المبيعات.
                        </div>
                    </div>
                </div>

                <div class="accordion-item mb-3">
                    <h2 class="accordion-header" id="faq4">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                            كم تستغرق عملية التمويل؟
                        </button>
                    </h2>
                    <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            عادة تستغرق عملية التمويل من 3-7 أيام عمل، حسب البنك والمستندات المقدمة. سنقوم بإشعاركم فور الحصول على الموافقة أو الرفض.
                        </div>
                    </div>
                </div>

                <div class="accordion-item mb-3">
                    <h2 class="accordion-header" id="faq5">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                            هل تقدمون خدمة التوصيل؟
                        </button>
                    </h2>
                    <div id="collapse5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            نعم، نقدم خدمة التوصيل داخل المدن الرئيسية مقابل رسوم إضافية. يمكنكم أيضاً استلام السيارة من أحد فروعنا مجاناً.
                        </div>
                    </div>
                </div>

                <div class="accordion-item mb-3">
                    <h2 class="accordion-header" id="faq6">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse6">
                            ما هو الضمان المقدم على السيارات؟
                        </button>
                    </h2>
                    <div id="collapse6" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            السيارات الجديدة تأتي بضمان الوكيل الكامل. السيارات المستعملة تأتي بضمان محدود يختلف حسب عمر وحالة السيارة. تفاصيل الضمان متوفرة مع كل سيارة.
                        </div>
                    </div>
                </div>
            </div>

            <div class="contact-section mt-5 p-3 bg-light rounded">
                <h4 class="h6 text-secondary">لم تجد إجابة لسؤالك؟</h4>
                <p class="mb-1">تواصل مع فريق الدعم على:</p>
                <p class="mb-1"><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                <p class="mb-0"><strong>الهاتف:</strong> 920000000</p>
            </div>
        </div>';
    }

    /**
     * محتوى صفحة "اتصل بنا" الافتراضي
     *
     * @return string محتوى HTML للصفحة
     */
    private function getContactUsContent(): string
    {
        return '
        <div class="contact-us-content">
            <div class="intro-section mb-5">
                <p class="lead">نحن هنا لخدمتكم. تواصلوا معنا عبر أي من الطرق التالية وسنكون سعداء لمساعدتكم</p>
            </div>

            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="contact-info">
                        <h3 class="h5 text-secondary mb-4">معلومات التواصل</h3>

                        <div class="contact-item mb-3">
                            <i class="fas fa-phone text-primary me-3"></i>
                            <div>
                                <strong>الهاتف الموحد</strong><br>
                                920000000
                            </div>
                        </div>

                        <div class="contact-item mb-3">
                            <i class="fas fa-envelope text-primary me-3"></i>
                            <div>
                                <strong>البريد الإلكتروني</strong><br>
                                <EMAIL>
                            </div>
                        </div>

                        <div class="contact-item mb-3">
                            <i class="fab fa-whatsapp text-success me-3"></i>
                            <div>
                                <strong>واتساب</strong><br>
                                +966 50 000 0000
                            </div>
                        </div>

                        <div class="contact-item mb-3">
                            <i class="fas fa-clock text-primary me-3"></i>
                            <div>
                                <strong>ساعات العمل</strong><br>
                                السبت - الخميس: 9:00 ص - 10:00 م<br>
                                الجمعة: 2:00 م - 10:00 م
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="contact-form">
                        <h3 class="h5 text-secondary mb-4">أرسل لنا رسالة</h3>
                        <form>
                            <div class="mb-3">
                                <label for="name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" required>
                            </div>
                            <div class="mb-3">
                                <label for="subject" class="form-label">الموضوع</label>
                                <select class="form-select" id="subject" required>
                                    <option value="">اختر الموضوع</option>
                                    <option value="sales">استفسار عن سيارة</option>
                                    <option value="financing">استفسار عن التمويل</option>
                                    <option value="service">خدمة ما بعد البيع</option>
                                    <option value="complaint">شكوى</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="message" class="form-label">الرسالة</label>
                                <textarea class="form-control" id="message" rows="4" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">إرسال الرسالة</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="branches-section mt-5">
                <h3 class="h5 text-secondary mb-4">فروعنا</h3>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="branch-card p-3 border rounded">
                            <h6 class="text-primary">فرع الرياض الرئيسي</h6>
                            <p class="small mb-1">طريق الملك فهد، حي العليا</p>
                            <p class="small mb-1">هاتف: 011-000-0000</p>
                            <p class="small mb-0">البريد: <EMAIL></p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="branch-card p-3 border rounded">
                            <h6 class="text-primary">فرع جدة</h6>
                            <p class="small mb-1">طريق الأمير محمد بن عبدالعزيز</p>
                            <p class="small mb-1">هاتف: 012-000-0000</p>
                            <p class="small mb-0">البريد: <EMAIL></p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="branch-card p-3 border rounded">
                            <h6 class="text-primary">فرع الدمام</h6>
                            <p class="small mb-1">طريق الملك عبدالعزيز</p>
                            <p class="small mb-1">هاتف: 013-000-0000</p>
                            <p class="small mb-0">البريد: <EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
}
