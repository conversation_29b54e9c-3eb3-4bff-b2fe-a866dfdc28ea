<?php

namespace Modules\UserManagement\Http\Requests\Site;

use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب إعادة تعيين كلمة المرور للموقع العام
 *
 * يتحقق هذا الطلب من صحة بيانات إعادة تعيين كلمة المرور
 * ويطبق جميع قواعد التحقق المطلوبة حسب المتطلبات
 */
class ResetPasswordRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب
     *
     * @return array
     */
    public function rules()
    {
        return [
            'token' => [
                'required',
                'string'
            ],
            'email' => [
                'required',
                'email',
                'exists:users,email'
            ],
            'password' => [
                'required',
                'string',
                'min:8',
                'confirmed',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?~]).{8,}$/'
            ],
            'password_confirmation' => [
                'required',
                'string'
            ]
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة لقواعد التحقق
     *
     * @return array
     */
    public function messages()
    {
        return [
            'token.required' => 'رمز إعادة التعيين مطلوب.',
            'token.string' => 'رمز إعادة التعيين يجب أن يكون نصاً.',
            
            'email.required' => 'البريد الإلكتروني مطلوب.',
            'email.email' => 'يرجى إدخال بريد إلكتروني صالح.',
            'email.exists' => 'البريد الإلكتروني المدخل غير مسجل في النظام.',
            
            'password.required' => 'كلمة المرور الجديدة مطلوبة.',
            'password.string' => 'كلمة المرور يجب أن تكون نصاً.',
            'password.min' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل.',
            'password.confirmed' => 'تأكيد كلمة المرور غير متطابق.',
            'password.regex' => 'كلمة المرور يجب أن تحتوي على حروف كبيرة وصغيرة وأرقام ورمز خاص واحد على الأقل.',
            
            'password_confirmation.required' => 'تأكيد كلمة المرور مطلوب.',
            'password_confirmation.string' => 'تأكيد كلمة المرور يجب أن يكون نصاً.'
        ];
    }

    /**
     * الحصول على أسماء الحقول المخصصة للعرض في رسائل الخطأ
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'token' => 'رمز إعادة التعيين',
            'email' => 'البريد الإلكتروني',
            'password' => 'كلمة المرور الجديدة',
            'password_confirmation' => 'تأكيد كلمة المرور'
        ];
    }

    /**
     * تحضير البيانات للتحقق
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // تحويل البريد الإلكتروني إلى أحرف صغيرة
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->email)
            ]);
        }
    }
}
