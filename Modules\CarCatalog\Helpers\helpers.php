<?php

/**
 * ملف الدوال المساعدة لموديول CarCatalog.
 *
 * يحتوي هذا الملف على مجموعة من الدوال المساعدة المخصصة للتعامل مع
 * بيانات السيارات وعرضها في واجهة المستخدم
 *
 * <AUTHOR> Development Team
 *
 * @version 1.0.0
 *
 * @since 2024
 */

if (!function_exists('car_status_label')) {
    /**
     * إرجاع تسمية حالة السيارة باللغة العربية.
     *
     * تحول هذه الدالة حالة السيارة من الإنجليزية إلى العربية
     * لعرضها في واجهة المستخدم بشكل مفهوم
     *
     * @param string $status حالة السيارة (active, inactive, sold, featured)
     *
     * @return string التسمية العربية للحالة
     *
     * @example
     * car_status_label('active'); // returns 'نشطة'
     * car_status_label('sold'); // returns 'مباعة'
     */
    function car_status_label(string $status): string
    {
        $labels = [
            'active'   => 'نشطة',
            'inactive' => 'غير نشطة',
            'sold'     => 'مباعة',
            'featured' => 'مميزة',
        ];

        return $labels[$status] ?? 'غير محدد';
    }
}

if (!function_exists('car_status_badge_class')) {
    /**
     * إرجاع كلاس Bootstrap badge لحالة السيارة.
     *
     * تحدد هذه الدالة لون شارة Bootstrap المناسب لحالة السيارة
     * لعرضها بألوان مميزة في واجهة المستخدم
     *
     * @param string $status حالة السيارة (active, inactive, sold, featured)
     *
     * @return string كلاس Bootstrap badge (success, secondary, danger, warning)
     *
     * @example
     * car_status_badge_class('active'); // returns 'success'
     * car_status_badge_class('sold'); // returns 'danger'
     */
    function car_status_badge_class(string $status): string
    {
        $classes = [
            'active'   => 'success',
            'inactive' => 'secondary',
            'sold'     => 'danger',
            'featured' => 'warning',
        ];

        return $classes[$status] ?? 'secondary';
    }
}

if (!function_exists('car_condition_label')) {
    /**
     * إرجاع تسمية حالة السيارة (جديدة/مستعملة) باللغة العربية.
     *
     * @param string $condition حالة السيارة
     *
     * @return string التسمية العربية للحالة
     */
    function car_condition_label(string $condition): string
    {
        $labels = [
            'new'                 => 'جديدة',
            'used'                => 'مستعملة',
            'certified_pre_owned' => 'مستعملة معتمدة',
        ];

        return $labels[$condition] ?? 'غير محدد';
    }
}

if (!function_exists('car_condition_badge_class')) {
    /**
     * إرجاع كلاس Bootstrap badge لحالة السيارة (جديدة/مستعملة).
     *
     * @param string $condition حالة السيارة
     *
     * @return string كلاس Bootstrap badge
     */
    function car_condition_badge_class(string $condition): string
    {
        $classes = [
            'new'                 => 'success',
            'used'                => 'primary',
            'certified_pre_owned' => 'info',
        ];

        return $classes[$condition] ?? 'secondary';
    }
}
