<?php

namespace Modules\UserManagement\Http\Controllers\Site;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Modules\Core\Http\Controllers\BaseController;
use Modules\UserManagement\Http\Requests\Site\RegisterRequest;
use Modules\UserManagement\Http\Requests\Site\LoginRequest;
use Modules\UserManagement\Http\Requests\Site\ResetPasswordRequest;
use Modules\UserManagement\Models\User;
use Modules\Notification\Notifications\UserVerificationOtpNotification;

/**
 * متحكم صفحات المصادقة للموقع العام
 *
 * يتعامل هذا المتحكم مع عرض صفحات المصادقة للعملاء في الموقع العام
 * بما في ذلك التسجيل، تسجيل الدخول، التحقق من OTP، واستعادة كلمة المرور
 */
class SiteAuthController extends BaseController
{
    /**
     * عرض صفحة نموذج التسجيل
     *
     * يعرض نموذج إنشاء حساب جديد للعملاء في الموقع العام
     * يتضمن حقول: الاسم الأول، اسم العائلة، البريد الإلكتروني، رقم الجوال، كلمة المرور
     *
     * @return View
     */
    public function showRegisterForm(): View
    {
        return view('site.auth.register');
    }

    /**
     * عرض صفحة نموذج تسجيل الدخول
     *
     * يعرض نموذج تسجيل الدخول للعملاء في الموقع العام
     * يتضمن حقول: البريد الإلكتروني أو رقم الجوال، كلمة المرور، تذكرني
     *
     * @return View
     */
    public function showLoginForm(): View
    {
        return view('site.auth.login');
    }

    /**
     * عرض صفحة نموذج التحقق من OTP
     *
     * يعرض نموذج إدخال رمز OTP المرسل للجوال بعد التسجيل
     * يمكن تمرير رقم الجوال من الـ request أو الـ session لعرضه في الصفحة
     *
     * @param Request $request
     * @return View
     */
    public function showVerifyOtpForm(Request $request): View
    {
        // يمكن الحصول على رقم الجوال من الـ request أو الـ session
        $phoneNumber = $request->get('phone_number') ?? session('verification_phone_number');

        return view('site.auth.verify_otp', [
            'phone_number' => $phoneNumber
        ]);
    }

    /**
     * عرض صفحة نموذج طلب استعادة كلمة المرور
     *
     * يعرض نموذج طلب إرسال رابط إعادة تعيين كلمة المرور
     * يتضمن حقل البريد الإلكتروني المسجل
     *
     * @return View
     */
    public function showForgotPasswordForm(): View
    {
        return view('site.auth.forgot_password');
    }

    /**
     * عرض صفحة نموذج إعادة تعيين كلمة المرور
     *
     * يعرض نموذج إعادة تعيين كلمة المرور باستخدام الرمز المميز
     * يتم الوصول لهذه الصفحة عبر الرابط المرسل للبريد الإلكتروني
     *
     * @param Request $request
     * @param string $token الرمز المميز لإعادة تعيين كلمة المرور
     * @return View
     */
    public function showResetPasswordForm(Request $request, string $token): View
    {
        // تمرير الرمز المميز والبريد الإلكتروني (إذا كان متوفر) إلى الـ view
        return view('site.auth.reset_password', [
            'token' => $token,
            'email' => $request->get('email')
        ]);
    }

    /**
     * معالجة طلب تسجيل مستخدم جديد
     *
     * يقوم بإنشاء حساب مستخدم جديد، تعيين دور العميل،
     * إرسال رمز OTP للتحقق من رقم الجوال، وإعادة التوجيه لصفحة التحقق
     *
     * @param RegisterRequest $request
     * @return RedirectResponse
     */
    public function register(RegisterRequest $request): RedirectResponse
    {
        try {
            // إنشاء المستخدم الجديد
            $user = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'password' => Hash::make($request->password),
                'status' => 'pending_verification'
            ]);

            // تعيين دور العميل للمستخدم الجديد
            $user->assignRole('Customer');

            // توليد رمز OTP (6 أرقام)
            $otpCode = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);

            // حفظ رمز OTP مع تاريخ انتهاء الصلاحية (10 دقائق)
            $user->update([
                'otp_code' => Hash::make($otpCode), // تشفير رمز OTP
                'otp_expires_at' => now()->addMinutes(10)
            ]);

            // إرسال رمز OTP إلى رقم الجوال
            $user->notify(new UserVerificationOtpNotification($otpCode));

            // حفظ رقم الجوال في الجلسة للتحقق لاحقاً
            session(['verification_phone_number' => $user->phone_number]);

            return redirect()->route('site.auth.verify.otp.form')
                ->with('success', 'تم إنشاء حسابك بنجاح. تم إرسال رمز التحقق إلى رقم جوالك.');

        } catch (\Exception $e) {
            return back()
                ->withInput($request->except('password', 'password_confirmation'))
                ->with('error', 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * معالجة طلب التحقق من رمز OTP
     *
     * يتحقق من صحة رمز OTP المدخل، يفعل الحساب،
     * يسجل دخول المستخدم، ويعيد التوجيه للصفحة المناسبة
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function verifyOtp(Request $request): RedirectResponse
    {
        // التحقق من صحة رمز OTP
        $request->validate([
            'otp_code' => ['required', 'string', 'size:6', 'regex:/^\d{6}$/']
        ], [
            'otp_code.required' => 'رمز التحقق مطلوب.',
            'otp_code.size' => 'رمز التحقق يجب أن يكون 6 أرقام.',
            'otp_code.regex' => 'رمز التحقق يجب أن يحتوي على أرقام فقط.'
        ]);

        try {
            // استرجاع رقم الجوال من الجلسة
            $phoneNumber = session('verification_phone_number');

            if (!$phoneNumber) {
                return redirect()->route('site.auth.register.form')
                    ->with('error', 'انتهت صلاحية جلسة التحقق. يرجى إعادة التسجيل.');
            }

            // البحث عن المستخدم بواسطة رقم الجوال
            $user = User::where('phone_number', $phoneNumber)
                ->where('phone_verified_at', null)
                ->where('otp_expires_at', '>', now())
                ->first();

            if (!$user) {
                return back()->with('error', 'لم يتم العثور على طلب تحقق صالح أو انتهت صلاحية الرمز.');
            }

            // التحقق من تطابق رمز OTP
            if (!Hash::check($request->otp_code, $user->otp_code)) {
                return back()->with('error', 'رمز التحقق غير صحيح. يرجى المحاولة مرة أخرى.');
            }

            // تفعيل الحساب وتسجيل الدخول
            $user->update([
                'phone_verified_at' => now(),
                'status' => 'active',
                'otp_code' => null,
                'otp_expires_at' => null
            ]);

            // تسجيل دخول المستخدم
            Auth::login($user);

            // مسح رقم الجوال من الجلسة
            session()->forget('verification_phone_number');

            // إرسال إشعار ترحيبي (اختياري)
            // $user->notify(new NewUserWelcomeNotification($user));

            return redirect()->intended(route('site.home'))
                ->with('success', 'تم تفعيل حسابك بنجاح! مرحباً بك في ' . config('app.name'));

        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء التحقق من الرمز. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * معالجة طلب تسجيل دخول مستخدم من الموقع العام
     *
     * يقوم بالتحقق من صحة بيانات الاعتماد، التحقق من دور المستخدم وحالة الحساب،
     * إنشاء الجلسة، تحديث آخر تسجيل دخول، وإعادة التوجيه المناسب
     *
     * @param LoginRequest $request
     * @return RedirectResponse
     */
    public function login(LoginRequest $request): RedirectResponse
    {
        try {
            // الحصول على معرف تسجيل الدخول المنظف
            $identifier = $request->getCleanIdentifier();
            $identifierType = $request->getIdentifierType();

            // التحقق من صحة نوع معرف تسجيل الدخول
            if ($identifierType === 'invalid') {
                return back()->withErrors([
                    'identifier' => 'يرجى إدخال بريد إلكتروني أو رقم جوال صالح.'
                ])->withInput($request->except('password'));
            }

            // تحضير بيانات الاعتماد للمصادقة
            $credentials = [
                'password' => $request->password
            ];

            // إضافة الحقل المناسب حسب نوع المعرف
            if ($identifierType === 'email') {
                $credentials['email'] = $identifier;
            } else { // phone
                $credentials['phone_number'] = $identifier;
            }

            // محاولة مصادقة المستخدم
            if (Auth::attempt($credentials, $request->boolean('remember'))) {
                $user = Auth::user();

                // التحقق من أن المستخدم لديه دور 'Customer'
                if (!$user->hasRole('Customer')) {
                    Auth::logout();
                    return back()->withErrors([
                        'identifier' => 'هذا الحساب غير مخول لتسجيل الدخول في الموقع العام.'
                    ])->withInput($request->except('password'));
                }

                // التحقق من أن حالة الحساب نشطة
                if ($user->status !== 'active') {
                    Auth::logout();

                    $errorMessage = match($user->status) {
                        'pending_verification' => 'يجب تفعيل حسابك أولاً. يرجى التحقق من رقم جوالك.',
                        'inactive' => 'حسابك غير نشط. يرجى التواصل مع الإدارة.',
                        'banned' => 'تم حظر حسابك. يرجى التواصل مع الإدارة.',
                        default => 'حسابك غير نشط. يرجى التواصل مع الإدارة.'
                    };

                    return back()->withErrors([
                        'identifier' => $errorMessage
                    ])->withInput($request->except('password'));
                }

                // تجديد الجلسة لمنع session fixation attacks
                $request->session()->regenerate();

                // تحديث آخر تسجيل دخول
                $user->update([
                    'last_login_at' => now()
                ]);

                // إعادة التوجيه إلى الصفحة المقصودة أو لوحة تحكم العميل
                return redirect()->intended(route('site.home'))
                    ->with('success', 'مرحباً بك ' . $user->first_name . '! تم تسجيل دخولك بنجاح.');

            } else {
                // فشل المصادقة - بيانات اعتماد خاطئة
                return back()->withErrors([
                    'identifier' => 'بيانات الاعتماد المدخلة غير صحيحة.'
                ])->withInput($request->except('password'));
            }

        } catch (\Exception $e) {
            // تسجيل الخطأ في اللوج
            \Log::error('خطأ في تسجيل الدخول: ' . $e->getMessage(), [
                'identifier' => $request->identifier ?? 'غير محدد',
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip()
            ]);

            return back()->with('error', 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.')
                ->withInput($request->except('password'));
        }
    }

    /**
     * معالجة طلب إرسال رابط إعادة تعيين كلمة المرور
     *
     * يتحقق من صحة البريد الإلكتروني المدخل ويرسل رابط إعادة التعيين
     * إذا كان البريد مسجلاً في النظام
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function sendResetLinkEmail(Request $request): RedirectResponse
    {
        try {
            // التحقق من صحة البريد الإلكتروني
            $request->validate([
                'email' => ['required', 'email']
            ], [
                'email.required' => 'البريد الإلكتروني مطلوب.',
                'email.email' => 'يرجى إدخال بريد إلكتروني صالح.'
            ]);

            // محاولة إرسال رابط إعادة التعيين
            $status = Password::sendResetLink(
                $request->only('email')
            );

            // معالجة الاستجابة
            if ($status === Password::RESET_LINK_SENT) {
                return back()->with('status', 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.');
            }

            // في حالة عدم وجود البريد الإلكتروني أو أي خطأ آخر
            return back()->withErrors([
                'email' => 'لم نتمكن من العثور على مستخدم بهذا البريد الإلكتروني.'
            ]);

        } catch (\Exception $e) {
            // تسجيل الخطأ في اللوج
            \Log::error('خطأ في إرسال رابط إعادة تعيين كلمة المرور: ' . $e->getMessage(), [
                'email' => $request->email ?? 'غير محدد',
                'ip' => $request->ip()
            ]);

            return back()->with('error', 'حدث خطأ أثناء إرسال رابط إعادة التعيين. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * معالجة طلب إعادة تعيين كلمة المرور
     *
     * يتحقق من صحة الرمز المميز وكلمة المرور الجديدة
     * ويقوم بتحديث كلمة مرور المستخدم
     *
     * @param ResetPasswordRequest $request
     * @return RedirectResponse
     */
    public function resetPassword(ResetPasswordRequest $request): RedirectResponse
    {
        try {
            // محاولة إعادة تعيين كلمة المرور
            $status = Password::reset(
                $request->only('email', 'password', 'password_confirmation', 'token'),
                function ($user, $password) {
                    // تحديث كلمة مرور المستخدم
                    $user->forceFill([
                        'password' => Hash::make($password)
                    ])->setRememberToken(Str::random(60));

                    $user->save();

                    // تسجيل دخول المستخدم تلقائياً بعد إعادة التعيين
                    Auth::login($user);
                }
            );

            // معالجة الاستجابة
            if ($status === Password::PASSWORD_RESET) {
                return redirect()->route('site.home')
                    ->with('success', 'تم إعادة تعيين كلمة المرور بنجاح! مرحباً بك مرة أخرى.');
            }

            // في حالة فشل إعادة التعيين
            $errorMessage = match($status) {
                Password::INVALID_TOKEN => 'رابط إعادة التعيين غير صالح أو منتهي الصلاحية.',
                Password::INVALID_USER => 'لم نتمكن من العثور على مستخدم بهذا البريد الإلكتروني.',
                default => 'حدث خطأ أثناء إعادة تعيين كلمة المرور.'
            };

            return back()->withErrors(['email' => $errorMessage]);

        } catch (\Exception $e) {
            // تسجيل الخطأ في اللوج
            \Log::error('خطأ في إعادة تعيين كلمة المرور: ' . $e->getMessage(), [
                'email' => $request->email ?? 'غير محدد',
                'ip' => $request->ip()
            ]);

            return back()->with('error', 'حدث خطأ أثناء إعادة تعيين كلمة المرور. يرجى المحاولة مرة أخرى.');
        }
    }


}
