<?php

/**
 * ملف الدوال المساعدة العامة للنظام
 *
 * يحتوي هذا الملف على مجموعة من الدوال المساعدة التي يمكن استخدامها في جميع أنحاء التطبيق
 * لتبسيط المهام المتكررة وزيادة كفاءة التطوير
 */
if (! function_exists('setting')) {
    /**
     * الحصول على قيمة إعداد معين من نظام الإعدادات
     *
     * @param  string  $key  مفتاح الإعداد
     * @param  mixed  $default  القيمة الافتراضية إذا لم يوجد الإعداد
     * @return mixed قيمة الإعداد أو القيمة الافتراضية
     */
    function setting(string $key, $default = null)
    {
        return app(\Modules\Core\Services\SettingsService::class)->get($key, $default);
    }
}

if (! function_exists('format_currency')) {
    /**
     * تنسيق رقم كعملة
     *
     * @param  float  $amount  المبلغ المراد تنسيقه
     * @param  string  $currencySymbol  رمز العملة
     * @param  int  $decimals  عدد الأرقام العشرية
     * @param  string  $decimalSeparator  فاصل الأرقام العشرية
     * @param  string  $thousandsSeparator  فاصل الآلاف
     * @return string المبلغ المنسق كعملة
     */
    function format_currency(float $amount, string $currencySymbol = 'ر.س', int $decimals = 2, string $decimalSeparator = '.', string $thousandsSeparator = ','): string
    {
        return number_format($amount, $decimals, $decimalSeparator, $thousandsSeparator).' '.$currencySymbol;
    }
}

if (! function_exists('format_datetime_for_display')) {
    /**
     * تحويل سلسلة تاريخ ووقت إلى تنسيق عرض محدد
     *
     * @param  string|null  $datetimeString  سلسلة التاريخ والوقت
     * @param  string  $format  تنسيق العرض
     * @return string|null التاريخ والوقت المنسق أو null إذا كان الإدخال null أو غير صالح
     */
    function format_datetime_for_display(?string $datetimeString, string $format = 'Y-m-d h:i A'): ?string
    {
        if (is_null($datetimeString)) {
            return null;
        }

        try {
            return \Carbon\Carbon::parse($datetimeString)->translatedFormat($format);
        } catch (\Exception $e) {
            return null;
        }
    }
}

if (! function_exists('generate_otp')) {
    /**
     * توليد رمز OTP رقمي عشوائي
     *
     * @param  int  $length  طول الرمز
     * @return string رمز OTP
     */
    function generate_otp(int $length = 6): string
    {
        $generator = '1234567890';
        $result = '';

        for ($i = 1; $i <= $length; $i++) {
            $result .= substr($generator, (rand() % (strlen($generator))), 1);
        }

        return $result;
    }
}

if (! function_exists('get_permission_translation')) {
    /**
     * الحصول على ترجمة الصلاحية
     *
     * @param string $permission اسم الصلاحية
     * @return array ['name' => 'الاسم المترجم', 'description' => 'الوصف']
     */
    function get_permission_translation(string $permission): array
    {
        $translations = [
            'access_admin_dashboard' => [
                'name' => 'الوصول للوحة التحكم الإدارية',
                'description' => 'يسمح للمستخدم بالوصول إلى لوحة التحكم الإدارية والتنقل بين أقسامها'
            ],
            'access_customer_dashboard' => [
                'name' => 'الوصول للوحة تحكم العميل',
                'description' => 'يسمح للمستخدم بالوصول إلى لوحة تحكم العميل وإدارة حسابه الشخصي'
            ],
            'view_cars_admin' => [
                'name' => 'عرض السيارات في الإدارة',
                'description' => 'يسمح بعرض قائمة السيارات وتفاصيلها في لوحة التحكم الإدارية'
            ],
            'manage_cars_admin' => [
                'name' => 'إدارة السيارات',
                'description' => 'يسمح بإضافة وتعديل وحذف السيارات في لوحة التحكم الإدارية'
            ],
            'manage_car_metadata' => [
                'name' => 'إدارة بيانات السيارات الوصفية',
                'description' => 'يسمح بإدارة الماركات والموديلات والألوان وأنواع الوقود وغيرها من البيانات الوصفية للسيارات'
            ],
            'manage_roles_permissions' => [
                'name' => 'إدارة الأدوار والصلاحيات',
                'description' => 'يسمح بإنشاء وتعديل وحذف الأدوار وتعيين الصلاحيات للمستخدمين'
            ],
            'manage_system_settings' => [
                'name' => 'إدارة إعدادات النظام',
                'description' => 'يسمح بعرض وتعديل إعدادات النظام العامة وإعدادات SEO'
            ],
        ];

        return $translations[$permission] ?? [
            'name' => $permission,
            'description' => 'صلاحية في النظام'
        ];
    }
}

if (! function_exists('format_file_size')) {
    /**
     * تنسيق حجم الملف بوحدة مناسبة
     *
     * @param  int  $bytes  حجم الملف بالبايت
     * @param  int  $precision  عدد الأرقام العشرية
     * @return string حجم الملف المنسق
     */
    function format_file_size(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

if (! function_exists('truncate_text')) {
    /**
     * قطع النص مع إضافة نقاط في النهاية
     *
     * @param  string  $text  النص المراد قطعه
     * @param  int  $length  الطول المطلوب
     * @param  string  $suffix  النص المضاف في النهاية
     * @return string النص المقطوع
     */
    function truncate_text(string $text, int $length = 100, string $suffix = '...'): string
    {
        if (mb_strlen($text) <= $length) {
            return $text;
        }

        return mb_substr($text, 0, $length) . $suffix;
    }
}

if (! function_exists('format_number')) {
    /**
     * تنسيق رقم مع فواصل الآلاف
     *
     * @param  float  $number  الرقم المراد تنسيقه
     * @param  int  $decimals  عدد الأرقام العشرية
     * @param  string  $decimalSeparator  فاصل الأرقام العشرية
     * @param  string  $thousandsSeparator  فاصل الآلاف
     * @return string الرقم المنسق
     */
    function format_number(float $number, int $decimals = 0, string $decimalSeparator = '.', string $thousandsSeparator = ','): string
    {
        return number_format($number, $decimals, $decimalSeparator, $thousandsSeparator);
    }
}

if (! function_exists('get_status_badge')) {
    /**
     * إرجاع HTML badge للحالة
     *
     * @param  string  $status  الحالة
     * @param  string  $label  النص المعروض
     * @param  array  $classMap  خريطة الكلاسات للحالات
     * @return string HTML badge
     */
    function get_status_badge(string $status, string $label, array $classMap = []): string
    {
        $defaultClassMap = [
            'active' => 'success',
            'inactive' => 'secondary',
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            'completed' => 'info',
            'cancelled' => 'dark'
        ];

        $classMap = array_merge($defaultClassMap, $classMap);
        $badgeClass = $classMap[$status] ?? 'secondary';

        return '<span class="badge rounded-pill bg-' . $badgeClass . '">' . htmlspecialchars($label) . '</span>';
    }
}

if (! function_exists('get_permission_group_translation')) {
    /**
     * الحصول على ترجمة مجموعة الصلاحيات
     *
     * @param string $group اسم مجموعة الصلاحيات
     * @return array ['name' => 'الاسم المترجم', 'icon' => 'الأيقونة']
     */
    function get_permission_group_translation(string $group): array
    {
        $groups = [
            'dashboard' => [
                'name' => 'لوحة التحكم',
                'icon' => 'tachometer-alt'
            ],
            'cars' => [
                'name' => 'إدارة السيارات',
                'icon' => 'car'
            ],
            'car_metadata' => [
                'name' => 'بيانات السيارات الوصفية',
                'icon' => 'tags'
            ],
            'users' => [
                'name' => 'إدارة المستخدمين',
                'icon' => 'users'
            ],
            'roles' => [
                'name' => 'الأدوار والصلاحيات',
                'icon' => 'user-shield'
            ],
            'settings' => [
                'name' => 'إعدادات النظام',
                'icon' => 'cogs'
            ],
            'reports' => [
                'name' => 'التقارير',
                'icon' => 'chart-bar'
            ],
            'finance' => [
                'name' => 'الشؤون المالية',
                'icon' => 'money-bill-wave'
            ],
        ];

        return $groups[$group] ?? [
            'name' => $group,
            'icon' => 'circle'
        ];
    }
}
