{{-- 
    الخطوة 3: رفع المستندات - عملية شراء السيارة كاش
    
    يعرض هذا الـ view نموذج لرفع المستندات المطلوبة (الهوية ورخصة القيادة)
    بناءً على UIUX-FR.md (SITE-BUY-CASH-STEPX-001) و REQ-FR.md (MOD-ORDER-MGMT-FEAT-003)
--}}

@extends('site.layouts.site_layout')

@section('title', 'شراء السيارة - رفع المستندات')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            
            {{-- عنوان الصفحة ومؤشر التقدم --}}
            <div class="text-center mb-4">
                <h2 class="mb-3">شراء السيارة كاش</h2>
                
                {{-- مؤشر التقدم (Stepper) --}}
                <div class="progress-stepper mb-4">
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">البيانات الشخصية</div>
                    </div>
                    <div class="step completed">
                        <div class="step-number">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="step-title">تفاصيل الحجز</div>
                    </div>
                    <div class="step active">
                        <div class="step-number">3</div>
                        <div class="step-title">المستندات</div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-title">المراجعة والتأكيد</div>
                    </div>
                </div>
            </div>

            <div class="row">
                {{-- ملخص السيارة --}}
                <div class="col-lg-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                ملخص الطلب
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($car->getFirstMediaUrl('car_main_image') || $car->getFirstMediaUrl('car_images'))
                                <img src="{{ $car->getFirstMediaUrl('car_main_image') ?: $car->getFirstMediaUrl('car_images') }}" 
                                     alt="{{ $car->title }}" 
                                     class="img-fluid rounded mb-3">
                            @endif
                            
                            <h6 class="fw-bold">{{ $car->title }}</h6>
                            <p class="text-muted mb-2">
                                {{ $car->brand->name ?? '' }} - {{ $car->carModel->name ?? '' }}
                            </p>
                            <p class="text-muted mb-3">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $car->manufacturingYear->year ?? '' }}
                            </p>
                            
                            <div class="price-summary">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>مبلغ الحجز:</span>
                                    <span class="fw-bold text-success">
                                        {{ number_format($reservationAmount, 0) }} {{ $car->currency }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- معلومات مهمة --}}
                    <div class="card shadow-sm mt-3">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات مهمة
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0 small">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    الصور يجب أن تكون واضحة ومقروءة
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    حجم الملف لا يتجاوز 5 ميجابايت
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    الصيغ المقبولة: JPG, PNG, PDF
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    جميع البيانات محمية ومشفرة
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                {{-- نموذج رفع المستندات --}}
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-upload me-2"></i>
                                رفع المستندات المطلوبة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('site.order.cash.step4') }}" method="GET" id="documentsForm" enctype="multipart/form-data">
                                
                                {{-- الهوية الوطنية - الوجه الأمامي --}}
                                <div class="document-upload mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-id-card me-2"></i>
                                        الهوية الوطنية/الإقامة - الوجه الأمامي
                                        <span class="text-danger">*</span>
                                    </label>
                                    
                                    <div class="upload-area" id="national_id_front_area">
                                        <input type="file" 
                                               class="form-control d-none" 
                                               id="national_id_front" 
                                               name="national_id_front" 
                                               accept="image/*,.pdf" 
                                               required>
                                        
                                        <div class="upload-placeholder" onclick="document.getElementById('national_id_front').click()">
                                            <i class="fas fa-cloud-upload-alt fs-1 text-muted mb-3"></i>
                                            <p class="mb-2">انقر لاختيار الملف أو اسحب الملف هنا</p>
                                            <small class="text-muted">JPG, PNG, PDF - حتى 5 ميجابايت</small>
                                        </div>
                                        
                                        <div class="upload-preview d-none">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-image text-primary me-2"></i>
                                                    <span class="file-name"></span>
                                                </div>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile('national_id_front')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{-- الهوية الوطنية - الوجه الخلفي --}}
                                <div class="document-upload mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-id-card me-2"></i>
                                        الهوية الوطنية/الإقامة - الوجه الخلفي
                                        <span class="text-danger">*</span>
                                    </label>
                                    
                                    <div class="upload-area" id="national_id_back_area">
                                        <input type="file" 
                                               class="form-control d-none" 
                                               id="national_id_back" 
                                               name="national_id_back" 
                                               accept="image/*,.pdf" 
                                               required>
                                        
                                        <div class="upload-placeholder" onclick="document.getElementById('national_id_back').click()">
                                            <i class="fas fa-cloud-upload-alt fs-1 text-muted mb-3"></i>
                                            <p class="mb-2">انقر لاختيار الملف أو اسحب الملف هنا</p>
                                            <small class="text-muted">JPG, PNG, PDF - حتى 5 ميجابايت</small>
                                        </div>
                                        
                                        <div class="upload-preview d-none">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-image text-primary me-2"></i>
                                                    <span class="file-name"></span>
                                                </div>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile('national_id_back')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{-- رخصة القيادة --}}
                                <div class="document-upload mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-car me-2"></i>
                                        رخصة القيادة
                                        <span class="text-danger">*</span>
                                    </label>
                                    
                                    <div class="upload-area" id="driving_license_area">
                                        <input type="file" 
                                               class="form-control d-none" 
                                               id="driving_license" 
                                               name="driving_license" 
                                               accept="image/*,.pdf" 
                                               required>
                                        
                                        <div class="upload-placeholder" onclick="document.getElementById('driving_license').click()">
                                            <i class="fas fa-cloud-upload-alt fs-1 text-muted mb-3"></i>
                                            <p class="mb-2">انقر لاختيار الملف أو اسحب الملف هنا</p>
                                            <small class="text-muted">JPG, PNG, PDF - حتى 5 ميجابايت</small>
                                        </div>
                                        
                                        <div class="upload-preview d-none">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-image text-primary me-2"></i>
                                                    <span class="file-name"></span>
                                                </div>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile('driving_license')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{-- ملاحظات إضافية --}}
                                <div class="mb-4">
                                    <label for="additional_notes" class="form-label">
                                        <i class="fas fa-comment me-2"></i>
                                        ملاحظات إضافية (اختياري)
                                    </label>
                                    <textarea class="form-control" 
                                              id="additional_notes" 
                                              name="additional_notes" 
                                              rows="3" 
                                              placeholder="أي ملاحظات أو طلبات خاصة..."></textarea>
                                </div>

                                {{-- أزرار التنقل --}}
                                <div class="d-flex justify-content-between mt-4">
                                    <a href="{{ route('site.order.cash.step2') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        السابق
                                    </a>
                                    
                                    <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                        التالي: المراجعة والتأكيد
                                        <i class="fas fa-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress-stepper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.5;
}

.step.active, .step.completed {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.step.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.step.completed .step-number {
    background-color: #198754;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    text-align: center;
}

.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
}

.upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

.upload-preview {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.document-upload {
    position: relative;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInputs = ['national_id_front', 'national_id_back', 'driving_license'];
    
    fileInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        const area = document.getElementById(inputId + '_area');
        
        input.addEventListener('change', function(e) {
            handleFileSelect(e, inputId);
        });
        
        // Drag and drop functionality
        area.addEventListener('dragover', function(e) {
            e.preventDefault();
            area.classList.add('dragover');
        });
        
        area.addEventListener('dragleave', function(e) {
            e.preventDefault();
            area.classList.remove('dragover');
        });
        
        area.addEventListener('drop', function(e) {
            e.preventDefault();
            area.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                input.files = files;
                handleFileSelect({target: input}, inputId);
            }
        });
    });
    
    function handleFileSelect(event, inputId) {
        const file = event.target.files[0];
        const area = document.getElementById(inputId + '_area');
        const placeholder = area.querySelector('.upload-placeholder');
        const preview = area.querySelector('.upload-preview');
        const fileName = preview.querySelector('.file-name');
        
        if (file) {
            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('حجم الملف يجب أن يكون أقل من 5 ميجابايت');
                event.target.value = '';
                return;
            }
            
            // Show preview
            placeholder.classList.add('d-none');
            preview.classList.remove('d-none');
            fileName.textContent = file.name;
            
            checkFormCompletion();
        }
    }
    
    window.removeFile = function(inputId) {
        const input = document.getElementById(inputId);
        const area = document.getElementById(inputId + '_area');
        const placeholder = area.querySelector('.upload-placeholder');
        const preview = area.querySelector('.upload-preview');
        
        input.value = '';
        placeholder.classList.remove('d-none');
        preview.classList.add('d-none');
        
        checkFormCompletion();
    };
    
    function checkFormCompletion() {
        const requiredFiles = ['national_id_front', 'national_id_back', 'driving_license'];
        const submitBtn = document.getElementById('submitBtn');
        
        const allFilesUploaded = requiredFiles.every(inputId => {
            const input = document.getElementById(inputId);
            return input.files.length > 0;
        });
        
        submitBtn.disabled = !allFilesUploaded;
    }
});
</script>
@endpush
