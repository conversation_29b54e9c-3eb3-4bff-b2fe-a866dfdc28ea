@extends('dashboard::layouts.admin_layout')

@section('title', 'تفاصيل فئة الميزة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid">
    {{-- عنوان الصفحة --}}
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">تفاصيل فئة الميزة: {{ $featurecategory->name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item">إدارة السيارات</li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.feature-categories.index') }}">فئات الميزات</a></li>
                        <li class="breadcrumb-item active">التفاصيل</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    {{-- أزرار الإجراءات --}}
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ route('admin.feature-categories.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للقائمة
                    </a>
                </div>
                <div>
                    <a href="{{ route('admin.feature-categories.edit', $featurecategory) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    @if($featurecategory->features_count == 0)
                        <form action="{{ route('admin.feature-categories.destroy', $featurecategory) }}" 
                              method="POST" 
                              style="display: inline;"
                              onsubmit="return confirm('هل أنت متأكد من حذف فئة الميزة هذه؟')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>

    {{-- تفاصيل فئة الميزة --}}
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">معلومات فئة الميزة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">اسم فئة الميزة:</label>
                                <p class="form-control-plaintext">{{ $featurecategory->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge rounded-pill bg-{{ $featurecategory->status ? 'success' : 'danger' }}">
                                        {{ $featurecategory->status ? 'نشط' : 'غير نشط' }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">ترتيب العرض:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-secondary">{{ $featurecategory->display_order }}</span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">عدد الميزات المرتبطة:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-info">{{ $featurecategory->features_count }}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($featurecategory->description)
                        <div class="mb-3">
                            <label class="form-label fw-bold">الوصف:</label>
                            <p class="form-control-plaintext">{{ $featurecategory->description }}</p>
                        </div>
                    @endif

                    @if($featurecategory->created_at)
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                                    <p class="form-control-plaintext">{{ $featurecategory->created_at->format('Y-m-d H:i') }}</p>
                                </div>
                            </div>
                            @if($featurecategory->updated_at)
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">تاريخ آخر تحديث:</label>
                                        <p class="form-control-plaintext">{{ $featurecategory->updated_at->format('Y-m-d H:i') }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">إحصائيات</h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="fas fa-list-alt fa-3x text-primary"></i>
                        </div>
                        <h3 class="text-primary">{{ $featurecategory->features_count }}</h3>
                        <p class="text-muted">ميزة مرتبطة</p>
                    </div>
                    
                    @if($featurecategory->features_count > 0)
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لا يمكن حذف فئة الميزة لأنها مرتبطة بميزات أخرى.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
