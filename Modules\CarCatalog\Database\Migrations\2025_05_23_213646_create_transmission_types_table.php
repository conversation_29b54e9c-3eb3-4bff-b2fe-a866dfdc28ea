<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('transmission_types', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name', 50)->unique()->comment('اسم نوع ناقل الحركة');
            $table->text('description')->nullable()->comment('وصف نوع ناقل الحركة');
            $table->boolean('status')->default(true)->comment('نشط/غير نشط');

            // إنشاء الفهارس
            $table->index('name');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('transmission_types');
    }
};
