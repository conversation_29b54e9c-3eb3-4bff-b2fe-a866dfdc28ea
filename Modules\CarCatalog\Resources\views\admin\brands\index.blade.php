@extends('dashboard::layouts.admin_layout')

@section('title', 'إدارة الماركات')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إدارة الماركات',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'الماركات', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.brands.create') . '" class="btn btn-brand-primary">
            <i class="fas fa-plus me-1"></i> إضافة ماركة جديدة
        </a>'
    ])

    {{-- عرض رسائل النجاح والخطأ --}}
    @if (session('success'))
        <div class="alert brand-alert brand-alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert brand-alert brand-alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {{-- نموذج البحث والفلترة --}}
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.brands.index') }}" class="mb-0">
                <div class="row gx-2 gy-2 align-items-center">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="بحث بالاسم..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">كل الحالات</option>
                            <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>نشط</option>
                            <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-brand-info w-100">بحث</button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ route('admin.brands.index') }}" class="btn btn-brand-secondary w-100">إعادة تعيين</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- جدول الماركات --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">قائمة الماركات</h5>
        </div>
        <div class="card-body table-responsive p-0">
            <table class="table table-hover recent-activity-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الشعار</th>
                        <th>اسم الماركة</th>
                        <th>الحالة</th>
                        <th>عدد الموديلات</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($brands as $brand)
                        <tr>
                            <td>{{ $brand->id }}</td>
                            <td>
                                <img src="{{ $brand->getFirstMediaUrl('brand_logos', 'thumb') ?: '/api/placeholder/50/50?text=No+Logo' }}"
                                     alt="{{ $brand->name }}" width="50" class="img-thumbnail rounded">
                            </td>
                            <td>{{ $brand->name }}</td>
                            <td>
                                <span class="badge rounded-pill bg-{{ $brand->status ? 'success' : 'danger' }}">
                                    {{ $brand->status ? 'نشط' : 'غير نشط' }}
                                </span>
                            </td>
                            <td>{{ $brand->car_models_count }}</td>
                            <td>{{ format_datetime_for_display($brand->created_at, 'Y-m-d') }}</td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('admin.brands.edit', $brand->id) }}" class="btn btn-sm btn-warning me-1 action-btn edit" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.brands.destroy', $brand->id) }}" method="POST" class="d-inline"
                                          onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذه الماركة؟ سيتم حذف الماركة بشكل ناعم.');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger action-btn delete" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center">لا توجد ماركات لعرضها حاليًا.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    {{-- ترقيم الصفحات --}}
    <div class="mt-3">
        {{ $brands->appends(request()->query())->links('pagination::bootstrap-5') }}
    </div>
</div>
@endsection
