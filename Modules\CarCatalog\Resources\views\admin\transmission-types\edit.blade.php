@extends('dashboard::layouts.admin_layout')

@section('title', 'تعديل نوع ناقل الحركة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid">
    {{-- عنوان الصفحة --}}
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">تعديل نوع ناقل الحركة: {{ $transmissiontype->name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.transmission-types.index') }}">أنواع ناقل الحركة</a></li>
                        <li class="breadcrumb-item active">تعديل</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    {{-- نموذج تعديل نوع ناقل الحركة --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">بيانات نوع ناقل الحركة</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.transmission-types.update', $transmissiontype->id) }}" method="POST">
                @csrf
                @method('PUT')
                
                @include('carcatalog::admin.transmission-types._form', ['transmissionType' => $transmissiontype])
                
                <div class="mt-4 d-flex justify-content-end">
                    <button type="submit" class="btn btn-success me-2">تحديث نوع ناقل الحركة</button>
                    <a href="{{ route('admin.transmission-types.index') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
