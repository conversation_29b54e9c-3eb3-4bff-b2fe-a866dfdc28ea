@extends('dashboard::layouts.admin_layout')

@section('title', 'تفاصيل اللون: ' . $color->name)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'تفاصيل اللون: ' . $color->name,
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'الألوان', 'url' => route('admin.colors.index')],
            ['name' => $color->name, 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.colors.edit', $color->id) . '" class="btn btn-brand-warning me-2">
            <i class="fas fa-edit me-1"></i> تعديل
        </a>
        <a href="' . route('admin.colors.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>'
    ])

    <div class="row">
        {{-- بيانات اللون --}}
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">معلومات اللون</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">اسم اللون:</label>
                                <p class="mb-0">{{ $color->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">كود Hex:</label>
                                <p class="mb-0">
                                    @if($color->hex_code)
                                        <code>{{ $color->hex_code }}</code>
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p class="mb-0">
                                    <span class="badge rounded-pill bg-{{ $color->status ? 'success' : 'danger' }}">
                                        {{ $color->status ? 'نشط' : 'غير نشط' }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">عدد السيارات المرتبطة:</label>
                                <p class="mb-0">{{ $color->cars_count ?? 0 }} سيارة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- معاينة اللون --}}
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">معاينة اللون</h5>
                </div>
                <div class="card-body text-center">
                    @if($color->hex_code)
                        <div style="width: 150px; height: 150px; background-color: {{ $color->hex_code }}; border: 1px solid #ddd; border-radius: 8px; margin: 0 auto;"
                             title="{{ $color->hex_code }}"></div>
                        <p class="mt-3 mb-0">
                            <strong>{{ $color->name }}</strong><br>
                            <code>{{ $color->hex_code }}</code>
                        </p>
                    @else
                        <div class="text-muted">
                            <i class="fas fa-palette fa-3x mb-3"></i>
                            <p>لا يوجد كود لون محدد</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
