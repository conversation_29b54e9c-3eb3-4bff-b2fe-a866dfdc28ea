<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\UserManagement\Models\Nationality;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // تعديل جدول المستخدمين الموجود
        Schema::table('users', function (Blueprint $table) {
            // حذف عمود name الموجود
            $table->dropColumn('name');

            // إضافة الأعمدة الجديدة
            $table->string('first_name', 100)->after('id');
            $table->string('last_name', 100)->after('first_name');
            $table->string('phone_number', 20)->unique()->after('email');
            $table->timestamp('phone_verified_at')->nullable()->after('email_verified_at');
            $table->unsignedBigInteger('profile_photo_id')->nullable()->after('password');
            $table->string('status', 50)->default('pending_verification')->after('profile_photo_id');
            $table->boolean('can_refer_customer')->default(false)->after('status');
            $table->timestamp('last_login_at')->nullable()->after('can_refer_customer');
            $table->string('address_line1', 255)->nullable()->after('last_login_at');
            $table->string('city', 100)->nullable()->after('address_line1');
            $table->string('national_id', 20)->nullable()->after('city');
            $table->date('date_of_birth')->nullable()->after('national_id');
            $table->foreignIdFor(Nationality::class)->nullable()->after('date_of_birth')->constrained()->onDelete('set null');
            $table->string('otp_code', 10)->nullable()->after('nationality_id');
            $table->timestamp('otp_expires_at')->nullable()->after('otp_code');
            $table->softDeletes();

            // إضافة الفهارس
            $table->index('first_name');
            $table->index('last_name');
            $table->index('status');
            $table->index('profile_photo_id');
            $table->index('nationality_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            // إزالة الفهارس
            $table->dropIndex(['first_name']);
            $table->dropIndex(['last_name']);
            $table->dropIndex(['status']);
            $table->dropIndex(['profile_photo_id']);
            $table->dropIndex(['nationality_id']);

            // إزالة العلاقات الخارجية
            $table->dropForeign(['nationality_id']);

            // إزالة الأعمدة المضافة
            $table->dropColumn([
                'first_name',
                'last_name',
                'phone_number',
                'phone_verified_at',
                'profile_photo_id',
                'status',
                'can_refer_customer',
                'last_login_at',
                'address_line1',
                'city',
                'national_id',
                'date_of_birth',
                'nationality_id',
                'otp_code',
                'otp_expires_at',
                'deleted_at',
            ]);

            // إعادة إضافة عمود name
            $table->string('name')->after('id');
        });
    }
};
