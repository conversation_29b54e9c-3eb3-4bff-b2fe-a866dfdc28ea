<?php

namespace App\Helpers;

use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * Class MediaHelper
 * 
 * مساعد لإدارة الوسائط والصور في المشروع
 * يوفر طرق مساعدة للتعامل مع الصور والملفات
 * 
 * @package App\Helpers
 */
class MediaHelper
{
    /**
     * أحجام التحويلات المعيارية
     */
    public const CONVERSION_SIZES = [
        'thumb' => ['width' => 150, 'height' => 150],
        'medium' => ['width' => 400, 'height' => 300],
        'large' => ['width' => 800, 'height' => 600],
        'print' => ['width' => 1920, 'height' => 1440],
    ];

    /**
     * أحجام شعارات الماركات
     */
    public const BRAND_LOGO_SIZES = [
        'thumb' => ['width' => 50, 'height' => 50],
        'medium' => ['width' => 100, 'height' => 100],
        'large' => ['width' => 200, 'height' => 200],
    ];

    /**
     * أحجام الأيقونات
     */
    public const ICON_SIZES = [
        'thumb' => ['width' => 32, 'height' => 32],
        'medium' => ['width' => 64, 'height' => 64],
        'large' => ['width' => 128, 'height' => 128],
    ];

    /**
     * أنواع الملفات المدعومة للصور
     */
    public const SUPPORTED_IMAGE_TYPES = [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/svg+xml'
    ];

    /**
     * أنواع الملفات المدعومة للمستندات
     */
    public const SUPPORTED_DOCUMENT_TYPES = [
        'application/pdf',
        'image/jpeg',
        'image/png'
    ];

    /**
     * الحصول على URL الصورة مع fallback
     *
     * @param HasMedia $model
     * @param string $collection
     * @param string $conversion
     * @param string $fallback
     * @return string
     */
    public static function getImageUrl(HasMedia $model, string $collection = 'default', string $conversion = 'thumb', string $fallback = '/images/no-image.png'): string
    {
        $media = $model->getFirstMedia($collection);
        
        if ($media) {
            return $media->getUrl($conversion);
        }
        
        return asset($fallback);
    }

    /**
     * الحصول على معلومات الصورة
     *
     * @param Media $media
     * @return array
     */
    public static function getImageInfo(Media $media): array
    {
        return [
            'id' => $media->id,
            'name' => $media->name,
            'file_name' => $media->file_name,
            'mime_type' => $media->mime_type,
            'size' => $media->size,
            'human_readable_size' => $media->human_readable_size,
            'url' => $media->getUrl(),
            'conversions' => $media->generated_conversions,
        ];
    }

    /**
     * التحقق من نوع الملف
     *
     * @param string $mimeType
     * @param string $type
     * @return bool
     */
    public static function isValidFileType(string $mimeType, string $type = 'image'): bool
    {
        $supportedTypes = $type === 'image' 
            ? self::SUPPORTED_IMAGE_TYPES 
            : self::SUPPORTED_DOCUMENT_TYPES;
            
        return in_array($mimeType, $supportedTypes);
    }

    /**
     * تنسيق حجم الملف
     *
     * @param int $bytes
     * @return string
     */
    public static function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * إنشاء HTML للصورة مع التحويلات
     *
     * @param HasMedia $model
     * @param string $collection
     * @param array $attributes
     * @return string
     */
    public static function renderImage(HasMedia $model, string $collection = 'default', array $attributes = []): string
    {
        $media = $model->getFirstMedia($collection);
        
        if (!$media) {
            $fallback = $attributes['fallback'] ?? '/images/no-image.png';
            $alt = $attributes['alt'] ?? 'صورة غير متوفرة';
            $class = $attributes['class'] ?? '';
            
            return "<img src='" . asset($fallback) . "' alt='{$alt}' class='{$class}'>";
        }

        $conversion = $attributes['conversion'] ?? 'thumb';
        $alt = $attributes['alt'] ?? $media->name;
        $class = $attributes['class'] ?? '';
        $style = $attributes['style'] ?? '';
        
        $url = $media->getUrl($conversion);
        
        return "<img src='{$url}' alt='{$alt}' class='{$class}' style='{$style}'>";
    }

    /**
     * الحصول على جميع التحويلات المتاحة للصورة
     *
     * @param Media $media
     * @return array
     */
    public static function getAvailableConversions(Media $media): array
    {
        $conversions = [];
        
        foreach ($media->generated_conversions as $conversion => $generated) {
            if ($generated) {
                $conversions[$conversion] = [
                    'url' => $media->getUrl($conversion),
                    'generated' => $generated
                ];
            }
        }
        
        return $conversions;
    }

    /**
     * حذف جميع الوسائط من مجموعة معينة
     *
     * @param HasMedia $model
     * @param string $collection
     * @return bool
     */
    public static function clearCollection(HasMedia $model, string $collection): bool
    {
        try {
            $model->clearMediaCollection($collection);
            return true;
        } catch (\Exception $e) {
            \Log::error('Error clearing media collection: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * نسخ الوسائط من نموذج إلى آخر
     *
     * @param HasMedia $source
     * @param HasMedia $target
     * @param string $sourceCollection
     * @param string $targetCollection
     * @return bool
     */
    public static function copyMedia(HasMedia $source, HasMedia $target, string $sourceCollection, string $targetCollection = null): bool
    {
        try {
            $targetCollection = $targetCollection ?? $sourceCollection;
            
            $mediaItems = $source->getMedia($sourceCollection);
            
            foreach ($mediaItems as $media) {
                $target->addMediaFromUrl($media->getUrl())
                    ->toMediaCollection($targetCollection);
            }
            
            return true;
        } catch (\Exception $e) {
            \Log::error('Error copying media: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إحصائيات الوسائط للنموذج
     *
     * @param HasMedia $model
     * @return array
     */
    public static function getMediaStats(HasMedia $model): array
    {
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'collections' => []
        ];

        $allMedia = $model->getMedia();
        $stats['total_files'] = $allMedia->count();
        $stats['total_size'] = $allMedia->sum('size');

        foreach ($allMedia->groupBy('collection_name') as $collection => $media) {
            $stats['collections'][$collection] = [
                'count' => $media->count(),
                'size' => $media->sum('size'),
                'human_readable_size' => self::formatFileSize($media->sum('size'))
            ];
        }

        $stats['human_readable_total_size'] = self::formatFileSize($stats['total_size']);

        return $stats;
    }
}
