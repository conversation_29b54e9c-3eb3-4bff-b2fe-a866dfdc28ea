<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('cars', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('title', 200)->comment('عنوان السيارة');
            $table->text('description')->nullable()->comment('وصف السيارة');
            $table->unsignedBigInteger('brand_id')->comment('معرّف الماركة');
            $table->unsignedBigInteger('car_model_id')->comment('معرّف الموديل');
            $table->unsignedBigInteger('manufacturing_year_id')->comment('معرّف سنة الصنع');
            $table->unsignedBigInteger('body_type_id')->comment('معرّف نوع الهيكل');
            $table->unsignedBigInteger('transmission_type_id')->comment('معرّف نوع ناقل الحركة');
            $table->unsignedBigInteger('fuel_type_id')->comment('معرّف نوع الوقود');
            $table->unsignedBigInteger('main_color_id')->comment('معرّف اللون الرئيسي');
            $table->unsignedBigInteger('interior_color_id')->nullable()->comment('معرّف لون المقصورة الداخلية');
            $table->decimal('price', 12, 2)->comment('سعر السيارة');
            $table->string('currency', 3)->default('SAR')->comment('عملة السعر');
            $table->decimal('offer_price', 12, 2)->nullable()->comment('سعر العرض الخاص');
            $table->date('offer_start_date')->nullable()->comment('تاريخ بداية العرض');
            $table->date('offer_end_date')->nullable()->comment('تاريخ نهاية العرض');
            $table->unsignedInteger('mileage')->nullable()->comment('عدد الكيلومترات المقطوعة');
            $table->string('mileage_unit', 10)->default('km')->comment('وحدة قياس المسافة');
            $table->unsignedInteger('engine_capacity')->nullable()->comment('سعة المحرك بالسي سي');
            $table->unsignedTinyInteger('doors_count')->nullable()->comment('عدد الأبواب');
            $table->unsignedTinyInteger('seats_count')->nullable()->comment('عدد المقاعد');
            $table->string('vin', 50)->nullable()->unique()->comment('رقم الهيكل VIN');
            $table->string('plate_number', 20)->nullable()->comment('رقم اللوحة');
            $table->enum('condition', ['new', 'used', 'certified_pre_owned'])->default('used')->comment('حالة السيارة (جديدة/مستعملة/معتمدة)');
            $table->string('video_url')->nullable()->comment('رابط فيديو السيارة');
            $table->boolean('is_featured')->default(false)->comment('سيارة مميزة');
            $table->boolean('is_sold')->default(false)->comment('تم بيعها');
            $table->boolean('is_active')->default(true)->comment('نشطة/غير نشطة');
            $table->unsignedBigInteger('created_by')->nullable()->comment('معرّف المستخدم الذي أنشأ السيارة');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('معرّف المستخدم الذي عدل السيارة');
            $table->timestamps();
            $table->softDeletes();

            // إنشاء الفهارس
            $table->index('title');
            $table->index('brand_id');
            $table->index('car_model_id');
            $table->index('manufacturing_year_id');
            $table->index('body_type_id');
            $table->index('transmission_type_id');
            $table->index('fuel_type_id');
            $table->index('main_color_id');
            $table->index('interior_color_id');
            $table->index('price');
            $table->index('currency');
            $table->index('offer_price');
            $table->index('offer_start_date');
            $table->index('offer_end_date');
            $table->index('mileage');
            $table->index('engine_capacity');
            $table->index('condition');
            $table->index('is_featured');
            $table->index('is_sold');
            $table->index('is_active');
            $table->index('created_by');
            $table->index('updated_by');

            // إنشاء المفاتيح الخارجية
            $table->foreign('brand_id')
                ->references('id')
                ->on('brands')
                ->onDelete('restrict')
                ->onUpdate('cascade');

            $table->foreign('car_model_id')
                ->references('id')
                ->on('car_models')
                ->onDelete('restrict')
                ->onUpdate('cascade');

            $table->foreign('manufacturing_year_id')
                ->references('id')
                ->on('manufacturing_years')
                ->onDelete('restrict')
                ->onUpdate('cascade');

            $table->foreign('body_type_id')
                ->references('id')
                ->on('body_types')
                ->onDelete('restrict')
                ->onUpdate('cascade');

            $table->foreign('transmission_type_id')
                ->references('id')
                ->on('transmission_types')
                ->onDelete('restrict')
                ->onUpdate('cascade');

            $table->foreign('fuel_type_id')
                ->references('id')
                ->on('fuel_types')
                ->onDelete('restrict')
                ->onUpdate('cascade');

            $table->foreign('main_color_id')
                ->references('id')
                ->on('colors')
                ->onDelete('restrict')
                ->onUpdate('cascade');

            $table->foreign('interior_color_id')
                ->references('id')
                ->on('colors')
                ->onDelete('restrict')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('cars');
    }
};
