@extends('dashboard::layouts.admin_layout')

@section('title', 'إضافة سنة صنع جديدة')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/brand-identity.css') }}">
@endpush

@section('content')
<div class="container-fluid py-4">
    {{-- عنوان الصفحة ومسار التنقل --}}
    @include('dashboard::layouts.partials._breadcrumbs', [
        'title' => 'إضافة سنة صنع جديدة',
        'breadcrumbs' => [
            ['name' => 'لوحة التحكم', 'url' => route('admin.dashboard')],
            ['name' => 'إدارة السيارات', 'url' => null],
            ['name' => 'سنوات الصنع', 'url' => route('admin.years.index')],
            ['name' => 'إضافة جديدة', 'active' => true]
        ],
        'actions' => '<a href="' . route('admin.years.index') . '" class="btn btn-brand-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>'
    ])

    {{-- نموذج إضافة سنة صنع --}}
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">بيانات سنة الصنع</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.years.store') }}" method="POST">
                        @csrf

                        @include('carcatalog::admin.years._form')

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.years.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        {{-- معلومات إضافية --}}
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">معلومات مهمة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> ملاحظات:</h6>
                        <ul class="mb-0">
                            <li>سنة الصنع يجب أن تكون من 2000 إلى {{ date('Y') + 2 }}</li>
                            <li>لا يمكن تكرار نفس السنة</li>
                            <li>يمكن تعطيل السنة بدلاً من حذفها</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
