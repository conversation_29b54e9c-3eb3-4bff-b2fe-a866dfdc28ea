<?php

namespace Modules\CarCatalog\Http\Controllers\Admin;

use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Modules\CarCatalog\Http\Requests\Admin\StoreManufacturingYearRequest;
use Modules\CarCatalog\Http\Requests\Admin\UpdateManufacturingYearRequest;
use Modules\CarCatalog\Models\ManufacturingYear;
use Modules\Core\Http\Controllers\BaseController;

/**
 * وحدة تحكم إدارة سنوات صنع السيارات.
 *
 * تتعامل هذه الوحدة مع عمليات CRUD لسنوات صنع السيارات في لوحة تحكم الإدارة
 */
class ManufacturingYearController extends BaseController
{
    /**
     * عرض قائمة سنوات صنع السيارات.
     *
     * @param Request $request طلب HTTP
     *
     * @return View
     */
    public function index(Request $request): View
    {
        $query = ManufacturingYear::withCount('cars');

        // تطبيق فلتر البحث بالسنة إذا تم تقديمه
        if ($request->filled('search')) {
            $query->where('year', 'like', '%' . $request->input('search') . '%');
        }

        // تطبيق فلتر الحالة إذا تم تقديمه
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // ترتيب النتائج حسب السنة تنازليًا
        $query->orderBy('year', 'desc');

        // تطبيق التصفح مع الاحتفاظ بمعاملات البحث
        $years = $query->paginate(15)->withQueryString();

        return view('carcatalog::admin.years.index', compact('years'));
    }

    /**
     * عرض نموذج إضافة سنة صنع جديدة.
     *
     * @return View
     */
    public function create(): View
    {
        return view('carcatalog::admin.years.create');
    }

    /**
     * تخزين سنة صنع جديدة.
     *
     * @param StoreManufacturingYearRequest $request طلب تخزين سنة صنع
     *
     * @return RedirectResponse
     */
    public function store(StoreManufacturingYearRequest $request): RedirectResponse
    {
        // إنشاء سنة الصنع باستخدام البيانات المتحقق منها
        ManufacturingYear::create($request->validated());

        return redirect()->route('admin.years.index')->with('success', 'تمت إضافة سنة الصنع بنجاح.');
    }

    /**
     * عرض تفاصيل سنة صنع محددة.
     *
     * @param ManufacturingYear $year سنة الصنع المراد عرضها
     *
     * @return View
     */
    public function show(ManufacturingYear $year): View
    {
        // تحميل السيارات المرتبطة بسنة الصنع
        $year->loadCount('cars');

        return view('carcatalog::admin.years.show', compact('year'));
    }

    /**
     * عرض نموذج تعديل سنة صنع موجودة.
     *
     * @param ManufacturingYear $year سنة الصنع المراد تعديلها
     *
     * @return View
     */
    public function edit(ManufacturingYear $year): View
    {
        return view('carcatalog::admin.years.edit', compact('year'));
    }

    /**
     * تحديث سنة صنع موجودة.
     *
     * @param UpdateManufacturingYearRequest $request طلب تحديث سنة صنع
     * @param ManufacturingYear $year سنة الصنع المراد تحديثها
     *
     * @return RedirectResponse
     */
    public function update(UpdateManufacturingYearRequest $request, ManufacturingYear $year): RedirectResponse
    {
        // تحديث سنة الصنع باستخدام البيانات المتحقق منها
        $year->update($request->validated());

        return redirect()->route('admin.years.index')->with('success', 'تم تحديث سنة الصنع بنجاح.');
    }

    /**
     * حذف سنة صنع موجودة.
     *
     * @param ManufacturingYear $year سنة الصنع المراد حذفها
     *
     * @return RedirectResponse
     */
    public function destroy(ManufacturingYear $year): RedirectResponse
    {
        // التحقق من وجود سيارات مرتبطة بسنة الصنع
        if ($year->cars()->count() > 0) {
            return redirect()->route('admin.years.index')
                ->with('error', 'لا يمكن حذف سنة الصنع لأنها مرتبطة بسيارات موجودة.');
        }

        // حذف سنة الصنع
        $year->delete();

        return redirect()->route('admin.years.index')->with('success', 'تم حذف سنة الصنع بنجاح.');
    }
}
