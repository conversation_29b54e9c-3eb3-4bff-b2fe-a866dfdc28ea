{"php": "8.2.12", "version": "3.75.0:v3.75.0#399a128ff2fdaf4281e4e79b755693286cdf325c", "indent": "    ", "lineEnding": "\n", "rules": {"binary_operator_spaces": {"default": "single_space", "operators": {"=>": "align_single_space_minimal"}}, "blank_line_after_opening_tag": true, "blank_line_between_import_groups": true, "blank_lines_before_namespace": true, "braces_position": {"allow_single_line_empty_anonymous_classes": true}, "class_definition": {"inline_constructor_arguments": false, "space_before_parenthesis": true}, "compact_nullable_type_declaration": true, "declare_equal_normalize": true, "lowercase_cast": true, "lowercase_static_reference": true, "new_with_parentheses": true, "no_blank_lines_after_class_opening": true, "no_extra_blank_lines": {"tokens": ["extra", "throw", "use", "use_trait"]}, "no_leading_import_slash": true, "no_whitespace_in_blank_line": true, "ordered_class_elements": {"order": ["use_trait"]}, "ordered_imports": {"sort_algorithm": "alpha", "imports_order": ["class", "function", "const"]}, "return_type_declaration": true, "short_scalar_cast": true, "single_import_per_statement": true, "single_space_around_construct": {"constructs_followed_by_a_single_space": ["abstract", "as", "case", "catch", "class", "const_import", "do", "else", "elseif", "final", "finally", "for", "foreach", "function", "function_import", "if", "insteadof", "interface", "namespace", "new", "private", "protected", "public", "static", "switch", "trait", "try", "use", "use_lambda", "while"], "constructs_preceded_by_a_single_space": ["as", "else", "elseif", "use_lambda"]}, "single_trait_insert_per_statement": true, "ternary_operator_spaces": true, "unary_operator_spaces": true, "visibility_required": true, "blank_line_after_namespace": true, "constant_case": true, "control_structure_braces": true, "control_structure_continuation_position": true, "elseif": true, "function_declaration": true, "indentation_type": true, "line_ending": true, "lowercase_keywords": true, "method_argument_space": {"on_multiline": "ensure_fully_multiline", "keep_multiple_spaces_after_comma": true}, "no_break_comment": true, "no_closing_tag": true, "no_multiple_statements_per_line": true, "no_space_around_double_colon": true, "no_spaces_after_function_name": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "single_blank_line_at_eof": true, "single_class_element_per_statement": true, "single_line_after_imports": true, "spaces_inside_parentheses": true, "statement_indentation": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "encoding": true, "full_opening_tag": true, "array_syntax": {"syntax": "short"}, "array_indentation": true, "trim_array_spaces": true, "no_trailing_comma_in_singleline_array": true, "trailing_comma_in_multiline": {"elements": ["arrays"]}, "blank_line_before_statement": {"statements": ["return", "throw", "try"]}, "braces": {"allow_single_line_closure": true, "position_after_functions_and_oop_constructs": "next", "position_after_control_structures": "same", "position_after_anonymous_constructs": "same"}, "cast_spaces": {"space": "single"}, "class_attributes_separation": {"elements": {"method": "one", "property": "one"}}, "single_line_comment_style": {"comment_types": ["hash"]}, "multiline_comment_opening_closing": true, "no_alternative_syntax": true, "no_superfluous_elseif": true, "no_useless_else": true, "function_typehint_space": true, "no_spaces_inside_parenthesis": true, "no_unused_imports": true, "magic_constant_casing": true, "native_function_casing": true, "no_alias_functions": true, "no_leading_namespace_whitespace": true, "concat_space": {"spacing": "one"}, "increment_style": {"style": "post"}, "logical_operators": true, "new_with_braces": true, "object_operator_without_whitespace": true, "standardize_not_equals": true, "phpdoc_add_missing_param_annotation": true, "phpdoc_align": {"align": "left"}, "phpdoc_annotation_without_dot": true, "phpdoc_indent": true, "phpdoc_inline_tag_normalizer": true, "phpdoc_no_access": true, "phpdoc_no_alias_tag": true, "phpdoc_no_empty_return": true, "phpdoc_no_package": true, "phpdoc_no_useless_inheritdoc": true, "phpdoc_return_self_reference": true, "phpdoc_scalar": true, "phpdoc_separation": true, "phpdoc_single_line_var_spacing": true, "phpdoc_summary": true, "phpdoc_to_comment": true, "phpdoc_trim": true, "phpdoc_trim_consecutive_blank_line_separation": true, "phpdoc_types": true, "phpdoc_types_order": {"null_adjustment": "always_last"}, "phpdoc_var_without_name": true, "no_useless_return": true, "simplified_null_return": true, "no_empty_statement": true, "no_singleline_whitespace_before_semicolons": true, "semicolon_after_instruction": true, "space_after_semicolon": {"remove_in_empty_for_expressions": true}, "single_quote": true, "escape_implicit_backslashes": true, "explicit_string_variable": true, "heredoc_to_nowdoc": true, "no_binary_string": true, "compact_nullable_typehint": true, "no_spaces_around_offset": true, "no_whitespace_before_comma_in_array": true, "normalize_index_brace": true, "types_spaces": true, "whitespace_after_comma_in_array": true}, "hashes": {"Modules\\CarCatalog\\Config\\config.php": "97cae96648da03faa8641105db9f7fa3", "Modules\\CarCatalog\\Database\\factories\\TransmissionTypeFactory.php": "ef4473812884d04fad3aba556109d1c5", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_213014_create_brands_table.php": "4624769026c9063cd6f32fe0e4668e85", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_213136_create_car_models_table.php": "5c1427ea696e2f3e741cbd4e3498d4cf", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_213440_create_manufacturing_years_table.php": "a665f34aadb52ca7c6efe254bf1144a8", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_213544_create_colors_table.php": "cceea702295f65208394984b2a71b6b6", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_213646_create_transmission_types_table.php": "5066b0bdc9cb80fc652e41814ce33842", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_213747_create_fuel_types_table.php": "70f491912c75a06361b0caf41f1d4d6b", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_213845_create_body_types_table.php": "2637441e261a6c00e79f690ccdd40475", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_213954_create_feature_categories_table.php": "d5c1938afc8bd2816c046b8cad5b7541", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_214224_create_car_features_table.php": "5d7e611947235b78e791839156da7594", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_214336_create_cars_table.php": "a1c71337510253c298fc6eae84663b63", "Modules\\CarCatalog\\Database\\Migrations\\2025_05_23_214508_create_car_car_feature_table.php": "4ee46ef13a1c6a1dd6bba28a10a19a40", "Modules\\CarCatalog\\Database\\Seeders\\CarCatalogDatabaseSeeder.php": "cb71336add53d4e5bc99a7116017f2b6", "Modules\\CarCatalog\\Database\\Seeders\\CarCatalogTestDataSeeder.php": "194dba0ffefad87c5bab3bf3855fdef0", "Modules\\CarCatalog\\Database\\Seeders\\TestOrdersSeeder.php": "95a1354c46aca245008cebc75cbba30d", "Modules\\CarCatalog\\Helpers\\helpers.php": "cbfcbd1c671dca21cbb17fd90375bf05", "Modules\\CarCatalog\\Http\\Controllers\\Admin\\BodyTypeController.php": "3ea5e9ac24e797cf376f49714e000098", "Modules\\CarCatalog\\Http\\Controllers\\Admin\\BrandController.php": "bb51b6a24fd4066d3dc7d87182d9ba41", "Modules\\CarCatalog\\Http\\Controllers\\Admin\\CarController.php": "fe7d8b3a85ab0b7ffc91b677a918a073", "Modules\\CarCatalog\\Http\\Controllers\\Admin\\CarFeatureController.php": "70cfc8589c11e4b3d51140a3a96478d1", "Modules\\CarCatalog\\Http\\Controllers\\Admin\\CarModelController.php": "0d99c60d1fef2c6a25d1bb207263950a", "Modules\\CarCatalog\\Http\\Controllers\\Admin\\ColorController.php": "8cbd054d9fc0382181aaf5b398a786fe", "Modules\\CarCatalog\\Http\\Controllers\\Admin\\FeatureCategoryController.php": "969d0530ea325cff0de368335c52db7b", "Modules\\CarCatalog\\Http\\Controllers\\Admin\\FuelTypeController.php": "cc47b4bfc2397df55c10a7bce10eb07b", "Modules\\CarCatalog\\Http\\Controllers\\Admin\\ManufacturingYearController.php": "ab73eb6b36137ad82481784d1e8f86af", "Modules\\CarCatalog\\Http\\Controllers\\Admin\\TransmissionTypeController.php": "5083a362b4482db030f5caaf84eeccaa", "Modules\\CarCatalog\\Http\\Controllers\\CarCatalogController.php": "c599ef5675075cfe34338b5bd01e90b9", "Modules\\CarCatalog\\Http\\Requests\\Admin\\StoreBodyTypeRequest.php": "8ebbc7ccb2f37b8f828cc1e0f1d8ad4b", "Modules\\CarCatalog\\Http\\Requests\\Admin\\StoreBrandRequest.php": "276d0c0847d61f443a95539b9046503d", "Modules\\CarCatalog\\Http\\Requests\\Admin\\StoreCarFeatureRequest.php": "8b7dc6279f4844c42beed5d6a00a9651", "Modules\\CarCatalog\\Http\\Requests\\Admin\\StoreCarModelRequest.php": "c96a70a273f9f008aaa5e2615b7529b7", "Modules\\CarCatalog\\Http\\Requests\\Admin\\StoreCarRequest.php": "c202c4feda5f91c405ca7714c7139248", "Modules\\CarCatalog\\Http\\Requests\\Admin\\StoreColorRequest.php": "1982729b247efcc13d0826a628a75619", "Modules\\CarCatalog\\Http\\Requests\\Admin\\StoreFeatureCategoryRequest.php": "49fb8841811c0a526bd55ce71e254a1b", "Modules\\CarCatalog\\Http\\Requests\\Admin\\StoreFuelTypeRequest.php": "9c5fca5d863a091071bdf33ddf7a8e5c", "Modules\\CarCatalog\\Http\\Requests\\Admin\\StoreManufacturingYearRequest.php": "02824940ddf439c636e1d50dea5b2fd4", "Modules\\CarCatalog\\Http\\Requests\\Admin\\StoreTransmissionTypeRequest.php": "7c4a563e3d0fa69036574af60640365c", "Modules\\CarCatalog\\Http\\Requests\\Admin\\UpdateBodyTypeRequest.php": "958c8c64ff52503679df458ab7171555", "Modules\\CarCatalog\\Http\\Requests\\Admin\\UpdateBrandRequest.php": "a75c45ee29de470b848922fce238122d", "Modules\\CarCatalog\\Http\\Requests\\Admin\\UpdateCarFeatureRequest.php": "9f733c103aab57795f0e9bf09992d762", "Modules\\CarCatalog\\Http\\Requests\\Admin\\UpdateCarModelRequest.php": "2fe4195339725b15114cc6307cd1bbe3", "Modules\\CarCatalog\\Http\\Requests\\Admin\\UpdateCarRequest.php": "d7f212507e591e2ca490d3b6e12b4131", "Modules\\CarCatalog\\Http\\Requests\\Admin\\UpdateColorRequest.php": "da2c3f5b2873e84472e0f4a5c58398e0", "Modules\\CarCatalog\\Http\\Requests\\Admin\\UpdateFeatureCategoryRequest.php": "47fa035374860bbb4d64a1e518a1e96d", "Modules\\CarCatalog\\Http\\Requests\\Admin\\UpdateFuelTypeRequest.php": "9278d786ebbdc2c78cba8ecf67841eac", "Modules\\CarCatalog\\Http\\Requests\\Admin\\UpdateManufacturingYearRequest.php": "88f0094e3fdbc3e139fefc9eb4be7381", "Modules\\CarCatalog\\Http\\Requests\\Admin\\UpdateTransmissionTypeRequest.php": "2782ff8cf10592a7acd12e0616045d78", "Modules\\CarCatalog\\Models\\BodyType.php": "f03d84bf09548bb9d0bc493c92182084", "Modules\\CarCatalog\\Models\\Brand.php": "1697643817e57cb0afebbc525d91c108", "Modules\\CarCatalog\\Models\\Car.php": "299488875c6495c30aba8e42c8986982", "Modules\\CarCatalog\\Models\\CarFeature.php": "09200da47602c608d41b6ed4e6b1e3ea", "Modules\\CarCatalog\\Models\\CarModel.php": "5328115bc9ae13529b1597d9bf8b2e0f", "Modules\\CarCatalog\\Models\\Color.php": "907136fe4100a233a1dd65c5751b9d69", "Modules\\CarCatalog\\Models\\FeatureCategory.php": "ad1bda451cf5b2d9ad82370fc5a9201a", "Modules\\CarCatalog\\Models\\FuelType.php": "a2d9f1f7c005aad227c1fc85f9042e61", "Modules\\CarCatalog\\Models\\ManufacturingYear.php": "72366ab88ce5f9a880ba5815c55b8453", "Modules\\CarCatalog\\Models\\TransmissionType.php": "36144c6065bca0e77c6ebf5539d1747a", "Modules\\CarCatalog\\Providers\\CarCatalogServiceProvider.php": "e2a3439296201260d370315cd27bf1e9", "Modules\\CarCatalog\\Providers\\RouteServiceProvider.php": "9e78648c771f14f90aa8c0113958f38a", "Modules\\CarCatalog\\Resources\\views\\admin\\bodytypes\\create.blade.php": "2149735986635945e912b8461cbc9d42", "Modules\\CarCatalog\\Resources\\views\\admin\\bodytypes\\edit.blade.php": "aa550762a950387a2c525dd7bb39daa5", "Modules\\CarCatalog\\Resources\\views\\admin\\bodytypes\\index.blade.php": "1c63b7d8e7b7e4a41a99124812dde15d", "Modules\\CarCatalog\\Resources\\views\\admin\\bodytypes\\show.blade.php": "0d1747f90120f3677d91ca36f805b68d", "Modules\\CarCatalog\\Resources\\views\\admin\\bodytypes\\_form.blade.php": "4b9e7558744763881753fe2e69ed109e", "Modules\\CarCatalog\\Resources\\views\\admin\\brands\\create.blade.php": "88c42ba92561b5e0c35b9816fc9065b0", "Modules\\CarCatalog\\Resources\\views\\admin\\brands\\edit.blade.php": "70fb4b4490062b21bc63603c7a02cc51", "Modules\\CarCatalog\\Resources\\views\\admin\\brands\\index.blade.php": "6f79501f6939a398e27533d30161f25a", "Modules\\CarCatalog\\Resources\\views\\admin\\brands\\_form.blade.php": "aeaaaaf7e7925e391c25fb7c507f82e5", "Modules\\CarCatalog\\Resources\\views\\admin\\carfeatures\\create.blade.php": "8fb504b183a63ad550db5498f2d3d88a", "Modules\\CarCatalog\\Resources\\views\\admin\\carfeatures\\edit.blade.php": "938f52b1627d4f99d896bc94acdb807c", "Modules\\CarCatalog\\Resources\\views\\admin\\carfeatures\\index.blade.php": "fa9f6c2d5548ad89cce253652aebe8fe", "Modules\\CarCatalog\\Resources\\views\\admin\\carfeatures\\show.blade.php": "73a336f8dedcc7edc3c5928145ef7ca0", "Modules\\CarCatalog\\Resources\\views\\admin\\carfeatures\\_form.blade.php": "69f21426197e32884dfa44dde298b11a", "Modules\\CarCatalog\\Resources\\views\\admin\\cars\\create.blade.php": "94b9c6f230ec11c7244d067ecab51e48", "Modules\\CarCatalog\\Resources\\views\\admin\\cars\\edit.blade.php": "c1563177ae64350b4e27a0a448a3e016", "Modules\\CarCatalog\\Resources\\views\\admin\\cars\\index.blade.php": "a115a06015f7bf069e4c958c09a56346", "Modules\\CarCatalog\\Resources\\views\\admin\\cars\\partials\\stepper_basic_info.blade.php": "924c388ccff0d1b21ad843764ccd54a4", "Modules\\CarCatalog\\Resources\\views\\admin\\cars\\partials\\stepper_features.blade.php": "46a0717625b9c811c91f93fdc476ebfd", "Modules\\CarCatalog\\Resources\\views\\admin\\cars\\partials\\stepper_images.blade.php": "63fb88ed112fd2d0b26b83065806721e", "Modules\\CarCatalog\\Resources\\views\\admin\\cars\\partials\\stepper_price_status.blade.php": "f3256b41ea21947340a023cbec643451", "Modules\\CarCatalog\\Resources\\views\\admin\\cars\\partials\\stepper_tech_specs.blade.php": "5037938a2111a5414aa5c04c80fa246d", "Modules\\CarCatalog\\Resources\\views\\admin\\cars\\show.blade.php": "9fe2fde903f32511e3ffeee48a855119", "Modules\\CarCatalog\\Resources\\views\\admin\\colors\\create.blade.php": "cfaa9653464bcf756fa8e7cab2fd27d3", "Modules\\CarCatalog\\Resources\\views\\admin\\colors\\edit.blade.php": "502c7012e1dddf622512785e24f3a024", "Modules\\CarCatalog\\Resources\\views\\admin\\colors\\index.blade.php": "3377668084ce3bf19c179f0edb0edf42", "Modules\\CarCatalog\\Resources\\views\\admin\\colors\\show.blade.php": "06440f47c568549c8c68b2a34d4be3fa", "Modules\\CarCatalog\\Resources\\views\\admin\\colors\\_form.blade.php": "df5d6d98f4e0b404c86565777170495b", "Modules\\CarCatalog\\Resources\\views\\admin\\featurecategories\\create.blade.php": "6ca78c98cb95106b728acc6fa15024d5", "Modules\\CarCatalog\\Resources\\views\\admin\\featurecategories\\edit.blade.php": "b5f47c7ab6bd43490259c4d13befa4e7", "Modules\\CarCatalog\\Resources\\views\\admin\\featurecategories\\index.blade.php": "e1d99ac607aca8538a0712d42bdcb6bd", "Modules\\CarCatalog\\Resources\\views\\admin\\featurecategories\\show.blade.php": "8882bd63c3f985a1cbddcfe797b60eca", "Modules\\CarCatalog\\Resources\\views\\admin\\featurecategories\\_form.blade.php": "8b2db82247dce545352a8cec2eeead65", "Modules\\CarCatalog\\Resources\\views\\admin\\fuel-types\\create.blade.php": "86cb692751e391b0d5fe3864f804be6a", "Modules\\CarCatalog\\Resources\\views\\admin\\fuel-types\\edit.blade.php": "00687c5e32d4bccb395db7081c4171e2", "Modules\\CarCatalog\\Resources\\views\\admin\\fuel-types\\index.blade.php": "e8b0b7720c1bdc8602ddbcbe56751ae6", "Modules\\CarCatalog\\Resources\\views\\admin\\fuel-types\\show.blade.php": "2db56bf6d0608f89f449ed7fc4e80f69", "Modules\\CarCatalog\\Resources\\views\\admin\\fuel-types\\_form.blade.php": "e4f6e503d2089cd90064520a29c3d50f", "Modules\\CarCatalog\\Resources\\views\\admin\\models\\create.blade.php": "7a34e682b4d5480d3f9298ec2ce30891", "Modules\\CarCatalog\\Resources\\views\\admin\\models\\edit.blade.php": "1f71a26801ab4f80c3fb4deab150e3d4", "Modules\\CarCatalog\\Resources\\views\\admin\\models\\index.blade.php": "75e319504ccc8208b8c2ee7d7626b53b", "Modules\\CarCatalog\\Resources\\views\\admin\\models\\_form.blade.php": "0419a9d7f7c7edcaa4e233e445773af4", "Modules\\CarCatalog\\Resources\\views\\admin\\transmission-types\\create.blade.php": "1d82d0e6f56dc4fa6493abee625cc6bd", "Modules\\CarCatalog\\Resources\\views\\admin\\transmission-types\\edit.blade.php": "2978ef44c303f1184a5f0690643e0192", "Modules\\CarCatalog\\Resources\\views\\admin\\transmission-types\\index.blade.php": "726114d28eef58fa755e70c3edfa4a7e", "Modules\\CarCatalog\\Resources\\views\\admin\\transmission-types\\show.blade.php": "2e9838254e9307992001ae33ee49ab59", "Modules\\CarCatalog\\Resources\\views\\admin\\transmission-types\\_form.blade.php": "bb2ba96821a831c6ef74d10226fd138a", "Modules\\CarCatalog\\Resources\\views\\admin\\years\\create.blade.php": "b2f21e643a5c1ea14c1741f2925084d3", "Modules\\CarCatalog\\Resources\\views\\admin\\years\\edit.blade.php": "9ee08b39a44f15e7f4c9a90947e4e5a0", "Modules\\CarCatalog\\Resources\\views\\admin\\years\\index.blade.php": "02a3d314f25b25421b0c5bf3e596bad7", "Modules\\CarCatalog\\Resources\\views\\admin\\years\\_form.blade.php": "342dc51bc95ffa3fc269e070aec3fb2a", "Modules\\CarCatalog\\Resources\\views\\index.blade.php": "2fdb29ce5af907c390d0c15955373825", "Modules\\CarCatalog\\Resources\\views\\layouts\\master.blade.php": "164a754a92cf863791e8a6e6faad36a3", "Modules\\CarCatalog\\Routes\\admin.php": "c38ad690400f6efaa1d316f856aa36fd", "Modules\\CarCatalog\\Routes\\api.php": "2e15a67b5ccc50d0f20507c2b3ead1d8", "Modules\\CarCatalog\\Routes\\web.php": "4d3e8e60bf8d2e52a8d482f1ec04d0bc"}}