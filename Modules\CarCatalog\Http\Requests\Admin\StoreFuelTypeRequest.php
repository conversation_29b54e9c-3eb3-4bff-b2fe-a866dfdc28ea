<?php

namespace Modules\CarCatalog\Http\Requests\Admin;

use Modules\Core\Http\Requests\BaseRequest;

/**
 * طلب تخزين نوع وقود جديد.
 *
 * يتحقق هذا الطلب من صحة بيانات إضافة نوع وقود جديد
 */
class StoreFuelTypeRequest extends BaseRequest
{
    /**
     * الحصول على قواعد التحقق من صحة البيانات المطبقة على الطلب.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'        => 'required|string|max:50|unique:fuel_types,name',
            'description' => 'nullable|string|max:500',
            'status'      => 'required|boolean',
        ];
    }

    /**
     * الحصول على رسائل الخطأ المخصصة للقواعد المحددة.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required'      => 'اسم نوع الوقود مطلوب',
            'name.string'        => 'اسم نوع الوقود يجب أن يكون نصًا',
            'name.max'           => 'اسم نوع الوقود يجب ألا يتجاوز 50 حرف',
            'name.unique'        => 'اسم نوع الوقود موجود بالفعل',
            'description.string' => 'الوصف يجب أن يكون نصًا',
            'description.max'    => 'الوصف يجب ألا يتجاوز 500 حرف',
            'status.required'    => 'حالة نوع الوقود مطلوبة',
            'status.boolean'     => 'حالة نوع الوقود يجب أن تكون صحيحة أو خاطئة',
        ];
    }

    /**
     * الحصول على أسماء الخصائص المخصصة للتحقق
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name'        => 'اسم نوع الوقود',
            'description' => 'الوصف',
            'status'      => 'الحالة',
        ];
    }
}
